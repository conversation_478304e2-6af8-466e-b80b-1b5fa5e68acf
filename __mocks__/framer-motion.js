
import classnames from "classnames";

export const AnimatePresence = ({ children }) => (
  <div className="mock-animate-presence">{children}</div>
);
const MockMotionDiv = ({ className, ...props }) => (
  <div className={classnames("mock-motion-div", className)} {...props} />
);
const MockMotionI = ({ className, ...props }) => (
  <i className={classnames("mock-motion-i", className)} {...props} />
);

export const motion = {
  div: MockMotionDiv,
  i: MockMotionI
};
