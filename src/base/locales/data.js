export default {
  en: {
    "components.saving": "(saving ...)",
    "components.wait": "please wait ...",
    "components.error":
      "there was an error, please try again or contact support",
    "components.validate": "Validate",
    "components.page_sidebar_section.instance": "Instance {{number}}",
    "components.question_counter.number": "Number",
    "components.question_date_time.date": "Date",
    "components.question_date_time.time": "Time",
    "components.answer_source.discharge_date": "Discharge Date",
    "components.answer_source.registry": "Registry",
    "components.answer_source.source": "Source:",
    "components.answer_source.visit": "Visit",
    "components.collection_long_list.selected": "Your selected options",
    "components.collection_long_list.none": "No options currently selected",
    "errors.403": "You are not authorized to perform that action",
    "errors.500": "The server failed to fulfill the request",
    "errors.404.facility":
      "The questionnaire you are trying to view is not associated with the facility you are currently logged into. Please verify you are logged into the correct facility, and try again.",
    "application.add_questionnaire": "Add Questionnaire",
    "application.add_need_help": "need help? ",
    "application.patients": "Patients",
    "application.select_text": "Please select ...",
    "application.visit_remote_support": "visit remote support &#8594;",
    "titles.questionnaire.main": "{{name}} Questionnaire",
    "titles.questionnaire.sub": "{{count}} questions",
    "titles.questionnaires.main": "All Questionnaires",
    "titles.questionnaires.sub": "",
    "titles.validation_report.main": "Validation Report",
    "titles.validation_report.sub": "",
    "navigation.back": "Back",
    "navigation.go_back": "go back",
    "navigation.back_to_patient": "Back to patient",
    "navigation.back_to_case": "Back to case",
    "navigation.save_and_close": "Save and close",
    "navigation.next": "Next Section",
    "navigation.questionnaire": "Questionnaire",
    "questionnaire.all_questions": "All Questions for this questionnaire",
    "questionnaire.question_id": "ID",
    "questionnaire.question_prompt": "Prompt",
    "questionnaire_response.section_wrap": "Choose Registry Section",
    "questionnaire_response.section_form_elements": "Registry Section Elements",
    "questionnaire_response.section_notes": "Notes",
    "sts_validation_report.none": "No results",
    "sts_validation_report.form.fields": "Fields",
    "sts_validation_report.form.start_date": "Surgical start date",
    "sts_validation_report.form.end_date": "Surgical end date",
    "sts_validation_report.form.filter": "Filter",
    "sts_validation_report.form.clear": "Clear",
    "sts_validation_report.form.print": "Print",
    "sts_validation_report.form.error_type": "Type of Error",
    "sts_validation_report.row.name": "Patient Name",
    "sts_validation_report.row.mrn": "MRN",
    "sts_validation_report.row.time_period": "Time period",
    "sts_validation_report.row.admitted": "Admitted Date",
    "sts_validation_report.row.errors": "Errors",
    "sts_validation_report.requirement.should": "Should",
    "sts_validation_report.requirement.must": "Must",
    "sts_validation_report.heading.errors": "Errors",
    "sts_validation_report.heading.warnings": "Warnings",
    "validation_report.none": "No results",
    "validation_report.form.year": "Year of Discharge",
    "validation_report.form.quarter": "Quarter of Discharge",
    "validation_report.form.first": "First",
    "validation_report.form.second": "Second",
    "validation_report.form.third": "Third",
    "validation_report.form.fourth": "Fourth",
    "validation_report.form.no_discharge": "No Discharge Date",
    "validation_report.form.filter": "Filter",
    "validation_report.form.clear": "Clear",
    "validation_report.form.error_type": "Type of Error",
    "validation_report.form.date_option": "Date Option",
    "validation_report.row.name": "Patient Name",
    "validation_report.row.mrn": "MRN",
    "validation_report.row.discharge": "Discharge Date",
    "validation_report.row.admitted": "Admitted Date",
    "validation_report.row.errors": "Errors",
    "validation_report.date_selection.current_quarter": "Current Quarter",
    "validation_report.date_selection.previous_quarter": "Previous Quarter",
    "validation_report.date_selection.custom": "Custom date range",
    "validation_report.date_selection.no_discharge_date":
      "Include no discharge date",
    "validation_report.error_label": "All Validation Types",
    "validation_report.error_types.error": "Error",
    "validation_report.error_types.warning": "Warning",
    "validation_report.error_types.presence": "Presence",
    "validation_report.error_types.unusualData": "Unusual Data",
    "validation_report.edit_label": "Edits",
    "validation_report.edit_types.State": "State",
    "validation_report.edit_types.Seer": "SEER",
    "validation_report.edit_types.Coc": "CoC",
    "validation_report.edit_CoC_label": "CoC",
    "validation_report.edit_types.NCDB": "NCDB",
    "validation_report.edit_types.RCRS": "RCRS",
    "validation_report.filter_table.surgery_date": "Surgery Date",
    "validation_report.requirement.warning": "Should",
    "validation_report.requirement.error": "Must",
    "validation_report.heading.error": "Errors",
    "validation_report.heading.warning": "Warnings",
    "validation_report.heading.presence": "Blank fields",
    "validation_report.heading.registry": "Registry",
    "validation_report.heading.date_selection": "Date Selection",
    "validation_report.heading.validation_type": "Select Validation Type",
    "validation_report.progress_screen.validating":
      "Validating questionnaires...",
    "validation_report.section.filters": "Validation Report Filters",
    "validation_report.summary.exclude": "Exclude Fields",
    "validation_report.summary.include": "Include Fields",
    "validation_report.summary.load_more": "Load More (10+)",
    "validation_report.summary.overlay": "Run Report to refresh results",
    "validation_report.summary.prompt":
      "Make optional adjustments and rerun visible fields or view questionnaires with the following settings",
    "validation_report.summary.prompt_header": "Report Results Options",
    "validation_report.summary.responses_count":
      "{responsesCount, number} {responsesCount, plural, one {Questionnaire Result} other {Questionnaire Results}}",
    "validation_report.summary.run_message":
      "Run report to see results and view options here",
    "validation_report.summary.view_all": "View All",
    "user_tab.logout": "Logout",
    "navbar.qcentrix": "Q-Centrix",
    "navbar.qapps": "Q-Apps",
    "footer.need_help": "need help?",
    "footer.remote_support": "visit remote support →",
    "footer.support_email": "mailto:<EMAIL>",
    "header.assigned_abstractor": "Assigned Abstractor",
    "header.assigned_date": "Assigned Date",
    "navigation.ordinal":
      "{index, selectordinal, one {#st} two {#nd} few {#rd} other {#th}} Course",
    "navigation.instance": "Instance {index}"
  }
};
