import { applyMiddleware, createStore } from "redux";
import thunk from "redux-thunk";
import { composeWithDevTools } from "redux-devtools-extension/developmentOnly";

import reducer from "./reducer";

export function configureStore() {
  const store = createStore(
    reducer,
    composeWithDevTools(applyMiddleware(thunk))
  );

  if (module.hot) {
    module.hot.accept("./reducer", () => {
      // eslint-disable-next-line global-require
      const nextReducer = require("./reducer").default;

      store.replaceReducer(nextReducer);
    });
  }

  return store;
}

export default configureStore;
