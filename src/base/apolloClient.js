import {
  ApolloClient,
  ApolloLink,
  createHttpLink,
  InMemoryCache
} from "@apollo/client";
import { onError } from "@apollo/client/link/error";

import { getCSRFToken } from "utils/serverRequest";

import possibleTypes from "./possibleTypes.json";
import redirectToLogin from "utils/redirectToLogin";

/**
 * Afterware to handle Apollo request errors.
 * For now only 401 is handled to redirect to login
 */
const errorLink = onError(({ networkError }) => {
  if (networkError && networkError.statusCode === 401) {
    redirectToLogin();
  }
});

/**
 * Middleware that adds the XSRF token to header on every Apollo request
 */

const middlewareLink = new ApolloLink((operation, forward) => {
  operation.setContext({
    headers: {
      "X-CSRF-Token": getCSRFToken()
    }
  });
  return forward(operation);
});

const getLink = url => {
  /**
   * Initialize the Apollo network interface configuration.
   */
  let link = createHttpLink({
    uri: url,
    credentials: "same-origin"
  });

  /**
   * Adds middleware(s) to every Apollo requests
   */
  link = middlewareLink.concat(link);
  link = errorLink.concat(link);

  return link;
};

// Creates Cache to hold objects
export const cache = new InMemoryCache({
  possibleTypes
});

export const apolloClient = url =>
  new ApolloClient({
    link: getLink(url),
    cache
  });

export default apolloClient;
