{"DisplayLogic": ["BooleanSequenceDisplayLogic", "CounterTableDisplayLogic", "EnumerableTableDisplayLogic", "OtherDisplayLogic", "QuestionListDisplayLogic", "QuestionFillButtonDisplayLogic", "SectionDisplayLogic", "Subheader<PERSON><PERSON>playLog<PERSON>", "TableDisplayLogic", "ThreeColumnDisplayLogic", "TwoColumnDisplayLogic", "QuestionCopyButtonDisplayLogic"], "Question": ["CalculatedQuestion", "CounterQuestion", "DateOrEnumerableQuestion", "DependentEnumerableQuestion", "DependentExternalEnumerableSearchableQuestion", "DependentFilteredEnumerableSearchableQuestion", "DateQuestion", "DateTimeOrEnumerableQuestion", "EnumerableQuestion", "EnumerableCollectionQuestion", "EnumerableCollectionLongListQuestion", "EnumerableCollectionSearchableQuestion", "EnumerableOrEnumerableCollectionQuestion", "EnumerableOrOpenQuestion", "EnumerableSearchableQuestion", "EnumerableCollectionSearchableFavoritableQuestion", "EnumerableSearchableFavoritableQuestion", "GeneratedQuestion", "DateQuestion", "DateOrEnumerableQuestion", "DateTimeOrEnumerableQuestion", "DateTimeQuestion", "NumberQuestion", "NumberOrEnumerableQuestion", "TimeStringOrEnumerableQuestion", "NumberSearchableQuestion", "NumberWithEnumerableQuestion", "NumberWithUnitQuestion", "OpenQuestion", "OtherQuestion", "RepeatableEnumerableQuestion", "TextAreaQuestion", "TimeStringQuestion"], "Rule": ["EnumerableEqualityRule", "OtherRule", "UnconditionalRule", "EnumerableInclusionRule"], "BaseAnswer": ["Answer", "AnswerWithWarnings", "AnswerWithErrors"], "RegistryConfigurationTableData": ["MaintenanceData", "StateData", "CoCData", "ExtractData", "ConversionData", "ImportData"]}