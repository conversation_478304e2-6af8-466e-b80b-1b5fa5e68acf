/* eslint-disable react/sort-prop-types */

import {
  HeaderFacilitySelector,
  SecondaryToolbar,
  SideNavMenu,
  Toaster,
  Topbar
} from "@q-centrix/q-components-react";
import PropTypes from "prop-types";
import "styles/layout.scss";

const Layout = ({
  tbChildren,
  tbOnClick,
  stTitle,
  stChildren,
  stContainerClassName,
  stTitleClassName,
  stChildrenClassName,
  children,
  client,
  onBackArrowClick,
  onLogoClick
}) => {
  const shouldDisplaySecondaryToolbar = stTitle || stChildren;

  return (
    <div className="tw-flex tw-overflow-y-auto tw-min-h-screen">
      <Toaster />
      <SideNavMenu />
      <section className="tw-flex tw-flex-col tw-flex-1">
        <header className="tw-z-50 layout-header tw-shadow-qc">
          <Topbar
            client={client}
            onBackArrowClick={onBackArrowClick}
            onClick={tbOnClick}
            onLogoClick={onLogoClick}
          >
            <HeaderFacilitySelector />
            {tbChildren}
          </Topbar>
          {shouldDisplaySecondaryToolbar && (
            <SecondaryToolbar
              childrenClassName={stChildrenClassName}
              containerClassName={stContainerClassName}
              title={stTitle}
              titleClassName={stTitleClassName}
            >
              {stChildren}
            </SecondaryToolbar>
          )}
        </header>
        <div className="tw-h-full">{children}</div>
      </section>
    </div>
  );
};

Layout.propTypes = {
  tbChildren: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
  ]),
  stChildren: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
  ]),
  stTitle: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  tbOnClick: PropTypes.func,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
  ]),
  onBackArrowClick: PropTypes.func,
  onLogoClick: PropTypes.func
};

export default Layout;
