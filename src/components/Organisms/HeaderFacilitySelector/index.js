import React, { useState, useRef, useEffect } from "react";
import PropTypes from "prop-types";
import Tag from "../../Molecules/Tag";
import Select from "../Select";
import { useComponentLogic } from "../SideNavMenu/FacilitySelector/hooks";

/**
 * HeaderFacilitySelector - A facility selector component designed for the primary header
 * Uses a blue tag with integrated chevron and dropdown functionality to display and select facilities
 *
 * @param {function} onFacilityChange - Callback function when facility changes
 */
const HeaderFacilitySelector = ({ onFacilityChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const tagRef = useRef(null);

  const {
    formattedFacility,
    options,
    page,
    totalCount,
    handleChange,
    handlePageChange,
    handleSearchChange
  } = useComponentLogic({ onFacilityChange, smallPagination: true });

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = event => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        tagRef.current &&
        !tagRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    // eslint-disable-next-line no-undef
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // eslint-disable-next-line no-undef
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleTagClick = () => {
    setIsOpen(!isOpen);
  };

  const handleSelectChange = selectedOption => {
    handleChange(selectedOption);
    setIsOpen(false);
  };

  const displayText = formattedFacility?.label || "Select Facility";

  return (
    <div className="tw-relative tw-flex tw-items-center">
      <div ref={tagRef} className="tw-flex tw-items-center tw-gap-0">
        <Tag
          status="blue"
          text={displayText}
          onClick={handleTagClick}
          className="tw-cursor-pointer hover:tw-bg-qcInfo-200 tw-transition-colors !tw-pr-1"
        />
        <div 
          className="tw-flex tw-items-center tw-justify-center tw-w-6 tw-h-6 tw-bg-qcInfo-100 tw-rounded-r-full tw-cursor-pointer hover:tw-bg-qcInfo-200 tw-transition-colors tw-ml-[-6px]"
          onClick={handleTagClick}
        >
          <i
            className={`fa-solid fa-chevron-${
              isOpen ? "up" : "down"
            } tw-text-xs tw-text-qcInfo-800`}
          />
        </div>
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="tw-absolute tw-top-full tw-left-0 tw-mt-2 tw-z-50 tw-min-w-[350px] tw-bg-white tw-border tw-border-gray-200 tw-rounded-md tw-shadow-lg"
        >
          <div className="tw-p-3">
            <Select
              id="header-facility-selector"
              name="header-facility-selector"
              label=""
              value={formattedFacility}
              options={options}
              page={page}
              totalCount={totalCount}
              onChange={handleSelectChange}
              onPageChange={handlePageChange}
              onInputChange={handleSearchChange}
              smallPagination={true}
              placeholder="Search facilities..."
              isSearchable={true}
              containerClassName=""
              feedbackText=""
              feedbackType="neutral"
              feedbackClassName=""
              labelClassName=""
              clearable={false}
              inputClassName=""
            />
          </div>
        </div>
      )}
    </div>
  );
};

HeaderFacilitySelector.propTypes = {
  onFacilityChange: PropTypes.func
};

HeaderFacilitySelector.defaultProps = {
  onFacilityChange: undefined
};

export default HeaderFacilitySelector;
