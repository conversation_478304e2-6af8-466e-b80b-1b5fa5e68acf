import Button from ".";

/**
 * `Button` is an Atom Level component designed to be the default button design.
 **/
export default {
  component: Button,
  tags: ["autodocs"],
  args: {
    children: "Button",
    disabled: false,
    outline: false,
    customStyle: ""
  },
  argTypes: {
    children: {
      control: "text"
    },
    bg: {
      control: { type: "select" },
      options: [
        "main",
        "success",
        "warning",
        "danger",
        "neutral",
        "default",
        "qcMidnightIrisGradient",
        "qcIrisSkyGradient",
        "qcOceanSkyGradient",
        "qcNightOceanGradient",
        "qcMidnightPurple",
        "qcNightSlateGradient"
      ]
    },
    customStyle: {
      control: false
    }
  }
};

export const Default = {
  args: {
    bg: "default"
  }
};
