import React, { forwardRef, ReactNode, ButtonHTMLAttributes } from "react";
import classnames from "classnames";
import twMerge from "../../../utils/tailwind/twMerge";

type BgType =
  | "main"
  | "success"
  | "warning"
  | "danger"
  | "neutral"
  | "default"
  | "qcMidnightIrisGradient"
  | "qcIrisSkyGradient"
  | "qcOceanSkyGradient"
  | "qcNightOceanGradient"
  | "qcMidnightPurple"
  | "qcNightSlateGradient";

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  bg?: BgType;
  disabled?: boolean;
  outline?: boolean;
  onClick?: () => void;
  children: ReactNode;
  customStyle?: string;
}

const bgClassMap: Record<BgType, { solid: string; outline: string }> = {
  main: {
    solid: "tw-bg-qcOceanBlue tw-text-white",
    outline:
      "tw-bg-qcInfo-100 tw-border-qcInfo-200 tw-text-qcInfo-900 tw-border-2"
  },
  success: {
    solid: "tw-bg-qcSuccess-700 tw-text-white",
    outline:
      "!tw-bg-qcSuccess-100 tw-border-qcSuccess-300 tw-text-qcSuccess-900 tw-border-2"
  },
  warning: {
    solid: "tw-bg-qcWarning-500 tw-text-black",
    outline:
      "tw-bg-qcWarning-200 tw-border-qcWarning-500 tw-text-qcSunset-900 tw-border-2"
  },
  danger: {
    solid: "tw-bg-qcDanger-700 tw-text-white",
    outline:
      "tw-bg-qcDanger-100 tw-border-qcDanger-200 tw-text-qcDanger-900 tw-border-2"
  },
  neutral: {
    solid: "tw-bg-qcNeutrals-700 tw-text-white",
    outline:
      "tw-bg-qcNeutrals-300 tw-text-qcNeutrals-800 tw-border-qcNeutrals-400 tw-border-2"
  },
  default: {
    solid: "tw-bg-qcIris-700 tw-text-white",
    outline:
      "tw-bg-qcIris-100 tw-border-qcIris-200 tw-text-qcIris-900 tw-border-2"
  },
  qcMidnightIrisGradient: {
    solid: "tw-bg-qcMidnightIris tw-background-button tw-text-white",
    outline: "tw-bg-qcMidnightIris tw-background-button tw-text-white"
  },
  qcIrisSkyGradient: {
    solid: "tw-bg-qcIrisSky tw-background-button tw-text-white",
    outline: "tw-bg-qcIrisSky tw-background-button tw-text-white"
  },
  qcOceanSkyGradient: {
    solid: "tw-bg-qcOceanSky tw-background-button tw-text-white",
    outline: "tw-bg-qcOceanSky tw-background-button tw-text-white"
  },
  qcNightOceanGradient: {
    solid: "tw-bg-qcNightOcean tw-background-button tw-text-white",
    outline: "tw-bg-qcNightOcean tw-background-button tw-text-white"
  },
  qcMidnightPurple: {
    solid: "tw-bg-qcMidnightPurple tw-text-white",
    outline: "tw-bg-qcMidnightPurple tw-text-white"
  },
  qcNightSlateGradient: {
    solid: "tw-bg-qcNightSlate tw-background-button tw-text-white",
    outline: "tw-bg-qcNightSlate tw-background-button tw-text-white"
  }
};

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      bg = "main",
      disabled = false,
      outline = false,
      onClick = () => {},
      children,
      customStyle = "",
      ...props
    },
    ref
  ) => {
    const bgStyles = bgClassMap[bg];
    const baseClasses =
      "tw-inline-flex tw-h-10 tw-px-4 tw-py-3 tw-justify-center tw-items-center tw-gap-3 tw-shrink-0 tw-rounded-md tw-font-inter tw-text-sm tw-not-italic tw-font-semibold tw-leading-normal hover:tw-shadow-button hover:tw-cursor-pointer hover:tw-opacity-92 hover:tw-buttonHover disabled:tw-opacity-[0.38] disabled:tw-cursor-default disabled:tw-shadow-none";
    const colorClasses = outline ? bgStyles.outline : bgStyles.solid;

    const className = twMerge(
      classnames(baseClasses, colorClasses, customStyle)
    );

    return (
      <button
        ref={ref}
        disabled={disabled}
        onClick={onClick}
        className={className}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
