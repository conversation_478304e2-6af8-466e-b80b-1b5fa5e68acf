// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Button renders component 1`] = `
<button
  className="tw-inline-flex tw-h-10 tw-px-4 tw-py-3 tw-justify-center tw-items-center tw-gap-3 tw-shrink-0 tw-rounded-md tw-font-inter tw-text-sm tw-not-italic tw-font-semibold tw-leading-normal hover:tw-shadow-button hover:tw-cursor-pointer hover:tw-opacity-92 hover:tw-buttonHover disabled:tw-opacity-[0.38] disabled:tw-cursor-default disabled:tw-shadow-none tw-bg-qcOceanBlue tw-text-white"
  disabled={false}
  onClick={[Function]}
/>
`;

exports[`Button renders component with bg = danger and children = Danger with onClick = alert 1`] = `
<button
  className="tw-inline-flex tw-h-10 tw-px-4 tw-py-3 tw-justify-center tw-items-center tw-gap-3 tw-shrink-0 tw-rounded-md tw-font-inter tw-text-sm tw-not-italic tw-font-semibold tw-leading-normal hover:tw-shadow-button hover:tw-cursor-pointer hover:tw-opacity-92 hover:tw-buttonHover disabled:tw-opacity-[0.38] disabled:tw-cursor-default disabled:tw-shadow-none tw-bg-qcDanger-700 tw-text-white"
  disabled={false}
  onClick={[Function]}
>
  Danger
</button>
`;

exports[`Button renders component with bg = main and text = Button, outline = true with onClick = alert 1`] = `
<button
  Children="Button"
  className="tw-inline-flex tw-h-10 tw-px-4 tw-py-3 tw-justify-center tw-items-center tw-gap-3 tw-shrink-0 tw-rounded-md tw-font-inter tw-text-sm tw-not-italic tw-font-semibold tw-leading-normal hover:tw-shadow-button hover:tw-cursor-pointer hover:tw-opacity-92 hover:tw-buttonHover disabled:tw-opacity-[0.38] disabled:tw-cursor-default disabled:tw-shadow-none tw-bg-qcInfo-100 tw-border-qcInfo-200 tw-text-qcInfo-900 tw-border-2"
  disabled={false}
  onClick={[Function]}
/>
`;

exports[`Button renders component with bg = success and children = Success 1`] = `
<button
  className="tw-inline-flex tw-h-10 tw-px-4 tw-py-3 tw-justify-center tw-items-center tw-gap-3 tw-shrink-0 tw-rounded-md tw-font-inter tw-text-sm tw-not-italic tw-font-semibold tw-leading-normal hover:tw-shadow-button hover:tw-cursor-pointer hover:tw-opacity-92 hover:tw-buttonHover disabled:tw-opacity-[0.38] disabled:tw-cursor-default disabled:tw-shadow-none tw-bg-qcSuccess-700 tw-text-white"
  disabled={false}
  onClick={[Function]}
>
  Success
</button>
`;
