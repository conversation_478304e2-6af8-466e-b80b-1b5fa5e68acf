import React from "react";
import { create } from "react-test-renderer";
import { Button } from "../index";

const render = props => create(<Button {...props}>{props.children}</Button>);

describe("Button", () => {
  test("renders component", () => {
    const component = render({ children: null });

    expect(component).toMatchSnapshot();
  });
  test("renders component with bg = success and children = Success", () => {
    const component = render({
      bg: "success",
      children: "Success"
    });

    expect(component).toMatchSnapshot();
  });
  test("renders component with bg = danger and children = Danger with onClick = alert", () => {
    const component = render({
      bg: "danger",
      children: "Danger",
      onClick: () => alert("Danger!")
    });

    expect(component).toMatchSnapshot();
  });
  test("renders component with bg = main and text = Button, outline = true with onClick = alert", () => {
    const component = render({
      bg: "main",
      Children: "Button",
      outline: true,

      onClick: () => alert("Regular Button")
    });

    expect(component).toMatchSnapshot();
  });
});
