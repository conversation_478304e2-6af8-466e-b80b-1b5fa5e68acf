/* line 1, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle,
.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
  margin-left: -8px;
  position: absolute;
}

/* line 8, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle,
.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow,
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker__year-read-view--down-arrow::before,
.react-datepicker__month-read-view--down-arrow::before,
.react-datepicker__month-year-read-view--down-arrow::before {
  box-sizing: content-box;
  position: absolute;
  border: 8px solid transparent;
  height: 0;
  width: 1px;
}

/* line 20, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker__year-read-view--down-arrow::before,
.react-datepicker__month-read-view--down-arrow::before,
.react-datepicker__month-year-read-view--down-arrow::before {
  content: "";
  z-index: -1;
  border-width: 8px;
  left: -8px;
  border-bottom-color: #aeaeae;
}

/* line 30, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
  top: 0;
  margin-top: -8px;
}

/* line 35, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle,
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before {
  border-top: none;
  border-bottom-color: #f0f0f0;
}

/* line 40, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before {
  top: -1px;
  border-bottom-color: #aeaeae;
}

/* line 45, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
  bottom: 0;
  margin-bottom: -8px;
}

/* line 52, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow,
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker__year-read-view--down-arrow::before,
.react-datepicker__month-read-view--down-arrow::before,
.react-datepicker__month-year-read-view--down-arrow::before {
  border-bottom: none;
  border-top-color: #fff;
}

/* line 61, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker__year-read-view--down-arrow::before,
.react-datepicker__month-read-view--down-arrow::before,
.react-datepicker__month-year-read-view--down-arrow::before {
  bottom: -1px;
  border-top-color: #aeaeae;
}

/* line 68, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-wrapper {
  display: inline-block;
  padding: 0 0.3rem 0 0;
  border: 0;
}

/* line 74, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 0.8rem;
  background-color: #fff;
  color: #000;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  display: inline-block;
  position: relative;
}

/* line 85, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker--time-only .react-datepicker__triangle {
  left: 35px;
}

/* line 89, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker--time-only .react-datepicker__time-container {
  border-left: 0;
}

/* line 93, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker--time-only .react-datepicker__time {
  border-radius: 0.3rem;
}

/* line 97, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker--time-only .react-datepicker__time-box {
  border-radius: 0.3rem;
}

/* line 101, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__triangle {
  position: absolute;
  left: 50px;
}

/* line 106, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper {
  z-index: 1;
}

/* line 110, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="bottom"] {
  margin-top: 10px;
}

/* line 114, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="top"] {
  margin-bottom: 10px;
}

/* line 118, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="right"] {
  margin-left: 8px;
}

/* line 122, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="right"] .react-datepicker__triangle {
  left: auto;
  right: 42px;
}

/* line 127, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="left"] {
  margin-right: 8px;
}

/* line 131, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-popper[data-placement^="left"] .react-datepicker__triangle {
  left: 42px;
  right: auto;
}

/* line 136, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__header {
  text-align: center;
  background-color: #f0f0f0;
  border-bottom: 1px solid #aeaeae;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  padding-top: 8px;
  position: relative;
}

/* line 146, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__header--time {
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

/* line 152, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-dropdown-container--select,
.react-datepicker__month-dropdown-container--select,
.react-datepicker__month-year-dropdown-container--select,
.react-datepicker__year-dropdown-container--scroll,
.react-datepicker__month-dropdown-container--scroll,
.react-datepicker__month-year-dropdown-container--scroll {
  display: inline-block;
  margin: 0 2px;
}

/* line 162, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  margin-top: 0;
  color: #000;
  font-weight: bold;
  font-size: 0.944rem;
}

/* line 171, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker-time__header {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* line 177, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation {
  background: none;
  line-height: 1.7rem;
  text-align: center;
  cursor: pointer;
  position: absolute;
  top: 10px;
  width: 0;
  padding: 0;
  border: 0.45rem solid transparent;
  z-index: 1;
  height: 10px;
  width: 10px;
  text-indent: -999em;
  overflow: hidden;
}

/* line 194, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--previous {
  left: 10px;
  border-right-color: #ccc;
}

/* line 199, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--previous:hover {
  border-right-color: #b3b3b3;
}

/* line 203, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--previous--disabled,
.react-datepicker__navigation--previous--disabled:hover {
  border-right-color: #e6e6e6;
  cursor: default;
}

/* line 208, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--next {
  right: 10px;
  border-left-color: #ccc;
}

/* line 213, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--next--with-time:not(
    .react-datepicker__navigation--next--with-today-button
  ) {
  right: 80px;
}

/* line 217, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--next:hover {
  border-left-color: #b3b3b3;
}

/* line 221, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--next--disabled,
.react-datepicker__navigation--next--disabled:hover {
  border-left-color: #e6e6e6;
  cursor: default;
}

/* line 226, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--years {
  position: relative;
  top: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* line 234, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--years-previous {
  top: 4px;
  border-top-color: #ccc;
}

/* line 239, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--years-previous:hover {
  border-top-color: #b3b3b3;
}

/* line 243, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--years-upcoming {
  top: -4px;
  border-bottom-color: #ccc;
}

/* line 248, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__navigation--years-upcoming:hover {
  border-bottom-color: #b3b3b3;
}

/* line 252, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month-container {
  float: left;
}

/* line 256, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month {
  margin: 0.4rem;
  text-align: center;
}

/* line 261, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month .react-datepicker__month-text,
.react-datepicker__month .react-datepicker__quarter-text {
  display: inline-block;
  width: 4rem;
  margin: 2px;
}

/* line 268, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container {
  clear: both;
  width: 100%;
  float: left;
  margin: 5px 0 10px 15px;
  text-align: left;
}

/* line 276, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container .react-datepicker-time__caption {
  display: inline-block;
}

/* line 280, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container
  .react-datepicker-time__input-container {
  display: inline-block;
}

/* line 284, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input {
  display: inline-block;
  margin-left: 10px;
}

/* line 289, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input {
  width: 85px;
}

/* line 293, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input[type="time"]::-webkit-inner-spin-button,
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input[type="time"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* line 299, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input[type="time"] {
  -moz-appearance: textfield;
}

/* line 303, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__delimiter {
  margin-left: 5px;
  display: inline-block;
}

/* line 308, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container {
  float: right;
  border-left: 1px solid #aeaeae;
  width: 85px;
}

/* line 314, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container--with-today-button {
  display: inline;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  position: absolute;
  right: -72px;
  top: 0;
}

/* line 323, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container .react-datepicker__time {
  position: relative;
  background: white;
}

/* line 328, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box {
  width: 85px;
  overflow-x: hidden;
  margin: 0 auto;
  text-align: center;
}

/* line 335, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list {
  list-style: none;
  margin: 0;
  height: calc(195px + (1.7rem / 2));
  overflow-y: scroll;
  padding-right: 0px;
  padding-left: 0px;
  width: 100%;
  box-sizing: content-box;
}

/* line 346, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item {
  height: 30px;
  padding: 5px 10px;
  white-space: nowrap;
}

/* line 352, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item:hover {
  cursor: pointer;
  background-color: #f0f0f0;
}

/* line 357, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--selected {
  background-color: #216ba5;
  color: white;
  font-weight: bold;
}

/* line 363, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--selected:hover {
  background-color: #216ba5;
}

/* line 367, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--disabled {
  color: #ccc;
}

/* line 371, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--disabled:hover {
  cursor: default;
  background-color: transparent;
}

/* line 376, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__week-number {
  color: #ccc;
  display: inline-block;
  width: 1.7rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.166rem;
}

/* line 385, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__week-number.react-datepicker__week-number--clickable {
  cursor: pointer;
}

/* line 389, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__week-number.react-datepicker__week-number--clickable:hover {
  border-radius: 0.3rem;
  background-color: #f0f0f0;
}

/* line 394, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day-names,
.react-datepicker__week {
  white-space: nowrap;
}

/* line 399, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #000;
  display: inline-block;
  width: 1.7rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.166rem;
}

/* line 410, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month--selected,
.react-datepicker__month--in-selecting-range,
.react-datepicker__month--in-range,
.react-datepicker__quarter--selected,
.react-datepicker__quarter--in-selecting-range,
.react-datepicker__quarter--in-range {
  border-radius: 0.3rem;
  background-color: #216ba5;
  color: #fff;
}

/* line 419, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month--selected:hover,
.react-datepicker__month--in-selecting-range:hover,
.react-datepicker__month--in-range:hover,
.react-datepicker__quarter--selected:hover,
.react-datepicker__quarter--in-selecting-range:hover,
.react-datepicker__quarter--in-range:hover {
  background-color: #1d5d90;
}

/* line 426, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month--disabled,
.react-datepicker__quarter--disabled {
  color: #ccc;
  pointer-events: none;
}

/* line 432, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month--disabled:hover,
.react-datepicker__quarter--disabled:hover {
  cursor: default;
  background-color: transparent;
}

/* line 438, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day,
.react-datepicker__month-text,
.react-datepicker__quarter-text {
  cursor: pointer;
}

/* line 444, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day:hover,
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover {
  border-radius: 0.3rem;
  background-color: #f0f0f0;
}

/* line 451, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--today,
.react-datepicker__month-text--today,
.react-datepicker__quarter-text--today {
  font-weight: bold;
}

/* line 457, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--highlighted,
.react-datepicker__month-text--highlighted,
.react-datepicker__quarter-text--highlighted {
  border-radius: 0.3rem;
  background-color: #3dcc4a;
  color: #fff;
}

/* line 465, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--highlighted:hover,
.react-datepicker__month-text--highlighted:hover,
.react-datepicker__quarter-text--highlighted:hover {
  background-color: #32be3f;
}

/* line 471, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--highlighted-custom-1,
.react-datepicker__month-text--highlighted-custom-1,
.react-datepicker__quarter-text--highlighted-custom-1 {
  color: magenta;
}

/* line 477, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--highlighted-custom-2,
.react-datepicker__month-text--highlighted-custom-2,
.react-datepicker__quarter-text--highlighted-custom-2 {
  color: green;
}

/* line 483, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range {
  border-radius: 0.3rem;
  background-color: #216ba5;
  color: #fff;
}

/* line 495, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--selected:hover,
.react-datepicker__day--in-selecting-range:hover,
.react-datepicker__day--in-range:hover,
.react-datepicker__month-text--selected:hover,
.react-datepicker__month-text--in-selecting-range:hover,
.react-datepicker__month-text--in-range:hover,
.react-datepicker__quarter-text--selected:hover,
.react-datepicker__quarter-text--in-selecting-range:hover,
.react-datepicker__quarter-text--in-range:hover {
  background-color: #1d5d90;
}

/* line 505, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected {
  border-radius: 0.3rem;
  background-color: #2a87d0;
  color: #fff;
}

/* line 513, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__quarter-text--keyboard-selected:hover {
  background-color: #1d5d90;
}

/* line 519, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range {
  background-color: rgba(33, 107, 165, 0.5);
}

/* line 527, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__quarter-text--in-range {
  background-color: #f0f0f0;
  color: #000;
}

/* line 532, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--disabled,
.react-datepicker__month-text--disabled,
.react-datepicker__quarter-text--disabled {
  cursor: default;
  color: #ccc;
}

/* line 539, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__day--disabled:hover,
.react-datepicker__month-text--disabled:hover,
.react-datepicker__quarter-text--disabled:hover {
  background-color: transparent;
}

/* line 545, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month-text.react-datepicker__month--selected:hover,
.react-datepicker__month-text.react-datepicker__month--in-range:hover,
.react-datepicker__month-text.react-datepicker__quarter--selected:hover,
.react-datepicker__month-text.react-datepicker__quarter--in-range:hover,
.react-datepicker__quarter-text.react-datepicker__month--selected:hover,
.react-datepicker__quarter-text.react-datepicker__month--in-range:hover,
.react-datepicker__quarter-text.react-datepicker__quarter--selected:hover,
.react-datepicker__quarter-text.react-datepicker__quarter--in-range:hover {
  background-color: #216ba5;
}

/* line 553, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover {
  background-color: #f0f0f0;
}

/* line 558, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

/* line 564, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-read-view,
.react-datepicker__month-read-view,
.react-datepicker__month-year-read-view {
  border: 1px solid transparent;
  border-radius: 0.3rem;
}

/* line 571, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-read-view:hover,
.react-datepicker__month-read-view:hover,
.react-datepicker__month-year-read-view:hover {
  cursor: pointer;
}

/* line 577, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-read-view:hover
  .react-datepicker__year-read-view--down-arrow,
.react-datepicker__year-read-view:hover
  .react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-read-view:hover
  .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view:hover
  .react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view:hover
  .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-year-read-view:hover
  .react-datepicker__month-read-view--down-arrow {
  border-top-color: #b3b3b3;
}

/* line 586, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
  border-top-color: #ccc;
  float: right;
  margin-left: 20px;
  top: 8px;
  position: relative;
  border-width: 0.45rem;
}

/* line 597, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown,
.react-datepicker__month-year-dropdown {
  background-color: #f0f0f0;
  position: absolute;
  width: 50%;
  left: 25%;
  top: 30px;
  z-index: 1;
  text-align: center;
  border-radius: 0.3rem;
  border: 1px solid #aeaeae;
}

/* line 611, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-dropdown:hover,
.react-datepicker__month-dropdown:hover,
.react-datepicker__month-year-dropdown:hover {
  cursor: pointer;
}

/* line 617, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-dropdown--scrollable,
.react-datepicker__month-dropdown--scrollable,
.react-datepicker__month-year-dropdown--scrollable {
  height: 150px;
  overflow-y: scroll;
}

/* line 624, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option,
.react-datepicker__month-option,
.react-datepicker__month-year-option {
  line-height: 20px;
  width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* line 634, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option:first-of-type,
.react-datepicker__month-option:first-of-type,
.react-datepicker__month-year-option:first-of-type {
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

/* line 641, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option:last-of-type,
.react-datepicker__month-option:last-of-type,
.react-datepicker__month-year-option:last-of-type {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

/* line 652, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover,
.react-datepicker__month-year-option:hover {
  background-color: #ccc;
}

/* line 658, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option:hover
  .react-datepicker__navigation--years-upcoming,
.react-datepicker__month-option:hover
  .react-datepicker__navigation--years-upcoming,
.react-datepicker__month-year-option:hover
  .react-datepicker__navigation--years-upcoming {
  border-bottom-color: #b3b3b3;
}

/* line 664, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option:hover
  .react-datepicker__navigation--years-previous,
.react-datepicker__month-option:hover
  .react-datepicker__navigation--years-previous,
.react-datepicker__month-year-option:hover
  .react-datepicker__navigation--years-previous {
  border-top-color: #b3b3b3;
}

/* line 670, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected,
.react-datepicker__month-year-option--selected {
  position: absolute;
  left: 15px;
}

/* line 677, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__close-icon {
  background-color: transparent;
  border: 0;
  cursor: pointer;
  outline: 0;
  padding: 0;
  vertical-align: middle;
  position: absolute;
  height: 16px;
  width: 16px;
  top: 25%;
  right: 7px;
}

/* line 691, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__close-icon::after {
  cursor: pointer;
  background-color: #216ba5;
  color: #fff;
  border-radius: 50%;
  height: 16px;
  width: 16px;
  padding: 2px;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  display: table-cell;
  vertical-align: middle;
  content: "\00d7";
}

/* line 707, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__today-button {
  background: #f0f0f0;
  border-top: 1px solid #aeaeae;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  clear: left;
}

/* line 717, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  left: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  z-index: 2147483647;
}

/* line 730, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__day-name,
.react-datepicker__portal .react-datepicker__day,
.react-datepicker__portal .react-datepicker__time-name {
  width: 3rem;
  line-height: 3rem;
}

@media (max-width: 400px), (max-height: 550px) {
  /* line 738, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
  .react-datepicker__portal .react-datepicker__day-name,
  .react-datepicker__portal .react-datepicker__day,
  .react-datepicker__portal .react-datepicker__time-name {
    width: 2rem;
    line-height: 2rem;
  }
}

/* line 746, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__current-month,
.react-datepicker__portal .react-datepicker-time__header {
  font-size: 1.44rem;
}

/* line 751, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation {
  border: 0.81rem solid transparent;
}

/* line 755, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation--previous {
  border-right-color: #ccc;
}

/* line 759, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation--previous:hover {
  border-right-color: #b3b3b3;
}

/* line 763, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation--previous--disabled,
.react-datepicker__portal
  .react-datepicker__navigation--previous--disabled:hover {
  border-right-color: #e6e6e6;
  cursor: default;
}

/* line 768, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation--next {
  border-left-color: #ccc;
}

/* line 772, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation--next:hover {
  border-left-color: #b3b3b3;
}

/* line 776, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/react-overrides/_react-datepicker.sass */
.react-datepicker__portal .react-datepicker__navigation--next--disabled,
.react-datepicker__portal .react-datepicker__navigation--next--disabled:hover {
  border-left-color: #e6e6e6;
  cursor: default;
}

/* line 3, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination {
  align-items: center;
  color: #5a6e83;
  display: flex;
  margin-left: auto;
}

/* line 10, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages button {
  background: transparent;
  border: none;
  color: #5a6e83;
  font-size: 11px;
  font-weight: bold;
  margin: 0;
  margin-right: 4px;
  min-width: 6px;
  padding: 0 4px;
}

/* line 20, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages button:last-of-type {
  margin-right: 0;
}

/* line 22, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages button.active {
  border: 1px solid #227b9c;
  border-radius: 3px;
}

/* line 25, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages-select {
  display: flex;
  align-items: center;
}

/* line 28, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages-select label {
  font-weight: 600;
  font-size: 10px;
  margin-left: 12px;
  margin-right: 6px;
}

/* line 33, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages-select .page-select-container {
  border-radius: 0;
  font-size: 10px;
  max-height: 30px;
}

/* line 37, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages-select .page-select-container .page-select__control,
.table-pagination
  .pages-select
  .page-select-container
  .page-select__indicators {
  max-height: 24px;
  min-height: 1px;
}

/* line 41, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination .pages-select .page-select-container .page-select__control {
  width: 40px;
}

/* line 43, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination
  .pages-select
  .page-select-container
  .page-select__input
  > input {
  max-height: 22px;
}

/* line 45, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination
  .pages-select
  .page-select-container
  .page-select__value-container {
  max-height: 30px;
  max-width: 28px;
}

/* line 49, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination
  .pages-select
  .page-select-container
  .page-select__value-container
  div {
  height: 100%;
  margin: 0;
  max-height: 24px;
}

/* line 53, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination
  .pages-select
  .page-select-container
  .page-select__dropdown-indicator {
  max-width: 16px;
  padding: 0 4px 0 0;
}

/* line 56, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination
  .pages-select
  .page-select-container
  .page-select__dropdown-indicator
  svg {
  height: 9px;
  fill: #5a6e83;
  width: 9px;
}

/* line 60, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/components/_pagination.sass */
.table-pagination
  .pages-select
  .page-select-container
  .page-select__indicator-separator {
  visibility: hidden;
}

/* line 4, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.align-vert {
  display: flex;
  height: 100%;
}

/* line 8, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.align-child {
  align-self: center;
}

/* line 11, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
hr.border {
  border-bottom: 3px solid #e0e0e0;
  margin: 0px 0 18px;
}

/* line 15, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav {
  padding: 0 49px 0 39px;
}

/* line 17, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div {
  align-items: center;
  display: flex;
  justify-content: flex-start;
}

/* line 21, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .export-tabs {
  display: flex;
  justify-content: space-between;
  width: 70%;
}

/* line 25, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .export-tabs a {
  font-size: 14px;
  padding: 0 4px 4px;
}

/* line 28, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .export-tabs a.active {
  border-bottom: 3px solid #3e76ae;
  font-weight: bold;
}

/* line 31, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .registries {
  align-items: center;
  display: flex;
  margin-left: auto;
}

/* line 35, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .registries label {
  color: #407cb7;
  font-size: 12px;
  margin-right: 8px;
}

/* line 39, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .registries .registry-select-container {
  font-size: 13px;
  height: 27px;
}

/* line 42, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__control,
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__indicators {
  max-height: 24px;
  min-height: 1px;
}

/* line 46, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__control {
  width: 155px;
  padding: 0;
}

/* line 49, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .registries .registry-select-container .registry-select__menu,
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__menu-list {
  min-height: 96px;
}

/* line 52, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__input
  > input {
  max-height: 24px;
}

/* line 54, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__value-container {
  padding: 0 0 0 2px;
  max-height: 20px;
  width: 60px;
}

/* line 58, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav > div .registries .registry-select-container div {
  height: 100%;
  margin: 0;
  min-height: 22px;
  max-height: 22px;
  padding-bottom: 2px;
  padding-top: 2px;
}

/* line 65, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.export-nav
  > div
  .registries
  .registry-select-container
  .registry-select__indicator-separator {
  visibility: hidden;
}

/* line 68, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.extract-results {
  margin-top: 15px;
}

/* line 71, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.extract-section {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* line 76, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.pag-location {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

/* line 80, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.__react_component_tooltip {
  width: 40%;
}

/* line 83, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-layout.sass */
.__react_component_tooltip .tooltip-grid .tooltip-row {
  display: flex;
  margin-bottom: 0.375rem;
}

/* line 2, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications {
  padding: 13px 16px 17px 17px;
  border: 1px solid #c7e1e9;
  background-color: #eef6f8;
}

/* line 6, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row {
  align-items: center;
  display: flex;
}

/* line 9, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row {
  align-items: center;
  display: flex;
  width: 85%;
}

/* line 13, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row > div {
  height: 52px;
}

/* line 15, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .checkbox-row {
  align-items: center;
  display: flex;
  margin-right: 24px;
  margin-top: 8px;
  width: 100%;
}

/* line 21, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .checkbox-row input,
.data-specifications .filter-row .config-row .checkbox-row label {
  margin-top: 6px;
}

/* line 24, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .checkbox-row input {
  margin-right: 6px;
}

/* line 26, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .select-column,
.data-specifications .filter-row .config-row .date-column {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-right: 12px;
  width: 100%;
}

/* line 36, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .date-column
  .react-datepicker-wrapper
  .react-datepicker__input-container
  input {
  border: 2px solid #c0cfdd;
  font-size: 12px;
  height: 36px;
  width: 100%;
  position: relative;
  border: 1px solid #c7e1e9;
  font-family: "Open Sans", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  transition: border-color 0.2s;
  font-size: 16px;
  padding: 8px;
  color: #284758;
  -ms-input-placeholder-color: #999;
  box-sizing: border-box;
  width: 100%;
  height: 34px;
}

/* line 41, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .select-column input {
  border: 2px solid #c0cfdd;
  font-size: 12px;
  height: 36px;
}

/* line 45, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .select-column input[type="file"] {
  padding: 6px;
}

/* line 47, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .select-column:last-of-type {
  margin-right: 30px;
}

/* line 49, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row label {
  color: #36485b;
  font-size: 10px;
}

/* line 52, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .filter-select-container {
  font-size: 13px;
  height: 40px;
}

/* line 55, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__control,
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__indicators {
  max-height: 36px;
  min-height: 1px;
}

/* line 59, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__control
  .css-16pqwjk-indicatorContainer,
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__control
  .css-1thkkgx-indicatorContainer,
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__indicators
  .css-16pqwjk-indicatorContainer,
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__indicators
  .css-1thkkgx-indicatorContainer {
  padding: 8px 0;
}

/* line 62, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__control {
  border: 2px solid #c0cfdd;
  border-radius: 0;
  padding: 0;
  width: 300px;
}

/* line 67, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__menu,
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__menu-list {
  min-height: 96px;
  padding-bottom: 2px;
}

/* line 71, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__input
  > input {
  max-height: 24px;
}

/* line 73, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__value-container {
  padding: 0 0 0 2px;
  margin-bottom: 4px;
  max-height: 20px;
  width: 60px;
}

/* line 78, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__value-container.filter-select__value-container--is-multi {
  align-items: center;
  display: flex;
  margin-bottom: 2px;
}

/* line 82, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__single-value,
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__multi-value {
  height: 100%;
  margin: 0;
  min-height: 22px;
  max-height: 22px;
  padding-bottom: 2px;
  padding-top: 2px;
}

/* line 90, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__multi-value {
  align-items: center;
  display: flex;
  margin-right: 4px;
  margin-top: 2px;
  width: 48%;
}

/* line 96, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__multi-value
  .filter-select__multi-value__remove {
  margin-top: 3px;
}

/* line 98, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .css-1g6gooi {
  margin: 0;
  max-height: 22px;
}

/* line 101, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__input {
  max-height: 22px;
}

/* line 103, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .filter-select-container
  .filter-select__indicator-separator {
  visibility: hidden;
}

/* line 105, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-row .narrow-column {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* line 110, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .narrow-column
  .filter-select-container
  .filter-select__control {
  width: 65px;
}

/* line 112, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-row
  .narrow-column
  .filter-select-container
  .filter-select__value-container {
  padding: 0 0 0 7px;
  width: 30px;
}

/* line 115, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-col {
  width: 100%;
}

/* line 117, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-col .config-row {
  justify-content: left;
  margin-bottom: 8px;
}

/* line 120, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-col .config-row > div {
  height: initial;
  min-height: 27px;
  width: 30%;
}

/* line 124, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div
  .filter-select__control,
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div
  .filter-select__indicators,
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div
  .filter-select__value-container {
  height: initial;
  min-height: 27px;
  max-height: initial;
}

/* line 130, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div
  .filter-select__control {
  max-height: 40px;
  overflow-y: hidden;
  width: 100%;
}

/* line 134, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div
  .filter-select__control--is-focused {
  max-height: initial;
}

/* line 136, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div
  .filter-select__indicators {
  align-self: flex-start;
  max-width: 18%;
}

/* line 139, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.fields-dropdown-container {
  margin-right: 12px;
  position: relative;
}

/* line 142, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.fields-dropdown-container
  .checkbox-row {
  flex-direction: row-reverse;
  position: absolute;
  right: 0;
  top: -0.875rem;
}

/* line 147, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.fields-dropdown-container
  .checkbox-row
  label {
  margin-right: 4px;
}

/* line 149, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-col .config-row > div.checkbox-column {
  display: flex;
  flex-direction: column;
  max-height: 150px;
  overflow-y: auto;
  width: 100%;
}

/* line 155, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row {
  padding: 4px 0;
}

/* line 157, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel {
  align-items: center;
  display: flex;
  padding-left: 32px;
}

/* line 161, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .select-column {
  align-items: center;
  flex-direction: row;
  justify-content: flex-start;
  width: 60%;
}

/* line 166, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .select-column
  label {
  width: 22%;
}

/* line 168, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .select-column
  .filter-select-container {
  width: 78%;
}

/* line 170, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .select-column
  .filter-select-container
  .react-select__menu,
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .select-column
  .filter-select-container
  .react-select__menu-list {
  max-height: 40px !important;
}

/* line 173, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .checkbox-row {
  margin: 0;
  width: 20%;
}

/* line 176, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .checkbox-row
  input,
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .checkbox-accordion-panel
  .checkbox-row
  label {
  margin-top: 0;
}

/* line 179, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .toggle-checkbox {
  align-items: center;
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  width: 15%;
}

/* line 185, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .config-col
  .config-row
  > div.checkbox-column
  .checkbox-multi-select-row
  .toggle-checkbox
  label {
  margin-left: 6px;
}

/* line 187, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .config-col .config-row:last-of-type {
  margin-bottom: 0;
}

/* line 189, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .export-extract-action-button {
  align-items: center;
  align-self: flex-end;
  background: #ffffff;
  border: 2px solid #cce0e8;
  color: #4f4f4f;
  display: flex;
  font-size: 17px;
  height: 34px;
  margin-left: auto;
  min-width: 110px;
  padding: 2px 1px 2px 11px;
}

/* line 201, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .export-extract-action-button span {
  margin-right: 10px;
}

/* line 203, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .export-extract-action-button .icon-container {
  align-items: center;
  color: #ffffff;
  display: flex;
  height: 30px;
  justify-content: center;
  margin-left: auto;
  width: 30px;
}

/* line 211, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .export-extract-action-button
  .icon-container.export {
  background: #fa9917;
}

/* line 213, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .export-extract-action-button
  .icon-container.extract {
  background: #146ec8;
}

/* line 215, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications
  .filter-row
  .export-extract-action-button
  .icon-container.import,
.data-specifications
  .filter-row
  .export-extract-action-button
  .icon-container.surgeon,
.data-specifications
  .filter-row
  .export-extract-action-button
  .icon-container.hospital,
.data-specifications
  .filter-row
  .export-extract-action-button
  .icon-container.update {
  background: #13b139;
}

/* line 220, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/export-extract/_registry-maintenance-data-specifications.sass */
.data-specifications .filter-row .export-extract-action-button .align-left {
  justify-content: left;
}

/* line 2, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .page_head {
  margin-bottom: 0;
}

/* line 5, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .page_head div.column h1 {
  margin-bottom: 0.5rem;
}

/* line 7, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .page_head div.column span {
  font-size: 1rem;
  font-style: italic;
  padding-left: 0.5rem;
}

/* line 11, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container {
  padding: 0 40px;
  margin: 0 auto;
  max-width: 1400px;
  min-width: 600px;
}

/* line 16, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container .case-counts {
  display: block;
  font-size: 0.875rem;
  margin: 1rem 0;
}

/* line 20, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container .case-counts .valid {
  color: #286f1b;
  font-weight: bold;
}

/* line 23, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary
  .case-upload-content-container
  .case-counts
  .invalid {
  color: red;
  font-weight: bold;
}

/* line 26, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table {
  border-collapse: collapse;
  width: 100%;
  font-size: 0.8rem;
}

/* line 30, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table td,
.case-upload-error-summary .case-upload-content-container table th {
  border: 1px solid #e5e5e5;
  padding: 8px;
}

/* line 33, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary
  .case-upload-content-container
  table
  td:nth-of-type(1),
.case-upload-error-summary
  .case-upload-content-container
  table
  th:nth-of-type(1) {
  width: 30%;
}

/* line 35, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary
  .case-upload-content-container
  table
  td:nth-of-type(2),
.case-upload-error-summary
  .case-upload-content-container
  table
  th:nth-of-type(2) {
  width: 45%;
}

/* line 37, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary
  .case-upload-content-container
  table
  td:nth-of-type(3),
.case-upload-error-summary
  .case-upload-content-container
  table
  th:nth-of-type(3) {
  width: 12.5%;
}

/* line 40, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary
  .case-upload-content-container
  table
  tr:nth-child(even) {
  background-color: #f2f2f2;
}

/* line 42, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table tr:hover {
  background-color: #ddd;
}

/* line 44, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table th {
  padding: 9px 0 9px 24px;
  text-align: left;
  background-color: #f4f9fb;
  color: #484848;
}

/* line 49, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary
  .case-upload-content-container
  table
  th
  input[type="checkbox"] {
  margin-right: 12px;
}

/* line 51, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table td {
  padding-left: 24px;
}

/* line 53, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table th.checkbox {
  align-items: center;
  border-bottom: 0;
  border-left: 0;
  display: flex;
  justify-content: center;
  padding-left: 3.1rem;
  text-align: center;
}

/* line 61, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-upload-error-summary/_case-upload-error-summary.sass */
.case-upload-error-summary .case-upload-content-container table td.checkbox {
  padding-left: 8px;
  text-align: center;
}

/* line 1, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container,
.menu.non-reportable-container {
  font-size: 0.6875rem;
  font-weight: normal;
  opacity: 1;
  padding: 0.625rem 0.75rem;
}

/* line 7, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container > div,
.menu.non-reportable-container > div {
  flex-direction: column;
  justify-content: space-between;
}

/* line 10, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container textarea,
.menu.non-reportable-container textarea {
  height: 7.75rem;
  margin: 0.5rem 0;
  resize: vertical;
}

/* line 14, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container button.success-btn,
.menu.non-reportable-container button.success-btn {
  background: #227b9c;
  color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.29);
  text-transform: uppercase;
  width: 100%;
  border-radius: 4px;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
}

/* line 26, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container button.success-btn:disabled,
.menu.non-reportable-container button.success-btn:disabled {
  background: #c4c4c4;
  color: #ffffff;
  cursor: not-allowed !important;
}

/* line 30, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container .react-datepicker-popper,
.menu.non-reportable-container .react-datepicker-popper {
  display: inline-block;
}

/* line 32, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container .react-datepicker-popper div,
.menu.non-reportable-container .react-datepicker-popper div {
  display: inline-block;
}

/* line 34, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu.add-new-primary-container
  .react-datepicker-popper
  .react-datepicker__header,
.menu.add-new-primary-container
  .react-datepicker-popper
  .react-datepicker__current-month,
.menu.non-reportable-container
  .react-datepicker-popper
  .react-datepicker__header,
.menu.non-reportable-container
  .react-datepicker-popper
  .react-datepicker__current-month {
  display: block;
}

/* line 38, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.menu {
  background: #fff;
  border-radius: 5px;
  border: 1px solid #d8d7d7;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  flex-direction: column;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  justify-content: space-between;
  padding: 1rem 0.5rem;
  position: absolute;
  width: 15rem;
  z-index: 10;
}

/* line 52, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table {
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%;
  min-height: 64px;
}

/* line 58, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th,
.case-finding-table tr td {
  font-size: 13px;
  font-weight: bold;
}

/* line 61, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr {
  background: #f4f4f4;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 64, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr div {
  display: flex;
}

/* line 66, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr label {
  align-items: center;
  align-self: flex-start;
  color: #227b9c;
  display: flex;
  flex-direction: column;
  font-size: 0.6875rem;
  font-weight: normal;
}

/* line 74, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th {
  border: 2px solid #d8d7d7;
  border-left-width: 1px;
  border-right-width: 0;
  color: #227b9c;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
}

/* line 81, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th span {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

/* line 87, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th span i {
  font-size: 0.5rem;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

/* line 90, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th span i.down {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 92, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th span.selected {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/* line 94, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:first-of-type {
  border-left-width: 2px;
  border-radius: 2px 0 0 2px !important;
  width: 22%;
}

/* line 98, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:nth-of-type(2) {
  width: 9%;
}

/* line 100, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:nth-of-type(3) {
  width: 9%;
}

/* line 102, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:nth-of-type(4) {
  width: 12%;
}

/* line 104, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:nth-of-type(5) {
  width: 13%;
}

/* line 106, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:nth-of-type(6) {
  width: 13%;
}

/* line 108, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:last-of-type {
  border-left: none;
  border-right-width: 2px;
  border-radius: 0 2px 2px 0;
  padding: 0 0.5rem;
}

/* line 114, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:last-of-type div {
  justify-content: space-between;
}

/* line 116, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:last-of-type div button {
  background-color: #70c64d;
  border-radius: 3px;
  border: 0;
  color: #fff;
  font-size: 0.75rem;
}

/* line 122, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:last-of-type div button:hover {
  background-color: #5faa40;
}

/* line 124, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr th:last-of-type div button:disabled {
  background-color: #a4a4a4;
  color: #ffffff;
}

/* line 127, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td {
  background: #f4f4f4;
  border-top: 1px solid #979797;
  border-bottom: 1px solid #979797;
  border-right-width: 0;
  color: #4f4f4f;
  padding: 0.75rem;
}

/* line 134, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td > input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: flex;
  height: 24px;
  width: 24px;
  border: 2px solid rgba(1, 95, 134, 0.87);
  border-radius: 3px;
}

/* line 141, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td > input:checked {
  background: rgba(1, 95, 134, 0.87);
}

/* line 143, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td .discharge-date {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* line 147, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td .discharge-date i {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
  font-size: 24px;
  color: rgba(1, 95, 134, 0.7);
}

/* line 151, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td .discharge-date i:hover {
  cursor: pointer;
}

/* line 153, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td > button {
  align-items: center;
  display: flex;
  flex-direction: column;
  font-size: 0.6875rem;
  justify-content: center;
  margin: auto;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 161, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td > button > i {
  color: rgba(1, 95, 134, 0.7);
}

/* line 163, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td > button:disabled {
  cursor: default !important;
}

/* line 165, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td > button:disabled > i {
  color: rgba(0, 0, 0, 0.29);
}

/* line 167, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td.unassigned {
  color: #969696;
  font-style: italic;
}

/* line 170, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:first-of-type {
  border-left: 1px solid #979797;
  border-radius: 2px 0 0 2px;
  cursor: pointer;
  font-size: 1.125rem;
  width: 5%;
}

/* line 176, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:first-of-type button {
  background: transparent;
  border: 0;
  height: 100%;
  position: relative;
  width: 100%;
}

/* line 182, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:first-of-type button i {
  left: 50%;
  position: absolute;
  top: 50%;
  -webkit-transform: rotate(-90deg) translate3d(-50%, -50%, 0);
  transform: rotate(-90deg) translate3d(-50%, -50%, 0);
  font-size: 24px;
  -webkit-transform-origin: 0 0 0;
  transform-origin: 0 0 0;
  transition: 0.2s -webkit-transform ease-in-out;
  transition: 0.2s transform ease-in-out;
  transition: 0.2s transform ease-in-out, 0.2s -webkit-transform ease-in-out;
}

/* line 192, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(2) {
  width: 5%;
}

/* line 194, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(3) {
  width: 14%;
}

/* line 196, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(4) {
  width: 10%;
}

/* line 198, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(5) {
  width: 9%;
}

/* line 200, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(6) {
  width: 13%;
}

/* line 202, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(7) {
  width: 14%;
}

/* line 204, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(8) {
  width: 17%;
}

/* line 206, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) {
  padding-right: 0.875rem;
  position: relative;
  width: 16%;
}

/* line 210, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(1, 95, 134, 0.12);
  border: 2px solid #015f86;
  border-radius: 18px;
  font-weight: 800;
  font-size: 14px;
  height: 36px;
}

/* line 222, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) button i {
  padding-left: 0.5rem;
}

/* line 224, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) button:disabled,
.case-finding-table tr td:nth-of-type(9) button.btn-2 {
  background: #a4a4a4;
  color: #ffffff;
}

/* line 227, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) div {
  justify-content: end;
}

/* line 229, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) .menu {
  margin-top: 0.25rem;
}

/* line 231, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) .menu button {
  width: 100%;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 234, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) .menu button.btn-3 {
  background: #fff;
  border: 1px solid #d8d7d7;
}

/* line 237, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(9) .menu button + button {
  margin-top: 0.75rem;
}

/* line 239, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(10) {
  padding: 0;
  text-align: center;
  width: 6%;
}

/* line 243, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(10) .menu {
  right: 9%;
}

/* line 245, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(10) > button {
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  background: transparent;
  border: 0;
  color: #227b9c;
  width: 100%;
}

/* line 251, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(10) i {
  font-size: 1.5rem;
}

/* line 253, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:nth-of-type(10) .menu.add-new-primary-container {
  font-size: 0.875rem;
  padding: 20px;
  min-width: 390px;
}

/* line 257, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container::before,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container::after {
  left: 82.5%;
}

/* line 259, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .react-datepicker-wrapper {
  padding: 0;
}

/* line 261, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .react-datepicker-wrapper
  input {
  border-radius: 4px;
  height: 43px;
  border: 1px solid rgba(0, 0, 0, 0.29);
  line-height: 24px;
  padding-top: 5px;
  padding-bottom: 5px;
  vertical-align: middle;
}

/* line 265, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  > div {
  width: 100%;
}

/* line 267, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  button {
  padding: 10px;
}

/* line 269, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-type-selector,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-site-selector {
  display: block;
  margin-bottom: 20px;
  width: 100%;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
}

/* line 279, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-assign-selector {
  display: block;
  margin-top: 20px;
  margin-bottom: 20px;
  width: 100%;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
}

/* line 289, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-site-selector__menu,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-type-selector__menu,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-assign-selector__menu,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-site-selector__menu-list,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-type-selector__menu-list,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-assign-selector__menu-list {
  border-radius: 0;
  flex-direction: column;
  width: 100%;
  color: rgba(0, 0, 0, 0.7);
}

/* line 299, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-type-selector__control,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-site-selector__control,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-assign-selector__control {
  height: 50px;
  border: 1px solid rgba(0, 0, 0, 0.29);
  border-radius: 4px;
}

/* line 305, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-type-selector__placeholder,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-site-selector__placeholder,
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-assign-selector__placeholder {
  color: rgba(0, 0, 0, 0.29);
}

/* line 312, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-site-selector__option:nth-child(even),
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .case-type-selector__option:nth-child(even) {
  background-color: rgba(34, 123, 156, 0.08);
}

/* line 314, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .first-contact {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

/* line 317, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .first-contact::-webkit-input-placeholder {
  color: #7e919b;
  font-style: italic;
}
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .first-contact:-ms-input-placeholder {
  color: #7e919b;
  font-style: italic;
}
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .first-contact::-ms-input-placeholder {
  color: #7e919b;
  font-style: italic;
}
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  .first-contact::placeholder {
  color: #7e919b;
  font-style: italic;
}

/* line 320, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  label {
  align-items: center;
  flex-direction: row;
  margin-bottom: 20px;
}

/* line 324, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  label
  input {
  border: 1px solid rgba(0, 0, 0, 0.29);
  border-radius: 2px;
  width: 16px;
  height: 18px;
  color: rgba(0, 0, 0, 0.29);
  font-family: "Font Awesome 6 Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
}

/* line 335, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table
  tr
  td:nth-of-type(10)
  .menu.add-new-primary-container
  label
  span {
  align-items: center;
  display: inline-flex;
  color: rgba(0, 0, 0, 0.29);
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
  margin-left: 0.5rem;
}

/* line 345, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding-table tr td:last-of-type {
  border-radius: 0 2px 2px 0;
  border-right: 1px solid #979797;
  position: relative;
}

/* line 350, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding {
  display: flex;
  flex-direction: column;
}

/* line 353, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers {
  margin-top: 0.5rem;
}

/* line 355, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table {
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%;
}

/* line 360, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr {
  background: #f4f4f4;
}

/* line 362, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container {
  padding: 0.25rem 2rem;
  width: 56.5%;
}

/* line 365, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .modal-content-container {
  padding: 20px 0 20px 24px;
  display: flex;
  justify-content: start;
  width: 400px;
  border-radius: 18px;
  background: #ffffff;
}

/* line 372, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .modal-content-container
  > div {
  display: flex;
  flex-direction: column;
  align-items: start;
}

/* line 376, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .modal-content-container
  > div
  > p {
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #000000;
  padding-bottom: 20px;
}

/* line 383, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .modal-content-container
  > div
  > div
  > button {
  margin-right: 8px;
}

/* line 385, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .modal-overlay-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

/* line 393, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .assign-cases-container {
  position: relative;
}

/* line 395, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .assign-cases-container
  .assign-cases {
  position: absolute;
  top: 37px;
  left: 0;
  width: 168px;
  max-height: 214px;
  align-items: flex-start;
  justify-content: flex-start;
  background: #fff;
  margin-top: 0;
  padding: 0.5rem;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 8px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
}

/* line 410, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .assign-cases-container
  .assign-cases
  .assign-case-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #000000;
}

/* line 419, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .assign-cases-container
  .assign-cases
  .assign-case-content
  > div {
  padding: 9px 4px;
}

/* line 421, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  .fixed-table-header-container
  .assign-cases-container
  .assign-cases
  .assign-case-content
  > div
  > p {
  display: inline-block;
  text-align: left;
  white-space: nowrap;
  width: 145px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* line 428, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th {
  color: #227b9c;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: bold;
  padding: 0.5rem 0.75rem;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 435, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th span {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

/* line 441, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th span i {
  font-size: 0.5rem;
  margin-left: 4px;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

/* line 445, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th span i.down {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 447, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th span.selected {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/* line 450, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:first-of-type
  div {
  justify-content: space-evenly;
  align-items: center;
}

/* line 453, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:first-of-type
  div
  input {
  width: 24px;
  height: 24px;
}

/* line 456, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th:last-of-type {
  padding: 0 0.5rem;
}

/* line 458, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-headers .case-finding-table tr th:last-of-type div {
  display: flex;
  justify-content: space-between;
}

/* line 461, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:last-of-type
  div
  button {
  background-color: #70c64d;
  border-radius: 3px;
  border: 0;
  color: #fff;
  font-size: 0.75rem;
}

/* line 467, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:last-of-type
  div
  button:hover {
  background-color: #5faa40;
}

/* line 469, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:last-of-type
  div
  button.disabled,
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:last-of-type
  div
  button:disabled {
  background-color: #cecece;
  color: #333 !important;
}

/* line 472, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-headers
  .case-finding-table
  tr
  th:last-of-type
  div
  label {
  align-items: center;
  display: flex;
  flex-direction: column;
  font-size: 0.6875rem;
  font-weight: normal;
}

/* line 478, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .actions {
  justify-content: center;
  width: 100%;
  flex-direction: column;
}

/* line 482, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .actions button.action,
.case-finding .actions a.action {
  background-color: #70c64d;
  border-radius: 5px;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  color: #ffffff;
  width: 100%;
  height: 34px;
  margin-top: 0.2rem;
}

/* line 493, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .actions button.action:hover,
.case-finding .actions a.action:hover {
  background-color: #5faa40;
}

/* line 495, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .actions button.action.disabled,
.case-finding .actions a.action.disabled {
  background-color: #cecece;
  color: #333 !important;
}

/* line 498, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .actions a.action {
  padding-top: 0.5rem;
  text-align: center;
  background-color: #227b9c;
}

/* line 502, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .actions a.action:hover {
  background-color: #1d6984;
}

/* line 504, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info {
  display: flex;
  margin-top: 0.5rem;
}

/* line 506, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
}

/* line 510, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters span {
  font-size: 12px;
  line-height: 18px;
  margin: 0 5px;
}

/* line 514, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters span:first-of-type {
  margin-left: 0;
  line-height: 28px;
}

/* line 517, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters input {
  height: 28px;
  width: 110px;
  font-size: 10px;
  margin: 0 5px;
  background: #ffffff;
  border: 1px solid #a4a4a4;
  box-sizing: border-box;
  box-shadow: inset 0px 4px 4px rgba(0, 0, 0, 0.1);
}

/* line 526, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters .group-label {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  padding: 0.2rem 0.4rem 0.2rem 0.1rem;
}

/* line 531, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters .group-label .content {
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  line-height: 11px;
  color: #36485b;
  flex-grow: 0;
}

/* line 538, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters .group-label .before,
.case-finding .case-finding-info .case-finding-filters .group-label .after {
  height: 5px;
  border-top: 1px solid #d8d7d7;
  flex-grow: 1;
}

/* line 542, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters .group-label .before {
  border-left: 1px solid #d8d7d7;
}

/* line 544, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters .group-label .after {
  border-right: 1px solid #d8d7d7;
}

/* line 546, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters button.filter {
  color: #fff;
  background-color: #7da1b4;
  height: 29px;
  width: 93px;
  margin: 0px 17px 0px 5px;
  border-radius: 5px;
  font-weight: 500;
  font-size: 10px;
  line-height: 18px;
  opacity: 1;
  border: none;
}

/* line 558, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters button.clear-filters {
  font-style: italic;
  font-weight: normal;
  font-size: 11px;
  line-height: 18px;
  color: #227b9c;
  background-color: transparent;
  text-decoration: underline;
  border: none;
  align-self: flex-end;
}

/* line 568, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-info .case-finding-filters .filter-select {
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 3px;
  margin: 0 5px;
  width: 110px;
  display: block;
}

/* line 575, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__control {
  min-height: 28px;
  line-height: 16px;
  border: 1px solid #a4a4a4;
  box-shadow: inset 0px 4px 4px rgba(0, 0, 0, 0.1);
  border-radius: 0;
}

/* line 581, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__value-container {
  padding: 0 8px;
}

/* line 583, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__placeholder {
  font-size: 10px;
}

/* line 585, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__input {
  font-size: 10px;
  color: #333333;
}

/* line 588, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__input
  input {
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  margin-left: 0px;
  margin-right: 2px;
  max-width: calc(100% - 8px);
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  white-space: nowrap;
  top: 50%;
  height: 28px;
  box-shadow: none;
}

/* line 600, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__indicator {
  padding: 0 2px;
}

/* line 602, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__single-value {
  font-size: 10px;
}

/* line 604, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__menu,
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__menu-list {
  flex-direction: column;
  width: 100%;
  border-radius: 0px;
}

/* line 608, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__menu-list {
  border: 1px solid #a4a4a4;
}

/* line 610, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__option {
  background-color: #fff;
  border-bottom: 1px solid #a4a4a4;
  color: #000;
  font-size: 10px;
}

/* line 615, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-info
  .case-finding-filters
  .filter-select
  .filter-select__option:nth-child(even) {
  background-color: rgba(34, 123, 156, 0.08);
}

/* line 618, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding .case-finding-rows .case-finding-container-row {
  font-size: 0.75rem;
  margin-top: 0.75rem;
  padding: 0.875rem 0;
}

/* line 622, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table {
  border-top-width: 0;
  border-radius: 2px;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  transition: 0.3s height ease-in-out;
}

/* line 628, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead {
  display: inherit;
  width: 100%;
}

/* line 631, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td {
  background: white;
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

/* line 636, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:first-of-type {
  width: 4.5%;
}

/* line 638, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:first-of-type
  i {
  -webkit-transform: rotate(0deg) translate3d(-50%, -50%, 0);
  transform: rotate(0deg) translate3d(-50%, -50%, 0);
}

/* line 640, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(2) {
  width: 4.25%;
}

/* line 642, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(3) {
  width: 10.2%;
}

/* line 644, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(4) {
  width: 7.5%;
}

/* line 646, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(5) {
  width: 7.5%;
}

/* line 648, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(6) {
  width: 10.5%;
}

/* line 650, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(7) {
  width: 11.2%;
}

/* line 652, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(8) {
  width: 13%;
}

/* line 654, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:nth-of-type(9) {
  width: 12%;
}

/* line 656, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  thead
  tr
  td:last-of-type {
  width: 4.75%;
}

/* line 658, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody,
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  > tr,
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  > tr
  > td {
  display: block;
  width: 100%;
}

/* line 663, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody {
  max-height: 33.5rem;
  overflow-y: auto;
  border: 1px solid #979797;
  border-top: 0;
  border-radius: 2px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

/* line 671, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr {
  background: transparent;
}

/* line 673, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td {
  border: none;
  cursor: initial;
  padding: 0.5rem 1.25rem;
  background: transparent;
}

/* line 678, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  div {
  display: flex;
}

/* line 680, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row {
  border-radius: 8px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-size: 0.75rem;
  font-weight: normal;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  padding-bottom: 1rem;
}

/* line 688, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right {
  align-items: center;
  flex-direction: column;
  padding: 1rem;
  width: 20%;
}

/* line 694, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  > div.actions
  button {
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 696, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.29);
  box-sizing: border-box;
  border-radius: 2px;
  width: 100%;
  display: block;
  margin-bottom: 0.1rem;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.29);
}

/* line 709, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__control {
  background: transparent;
  border: none;
  width: 100%;
}

/* line 713, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__indicators {
  padding: 0 0.5rem;
}

/* line 715, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__indicators
  i {
  color: rgba(1, 95, 134, 0.87);
}

/* line 717, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__control--is-disabled {
  color: rgba(0, 0, 0, 0.29);
}

/* line 719, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__control--is-disabled
  i {
  color: rgba(0, 0, 0, 0.29);
}

/* line 721, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__menu,
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__menu-list {
  flex-direction: column;
  width: 100%;
  border-radius: 2px;
}

/* line 727, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__option {
  background-color: #fff;
  border-bottom: 1px solid #a4a4a4;
  color: #000;
}

/* line 731, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__option:nth-child(even) {
  background-color: rgba(34, 123, 156, 0.08);
}

/* line 734, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-right
  .historical-case-row-selector
  .historical-case-row-selector__input
  input {
  height: 1rem;
}

/* line 736, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left {
  flex-direction: column;
  width: 100%;
}

/* line 740, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  span.title {
  font-weight: bold;
}

/* line 742, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header {
  align-items: center;
  background: #efefef;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  height: 3rem;
  justify-content: flex-start;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 751, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header.match {
  background: #fff8de;
}

/* line 753, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  span {
  font-size: 13px;
}

/* line 755, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  span.title {
  color: #000000;
  margin: 0 0.5rem 0 1rem;
  font-size: 13px;
  font-weight: 800;
}

/* line 760, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  span.title.match {
  color: #fa9918;
  font-style: italic;
}

/* line 763, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  span.title-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* line 767, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  div.grid-cell {
  display: flex;
  align-items: center;
  width: 20.75%;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

/* line 772, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  div.grid-cell:last-of-type {
  border: none;
  width: auto;
}

/* line 775, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid-header
  div.grid-cell
  .title-value {
  margin-right: 8px;
}

/* line 777, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid {
  background: #ffffff;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  font-size: 0.675rem;
  height: 7.5rem;
  justify-content: space-evenly;
}

/* line 784, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid
  div.grid-col {
  flex-direction: column;
  width: 25%;
  min-width: 0;
  margin-top: 1rem;
}

/* line 789, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid
  div.grid-col
  div.grid-cell {
  align-items: center;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  height: 33.3%;
  padding: 0 1rem;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 795, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid
  div.grid-col
  div.grid-cell
  span {
  width: 50%;
  font-size: 13px;
}

/* line 798, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid
  div.grid-col
  div.grid-cell


/* line 800, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr
  td
  > div.historical-case-row
  div.historical-case-row-left
  div.grid
  div.grid-col
  div.grid-cell
  span.title-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* line 804, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr:first-of-type
  td {
  padding-top: 1rem;
}

/* line 806, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.case-finding
  .case-finding-rows
  .case-finding-container-row
  .case-finding-table.open
  tbody
  tr:last-of-type
  td {
  padding-bottom: 1rem;
}

/* line 812, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions {
  float: right;
}

/* line 814, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions button {
  font-style: normal;
  font-weight: 800;
  font-size: 11px;
  line-height: 15px;
  text-align: center;
  color: #3f3e3e;
  background: #e7ca65;
  border-radius: 500px;
  border: none;
  padding: 4px 10px;
  cursor: pointer;
}

/* line 826, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions button.off {
  background-color: #cecece;
}

/* line 828, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions button:disabled {
  cursor: default;
}

/* line 830, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions .menu {
  right: 0;
  top: 66%;
}

/* line 833, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions .menu button {
  width: 100%;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
}

/* line 836, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions .menu button.btn-2 {
  background: #a4a4a4;
  color: #ffffff;
}

/* line 839, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions .menu button.btn-3 {
  background: #fff;
  border: 1px solid #d8d7d7;
}

/* line 842, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
header.page_head .column ul.tabs li.actions .menu button + button {
  margin-top: 0.75rem;
}

/* line 846, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.wdz-modal.layover .actions {
  text-align: center;
  padding-bottom: 20px;
}

/* line 849, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.wdz-modal.layover .actions button {
  font-style: normal;
  font-weight: 500;
  font-size: 13px;
  line-height: 18px;
  text-align: center;
  color: #ffffff;
  background: #70c64d;
  border-radius: 3px;
  border: none;
  padding: 4px 10px;
  cursor: pointer;
}

/* line 861, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.wdz-modal.layover .actions button:after {
  display: none;
}

/* line 863, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.wdz-modal.layover .actions buttonhover {
  color: #fff;
}

/* line 865, ../../../../../.rvm/gems/ruby-3.0.3/bundler/gems/ui-e1baeb184b8b/app/assets/stylesheets/ui/qapps/sections/case-finding/_case-finding.sass */
.wdz-modal.layover .actions button:disabled {
  cursor: default;
  background: #cecece;
}

.column {
  background-color: white;
  min-width: 600px;
  max-width: 100vw;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  padding-left: 40px;
  padding-right: 40px;
  clear: both;
  overflow: scroll;
}
