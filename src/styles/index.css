@import url(./custom-tw-base.css);

@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap");

body {
  margin: 0;
  font-family: "Source Sans Pro", -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

@layer utilities {
  .tw-scrollbar::-webkit-scrollbar {
    height: 4px;
    width: 325px;
  }

  .tw-scrollbar::-webkit-scrollbar-track {
    margin: 8px;
  }

  .tw-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 100px;
    background: #B5B5B5;
  }

  .tw-scrollbar-gutter-stable {
    scrollbar-gutter: stable;
  }

  .tw-scrollbar-gutter-stable-both {
    scrollbar-gutter: stable both-edges;
  }
}

@layer components {
  .qc-break-word {
    word-break: break-word;
  }
}
