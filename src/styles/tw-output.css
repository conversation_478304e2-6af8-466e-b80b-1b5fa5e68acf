@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap");

/*
! tailwindcss v3.3.2 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #ebebeb;
  /* 2 */
}

::before,
::after {
  --tw-content: "";
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
*/

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
  tab-size: 4;
  /* 3 */
  font-family: Source Sans Pro, ui-sans-serif, system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #d7d7d7;
  /* 2 */
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #d7d7d7;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #d7d7d7;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

/* Confirmation Modal */

.ReactModal__Body--open .confirmation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #00000020;
  transition: all ease-in-out 0.3s;
  z-index: 1000;
}

.ReactModal__Body--open .confirmation-modal {
  position: absolute;
  outline: 0;
  top: 50%;
  left: 50%;
  translate: -50% -50%;
  padding: 20px;
  height: -moz-fit-content;
  height: fit-content;
  background-color: #f3f3f3;
  border-radius: 4px;
  box-shadow: 2px 4px 4px #00000065;
}

.ReactModal__Body--open .confirmation-modal.size-xs {
  width: 300px;
}

.ReactModal__Body--open .confirmation-modal.size-sm {
  width: 350px;
}

.ReactModal__Body--open .confirmation-modal.size-lg {
  width: 620px;
}

.ReactModal__Body--open .confirmation-modal .modal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.ReactModal__Body--open .confirmation-modal .modal-content .modal-heading {
  margin: 0;
  height: 20px;
  font-family: "Source Sans Pro", "Source Code Pro", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #005f86;
}

.ReactModal__Body--open .confirmation-modal .modal-content .modal-heading.danger {
  color: #912018;
}

.ReactModal__Overlay .confirmation-modal {
  opacity: 0;
  transition: opacity 200ms ease-in-out;
}

.ReactModal__Overlay--after-open .confirmation-modal {
  opacity: 1;
}

.ReactModal__Overlay--before-close .confirmation-modal {
  opacity: 0;
}

/* SummaryPopUp */

.ReactModal__Body--open .summary-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #00000020;
  transition: all ease-in-out 0.4s;
  z-index: 1000;
}

.ReactModal__Body--open .summary-modal {
  position: absolute;
  outline: 0;
  top: 50%;
  left: 50%;
  translate: -50% -50%;
  padding: 20px;
  height: -moz-fit-content;
  height: fit-content;
  width: 620px;
  background-color: #FFF;
  border-radius: 6px;
  box-shadow: -1px 2px 6px 0px rgba(0, 0, 0, 0.20);
}

.ReactModal__Body--open .summary-modal.modal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.ReactModal__Overlay .summary-modal {
  opacity: 0;
  transition: opacity 200ms ease-in-out;
}

.ReactModal__Overlay--after-open .summary-modal {
  opacity: 1;
}

.ReactModal__Overlay--before-close .summary-modal {
  opacity: 0;
}

/* Card with header */

.card-with-header.border-10px {
  border-radius: 10px;
  overflow: hidden;
}

.card-with-header.full-height {
  height: 100%;
  overflow: scroll;
}

.card-with-header.no-box-shadow {
  box-shadow: none;
}

.card-with-header .header {
  background: #f3f3f3;
  font-style: normal;
  font-size: 16px;
  line-height: 20px;
  font-weight: 600;
  color: #000000;
}

.card-with-header .header.main {
  background: #005f86;
}

.card-with-header .header.main.title-white {
  font-size: 20px;
  line-height: 25px;
  color: #ffffff;
}

.card-with-header .body {
  background: white;
}

.card-with-header .body.has-border-gray {
  border: 1px solid rgba(0, 0, 0, 0.29);
}

.card-with-header .body.has-border-gray.border-radius-10px {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.card-with-header .body.gray {
  background-color: #f3f3f3;
}

.card-with-header .body.gray.has-border-gray {
  border: 1px solid #d7d7d7;
}

.card-with-header .body.gray.has-border-gray.border-radius-10px {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.card-with-header .body.gray.has-border-main {
  border: 1px solid #014967;
}

.card-with-header .body.gray.has-border-main.border-radius-5px {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* Switch component */

.switch-container .switch[data-isToggled="true"] {
  justify-content: flex-end;
  background-color: rgba(0, 95, 134, 0.29);
  transition: background-color 0.2s ease-in-out;
}

.switch-container .switch[data-isToggled="true"] .toggle {
  background-color: #005f86;
  transition: background-color 0.2s ease-in-out;
}

/* Dropdown */

.custom-dropdown .custom-clear-indicator {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  padding-right: 10px;
}

/* DateDropdown Component */

.date-dropdown .custom-dropdown {
  width: 130px;
  height: 40px;
}

.date-dropdown.disabled {
  opacity: 0.5;
}

.date-dropdown.\!disabled {
  opacity: 0.5 !important;
}

.date-dropdown.disabled i {
  color: rgba(0, 0, 0, 0.7);
}

.date-dropdown.\!disabled i {
  color: rgba(0, 0, 0, 0.7) !important;
}

/* Checkbox Component */

.checkbox.unchecked::before {
  font-family: "FontAwesome";
  content: "\f096";
}

.checkbox.checked::before {
  font-family: "FontAwesome";
  content: "\f14a";
}

.checkbox.\!checked::before {
  font-family: "FontAwesome" !important;
  content: "\f14a" !important;
}

.checkbox.indeterminate::before {
  font-family: "FontAwesome";
  content: "\f146";
}

.checkbox.\!indeterminate::before {
  font-family: "FontAwesome" !important;
  content: "\f146" !important;
}

.tw-pointer-events-none {
  pointer-events: none;
}

.tw-pointer-events-auto {
  pointer-events: auto;
}

.tw-fixed {
  position: fixed;
}

.tw-absolute {
  position: absolute;
}

.tw-relative {
  position: relative;
}

.tw-inset-0 {
  inset: 0px;
}

.tw-inset-y-2 {
  top: 0.5rem;
  bottom: 0.5rem;
}

.tw-left-56 {
  left: 14rem;
}

.tw-left-auto {
  left: auto;
}

.tw-right-0 {
  right: 0px;
}

.tw-right-5 {
  right: 1.25rem;
}

.tw-right-\[10px\] {
  right: 10px;
}

.tw-right-\[15px\] {
  right: 15px;
}

.tw-top-0 {
  top: 0px;
}

.tw-top-1\/2 {
  top: 50%;
}

.tw-top-10 {
  top: 2.5rem;
}

.tw-top-\[15px\] {
  top: 15px;
}

.tw-top-full {
  top: 100%;
}

.tw-z-10 {
  z-index: 10;
}

.tw-z-20 {
  z-index: 20;
}

.tw-z-50 {
  z-index: 50;
}

.tw-z-\[100\] {
  z-index: 100;
}

.tw-m-0 {
  margin: 0px;
}

.tw-mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.tw-my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.tw-my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.tw-my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.tw-my-\[8\.5px\] {
  margin-top: 8.5px;
  margin-bottom: 8.5px;
}

.tw-mb-1 {
  margin-bottom: 0.25rem;
}

.tw-mb-1\.5 {
  margin-bottom: 0.375rem;
}

.tw-mb-5 {
  margin-bottom: 1.25rem;
}

.tw-ml-10 {
  margin-left: 2.5rem;
}

.tw-ml-2 {
  margin-left: 0.5rem;
}

.tw-ml-2\.5 {
  margin-left: 0.625rem;
}

.tw-ml-\[10px\] {
  margin-left: 10px;
}

.tw-ml-auto {
  margin-left: auto;
}

.tw-mr-2 {
  margin-right: 0.5rem;
}

.tw-mr-5 {
  margin-right: 1.25rem;
}

.tw-mr-\[20px\] {
  margin-right: 20px;
}

.tw-mr-\[5px\] {
  margin-right: 5px;
}

.tw-mr-auto {
  margin-right: auto;
}

.tw-mt-1 {
  margin-top: 0.25rem;
}

.tw-mt-2 {
  margin-top: 0.5rem;
}

.tw-mt-2\.5 {
  margin-top: 0.625rem;
}

.tw-box-border {
  box-sizing: border-box;
}

.tw-block {
  display: block;
}

.tw-inline-block {
  display: inline-block;
}

.tw-flex {
  display: flex;
}

.tw-inline-flex {
  display: inline-flex;
}

.tw-table {
  display: table;
}

.tw-table-cell {
  display: table-cell;
}

.tw-table-row-group {
  display: table-row-group;
}

.tw-hidden {
  display: none;
}

.\!tw-h-6 {
  height: 1.5rem !important;
}

.tw-h-10 {
  height: 2.5rem;
}

.tw-h-12 {
  height: 3rem;
}

.tw-h-6 {
  height: 1.5rem;
}

.tw-h-8 {
  height: 2rem;
}

.tw-h-9 {
  height: 2.25rem;
}

.tw-h-\[14px\] {
  height: 14px;
}

.tw-h-\[24px\] {
  height: 24px;
}

.tw-h-\[25px\] {
  height: 25px;
}

.tw-h-\[35px\] {
  height: 35px;
}

.tw-h-\[39px\] {
  height: 39px;
}

.tw-h-\[40px\] {
  height: 40px;
}

.tw-h-\[45px\] {
  height: 45px;
}

.tw-h-\[50px\] {
  height: 50px;
}

.tw-h-\[60px\] {
  height: 60px;
}

.tw-h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.tw-h-full {
  height: 100%;
}

.tw-max-h-\[22px\] {
  max-height: 22px;
}

.tw-max-h-\[350px\] {
  max-height: 350px;
}

.tw-max-h-\[35px\] {
  max-height: 35px;
}

.tw-max-h-screen {
  max-height: 100vh;
}

.tw-min-h-\[32px\] {
  min-height: 32px;
}

.tw-min-h-\[50px\] {
  min-height: 50px;
}

.tw-min-h-\[80px\] {
  min-height: 80px;
}

.\!tw-w-fit {
  width: -moz-fit-content !important;
  width: fit-content !important;
}

.tw-w-6 {
  width: 1.5rem;
}

.tw-w-8 {
  width: 2rem;
}

.tw-w-\[100px\] {
  width: 100px;
}

.tw-w-\[14px\] {
  width: 14px;
}

.tw-w-\[25px\] {
  width: 25px;
}

.tw-w-\[35px\] {
  width: 35px;
}

.tw-w-\[398px\] {
  width: 398px;
}

.tw-w-\[39px\] {
  width: 39px;
}

.tw-w-\[420px\] {
  width: 420px;
}

.tw-w-\[438px\] {
  width: 438px;
}

.tw-w-\[49px\] {
  width: 49px;
}

.tw-w-\[50px\] {
  width: 50px;
}

.tw-w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.tw-w-full {
  width: 100%;
}

.tw-min-w-\[100px\] {
  min-width: 100px;
}

.tw-min-w-\[148px\] {
  min-width: 148px;
}

.tw-min-w-\[200px\] {
  min-width: 200px;
}

.tw-min-w-\[220px\] {
  min-width: 220px;
}

.tw-min-w-\[300px\] {
  min-width: 300px;
}

.tw-min-w-\[394px\] {
  min-width: 394px;
}

.tw-min-w-\[50px\] {
  min-width: 50px;
}

.tw-min-w-\[61px\] {
  min-width: 61px;
}

.tw-min-w-\[650px\] {
  min-width: 650px;
}

.tw-min-w-\[65px\] {
  min-width: 65px;
}

.tw-max-w-\[23px\] {
  max-width: 23px;
}

.tw-max-w-full {
  max-width: 100%;
}

.tw-max-w-screen-2xl {
  max-width: 1536px;
}

.tw-flex-1 {
  flex: 1 1 0%;
}

.tw-flex-shrink-0 {
  flex-shrink: 0;
}

.tw-shrink-0 {
  flex-shrink: 0;
}

.tw-flex-grow {
  flex-grow: 1;
}

.tw-flex-grow-0 {
  flex-grow: 0;
}

.tw-grow {
  flex-grow: 1;
}

.tw-border-collapse {
  border-collapse: collapse;
}

.tw-border-spacing-0 {
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}

.-tw-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.tw-translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.tw-transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes tw-spin {
  to {
    transform: rotate(360deg);
  }
}

.tw-animate-spin {
  animation: tw-spin 1s linear infinite;
}

.\!tw-cursor-not-allowed {
  cursor: not-allowed !important;
}

.tw-cursor-not-allowed {
  cursor: not-allowed;
}

.tw-cursor-pointer {
  cursor: pointer;
}

.tw-list-none {
  list-style-type: none;
}

.tw-appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.tw-flex-row {
  flex-direction: row;
}

.tw-flex-col {
  flex-direction: column;
}

.tw-flex-col-reverse {
  flex-direction: column-reverse;
}

.tw-flex-nowrap {
  flex-wrap: nowrap;
}

.tw-items-center {
  align-items: center;
}

.tw-justify-start {
  justify-content: flex-start;
}

.tw-justify-end {
  justify-content: flex-end;
}

.tw-justify-center {
  justify-content: center;
}

.tw-justify-between {
  justify-content: space-between;
}

.tw-gap-1 {
  gap: 0.25rem;
}

.tw-gap-10 {
  gap: 2.5rem;
}

.tw-gap-2 {
  gap: 0.5rem;
}

.tw-gap-2\.5 {
  gap: 0.625rem;
}

.tw-gap-3 {
  gap: 0.75rem;
}

.tw-gap-4 {
  gap: 1rem;
}

.tw-gap-5 {
  gap: 1.25rem;
}

.tw-gap-\[30px\] {
  gap: 30px;
}

.tw-gap-\[5px\] {
  gap: 5px;
}

.tw-gap-x-2 {
  -moz-column-gap: 0.5rem;
  column-gap: 0.5rem;
}

.tw-gap-x-2\.5 {
  -moz-column-gap: 0.625rem;
  column-gap: 0.625rem;
}

.tw-gap-x-5 {
  -moz-column-gap: 1.25rem;
  column-gap: 1.25rem;
}

.tw-gap-y-1 {
  row-gap: 0.25rem;
}

.tw-gap-y-1\.5 {
  row-gap: 0.375rem;
}

.tw-self-start {
  align-self: flex-start;
}

.tw-overflow-auto {
  overflow: auto;
}

.\!tw-overflow-hidden {
  overflow: hidden !important;
}

.tw-overflow-hidden {
  overflow: hidden;
}

.\!tw-overflow-visible {
  overflow: visible !important;
}

.tw-overflow-visible {
  overflow: visible;
}

.tw-overflow-scroll {
  overflow: scroll;
}

.tw-overflow-y-auto {
  overflow-y: auto;
}

.tw-overflow-y-scroll {
  overflow-y: scroll;
}

.tw-whitespace-nowrap {
  white-space: nowrap;
}

.tw-break-words {
  overflow-wrap: break-word;
}

.\!tw-rounded-\[50px\] {
  border-radius: 50px !important;
}

.\!tw-rounded-\[5px\] {
  border-radius: 5px !important;
}

.\!tw-rounded-\[6px\] {
  border-radius: 6px !important;
}

.\!tw-rounded-full {
  border-radius: 9999px !important;
}

.\!tw-rounded-md {
  border-radius: 0.375rem !important;
}

.tw-rounded {
  border-radius: 0.25rem;
}

.tw-rounded-2xl {
  border-radius: 1rem;
}

.tw-rounded-\[10px\] {
  border-radius: 10px;
}

.tw-rounded-\[50\%\] {
  border-radius: 50%;
}

.tw-rounded-\[5px\] {
  border-radius: 5px;
}

.tw-rounded-\[6px\] {
  border-radius: 6px;
}

.tw-rounded-full {
  border-radius: 9999px;
}

.tw-rounded-lg {
  border-radius: 0.5rem;
}

.tw-rounded-md {
  border-radius: 0.375rem;
}

.tw-rounded-xl {
  border-radius: 0.75rem;
}

.\!tw-rounded-t-\[5px\] {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px !important;
}

.tw-rounded-b-\[10px\] {
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}

.tw-rounded-b-\[5px\] {
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}

.tw-rounded-t-\[5px\] {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.tw-rounded-t-md {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}

.\!tw-border {
  border-width: 1px !important;
}

.\!tw-border-2 {
  border-width: 2px !important;
}

.tw-border {
  border-width: 1px;
}

.tw-border-2 {
  border-width: 2px;
}

.tw-border-\[1\.5px\] {
  border-width: 1.5px;
}

.tw-border-y-\[0\.5px\] {
  border-top-width: 0.5px;
  border-bottom-width: 0.5px;
}

.tw-border-b {
  border-bottom-width: 1px;
}

.tw-border-l {
  border-left-width: 1px;
}

.tw-border-r {
  border-right-width: 1px;
}

.tw-border-t {
  border-top-width: 1px;
}

.tw-border-solid {
  border-style: solid;
}

.tw-border-dashed {
  border-style: dashed;
}

.tw-border-none {
  border-style: none;
}

.\!tw-border-error-700 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(180 35 24 / var(--tw-border-opacity)) !important;
}

.\!tw-border-error-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(145 32 24 / var(--tw-border-opacity)) !important;
}

.\!tw-border-purple-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(95 33 103 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qc-blue-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 95 134 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcDanger-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(134 29 30 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcDanger-900 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(89 19 20 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcInfo-700 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(11 88 179 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcInfo-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(9 66 135 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcIris-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(57 20 125 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcNeutrals-600 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(89 87 112 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcNeutrals-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(42 39 51 / var(--tw-border-opacity)) !important;
}

.\!tw-border-qcSunset-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(136 57 0 / var(--tw-border-opacity)) !important;
}

.\!tw-border-success-800 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(5 96 58 / var(--tw-border-opacity)) !important;
}

.\!tw-border-warning-700 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(225 122 0 / var(--tw-border-opacity)) !important;
}

.tw-border-black {
  border-color: rgba(0, 0, 0, 1);
}

.tw-border-black-12 {
  border-color: rgba(0, 0, 0, 0.12);
}

.tw-border-black-29 {
  border-color: rgba(0, 0, 0, 0.29);
}

.tw-border-error-700 {
  --tw-border-opacity: 1;
  border-color: rgb(180 35 24 / var(--tw-border-opacity));
}

.tw-border-error-800 {
  --tw-border-opacity: 1;
  border-color: rgb(145 32 24 / var(--tw-border-opacity));
}

.tw-border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(235 235 235 / var(--tw-border-opacity));
}

.tw-border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(226 226 226 / var(--tw-border-opacity));
}

.tw-border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(215 215 215 / var(--tw-border-opacity));
}

.tw-border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(196 196 196 / var(--tw-border-opacity));
}

.tw-border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(181 181 181 / var(--tw-border-opacity));
}

.tw-border-green-800 {
  --tw-border-opacity: 1;
  border-color: rgb(58 145 63 / var(--tw-border-opacity));
}

.tw-border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(247 189 255 / var(--tw-border-opacity));
}

.tw-border-purple-800 {
  --tw-border-opacity: 1;
  border-color: rgb(95 33 103 / var(--tw-border-opacity));
}

.tw-border-purple-900 {
  --tw-border-opacity: 1;
  border-color: rgb(71 0 80 / var(--tw-border-opacity));
}

.tw-border-qc-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(192 237 255 / var(--tw-border-opacity));
}

.tw-border-qc-blue-800 {
  --tw-border-opacity: 1;
  border-color: rgb(0 95 134 / var(--tw-border-opacity));
}

.tw-border-qc-blue-800\/90 {
  border-color: rgb(0 95 134 / 0.9);
}

.tw-border-qc-blue-900 {
  --tw-border-opacity: 1;
  border-color: rgb(1 73 103 / var(--tw-border-opacity));
}

.tw-border-qcDanger-200 {
  --tw-border-opacity: 1;
  border-color: rgb(250 182 183 / var(--tw-border-opacity));
}

.tw-border-qcDanger-700 {
  --tw-border-opacity: 1;
  border-color: rgb(178 38 40 / var(--tw-border-opacity));
}

.tw-border-qcInfo-200 {
  --tw-border-opacity: 1;
  border-color: rgb(182 215 255 / var(--tw-border-opacity));
}

.tw-border-qcInfo-700 {
  --tw-border-opacity: 1;
  border-color: rgb(11 88 179 / var(--tw-border-opacity));
}

.tw-border-qcIris-500 {
  --tw-border-opacity: 1;
  border-color: rgb(128 72 230 / var(--tw-border-opacity));
}

.tw-border-qcIris-200 {
  --tw-border-opacity: 1;
  border-color: rgb(204 182 245 / var(--tw-border-opacity));
}

.tw-border-qcIris-800 {
  --tw-border-opacity: 1;
  border-color: rgb(57 20 125 / var(--tw-border-opacity));
}

.tw-border-qcNeutrals-400 {
  --tw-border-opacity: 1;
  border-color: rgb(194 197 213 / var(--tw-border-opacity));
}

.tw-border-qcNeutrals-600 {
  --tw-border-opacity: 1;
  border-color: rgb(89 87 112 / var(--tw-border-opacity));
}

.tw-border-qcSuccess-300 {
  --tw-border-opacity: 1;
  border-color: rgb(188 226 152 / var(--tw-border-opacity));
}

.tw-border-qcSuccess-800 {
  --tw-border-opacity: 1;
  border-color: rgb(58 96 22 / var(--tw-border-opacity));
}

.tw-border-qcSunset-800 {
  --tw-border-opacity: 1;
  border-color: rgb(136 57 0 / var(--tw-border-opacity));
}

.tw-border-qcWarning-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 189 61 / var(--tw-border-opacity));
}

.tw-border-qcWarning-700 {
  --tw-border-opacity: 1;
  border-color: rgb(168 98 0 / var(--tw-border-opacity));
}

.tw-border-success-800 {
  --tw-border-opacity: 1;
  border-color: rgb(5 96 58 / var(--tw-border-opacity));
}

.tw-border-warning-700 {
  --tw-border-opacity: 1;
  border-color: rgb(225 122 0 / var(--tw-border-opacity));
}

.tw-border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.\!tw-bg-error-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 243 242 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-gray-300 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(226 226 226 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-gray-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 243 243 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-purple-25 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 239 255 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-purple-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(252 222 255 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qc-blue-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(240 251 255 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qc-blue-800 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(0 95 134 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcDanger-200 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(250 182 183 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcDanger-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 237 237 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcInfo-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(218 235 255 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcInfo-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(237 245 255 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcIris-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(230 218 250 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcIris-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 237 253 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcNeutrals-200 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(247 249 255 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcNeutrals-300 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(234 236 246 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcSuccess-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(233 245 221 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcSunset-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 230 213 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-qcSunset-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 243 234 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-success-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(228 255 238 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-warning-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 249 227 / var(--tw-bg-opacity)) !important;
}

.\!tw-bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}

.tw-bg-error-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 45 32 / var(--tw-bg-opacity));
}

.tw-bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 240 240 / var(--tw-bg-opacity));
}

.tw-bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(235 235 235 / var(--tw-bg-opacity));
}

.tw-bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 243 243 / var(--tw-bg-opacity));
}

.tw-bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(92 92 92 / var(--tw-bg-opacity));
}

.tw-bg-green-25 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 255 241 / var(--tw-bg-opacity));
}

.tw-bg-oceanBlue {
  --tw-bg-opacity: 1;
  background-color: rgb(40 46 186 / var(--tw-bg-opacity));
}

.tw-bg-purple-25 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 239 255 / var(--tw-bg-opacity));
}

.tw-bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 222 255 / var(--tw-bg-opacity));
}

.tw-bg-purple-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(95 33 103 / var(--tw-bg-opacity));
}

.tw-bg-qc-blue-25 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 252 255 / var(--tw-bg-opacity));
}

.tw-bg-qc-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(131 219 255 / var(--tw-bg-opacity));
}

.tw-bg-qc-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 139 195 / var(--tw-bg-opacity));
}

.tw-bg-qc-blue-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 95 134 / var(--tw-bg-opacity));
}

.tw-bg-qc-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(1 73 103 / var(--tw-bg-opacity));
}

.tw-bg-qc-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 226 226 / var(--tw-bg-opacity));
}

.tw-bg-qcDanger-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 219 219 / var(--tw-bg-opacity));
}

.tw-bg-qcDanger-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 182 183 / var(--tw-bg-opacity));
}

.tw-bg-qcDanger-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 237 237 / var(--tw-bg-opacity));
}

.tw-bg-qcGlacier-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 232 245 / var(--tw-bg-opacity));
}

.tw-bg-qcGlacier-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 232 245 / var(--tw-bg-opacity));
}

.tw-bg-qcDanger-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(178 38 40 / var(--tw-bg-opacity));
}

.tw-bg-qcInfo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(218 235 255 / var(--tw-bg-opacity));
}

.tw-bg-qcInfo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 245 255 / var(--tw-bg-opacity));
}

.tw-bg-qcIris-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(230 218 250 / var(--tw-bg-opacity));
}

.tw-bg-qcIris-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 27 175 / var(--tw-bg-opacity));
}

.tw-bg-qcMidnightPurple {
  --tw-bg-opacity: 1;
  background-color: rgb(13 16 101 / var(--tw-bg-opacity));
}

.tw-bg-qcIris-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 108 235 / var(--tw-bg-opacity));
}

.tw-bg-qcIris-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 108 235 / var(--tw-bg-opacity));
}

.tw-bg-qcNeutrals-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 236 246 / var(--tw-bg-opacity));
}

.tw-bg-qcNeutrals-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(72 67 87 / var(--tw-bg-opacity));
}

.tw-bg-qcOceanBlue {
  --tw-bg-opacity: 1;
  background-color: rgb(40 46 186 / var(--tw-bg-opacity));
}

.tw-bg-qcSuccess-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 250 238 / var(--tw-bg-opacity));
}

.tw-bg-qcSuccess-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(77 128 29 / var(--tw-bg-opacity));
}

.tw-bg-qcSunset-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 213 / var(--tw-bg-opacity));
}

.tw-bg-qcWarning-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 229 177 / var(--tw-bg-opacity));
}

.tw-bg-qcWarning-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 236 / var(--tw-bg-opacity));
}

.tw-bg-qcWarning-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 189 61 / var(--tw-bg-opacity));
}

.tw-bg-success-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 152 85 / var(--tw-bg-opacity));
}

.tw-bg-transparent {
  background-color: transparent;
}

.tw-bg-warning-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(225 122 0 / var(--tw-bg-opacity));
}

.tw-bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.tw-bg-white\/75 {
  background-color: rgb(255 255 255 / 0.75);
}

.tw-bg-qcIrisSky {
  background-image: linear-gradient(129deg, #4F1BAF 33%, #0971EF 84%);
}

.tw-bg-qcMidnightIris {
  background-image: linear-gradient(129deg, #0D1065 16%, #4F1BAF 67%);
}

.tw-bg-qcNightOcean {
  background-image: linear-gradient(129deg, #1A132D 16.51%, #282EBA 67.2%);
}

.tw-bg-qcNightSlate {
  background-image: linear-gradient(129deg, #1A132D 33%, #7A7FA7 84%);
}

.tw-bg-qcOceanSky {
  background-image: linear-gradient(129deg, #282EBA 16%, #0971EF 84%);
}

.tw-fill-qcDanger-500 {
  fill: #F3494B;
}

.tw-fill-qcInfo-500 {
  fill: #489BFF;
}

.tw-fill-qcSuccess-500 {
  fill: #90CF54;
}

.tw-fill-qcWarning-500 {
  fill: #FFBD3D;
}

.\!tw-p-0 {
  padding: 0px !important;
}

.tw-p-0 {
  padding: 0px;
}

.tw-p-1 {
  padding: 0.25rem;
}

.tw-p-2 {
  padding: 0.5rem;
}

.tw-p-2\.5 {
  padding: 0.625rem;
}

.tw-p-4 {
  padding: 1rem;
}

.tw-p-5 {
  padding: 1.25rem;
}

.tw-p-\[5px\] {
  padding: 5px;
}

.tw-px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.tw-px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.tw-px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.tw-px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}

.tw-px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.tw-px-\[15px\] {
  padding-left: 15px;
  padding-right: 15px;
}

.tw-px-\[6px\] {
  padding-left: 6px;
  padding-right: 6px;
}

.tw-py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.tw-py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.tw-py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.tw-py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.tw-py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.tw-py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.tw-py-\[4px\] {
  padding-top: 4px;
  padding-bottom: 4px;
}

.tw-py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}

.tw-pb-\[5px\] {
  padding-bottom: 5px;
}

.tw-pl-0 {
  padding-left: 0px;
}

.tw-pl-2 {
  padding-left: 0.5rem;
}

.tw-pl-4 {
  padding-left: 1rem;
}

.tw-pl-5 {
  padding-left: 1.25rem;
}

.tw-pl-\[15px\] {
  padding-left: 15px;
}

.tw-pr-2 {
  padding-right: 0.5rem;
}

.tw-pr-2\.5 {
  padding-right: 0.625rem;
}

.tw-pr-3 {
  padding-right: 0.75rem;
}

.tw-pr-3\.5 {
  padding-right: 0.875rem;
}

.tw-pr-4 {
  padding-right: 1rem;
}

.tw-pr-5 {
  padding-right: 1.25rem;
}

.tw-pr-8 {
  padding-right: 2rem;
}

.tw-pr-\[15px\] {
  padding-right: 15px;
}

.tw-text-left {
  text-align: left;
}

.tw-text-center {
  text-align: center;
}

.tw-text-right {
  text-align: right;
}

.tw-align-middle {
  vertical-align: middle;
}

.tw-font-\[Inter\] {
  font-family: Inter;
}

.tw-font-inter {
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.tw-font-sans {
  font-family: Source Sans Pro, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.tw-text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.tw-text-\[10px\] {
  font-size: 10px;
}

.tw-text-\[11px\] {
  font-size: 11px;
}

.tw-text-\[13px\] {
  font-size: 13px;
}

.tw-text-\[14px\] {
  font-size: 14px;
}

.tw-text-\[30px\] {
  font-size: 30px;
}

.tw-text-\[32px\] {
  font-size: 32px;
}

.tw-text-\[40px\] {
  font-size: 40px;
}

.tw-text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.tw-text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.tw-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.tw-text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.tw-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.tw-font-black {
  font-weight: 900;
}

.tw-font-bold {
  font-weight: 700;
}

.tw-font-extrabold {
  font-weight: 800;
}

.tw-font-medium {
  font-weight: 500;
}

.tw-font-normal {
  font-weight: 400;
}

.tw-font-semibold {
  font-weight: 600;
}

.tw-italic {
  font-style: italic;
}

.tw-not-italic {
  font-style: normal;
}

.tw-leading-4 {
  line-height: 1rem;
}

.tw-leading-5 {
  line-height: 1.25rem;
}

.tw-leading-6 {
  line-height: 1.5rem;
}

.tw-leading-\[14px\] {
  line-height: 14px;
}

.tw-leading-\[15px\] {
  line-height: 15px;
}

.tw-leading-\[16px\] {
  line-height: 16px;
}

.tw-leading-\[18px\] {
  line-height: 18px;
}

.tw-leading-\[19px\] {
  line-height: 19px;
}

.tw-leading-\[22px\] {
  line-height: 22px;
}

.tw-leading-\[25px\] {
  line-height: 25px;
}

.tw-leading-normal {
  line-height: 1.5;
}

.\!tw-text-black-29 {
  color: rgba(0, 0, 0, 0.29) !important;
}

.\!tw-text-black-70 {
  color: rgba(0, 0, 0, 0.7) !important;
}

.\!tw-text-purple-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(95 33 103 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qc-blue-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(0 95 134 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcDanger-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(134 29 30 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcDanger-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(89 19 20 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcInfo-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(11 88 179 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcInfo-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(9 66 135 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcIris-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(57 20 125 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcNeutrals-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(89 87 112 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcNeutrals-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(42 39 51 / var(--tw-text-opacity)) !important;
}

.\!tw-text-qcSunset-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(136 57 0 / var(--tw-text-opacity)) !important;
}

.\!tw-text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.tw-text-black {
  color: rgba(0, 0, 0, 1);
}

.tw-text-black-12 {
  color: rgba(0, 0, 0, 0.12);
}

.tw-text-black-29 {
  color: rgba(0, 0, 0, 0.29);
}

.tw-text-black-70 {
  color: rgba(0, 0, 0, 0.7);
}

.tw-text-error-700 {
  --tw-text-opacity: 1;
  color: rgb(180 35 24 / var(--tw-text-opacity));
}

.tw-text-error-800 {
  --tw-text-opacity: 1;
  color: rgb(145 32 24 / var(--tw-text-opacity));
}

.tw-text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(215 215 215 / var(--tw-text-opacity));
}

.tw-text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(204 204 204 / var(--tw-text-opacity));
}

.tw-text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(196 196 196 / var(--tw-text-opacity));
}

.tw-text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(181 181 181 / var(--tw-text-opacity));
}

.tw-text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(124 124 124 / var(--tw-text-opacity));
}

.tw-text-midnightPurple {
  --tw-text-opacity: 1;
  color: rgb(13 16 101 / var(--tw-text-opacity));
}

.tw-text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(171 32 189 / var(--tw-text-opacity));
}

.tw-text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(95 33 103 / var(--tw-text-opacity));
}

.tw-text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(71 0 80 / var(--tw-text-opacity));
}

.tw-text-qc-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(192 237 255 / var(--tw-text-opacity));
}

.tw-text-qc-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(0 159 223 / var(--tw-text-opacity));
}

.tw-text-qc-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(0 117 165 / var(--tw-text-opacity));
}

.tw-text-qc-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(0 95 134 / var(--tw-text-opacity));
}

.tw-text-qc-blue-800\/90 {
  color: rgb(0 95 134 / 0.9);
}

.tw-text-qc-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(1 73 103 / var(--tw-text-opacity));
}

.tw-text-qc-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(255 128 39 / var(--tw-text-opacity));
}

.tw-text-qcDanger-700 {
  --tw-text-opacity: 1;
  color: rgb(178 38 40 / var(--tw-text-opacity));
}

.tw-text-qcDanger-800 {
  --tw-text-opacity: 1;
  color: rgb(134 29 30 / var(--tw-text-opacity));
}

.tw-text-qcGreen-700 {
  --tw-text-opacity: 1;
  color: rgb(77 128 29 / var(--tw-text-opacity));
}

.tw-text-qcGreen-700 {
  --tw-text-opacity: 1;
  color: rgb(77 128 29 / var(--tw-text-opacity));
}

.tw-text-qcDanger-900 {
  --tw-text-opacity: 1;
  color: rgb(89 19 20 / var(--tw-text-opacity));
}

.tw-text-qcInfo-700 {
  --tw-text-opacity: 1;
  color: rgb(11 88 179 / var(--tw-text-opacity));
}

.tw-text-qcInfo-800 {
  --tw-text-opacity: 1;
  color: rgb(9 66 135 / var(--tw-text-opacity));
}

.tw-text-qcInfo-900 {
  --tw-text-opacity: 1;
  color: rgb(6 44 90 / var(--tw-text-opacity));
}

.tw-text-qcIris-800 {
  --tw-text-opacity: 1;
  color: rgb(57 20 125 / var(--tw-text-opacity));
}

.tw-text-qcNeutrals-400 {
  --tw-text-opacity: 1;
  color: rgb(194 197 213 / var(--tw-text-opacity));
}

.tw-text-qcNeutrals-400 {
  --tw-text-opacity: 1;
  color: rgb(194 197 213 / var(--tw-text-opacity));
}

.tw-text-qcIris-900 {
  --tw-text-opacity: 1;
  color: rgb(34 12 75 / var(--tw-text-opacity));
}

.tw-text-qcNeutrals-800 {
  --tw-text-opacity: 1;
  color: rgb(42 39 51 / var(--tw-text-opacity));
}

.tw-text-qcSuccess-800 {
  --tw-text-opacity: 1;
  color: rgb(58 96 22 / var(--tw-text-opacity));
}

.tw-text-qcSuccess-900 {
  --tw-text-opacity: 1;
  color: rgb(39 64 15 / var(--tw-text-opacity));
}

.tw-text-qcSunset-800 {
  --tw-text-opacity: 1;
  color: rgb(136 57 0 / var(--tw-text-opacity));
}

.tw-text-qcSunset-900 {
  --tw-text-opacity: 1;
  color: rgb(82 34 0 / var(--tw-text-opacity));
}

.tw-text-qcWarning-700 {
  --tw-text-opacity: 1;
  color: rgb(168 98 0 / var(--tw-text-opacity));
}

.tw-text-success-800 {
  --tw-text-opacity: 1;
  color: rgb(5 96 58 / var(--tw-text-opacity));
}

.tw-text-warning-700 {
  --tw-text-opacity: 1;
  color: rgb(225 122 0 / var(--tw-text-opacity));
}

.tw-text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tw-underline {
  text-decoration-line: underline;
}

.tw-no-underline {
  text-decoration-line: none;
}

.tw-opacity-100 {
  opacity: 1;
}

.tw-opacity-25 {
  opacity: 0.25;
}

.tw-opacity-50 {
  opacity: 0.5;
}

.tw-opacity-60 {
  opacity: 0.6;
}

.tw-opacity-90 {
  opacity: 0.9;
}

.tw-opacity-\[0\.38\] {
  opacity: 0.38;
}

.\!tw-shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.tw-shadow-\[-1px_-2px_6px_rgba\(0\,0\,0\,0\.07\)\,inset_0px_-3px_7px_rgba\(0\,0\,0\,0\.1\)\] {
  --tw-shadow: -1px -2px 6px rgba(0, 0, 0, 0.07), inset 0px -3px 7px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: -1px -2px 6px var(--tw-shadow-color), inset 0px -3px 7px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.tw-shadow-card {
  --tw-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0px 4px 4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.tw-shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.tw-shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.tw-shadow-select {
  --tw-shadow: -1px 2px 6px 0px rgba(0, 0, 0, 0.20);
  --tw-shadow-colored: -1px 2px 6px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.tw-outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.tw-ring-offset-white {
  --tw-ring-offset-color: #FFFFFF;
}

.tw-backdrop-blur-\[2px\] {
  --tw-backdrop-blur: blur(2px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.tw-transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.tw-transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.tw-transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.tw-ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.tw-ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hide scrollbar for Chrome, Safari and Opera */

.tw-no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */

.tw-no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

/* Custom Tooltip */

/* Base tooltip styling */

.react-tooltip {
  padding: 10px !important;
  box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.25) !important;
  position: absolute;
  border-radius: 6px !important;
  font-family: Inter !important;
  font-size: 11px !important;
  font-style: normal !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

/* Light theme */

.tooltip-light {
  background-color: #ffffff !important;
  color: #1c64f2 !important;
}

/* Dark theme */

.tooltip-dark {
  background-color: #1c64f2 !important;
  color: #ffffff !important;
}

.placeholder\:tw-text-qcNeutrals-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(194 197 213 / var(--tw-text-opacity));
}

.placeholder\:tw-text-qcNeutrals-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(194 197 213 / var(--tw-text-opacity));
}

.before\:tw-block::before {
  content: var(--tw-content);
  display: block;
}

.before\:tw-h-\[16px\]::before {
  content: var(--tw-content);
  height: 16px;
}

.before\:tw-w-\[16px\]::before {
  content: var(--tw-content);
  width: 16px;
}

.before\:tw-rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:tw-leading-\[0px\]::before {
  content: var(--tw-content);
  line-height: 0px;
}

.before\:tw-text-black-29::before {
  content: var(--tw-content);
  color: rgba(0, 0, 0, 0.29);
}

.before\:tw-text-qc-blue-800::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(0 95 134 / var(--tw-text-opacity));
}

.before\:tw-shadow-\[inset_0px_0px_0px_2px_\#005f86\]::before {
  content: var(--tw-content);
  --tw-shadow: inset 0px 0px 0px 2px #005f86;
  --tw-shadow-colored: inset 0px 0px 0px 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:tw-shadow-\[inset_0px_0px_0px_2px_rgba\(0\2c 0\2c 0\2c 0\.29\)\]::before {
  content: var(--tw-content);
  --tw-shadow: inset 0px 0px 0px 2px rgba(0, 0, 0, 0.29);
  --tw-shadow-colored: inset 0px 0px 0px 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:tw-shadow-\[inset_0px_0px_0px_5px_\#005f86\]::before {
  content: var(--tw-content);
  --tw-shadow: inset 0px 0px 0px 5px #005f86;
  --tw-shadow-colored: inset 0px 0px 0px 5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:tw-shadow-\[inset_0px_0px_0px_5px_rgba\(0\2c 0\2c 0\2c 0\.29\)\]::before {
  content: var(--tw-content);
  --tw-shadow: inset 0px 0px 0px 5px rgba(0, 0, 0, 0.29);
  --tw-shadow-colored: inset 0px 0px 0px 5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:tw-transition-shadow::before {
  content: var(--tw-content);
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:tw-duration-200::before {
  content: var(--tw-content);
  transition-duration: 200ms;
}

.before\:tw-ease-in-out::before {
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.before\:tw-duration-200::before {
  content: var(--tw-content);
  animation-duration: 200ms;
}

.before\:tw-ease-in-out::before {
  content: var(--tw-content);
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.first\:tw-rounded-t-md:first-child {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}

.last\:tw-rounded-b-\[5px\]:last-child {
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}

.last\:tw-rounded-b-md:last-child {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.last\:tw-border-b-0:last-child {
  border-bottom-width: 0px;
}

.last\:tw-pb-0:last-child {
  padding-bottom: 0px;
}

.odd\:tw-bg-white:nth-child(odd) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.even\:tw-bg-gray-25:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity));
}

.hover\:tw-cursor-default:hover {
  cursor: default;
}

.hover\:tw-cursor-pointer:hover {
  cursor: pointer;
}

.hover\:tw-border-r-4:hover {
  border-right-width: 4px;
}

.hover\:tw-border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(235 235 235 / var(--tw-border-opacity));
}

.hover\:tw-border-qc-blue-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(0 139 195 / var(--tw-border-opacity));
}

.hover\:tw-border-qc-blue-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(1 73 103 / var(--tw-border-opacity));
}

.hover\:tw-border-qc-orange-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 105 0 / var(--tw-border-opacity));
}

.hover\:\!tw-bg-qc-blue-50:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(240 251 255 / var(--tw-bg-opacity)) !important;
}

.hover\:tw-bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 240 240 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(225 246 255 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-blue-25:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 252 255 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-blue-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(131 219 255 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 251 255 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 117 165 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-blue-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(1 73 103 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qc-orange-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(177 74 2 / var(--tw-bg-opacity));
}

.hover\:tw-bg-qcInfo-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(145 195 255 / var(--tw-bg-opacity));
}

.hover\:tw-bg-success-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(5 79 49 / var(--tw-bg-opacity));
}

.hover\:tw-bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:tw-text-error-900:hover {
  --tw-text-opacity: 1;
  color: rgb(122 39 26 / var(--tw-text-opacity));
}

.hover\:tw-text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(92 92 92 / var(--tw-text-opacity));
}

.hover\:tw-text-qc-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(0 95 134 / var(--tw-text-opacity));
}

.hover\:tw-text-qc-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(1 73 103 / var(--tw-text-opacity));
}

.hover\:tw-text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:tw-opacity-92:hover {
  opacity: 0.92;
}

.hover\:tw-opacity-\[0\.38\]:hover {
  opacity: 0.38;
}

.hover\:tw-shadow-button:hover {
  --tw-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0px 4px 4px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:tw-border:focus {
  border-width: 1px;
}

.focus\:tw-border-purple-600:focus {
  --tw-border-opacity: 1;
  border-color: rgb(171 32 189 / var(--tw-border-opacity));
}

.focus\:tw-border-purple-800:focus {
  --tw-border-opacity: 1;
  border-color: rgb(95 33 103 / var(--tw-border-opacity));
}

.focus\:tw-outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:tw-ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:tw-ring-gray-700:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(181 181 181 / var(--tw-ring-opacity));
}

.focus\:tw-ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.disabled\:tw-pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:tw-text-white:disabled {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.disabled\:tw-opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:hover\:tw-cursor-default:hover:disabled {
  cursor: default;
}

.tw-group:focus-within .group-focus-within\:tw-text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(95 33 103 / var(--tw-text-opacity));
}

.tw-group:hover .group-hover\:tw-block {
  display: block;
}

.tw-group:hover .group-hover\:tw-hidden {
  display: none;
}

.data-\[swipe\=cancel\]\:tw-translate-x-0[data-swipe=cancel] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=end\]\:tw-translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe=end] {
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=move\]\:tw-translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe=move] {
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=move\]\:tw-transition-none[data-swipe=move] {
  transition-property: none;
}

.data-\[state\=open\]\:tw-animate-in[data-state=open] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:tw-animate-out[data-state=closed] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[swipe\=end\]\:tw-animate-out[data-swipe=end] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:tw-fade-out-80[data-state=closed] {
  --tw-exit-opacity: 0.8;
}

.data-\[state\=closed\]\:tw-slide-out-to-right-full[data-state=closed] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=open\]\:tw-slide-in-from-top-full[data-state=open] {
  --tw-enter-translate-y: -100%;
}

@media (min-width: 640px) {
  .sm\:tw-bottom-20 {
    bottom: 5rem;
  }

  .sm\:tw-right-0 {
    right: 0px;
  }

  .sm\:tw-top-auto {
    top: auto;
  }

  .sm\:tw-flex-col {
    flex-direction: column;
  }

  .data-\[state\=open\]\:sm\:tw-slide-in-from-bottom-full[data-state=open] {
    --tw-enter-translate-y: 100%;
  }
}

@media (min-width: 768px) {
  .md\:tw-max-w-\[420px\] {
    max-width: 420px;
  }
}

@media (min-height: 650px) {
  .tall\:tw-flex {
    display: flex;
  }
}

.\[\&\:not\(\:last-child\)\]\:tw-border-b:not(:last-child) {
  border-bottom-width: 1px;
}
