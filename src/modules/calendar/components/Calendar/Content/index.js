import { useComponentLogic } from "./hooks";
import CalendarHeader from "./CalendarHeader";
import CalendarView from "./CalendarView";
import ListView from "./ListView";
import CalendarSecondaryHeader from "./CalendarSecondaryHeader";
import SelectTypeDropdown from "../SelectTypeDropdown";

const viewOptions = {
  calendar: <CalendarView />,
  list: <ListView />
};

const Content = ({ isExpanded, onToggleSize }) => {
  const { handleTabClick, calendarView, activeView, isModalOpen, clickedDateTime, handleAddClick, handleCloseModal, handleOptionClick } = useComponentLogic();

  return (
    <div className="tw-flex tw-h-full tw-flex-col">
      <CalendarHeader
        isExpanded={isExpanded}
        onToggleSize={onToggleSize}
        handleTabClick={handleTabClick}
        calendarView={calendarView}
      />
      <CalendarSecondaryHeader 
        activeView={activeView} 
        onAddClick={handleAddClick}
        isModalOpen={isModalOpen}
        onCloseModal={handleCloseModal}
        clickedDateTime={clickedDateTime}
        isExpanded={isExpanded}
        onOptionClick={handleOptionClick}
      />
      <div className="tw-flex tw-h-full tw-w-full tw-overflow-auto tw-bg-qcIris-900 tw-p-4 tw-text-qcIris-200">
        {viewOptions[activeView]}
      </div>
      {isExpanded && (
        <SelectTypeDropdown
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          clickedDateTime={clickedDateTime}
          isExpanded={isExpanded}
          onOptionClick={handleOptionClick}
        />
      )}
    </div>
  );
};

export default Content;
