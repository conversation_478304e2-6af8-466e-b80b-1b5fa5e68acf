import { assoc, find, ifElse, map, pipe, prop, propEq } from "ramda";
import { useState } from "react";
import useLocalStorage from "shared/hooks/useLocalStorage";

const viewOptions = [
  {
    id: "calendar",
    label: "Calendar",
    icon: "fa-regular fa-calendar",
    active: true
  },
  { id: "list", label: "List", icon: "fa-solid fa-list", active: false }
];

export const useComponentLogic = () => {
  const [calendarView, setCalendarView] = useLocalStorage(
    "calendarView",
    viewOptions
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [clickedDateTime, setClickedDateTime] = useState(null);

  const handleTabClick = id => {
    setCalendarView(
      map(
        ifElse(propEq("id", id), assoc("active", true), assoc("active", false))
      )
    );
  };

  const handleAddClick = () => {
    const currentDateTime = new Date().toISOString();
    setClickedDateTime(currentDateTime);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleOptionClick = type => {
    console.log(`Selected option: ${type}`);
    // TODO: Implement navigation/action for each type
  };

  const activeView = pipe(find(prop("active")), prop("id"))(calendarView);

  return {
    handleTabClick,
    calendarView,
    activeView,
    isModalOpen,
    clickedDateTime,
    handleAddClick,
    handleCloseModal,
    handleOptionClick
  };
};
