import { But<PERSON>, <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>roller<PERSON><PERSON> } from "@q-centrix/q-components-react";
import classNames from "classnames";
import { useState } from "react";
import WeekNavigator from "./WeekNavigator";

const SelectTypeDropdown = ({ isOpen, onClose, clickedDateTime }) => {
  if (!isOpen) return null;

  const handleOptionClick = type => {
    console.log(`Selected: ${type} at ${clickedDateTime}`);
    // TODO: Implement navigation/action for each type
    onClose();
  };

  return (
    <div className="tw-absolute tw-right-0 tw-top-full tw-z-50 tw-mt-1">
      {/* Dropdown - using design system colors */}
      <div className="tw-relative tw-w-[400px] tw-rounded-lg tw-border tw-flex tw-flex-col tw-gap-3 tw-p-3 tw-bg-qcIris-50 tw-border-qcIris-200 tw-shadow-qc">
        {/* Header */}
        <div className="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-pb-[10px]">
          <h3 className="tw-font-inter tw-font-semibold tw-text-lg tw-leading-[1.21em] tw-text-qcInfo-800 tw-m-0">
            Select Type
          </h3>
          <button
            onClick={onClose}
            className="tw-flex tw-justify-center tw-items-center tw-p-0 tw-text-base tw-bg-transparent tw-border-none tw-cursor-pointer tw-text-qcIris-800 tw-opacity-85 hover:tw-opacity-100"
          >
            <i className="fa-solid fa-times" />
          </button>
        </div>

        {/* Options - using design system colors */}
        <div className="tw-flex tw-flex-col tw-gap-3">
          {/* Case task - Purple theme */}
          <button
            onClick={() => handleOptionClick("case-task")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcIris-100 tw-border-qcIris-200 tw-text-qcIris-900"
          >
            Case task
          </button>

          {/* Activity step - Green theme */}
          <button
            onClick={() => handleOptionClick("activity-step")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcGreen-100 tw-border-qcGreen-300 tw-text-qcGreen-900"
          >
            Activity step
          </button>

          {/* Hours - Neutral theme */}
          <button
            onClick={() => handleOptionClick("hours")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400 tw-text-qcNeutrals-800"
          >
            Hours
          </button>

          {/* Calendar item - Neutral theme */}
          <button
            onClick={() => handleOptionClick("calendar-item")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400 tw-text-qcNeutrals-800"
          >
            Calendar item
          </button>
        </div>
      </div>
    </div>
  );
};

const CalendarHeader = ({
  isExpanded,
  onToggleSize,
  handleTabClick,
  calendarView
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [clickedDateTime, setClickedDateTime] = useState(null);

  const iconClass = classNames("fa-solid", {
    "fa-down-left-and-up-right-to-center fa-rotate-90": isExpanded,
    "fa-up-right-and-down-left-from-center": !isExpanded
  });

  const handleAddClick = () => {
    const currentDateTime = new Date().toISOString();
    setClickedDateTime(currentDateTime);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div className="tw-flex tw-max-h-[50px] tw-items-center tw-justify-between tw-self-stretch tw-border-b tw-border-qcIris-500 tw-bg-qcIris-300 tw-p-3">
        <Button
          onClick={onToggleSize}
          outline={!isExpanded}
          bg="main"
          customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3"
        >
          <i className={iconClass} />
        </Button>
        {isExpanded && <WeekNavigator />}
        <TabControllerBar className="tw-max-h-[38px] tw-max-w-[300px] tw-border-none tw-bg-qcIris-200">
          {calendarView.map(tab => (
            <Tab
              layoutId="calendarTabController"
              key={tab.id}
              tab={tab}
              onClick={() => handleTabClick(tab.id)}
              className="!tw-max-h-[30px] !tw-min-h-[30px] !tw-min-w-[140px] !tw-max-w-[140px] tw-bg-qcIris-200 !tw-py-[5px] tw-text-sm tw-font-semibold tw-text-qcIris-800"
              activeTabClassName="tw-bg-qcIris-800"
            />
          ))}
        </TabControllerBar>
        <div className="tw-flex tw-relative tw-gap-2 tw-items-center">
          <Button
            onClick={handleAddClick}
            outline
            bg="default"
            customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3 tw-bg-green-700 hover:tw-bg-green-600 tw-text-white"
          >
            <i className="fa-solid fa-plus" />
          </Button>
          <Button
            onClick={onToggleSize}
            outline
            bg="default"
            customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3"
          >
            <i className="fa-sharp fa-regular fa-chart-pie" />
          </Button>

          <SelectTypeDropdown
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            clickedDateTime={clickedDateTime}
          />
        </div>
      </div>
    </>
  );
};

export default CalendarHeader;
