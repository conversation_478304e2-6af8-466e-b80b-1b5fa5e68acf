import { Button, Tab, TabControllerBar } from "@q-centrix/q-components-react";
import classNames from "classnames";
import WeekNavigator from "./WeekNavigator";


const CalendarHeader = ({
  isExpanded,
  onToggleSize,
  handleTabClick,
  calendarView
}) => {
  const iconClass = classNames("fa-solid", {
    "fa-down-left-and-up-right-to-center fa-rotate-90": isExpanded,
    "fa-up-right-and-down-left-from-center": !isExpanded
  });

  return (
    <>
      <div className="tw-flex tw-max-h-[50px] tw-items-center tw-justify-between tw-self-stretch tw-border-b tw-border-qcIris-500 tw-bg-qcIris-300 tw-p-3">
        <Button
          onClick={onToggleSize}
          outline={!isExpanded}
          bg="main"
          customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3"
        >
          <i className={iconClass} />
        </Button>
        {isExpanded && <WeekNavigator />}
        <TabControllerBar className="tw-max-h-[38px] tw-max-w-[300px] tw-border-none tw-bg-qcIris-200">
          {calendarView.map(tab => (
            <Tab
              layoutId="calendarTabController"
              key={tab.id}
              tab={tab}
              onClick={() => handleTabClick(tab.id)}
              className="!tw-max-h-[30px] !tw-min-h-[30px] !tw-min-w-[140px] !tw-max-w-[140px] tw-bg-qcIris-200 !tw-py-[5px] tw-text-sm tw-font-semibold tw-text-qcIris-800"
              activeTabClassName="tw-bg-qcIris-800"
            />
          ))}
        </TabControllerBar>
        <div className="tw-flex tw-relative tw-gap-2 tw-items-center">
          <Button
            onClick={onToggleSize}
            outline
            bg="default"
            customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3"
          >
            <i className="fa-sharp fa-regular fa-chart-pie" />
          </Button>
        </div>
      </div>
    </>
  );
};

export default CalendarHeader;
