import { Button } from "@q-centrix/q-components-react";
import SelectTypeModal from "../../SelectTypeModal";
import CalendarViewSecondaryHeader from "./CalendarViewSecondaryHeader";
import ListViewSecondaryHeader from "./ListViewSecondaryHeader";
import { useComponentLogic } from "./hooks";

const secondayHeaderOptions = {
  calendar: <CalendarViewSecondaryHeader />,
  list: <ListViewSecondaryHeader />
};

const CalendarSecondaryHeader = ({ activeView, isExpanded }) => {
  const {
    isModalOpen,
    handleCaptureTimeAndOpenModal,
    handleCloseModal
  } = useComponentLogic();

  return (
    <div className="tw-flex tw-h-[50px] tw-w-full tw-items-center tw-justify-between tw-border-b tw-border-t tw-border-qcIris-500 tw-bg-qcIris-900 tw-py-2.5 tw-px-3.5 tw-text-qcIris-200">
      <div className="tw-flex tw-items-center">
        {secondayHeaderOptions[activeView]}
      </div>
      <div className="tw-flex tw-relative tw-gap-2 tw-items-center">
        <Button
          onClick={handleCaptureTimeAndOpenModal}
          bg="success"
          customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3"
        >
          <i className="tw-text-white fa-solid fa-plus" />
        </Button>
        <SelectTypeModal
          isModalOpen={isModalOpen}
          onModalClose={handleCloseModal}
          isExpanded={isExpanded}
        />
      </div>
    </div>
  );
};

export default CalendarSecondaryHeader;
