import { But<PERSON> } from "@q-centrix/q-components-react";
import CalendarViewSecondaryHeader from "./CalendarViewSecondaryHeader";
import ListViewSecondaryHeader from "./ListViewSecondaryHeader";
import SelectTypeDropdown from "../../SelectTypeDropdown";

const secondayHeaderOptions = {
  calendar: <CalendarViewSecondaryHeader />,
  list: <ListViewSecondaryHeader />
};

const CalendarSecondaryHeader = ({ 
  activeView, 
  onAddClick, 
  isModalOpen, 
  onCloseModal, 
  clickedDateTime, 
  isExpanded, 
  onOptionClick 
}) => (
  <div className="tw-flex tw-h-[50px] tw-w-full tw-items-center tw-justify-between tw-border-b tw-border-qcIris-500 tw-bg-qcIris-900 tw-p-4 tw-text-qcIris-200">
    <div className="tw-flex tw-items-center">
      {secondayHeaderOptions[activeView]}
    </div>
    <div className="tw-flex tw-relative tw-gap-2 tw-items-center">
      <Button
        onClick={onAddClick}
        bg="success"
        customStyle="!tw-max-w-[30px] !tw-max-h-[30px] tw-px-3"
      >
        <i className="tw-text-white fa-solid fa-plus" />
      </Button>
      {!isExpanded && (
        <SelectTypeDropdown
          isOpen={isModalOpen}
          onClose={onCloseModal}
          clickedDateTime={clickedDateTime}
          isExpanded={isExpanded}
          onOptionClick={onOptionClick}
        />
      )}
    </div>
  </div>
);

export default CalendarSecondaryHeader;
