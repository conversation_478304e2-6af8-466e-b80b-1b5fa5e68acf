import { Button } from "@q-centrix/q-components-react";
import classNames from "classnames";

const SelectTypeDropdown = ({
  isOpen,
  onClose,
  clickedDateTime,
  isExpanded,
  onOptionClick
}) => {
  if (!isOpen) return null;

  const handleOptionClick = type => {
    console.log(`Selected: ${type} at ${clickedDateTime}`);
    // TODO: Implement navigation/action for each type
    onOptionClick(type);
    onClose();
  };

  const positionClasses = classNames("tw-z-50", {
    "tw-fixed tw-left-1/2 tw-transform -tw-translate-x-1/2 tw-mt-[130px]": isExpanded,
    "tw-absolute tw-right-0 tw-top-full tw-mt-8": !isExpanded
  });

  return (
    <div className={positionClasses}>
      {/* Dropdown - using design system colors */}
      <div className="tw-relative tw-w-[400px] tw-rounded-lg tw-border tw-flex tw-flex-col tw-gap-3 tw-p-3 tw-bg-qcIris-50 tw-border-qcIris-200 tw-shadow-qc">
        {/* Header */}
        <div className="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-pb-[10px]">
          <h3 className="tw-font-inter tw-font-semibold tw-text-lg tw-leading-[1.21em] tw-text-qcInfo-800 tw-m-0">
            Select Type
          </h3>
          <Button
            onClick={onClose}
            customStyle="tw-flex tw-justify-center tw-items-center tw-p-0 tw-text-base tw-bg-transparent tw-border-none tw-cursor-pointer tw-text-qcIris-800 tw-opacity-85 hover:tw-opacity-100"
          >
            <i className="fa-solid fa-times" />
          </Button>
        </div>

        {/* Options - using design system colors */}
        <div className="tw-flex tw-flex-col tw-gap-3">
          {/* Case task - Purple theme */}
          <button
            type="button"
            onClick={() => handleOptionClick("case-task")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcIris-100 tw-border-qcIris-200 tw-text-qcIris-900"
          >
            Case task
          </button>

          {/* Activity step - Green theme */}
          <button
            type="button"
            onClick={() => handleOptionClick("activity-step")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcGreen-100 tw-border-qcGreen-300 tw-text-qcGreen-900"
          >
            Activity step
          </button>

          {/* Hours - Neutral theme */}
          <button
            type="button"
            onClick={() => handleOptionClick("hours")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400 tw-text-qcNeutrals-800"
          >
            Hours
          </button>

          {/* Calendar item - Neutral theme */}
          <button
            type="button"
            onClick={() => handleOptionClick("calendar-item")}
            className="tw-w-full tw-flex tw-items-center tw-justify-center tw-cursor-pointer tw-border-2 tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] hover:tw-opacity-90 tw-transition-opacity tw-py-3 tw-px-4 tw-rounded-md tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400 tw-text-qcNeutrals-800"
          >
            Calendar item
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectTypeDropdown;
