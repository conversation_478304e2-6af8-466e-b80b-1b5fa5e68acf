import { <PERSON><PERSON>, <PERSON> } from "@q-centrix/q-components-react";
import classNames from "classnames";

const SelectTypeModal = ({ isModalOpen, onModalClose, isExpanded }) => {
  if (!isModalOpen) return null;

  const positionClasses = classNames({
    "tw-fixed tw-left-1/2 tw-top-[18%] tw-transform -tw-translate-x-1/2": isExpanded,
    "tw-absolute tw-right-0 tw-top-full tw-mt-[30px]": !isExpanded
  });

  return (
    <div className={positionClasses}>
      <Card className="tw-w-[400px] tw-bg-qcIris-50 tw-border-qcIris-200 tw-rounded-lg tw-shadow-qc tw-flex tw-flex-col tw-gap-3 tw-p-3">
        <div className="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-pb-[10px]">
          <h3 className="tw-m-0 tw-text-lg tw-font-semibold tw-font-inter tw-text-qcInfo-800">
            Select Type
          </h3>
          <Button
            onClick={onModalClose}
            customStyle="tw-flex tw-justify-center tw-items-center tw-p-0 tw-bg-transparent tw-border-none tw-cursor-pointer tw-text-qcIris-800 tw-opacity-85  hover:tw-opacity-100"
          >
            <i className="fa-solid fa-times" />
          </Button>
        </div>

        <div className="tw-flex tw-flex-col tw-gap-3">
          <Card className="tw-cursor-pointer tw-border-2 tw-rounded-md tw-transition-opacity hover:tw-opacity-90 !tw-bg-transparent !tw-border-transparent !tw-shadow-none tw-bg-qcIris-100 tw-border-qcIris-200">
            <div className="tw-w-full tw-flex tw-items-center tw-justify-center tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] tw-py-3 tw-px-4 tw-rounded-md tw-text-qcIris-900 tw-bg-qcIris-100 tw-border-qcIris-200 tw-border-2">
              Case task
            </div>
          </Card>

          <Card className="tw-cursor-pointer tw-border-2 tw-rounded-md tw-transition-opacity hover:tw-opacity-90 !tw-bg-transparent !tw-border-transparent !tw-shadow-none tw-bg-qcGreen-100 tw-border-qcGreen-300">
            <div className="tw-flex tw-justify-center tw-items-center tw-px-4 tw-py-3 tw-w-full tw-text-sm tw-font-semibold tw-rounded-md tw-border-2 tw-font-inter tw-text-qcGreen-900 tw-bg-qcGreen-100 tw-border-qcGreen-300">
              Activity step
            </div>
          </Card>

          <Card className="tw-cursor-pointer tw-border-2 tw-rounded-md tw-transition-opacity hover:tw-opacity-90 !tw-bg-transparent !tw-border-transparent !tw-shadow-none tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400">
            <div className="tw-w-full tw-flex tw-items-center tw-justify-center tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] tw-py-3 tw-px-4 tw-rounded-md tw-text-qcNeutrals-800 tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400 tw-border-2">
              Hours
            </div>
          </Card>

          <Card className="tw-cursor-pointer tw-border-2 tw-rounded-md tw-transition-opacity hover:tw-opacity-90 !tw-bg-transparent !tw-border-transparent !tw-shadow-none tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400">
            <div className="tw-w-full tw-flex tw-items-center tw-justify-center tw-font-inter tw-font-semibold tw-text-sm tw-leading-[1.21em] tw-py-3 tw-px-4 tw-rounded-md tw-text-qcNeutrals-800 tw-bg-qcNeutrals-300 tw-border-qcNeutrals-400 tw-border-2">
              Calendar item
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default SelectTypeModal;
