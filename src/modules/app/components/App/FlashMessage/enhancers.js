import { connect } from "react-redux";
import { pathOr } from "ramda";
import { popFlashMessage } from "../../../actions";

function mapStateToProps(state) {
  const feed = pathOr([], ["app", "flashMessage", "feed"], state);

  return {
    feed
  };
}

function mapDispatchToProps(dispatch) {
  return {
    hide: () => dispatch(popFlashMessage())
  };
}

export default connect(mapStateToProps, mapDispatchToProps);
