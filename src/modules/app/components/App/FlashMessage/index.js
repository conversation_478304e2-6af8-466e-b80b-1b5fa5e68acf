import { either, isEmpty, isNil } from "ramda";
import enhance from "./enhancers";

export function FlashMessage({ feed, hide }) {
  if (either(isEmpty, isNil)(feed)) {
    return null;
  }
  return (
    <div id="flash">
      <p className="error">
        <span className="message">{feed[0].message}</span>
        <span className="close" onClick={hide}>
          <i className="fa fa-times" />
        </span>
      </p>
    </div>
  );
}

export default enhance(FlashMessage);
