import { decoratedWithDispatch } from "utils/test/decorated";
import EnhancedFlashMessage, { FlashMessage } from "..";
import { popFlashMessage } from "../../../../actions";
import { act, create } from "react-test-renderer";

describe("FlashMessage", () => {
  function render(feed) {
    return create(<FlashMessage feed={feed} hide={jest.fn()} />);
  }
  test("renders FlashMessage without messages in feed", () => {
    const flashMessage = render([]);

    expect(flashMessage).toMatchSnapshot();
  });

  test("renders FlashMessage with messages in the feed", () => {
    const flashMessage = render([
      { message: "hello 1" },
      { message: "hello 2" }
    ]);

    expect(flashMessage).toMatchSnapshot();
  });

  test("when close button is clicker hide method is called", () => {
    const hide = jest.fn();
    const output = create(
      <FlashMessage feed={[{ message: "hello" }]} hide={hide} />
    );
    const instance = output.root;
    const [button] = instance.findAllByProps({ className: "close" });

    act(() => {
      button.props.onClick();
    });

    expect(hide).toHaveBeenCalled();
  });

  test("renders component with message when state has a value", () => {
    const { component } = decoratedWithDispatch(
      EnhancedFlashMessage,
      {},
      {
        flashMessage: { feed: [{ message: "hello" }] }
      }
    );
    const output = create(component);

    expect(output).toMatchSnapshot();
  });

  test("renders empty component when state has no value", () => {
    const { component } = decoratedWithDispatch(EnhancedFlashMessage, {}, {});
    const output = create(component);

    expect(output).toMatchSnapshot();
  });

  test("when close button is clicked popMessage action is called", () => {
    const { component, dispatch } = decoratedWithDispatch(
      EnhancedFlashMessage,
      {},
      {
        flashMessage: { feed: [{ message: "hello" }] }
      }
    );
    const output = create(component);
    const instance = output.root;
    const [button] = instance.findAllByProps({ className: "close" });

    act(() => {
      button.props.onClick();
    });

    expect(dispatch).toHaveBeenCalledWith(popFlashMessage());
  });
});
