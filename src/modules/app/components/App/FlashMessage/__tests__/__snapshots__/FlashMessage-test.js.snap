// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FlashMessage renders FlashMessage with messages in the feed 1`] = `
<div
  id="flash"
>
  <p
    className="error"
  >
    <span
      className="message"
    >
      hello 1
    </span>
    <span
      className="close"
      onClick={[MockFunction]}
    >
      <i
        className="fa fa-times"
      />
    </span>
  </p>
</div>
`;

exports[`FlashMessage renders FlashMessage without messages in feed 1`] = `null`;

exports[`FlashMessage renders component with message when state has a value 1`] = `
<div
  id="flash"
>
  <p
    className="error"
  >
    <span
      className="message"
    >
      hello
    </span>
    <span
      className="close"
      onClick={[Function]}
    >
      <i
        className="fa fa-times"
      />
    </span>
  </p>
</div>
`;

exports[`FlashMessage renders empty component when state has no value 1`] = `null`;
