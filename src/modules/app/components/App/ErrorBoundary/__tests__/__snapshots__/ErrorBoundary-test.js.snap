// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ErrorBoundary renders when child throws error with custom fallback 1`] = `
<Layout
  onBackArrowClick={[Function]}
>
  <div>
    Error occurred
  </div>
</Layout>
`;

exports[`ErrorBoundary renders when child throws error with default fallback 1`] = `
<Layout
  onBackArrowClick={[Function]}
>
  <div
    className="tw-flex tw-min-h-[calc(100vh-50px)] tw-items-center tw-justify-center tw-bg-qcIce"
  >
    <div
      className="tw-flex tw-max-w-[600px] tw-flex-col tw-items-center"
    >
      <img
        alt="Dash error message"
        className="tw-w-150 tw-translate-x-[-30px] tw-translate-y-[9px] tw-rotate-[-2deg] tw-transform tw-self-end"
        src="/Dash7_2024.png"
      />
      <div
        className="!tw-w-fit  tw-rounded-xl tw-border-2 tw-border-qcIris-700 tw-bg-qcIris-50 tw-p-4 "
      >
        <div
          className="tw-text-left tw-font-inter tw-text-[18px] tw-font-semibold tw-leading-[26px] tw-text-qcIris-800"
        >
          <h2>
            Something went wrong.
          </h2>
          <details>
            <summary>
              Error details
            </summary>
            <pre>
              Error: Some Error Details
            </pre>
          </details>
        </div>
      </div>
    </div>
  </div>
</Layout>
`;
