import { create } from "react-test-renderer";
import { MemoryRouter } from "react-router-dom";
import ErrorBoundary from "..";

const CrashComponent = () => {
  throw new Error("Some Error Details");
};

jest.mock("shared/components/Layout", () => "Layout");

describe("ErrorBoundary", () => {
  test("renders when child throws error with custom fallback", () => {
    const component = create(
      <MemoryRouter>
        <ErrorBoundary fallback={<div>Error occurred</div>}>
          <CrashComponent />
        </ErrorBoundary>
      </MemoryRouter>
    );

    expect(component).toMatchSnapshot();
  });
  test("renders when child throws error with default fallback", () => {
    const component = create(
      <MemoryRouter>
        <ErrorBoundary>
          <CrashComponent />
        </ErrorBoundary>
      </MemoryRouter>
    );

    expect(component).toMatchSnapshot();
  });
});
