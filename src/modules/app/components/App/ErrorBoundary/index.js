import React from "react";
import Layout from "shared/components/Layout";
import { ApolloProvider } from "@apollo/client";
import apolloClient from "base/apolloClient";
import { withRouter } from "react-router-dom";
import { basePath } from "base/constants";

const client = apolloClient("/qapps/graphql");

class ErrorBoundary extends React.Component {
  static getDerivedStateFromError(_error) {
    return { hasError: true };
  }

  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  componentDidCatch(_error) {
    this.setState({ error: _error });
  }

  handleBackArrowClick = () => {
    const { history } = this.props;

    history.goBack();
  };

  render() {
    const { fallback, children } = this.props;
    const { hasError, error } = this.state;

    if (hasError) {
      return (
        <ApolloProvider client={client}>
          <Layout onBackArrowClick={this.handleBackArrowClick}>
            {fallback || (
              <div className="tw-flex tw-min-h-[calc(100vh-50px)] tw-items-center tw-justify-center tw-bg-qcIce">
                <div className="tw-flex tw-max-w-[600px] tw-flex-col tw-items-center">
                  <img
                    src={`${basePath}/Dash7_2024.png`}
                    alt="Dash error message"
                    className="tw-w-150 tw-translate-x-[-30px] tw-translate-y-[9px] tw-rotate-[-2deg] tw-transform tw-self-end"
                  />
                  <div className="!tw-w-fit  tw-rounded-xl tw-border-2 tw-border-qcIris-700 tw-bg-qcIris-50 tw-p-4 ">
                    <div className="tw-text-left tw-font-inter tw-text-[18px] tw-font-semibold tw-leading-[26px] tw-text-qcIris-800">
                      <h2>Something went wrong.</h2>
                      <details>
                        <summary>Error details</summary>
                        <pre>{error?.toString()}</pre>
                      </details>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Layout>
        </ApolloProvider>
      );
    }

    return children;
  }
}

export default withRouter(ErrorBoundary);
