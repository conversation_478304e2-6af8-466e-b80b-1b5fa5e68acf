import { connect } from "react-redux";
import { compose, lifecycle } from "recompose";
import { all, and, any, complement, equals, isEmpty, prop } from "ramda";
import { pendo<PERSON>pi<PERSON>ey } from "base/constants";

export const initializePendo = (user, facility = {}) => {
  if (window.pendoEnabled === "1") {
    window.initPendo(pendoApiKey, user, facility);
  }
};

export const userInfoLoaded = (props, prevProps = {}) => {
  // This method will return if the user and facility data is available
  // for the first time
  const { accountSettings, facility, enabledToggles } = props;
  const {
    accountSettings: oldAccountSettings,
    facility: oldFacility,
    enabledToggles: oldEnabledToggles
  } = prevProps;
  const allLoaded = all(prop("isLoaded"), [
    accountSettings,
    facility,
    enabledToggles
  ]);
  const anyWasntLoadedBefore =
    isEmpty(prevProps) ||
    any(complement(prop("isLoaded")), [
      oldAccountSettings,
      oldFacility,
      oldEnabledToggles
    ]);

  return and(allLoaded, anyWasntLoadedBefore);
};

export const shouldInitialize = (props, prevProps = {}, toggleName) =>
  userInfoLoaded(props, prevProps) &&
  any(equals(toggleName), props.enabledToggles.enabledFeatureToggles);

const mapStateToProps = state => ({
  accountSettings: state.app.accountSettings,
  facility: state.app.facility,
  enabledToggles: state.app.enabledFeatureToggles
});

export default compose(
  connect(mapStateToProps),
  lifecycle({
    componentDidMount() {
      if (shouldInitialize(this.props, undefined, "Pendo")) {
        initializePendo(this.props.accountSettings, this.props.facility);
      }
    },
    componentDidUpdate(nextProps) {
      if (shouldInitialize(this.props, nextProps, "Pendo")) {
        initializePendo(this.props.accountSettings, this.props.facility);
      }
    }
  })
);
