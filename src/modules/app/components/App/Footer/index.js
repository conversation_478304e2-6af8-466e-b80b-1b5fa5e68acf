import { FormattedMessage } from "react-intl";
import ContactEmailLink from "./ContactEmailLink";
import { version } from "base/constants";
import enhance from "./enhancers";

export function Footer() {
  return (
    <footer className="standard-footer">
      <div className="version-number">registries {version}</div>
      <div className="help-link">
        <FormattedMessage id="footer.need_help" defaultMessage="need help?" />{" "}
        <FormattedMessage
          id="footer.support_email"
          defaultMessage="mailto:<EMAIL>"
        >
          {href => <ContactEmailLink href={href} />}
        </FormattedMessage>
      </div>
    </footer>
  );
}

export default enhance(Footer);
