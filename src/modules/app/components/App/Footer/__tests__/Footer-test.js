import { decoratedIntl } from "utils/test/decorated";
import { Footer } from "..";
import { shouldInitialize } from "../enhancers";
import { create } from "react-test-renderer";

jest.mock("../ContactEmailLink", () => "ContactEmailLink");

describe("Footer", () => {
  test("renders footer", () => {
    const footer = create(decoratedIntl(Footer, {}));

    expect(footer).toMatchSnapshot();
  });

  describe("enhancers", () => {
    describe("shouldInitialize", () => {
      test("returns true when values loaded on mount (no prevProps) and Toggle enabled", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: ["Pendo"] }
        };

        expect(shouldInitialize(props, undefined, "Pendo")).toBeTruthy();
      });

      test("returns true when values loaded first time and Toggle enabled", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: ["Pendo"] }
        };
        const prevProps = {
          accountSettings: { isLoaded: false },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true }
        };

        expect(shouldInitialize(props, prevProps, "Pendo")).toBeTruthy();
      });

      test("returns false when values loaded on Mount (no prevProps), but Toggle is not enabled", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: [] }
        };

        expect(shouldInitialize(props, undefined, "Pendo")).toBeFalsy();
      });

      test("returns false when values loaded first time, but Toggle is not enabled", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: [] }
        };
        const prevProps = {
          accountSettings: { isLoaded: false },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true }
        };

        expect(shouldInitialize(props, prevProps, "Pendo")).toBeFalsy();
      });

      test("returns false when not all values loaded on Mount (no prevProps)", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: false },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: ["Pendo"] }
        };

        expect(shouldInitialize(props, undefined, "Pendo")).toBeFalsy();
      });

      test("returns false when not all values loaded for first time", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: false },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: ["Pendo"] }
        };
        const prevProps = {
          accountSettings: { isLoaded: false },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true }
        };

        expect(shouldInitialize(props, prevProps, "Pendo")).toBeFalsy();
      });

      test("returns false when all values loaded but they were already loaded", () => {
        const props = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true, enabledFeatureToggles: ["Pendo"] }
        };
        const prevProps = {
          accountSettings: { isLoaded: true },
          facility: { isLoaded: true },
          enabledToggles: { isLoaded: true }
        };

        expect(shouldInitialize(props, prevProps, "Pendo")).toBeFalsy();
      });
    });
  });
});
