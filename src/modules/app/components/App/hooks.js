import { useEffect, useState } from "react";
import axios from "axios";
import { defaultTo, identity, ifElse, path, pipe } from "ramda";
import { useDispatch } from "react-redux";
import {
  fetchAccountSettings,
  fetchHamburger,
  fetchEnabledFeatureToggles,
  fetchFacility
} from "modules/app/actions";
import redirectToLogin from "utils/redirectToLogin";

export const useComponentLogic = () => {
  const dispatch = useDispatch();
  const [tokenLoaded, setTokenLoaded] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const response = await axios({
          method: "get",
          url: "/xsrf-token",
          headers: {
            Accept: "application/json"
          }
        });

        pipe(
          defaultTo({}),
          path(["data", "token"]),
          ifElse(
            identity,
            token => window.sessionStorage.setItem("csrf", token),
            () => redirectToLogin()
          )
        )(response);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.log("error loading csrf token", e);
        redirectToLogin();
      }

      setTokenLoaded(true);
      dispatch(fetchAccountSettings());
      dispatch(fetchHamburger());
      dispatch(fetchEnabledFeatureToggles());
      dispatch(fetchFacility());
    })();
  }, [dispatch]);

  return { tokenLoaded };
};
