import { IntlProvider, addLocaleData } from "react-intl";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Route, Switch } from "react-router-dom";
import { Spinner, Toaster } from "@q-centrix/q-components-react";
import en from "react-intl/locale-data/en";

import { serverURI, basePath } from "base/constants";

import localeData from "base/locales/data";
import FlashMessage from "./FlashMessage";
import Questionnaire from "modules/questionnaire/components/ApolloMain";
import ValidationReport from "modules/validationReport/components/ApolloMain";
import Abstraction from "modules/abstraction/components/Main";
import Maintenance from "modules/maintenance/components/Main";
import DataReport from "modules/dataReport/components/Main";
import ErrorBoundary from "./ErrorBoundary";
import { useComponentLogic } from "./hooks";

import "styles/commons.scss";
import "styles/index.css";
import "@q-centrix/q-components-react/dist/q-components-react.cjs.development.css";

addLocaleData([...en]);

const locale = "en";
const messages = localeData[locale] || localeData.en;

export function App() {
  const { tokenLoaded } = useComponentLogic();

  if (!tokenLoaded) return <Spinner />;

  return (
    <IntlProvider locale={locale} messages={messages}>
      <BrowserRouter basename={basePath}>
        <ErrorBoundary>
          <FlashMessage />
          <Switch>
            <Route
              path="/questionnaire-response/:id/"
              component={Questionnaire}
            />
            <Route
              path="/validation-report/:id/"
              component={ValidationReport}
            />
            <Route path="/abstraction/:id/" component={Abstraction} />
            <Route path="/measures_and_registries" component={Maintenance} />
            <Route path="/data-report" component={DataReport} />
            <Route render={() => (window.location = serverURI)} />
          </Switch>
          <Toaster />
        </ErrorBoundary>
      </BrowserRouter>
    </IntlProvider>
  );
}

export default App;
