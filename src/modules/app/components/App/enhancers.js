import { connect } from "react-redux";
import { withRouter } from "react-router-dom";
import { compose, lifecycle } from "recompose";
import {
  fetchAccountSettings,
  fetchHamburger,
  fetchEnabledFeatureToggles,
  fetchFacility
} from "modules/app/actions";

const mapDispatchToProps = {
  fetchAccountSettings,
  fetchHamburger,
  fetchEnabledFeatureToggles,
  fetchFacility
};

export default compose(
  withRouter,
  connect(null, mapDispatchToProps),
  lifecycle({
    componentDidMount() {
      this.props.fetchAccountSettings();
      this.props.fetchHamburger();
      this.props.fetchEnabledFeatureToggles();
      this.props.fetchFacility();
    }
  })
);
