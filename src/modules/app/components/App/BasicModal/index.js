import React from "react";
import { FormattedMessage } from "react-intl";

export class BasicModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false
    };
  }

  handleToggleModal = () => {
    const { disabled } = this.props;
    const { visible } = this.state;

    if (!disabled) {
      this.setState({ visible: !visible });
    }
  };

  handleSaveAndClose = event => {
    event.preventDefault();
    const { save } = this.props;

    if (save) {
      save();
    }
    this.handleToggleModal();
  };

  render() {
    const { buttonText, children, disabled, title } = this.props;
    const { visible } = this.state;

    return (
      <span>
        <dd>
          <a
            className="modal-button"
            onClick={this.handleToggleModal}
            disabled={disabled}
          >
            {buttonText}
          </a>
        </dd>

        {visible && (
          <div className="modal condensed_form" id="prompt-select">
            <article>
              <form>
                <header>
                  <h2>{title}</h2>
                </header>
                {children}
                <fieldset className="modal_actions">
                  <button
                    type="submit"
                    className="save"
                    onClick={this.handleSaveAndClose}
                  >
                    {" "}
                    <FormattedMessage
                      id="navigation.save_and_close"
                      defaultMessage="Save and close"
                    />
                  </button>
                </fieldset>
              </form>
            </article>
          </div>
        )}
      </span>
    );
  }
}

export default BasicModal;
