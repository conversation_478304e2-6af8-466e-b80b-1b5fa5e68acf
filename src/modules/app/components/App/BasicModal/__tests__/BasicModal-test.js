import { IntlProvider } from "react-intl";

import { act, create } from "react-test-renderer";

import { BasicModal } from "..";

describe("BasicModal", () => {
  function renderComponent({ disabled, save }) {
    // We wrapped the component with IntlProvider as is needed for the FormattedMessages in the
    // component.
    return (
      <IntlProvider locale="en">
        <BasicModal
          buttonText="Open Modal"
          title="My Modal"
          disabled={disabled}
          name="basicModal"
          save={save}
        >
          <span>
            This is the children element that should be inside the modal panel
          </span>
        </BasicModal>
      </IntlProvider>
    );
  }

  test("disabled is properly set on class and input when true", () => {
    const modal = create(renderComponent({ disabled: true }));

    expect(modal).toMatchSnapshot();
  });
  test("disabled is properly set on class and input when false", () => {
    const modal = create(renderComponent({ disabled: false }));

    expect(modal).toMatchSnapshot();
  });

  test("button is not clickable when disabled is true", () => {
    const modal = create(renderComponent({ disabled: true }));
    const instance = modal.root;
    const buttons = instance.findAllByProps({ className: "modal-button" });
    const modals = instance.findAllByProps({
      className: "modal condensed_form"
    });

    expect(buttons).toHaveLength(1);
    expect(modals).toHaveLength(0);

    act(() => {
      buttons[0].props.onClick();
    });

    expect(modal).toMatchSnapshot();
  });
  test("button shows modal when clicked", () => {
    const modal = create(renderComponent({ disabled: false }));
    const instance = modal.root;
    const buttons = instance.findAllByProps({ className: "modal-button" });
    let modals = instance.findAllByProps({
      className: "modal condensed_form"
    });

    expect(buttons).toHaveLength(1);
    expect(modals).toHaveLength(0);

    act(() => {
      buttons[0].props.onClick();
    });

    modals = instance.findAllByProps({
      className: "modal condensed_form"
    });

    expect(modals).toHaveLength(1);
    expect(modal).toMatchSnapshot();
  });

  test("save is called and modal dissapear when save and close clicked", () => {
    const save = jest.fn();
    const modal = create(renderComponent({ disabled: false, save }));
    const instance = modal.root;
    const buttons = instance.findAllByProps({ className: "modal-button" });
    let modals = instance.findAllByProps({
      className: "modal condensed_form"
    });

    expect(buttons).toHaveLength(1);
    expect(modals).toHaveLength(0);

    act(() => {
      buttons[0].props.onClick();
    });

    modals = instance.findAllByProps({
      className: "modal condensed_form"
    });
    expect(modals).toHaveLength(1);

    const [saveBtn] = instance.findAllByProps({ className: "save" });

    act(() => {
      saveBtn.props.onClick({ preventDefault: jest.fn });
    });

    modals = instance.findAllByProps({
      className: "modal condensed_form"
    });

    expect(save).toHaveBeenCalled();
    expect(modals).toHaveLength(0);
  });
});
