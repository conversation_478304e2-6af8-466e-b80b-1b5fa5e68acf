// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BasicModal button is not clickable when disabled is true 1`] = `
<span>
  <dd>
    <a
      className="modal-button"
      disabled={true}
      onClick={[Function]}
    >
      Open Modal
    </a>
  </dd>
</span>
`;

exports[`BasicModal button shows modal when clicked 1`] = `
<span>
  <dd>
    <a
      className="modal-button"
      disabled={false}
      onClick={[Function]}
    >
      Open Modal
    </a>
  </dd>
  <div
    className="modal condensed_form"
    id="prompt-select"
  >
    <article>
      <form>
        <header>
          <h2>
            My Modal
          </h2>
        </header>
        <span>
          This is the children element that should be inside the modal panel
        </span>
        <fieldset
          className="modal_actions"
        >
          <button
            className="save"
            onClick={[Function]}
            type="submit"
          >
             
            <span>
              Save and close
            </span>
          </button>
        </fieldset>
      </form>
    </article>
  </div>
</span>
`;

exports[`BasicModal disabled is properly set on class and input when false 1`] = `
<span>
  <dd>
    <a
      className="modal-button"
      disabled={false}
      onClick={[Function]}
    >
      Open Modal
    </a>
  </dd>
</span>
`;

exports[`BasicModal disabled is properly set on class and input when true 1`] = `
<span>
  <dd>
    <a
      className="modal-button"
      disabled={true}
      onClick={[Function]}
    >
      Open Modal
    </a>
  </dd>
</span>
`;
