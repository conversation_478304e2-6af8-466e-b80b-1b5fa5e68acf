// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DropMenu renders DropMenu with children and open class 1`] = `
<li
  className="with_dropdown open"
>
  <a
    onClick={[MockFunction]}
  >
    <i
      className="fa fa-caret-down"
    />
    <i
      className="fa fa-bars"
    />
  </a>
  <ul
    className="menu-class"
  >
    <li>
      <a
        href="#"
      >
        Item #1
      </a>
    </li>
    <li>
      <a
        href="http://mysite.q-centrix.com"
      >
        Item #2
      </a>
    </li>
  </ul>
</li>
`;

exports[`DropMenu renders DropMenu without children and open class 1`] = `
<li
  className="with_dropdown"
>
  <a
    onClick={[MockFunction]}
  >
    <i
      className="fa fa-caret-down"
    />
    <i
      className="fa fa-bars"
    />
  </a>
</li>
`;
