import shallowRender from "utils/shallowRender";

import { DropMenu } from "..";

describe("DropMenu", () => {
  function render(open, children) {
    return shallowRender(
      <DropMenu open={open} onMenuClick={jest.fn()} icon="fa fa-caret-down">
        {children}
      </DropMenu>
    );
  }
  test("renders DropMenu without children and open class", () => {
    const dropMenu = render(false, null);

    expect(dropMenu).toMatchSnapshot();
  });
  test("renders DropMenu with children and open class", () => {
    const innerMenu = (
      <ul className="menu-class">
        <li>
          <a href="#">Item #1</a>
        </li>
        <li>
          <a href="http://mysite.q-centrix.com">Item #2</a>
        </li>
      </ul>
    );
    const dropMenu = render(true, innerMenu);

    expect(dropMenu).toMatchSnapshot();
  });
});
