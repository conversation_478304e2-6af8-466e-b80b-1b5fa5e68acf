import classnames from "classnames";

export function DropMenu(props) {
  const { open, onMenuClick, label, children, icon } = props;

  return (
    // eslint-disable-next-line camelcase
    <li className={classnames({ with_dropdown: true, open })}>
      <a onClick={onMenuClick}>
        <i className={icon} />
        {label ? label : <i className="fa fa-bars" />}
      </a>
      {children}
    </li>
  );
}

export default DropMenu;
