import { both, identity, prop } from "ramda";
import { serverURI } from "base/constants";
import DropMenu from "../DropMenu";
import enhance from "./enhancers";
import { cleanURL } from "utils/fp";

export function ModuleTab(props) {
  const { hamburger, open, onToggleTab } = props;

  if (!both(identity, prop("isLoaded"))(hamburger)) {
    return null;
  }
  return (
    <DropMenu open={open} onMenuClick={onToggleTab} icon="fa fa-caret-down">
      <ul className="app-modules-switcher">
        {hamburger.apps &&
          hamburger.apps.map(app => (
            <li key={app.name}>
              <a href={cleanURL(`${serverURI}${app.url}`)}>{app.name}</a>
            </li>
          ))}
      </ul>
    </DropMenu>
  );
}

export default enhance(ModuleTab);
