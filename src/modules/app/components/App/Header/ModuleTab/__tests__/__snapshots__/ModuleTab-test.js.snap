// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ModuleTab renders ModuleTab with isLoaded flag as false 1`] = `null`;

exports[`ModuleTab renders ModuleTab with isLoaded flag as true 1`] = `
<DropMenu
  icon="fa fa-caret-down"
  open={true}
>
  <ul
    className="app-modules-switcher"
  >
    <li>
      <a
        href="http://localhost:3000/case_detail"
      >
        Case Detail
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/qapps/cases_upload"
      >
        Case Upload
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/client_dashboard"
      >
        Q-Card
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/concurrent"
      >
        Concurrent review
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000http://localhost:4200/ecqm"
      >
        eCQM
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/infection_control"
      >
        Infection control
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000http://localhost:4200/nsqip"
      >
        NSQIP Reporting
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/qapps"
      >
        Q-Apps
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/readmissions/#/"
      >
        Readmissions
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/admin/patients"
      >
        Registry Abstraction
      </a>
    </li>
  </ul>
</DropMenu>
`;

exports[`ModuleTab renders ModuleTab without data 1`] = `null`;
