import shallowRender from "utils/shallowRender";

import { ModuleTab } from "..";

describe("ModuleTab", () => {
  function render(hamburger) {
    return shallowRender(
      <ModuleTab open toggleTab={i => i} index={0} hamburger={hamburger} />
    );
  }
  test("renders ModuleTab without data", () => {
    const moduleTab = render(null);

    expect(moduleTab).toMatchSnapshot();
  });

  test("renders ModuleTab with isLoaded flag as false", () => {
    const moduleTab = render({ isLoaded: false });

    expect(moduleTab).toMatchSnapshot();
  });

  test("renders ModuleTab with isLoaded flag as true", () => {
    const hamburger = {
      apps: [
        {
          name: "Case Detail",
          url: "/case_detail"
        },
        {
          name: "Case Upload",
          url: "/qapps/cases_upload"
        },
        {
          name: "Q-Card",
          url: "/client_dashboard"
        },
        {
          name: "Concurrent review",
          url: "/concurrent"
        },
        {
          name: "eCQM",
          url: "http://localhost:4200/ecqm"
        },
        {
          name: "Infection control",
          url: "/infection_control"
        },
        {
          name: "NSQIP Reporting",
          url: "http://localhost:4200/nsqip"
        },
        {
          name: "Q-Apps",
          url: "/qapps"
        },
        {
          name: "Readmissions",
          url: "/readmissions/#/"
        },
        {
          name: "Registry Abstraction",
          url: "/admin/patients"
        }
      ],
      isFetching: false,
      isLoaded: true
    };
    const moduleTab = render(hamburger);

    expect(moduleTab).toMatchSnapshot();
  });
});
