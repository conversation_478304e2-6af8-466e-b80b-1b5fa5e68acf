import { compose, withStateHand<PERSON> } from "recompose";
import { evolve, F, not } from "ramda";
import facilitySelector from "modules/app/selectors/facility";
import { connect } from "react-redux";

const mapStateToProps = state => ({
  facility: facilitySelector.facility(state)
});

export default compose(
  connect(mapStateToProps),
  withStateHandlers(
    {
      isUserTabOpen: false,
      isModuleTabOpen: false
    },
    {
      handleUserToggleTab: state => e => {
        e.preventDefault();
        return evolve({ isUserTabOpen: not, isModuleTabOpen: F }, state);
      },
      handleModuleToggleTab: state => e => {
        e.preventDefault();
        return evolve({ isModuleTabOpen: not, isUserTabOpen: F }, state);
      }
    }
  )
);
