import { FormattedMessage } from "react-intl";
import ModuleTab from "./ModuleTab";
import UserTab from "./UserTab";
import enhance from "./enhancers";

export function Header(props) {
  const {
    children,
    facility,
    isUserTabOpen,
    isModuleTabOpen,
    handleModuleToggleTab,
    handleUserToggleTab
  } = props;

  return (
    <header className="top-navigation">
      <nav>
        <div className="account-links">
          <h1>
            <a href="/">
              <FormattedMessage
                id="navbar.qcentrix"
                defaultMessage="Q-Centrix"
              />
            </a>
          </h1>
          {facility.standalone && <h2>{facility.institutionName}</h2>}
          <ul>
            <ModuleTab
              open={isModuleTabOpen}
              onToggleTab={handleModuleToggleTab}
            />
            <UserTab open={isUserTabOpen} onToggleTab={handleUserToggleTab} />
          </ul>
        </div>
        <div className="app-links">
          <ul>{children}</ul>
        </div>
      </nav>
    </header>
  );
}

export default enhance(Header);
