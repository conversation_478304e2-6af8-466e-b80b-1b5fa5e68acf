import shallowRender from "utils/shallowRender";

import { Header } from "..";

describe("Header", () => {
  function render(children, standaloneFlag = false) {
    const facility = {
      id: 1,
      institutionName: "My Institution Name",
      standalone: standaloneFlag
    };

    return shallowRender(
      <Header
        isUserTabOpen
        facility={facility}
        isModuleTabOpen={false}
        handleModuleToggleTab={jest.fn()}
        handleUserToggleTab={jest.fn()}
      >
        {children}
      </Header>
    );
  }
  test("renders Header without children", () => {
    const header = render(null);

    expect(header).toMatchSnapshot();
  });
  test("renders Header with children", () => {
    const innerMenu = (
      <li>
        <a href="/admin/patients">Patients</a>
      </li>
    );
    const header = render(innerMenu);

    expect(header).toMatchSnapshot();
  });

  test("renders h2 with institution name if standalone", () => {
    const header = render(null, true);

    expect(header).toMatchSnapshot();
  });

  test("does not render h2 with institution name if core", () => {
    const header = render(null, false);

    expect(header).toMatchSnapshot();
  });
});
