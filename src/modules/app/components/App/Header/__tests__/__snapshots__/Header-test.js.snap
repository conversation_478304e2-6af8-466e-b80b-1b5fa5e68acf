// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PERSON><PERSON> does not render h2 with institution name if core 1`] = `
<header
  className="top-navigation"
>
  <nav>
    <div
      className="account-links"
    >
      <h1>
        <a
          href="/"
        >
          <FormattedMessage
            defaultMessage="Q-Centrix"
            id="navbar.qcentrix"
            values={Object {}}
          />
        </a>
      </h1>
      <ul>
        <Memo(Connect(ModuleTab))
          onToggleTab={[MockFunction]}
          open={false}
        />
        <Memo(Connect(UserTab))
          onToggleTab={[MockFunction]}
          open={true}
        />
      </ul>
    </div>
    <div
      className="app-links"
    >
      <ul />
    </div>
  </nav>
</header>
`;

exports[`Header renders Header with children 1`] = `
<header
  className="top-navigation"
>
  <nav>
    <div
      className="account-links"
    >
      <h1>
        <a
          href="/"
        >
          <FormattedMessage
            defaultMessage="Q-Centrix"
            id="navbar.qcentrix"
            values={Object {}}
          />
        </a>
      </h1>
      <ul>
        <Memo(Connect(ModuleTab))
          onToggleTab={[MockFunction]}
          open={false}
        />
        <Memo(Connect(UserTab))
          onToggleTab={[MockFunction]}
          open={true}
        />
      </ul>
    </div>
    <div
      className="app-links"
    >
      <ul>
        <li>
          <a
            href="/admin/patients"
          >
            Patients
          </a>
        </li>
      </ul>
    </div>
  </nav>
</header>
`;

exports[`Header renders Header without children 1`] = `
<header
  className="top-navigation"
>
  <nav>
    <div
      className="account-links"
    >
      <h1>
        <a
          href="/"
        >
          <FormattedMessage
            defaultMessage="Q-Centrix"
            id="navbar.qcentrix"
            values={Object {}}
          />
        </a>
      </h1>
      <ul>
        <Memo(Connect(ModuleTab))
          onToggleTab={[MockFunction]}
          open={false}
        />
        <Memo(Connect(UserTab))
          onToggleTab={[MockFunction]}
          open={true}
        />
      </ul>
    </div>
    <div
      className="app-links"
    >
      <ul />
    </div>
  </nav>
</header>
`;

exports[`Header renders h2 with institution name if standalone 1`] = `
<header
  className="top-navigation"
>
  <nav>
    <div
      className="account-links"
    >
      <h1>
        <a
          href="/"
        >
          <FormattedMessage
            defaultMessage="Q-Centrix"
            id="navbar.qcentrix"
            values={Object {}}
          />
        </a>
      </h1>
      <h2>
        My Institution Name
      </h2>
      <ul>
        <Memo(Connect(ModuleTab))
          onToggleTab={[MockFunction]}
          open={false}
        />
        <Memo(Connect(UserTab))
          onToggleTab={[MockFunction]}
          open={true}
        />
      </ul>
    </div>
    <div
      className="app-links"
    >
      <ul />
    </div>
  </nav>
</header>
`;
