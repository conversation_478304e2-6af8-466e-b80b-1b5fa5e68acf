import shallowRender from "utils/shallowRender";

import { UserTab } from "..";

describe("UserTab", () => {
  function render(accountSettings) {
    return shallowRender(
      <UserTab
        open
        onToggleTab={i => i}
        index={0}
        accountSettings={accountSettings}
      />
    );
  }
  test("renders UserTab without data", () => {
    const userTab = render(null);

    expect(userTab).toMatchSnapshot();
  });

  test("renders UserTab with isLoaded flag as false", () => {
    const userTab = render({ isLoaded: false });

    expect(userTab).toMatchSnapshot();
  });

  test("renders UserTab with isLoaded flag as true", () => {
    const accountSettings = {
      email: "<EMAIL>",
      isFetching: false,
      isLoaded: true,
      menuLinks: [
        {
          asterisk: false,
          name: "Manage providers",
          url: "/providers"
        },
        {
          asterisk: false,
          name: "Manage users",
          url: "/users"
        },
        {
          asterisk: false,
          name: "Select facility",
          url: "/facilities"
        },
        {
          name: "Edit Feature Toggles",
          url: "/facilities/1/feature_toggles"
        },
        {
          fullUrl: true,
          name: "Resources",
          rel: "noopener noreferrer",
          target: "_blank",
          url: "https://qcentrix.sharepoint.com/prod_dev/SitePages/Q-Apps.aspx"
        },
        {
          asterisk: false,
          name: "Known Issues",
          url: "/known_issues"
        },
        {
          asterisk: false,
          name: "Release Notes",
          url: "/releases"
        },
        {
          asterisk: false,
          name: "Manage case detail users",
          url: "/system/case_detail/users"
        }
      ]
    };
    const userTab = render(accountSettings);

    expect(userTab).toMatchSnapshot();
  });
});
