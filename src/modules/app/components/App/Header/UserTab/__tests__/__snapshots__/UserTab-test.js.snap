// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UserTab renders UserTab with isLoaded flag as false 1`] = `null`;

exports[`UserTab renders UserTab with isLoaded flag as true 1`] = `
<DropMenu
  icon="fa fa-gear"
  label="<EMAIL>"
  onMenuClick={[Function]}
  open={true}
>
  <ul
    className="account-settings"
  >
    <li>
      <a
        href="http://localhost:3000/providers"
      >
        <LinkAsterisk
          asterisk={false}
        />
        Manage providers
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/users"
      >
        <LinkAsterisk
          asterisk={false}
        />
        Manage users
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/facilities"
      >
        <LinkAsterisk
          asterisk={false}
        />
        Select facility
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/facilities/1/feature_toggles"
      >
        <LinkAsterisk />
        Edit Feature Toggles
      </a>
    </li>
    <li>
      <a
        href="https://qcentrix.sharepoint.com/prod_dev/SitePages/Q-Apps.aspx"
        target="_blank"
      >
        <LinkAsterisk />
        Resources
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/known_issues"
      >
        <LinkAsterisk
          asterisk={false}
        />
        Known Issues
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/releases"
      >
        <LinkAsterisk
          asterisk={false}
        />
        Release Notes
      </a>
    </li>
    <li>
      <a
        href="http://localhost:3000/system/case_detail/users"
      >
        <LinkAsterisk
          asterisk={false}
        />
        Manage case detail users
      </a>
    </li>
    <li>
      <a
        className="logout"
        href="#"
      >
        <FormattedMessage
          defaultMessage="Logout"
          id="user_tab.logout"
          values={Object {}}
        />
      </a>
    </li>
  </ul>
</DropMenu>
`;

exports[`UserTab renders UserTab without data 1`] = `null`;
