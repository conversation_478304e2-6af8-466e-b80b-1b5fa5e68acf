import { FormattedMessage } from "react-intl";
import { both, identity, prop } from "ramda";
import { serverURI } from "base/constants";
import DropMenu from "../DropMenu";
import enhance from "./enhancers";
import { cleanURL } from "utils/fp";

const LinkAsterisk = ({ asterisk }) => {
  if (!asterisk) return null;

  return <i className="fa fa-asterisk" />;
};

export function UserTab(props) {
  const { accountSettings, open, onToggleTab, logOut } = props;

  if (!both(identity, prop("isLoaded"))(accountSettings)) {
    return null;
  }
  return (
    <DropMenu
      label={accountSettings.email}
      open={open}
      onMenuClick={onToggleTab}
      icon="fa fa-gear"
    >
      <ul className="account-settings">
        {accountSettings.menuLinks &&
          accountSettings.menuLinks.map(link => (
            <li key={link.name}>
              <a
                target={link.target}
                href={
                  link.fullUrl ? link.url : cleanURL(`${serverURI}${link.url}`)
                }
              >
                <LinkAsterisk asterisk={link.asterisk} />
                {link.name}
              </a>
            </li>
          ))}
        <li>
          <a href="#" className="logout" onClick={logOut}>
            <FormattedMessage id="user_tab.logout" defaultMessage="Logout" />
          </a>
        </li>
      </ul>
    </DropMenu>
  );
}

export default enhance(UserTab);
