import { App } from "..";
import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedWithDispatch } from "utils/test/decorated";
import { MemoryRouter } from "react-router-dom";

jest.mock("axios", () => ({
  // eslint-disable-next-line no-empty-function
  default: () => {}
}));
jest.mock("@q-centrix/q-components-react", () => ({
  Spinner: "Spinner",
  Toaster: "Toaster"
}));

describe("App", () => {
  test("it renders spinner before token call", () => {
    const { component } = decoratedWithDispatch(App, {}, {});

    const app = create(<MemoryRouter>{component}</MemoryRouter>);

    expect(app).toMatchSnapshot();
  });

  test("it renders component after token call", async () => {
    const { component } = decoratedWithDispatch(App, {}, {});

    const app = create(<MemoryRouter>{component}</MemoryRouter>);

    await act(() => wait(100));

    expect(app).toMatchSnapshot();
  });
});
