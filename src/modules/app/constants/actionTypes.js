import { createActionTypes } from "utils/createActionTypes";

export default createActionTypes("app", [
  "REQUEST_HAMBURGER",
  "REQUEST_ACCOUNT_SETTINGS",
  "REQUEST_ENABLED_FEATURE_TOGGLES",
  "REQUEST_FACILITY",
  "SUCCESS_HAMBURGER",
  "SUCCESS_ACCOUNT_SETTINGS",
  "SUCCESS_ENABLED_FEATURE_TOGGLES",
  "SUCCESS_FACILITY",
  "FAILURE_HAMBURGER",
  "FAILURE_ACCOUNT_SETTINGS",
  "FAILURE_ENABLED_FEATURE_TOGGLES",
  "FAILURE_FACILITY",
  "PUSH_FLASH_MESSAGE",
  "CLEAR_FLASH_MESSAGE",
  "POP_FLASH_MESSAGE",
  "LOGOUT",
  "LOGOUT_SUCCESS",
  "LOGOUT_FAILURE",
  "SET_MUTATION_LOADING",
  "ADD_CHAT_MESSAGE",
  "CYCLE_MESSAGE_CACHE",
  "SET_MESSAGE_CYCLING_CACHE",
  "SET_MSG_MODAL_TEXT",
  "SET_SELECTED_FACILITY",
  "TRACK_EVENT",
  "SET_ACCOUNT_SETTINGS"
]);
