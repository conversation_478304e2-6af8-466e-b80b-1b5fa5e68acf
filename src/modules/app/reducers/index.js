import { combineReducers } from "redux";

import { reducer as questions } from "modules/question";
import { reducer as questionnaire } from "modules/questionnaire";
import { reducer as validationReport } from "modules/validationReport";
import { reducer as maintenance } from "modules/maintenance";
import hamburger from "./hamburger";
import accountSettings from "./accountSettings";
import facility from "./facility";
import flashMessage from "./flashMessage";
import session from "./session";
import status from "./status";
import enabledFeatureToggles from "./enabledFeatureToggles";
import abstractionValidationReport from "../../abstraction/redux/reducers";
import dataReport from "modules/dataReport/redux/slices";

export const reducer = combineReducers({
  accountSettings,
  maintenance,
  flashMessage,
  hamburger,
  facility,
  questions,
  questionnaire,
  validationReport,
  session,
  status,
  enabledFeatureToggles,
  abstractionValidationReport,
  dataReport
});

export default reducer;
