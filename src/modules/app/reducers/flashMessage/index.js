import createReducer from "utils/createReducer";
import actionTypes from "../../constants/actionTypes";
import { always, append, evolve, remove } from "ramda";

const initialState = {
  feed: []
};
const handlers = {
  [actionTypes.PUSH_FLASH_MESSAGE]: pushFlashMessage,
  [actionTypes.CLEAR_FLASH_MESSAGE]: clearFlashMessage,
  [actionTypes.POP_FLASH_MESSAGE]: popFlashMessage
};

function pushFlashMessage(state, { payload }) {
  return evolve({ feed: append(payload) }, state);
}

function clearFlashMessage(state) {
  return evolve({ feed: always([]) }, state);
}

function popFlashMessage(state) {
  return evolve({ feed: remove(0, 1) }, state);
}

export default createReducer(initialState, handlers);
