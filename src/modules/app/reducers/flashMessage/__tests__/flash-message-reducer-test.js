import nullAction from "utils/test/nullAction";

import {
  pushFlashMessage,
  clearFlashMessage,
  popFlashMessage
} from "../../../actions";
import reducer from "..";

describe("flash message reducer", () => {
  const initialState = reducer(undefined, nullAction);

  test("verifies flash message push add message to feed", () => {
    const state = initialState;
    const action = pushFlashMessage("Hello");
    let newState = reducer(state, action);

    expect(newState).toMatchSnapshot();
    newState = reducer(newState, action);
    expect(newState).toMatchSnapshot();
  });

  test("verifies flash message clear clears feed", () => {
    const state = initialState;
    const push = pushFlashMessage("Hello");
    const action = clearFlashMessage();
    let newState = reducer(state, push);

    newState = reducer(newState, push);
    newState = reducer(newState, action);
    expect(newState).toMatchSnapshot();
  });

  test("verifies flash message remove first message from feed", () => {
    const state = initialState;
    const push = pushFlashMessage("Hello");
    const action = popFlashMessage();
    let newState = reducer(state, push);

    newState = reducer(newState, push);
    newState = reducer(newState, action);
    expect(newState).toMatchSnapshot();
  });
});
