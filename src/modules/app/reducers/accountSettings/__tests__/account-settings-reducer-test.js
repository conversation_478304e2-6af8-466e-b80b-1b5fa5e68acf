import nullAction from "utils/test/nullAction";

import {
  requestAccountSettings,
  successAccountSettings,
  failureAccountSettings
} from "../../../actions";
import reducer from "..";

describe("account settings reducer", () => {
  const initialState = reducer(undefined, nullAction);

  test("verifies flags for request accountSetting are correctly set", () => {
    const state = initialState;
    const action = requestAccountSettings();
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(true);
    expect(newState.isLoaded).toBe(false);
  });

  test("verifies flags after success accountSetting are correctly set", () => {
    const json = {
      data: {
        id: "1",
        type: "account_settings",
        attributes: {
          menuLinks: [
            { name: "Manage providers", url: "/providers", asterisk: false },
            { name: "Manage users", url: "/users", asterisk: false },
            { name: "Select facility", url: "/facilities", asterisk: false },
            {
              name: "Edit Feature Toggles",
              url: "/facilities/1/feature_toggles"
            },
            {
              name: "Resources",
              url: "https://qcentrix.sharepoint.com/prod_dev/SitePages/Q-Apps.aspx",
              target: "_blank",
              rel: "noopener noreferrer",
              fullUrl: true
            },
            { name: "Known Issues", url: "/known_issues", asterisk: false },
            { name: "Release Notes", url: "/releases", asterisk: false },
            {
              name: "Manage case detail users",
              url: "/system/case_detail/users",
              asterisk: false
            }
          ],
          email: "<EMAIL>"
        }
      }
    };
    const state = initialState;
    const action = successAccountSettings({}, json);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(true);
    expect(newState).toMatchSnapshot();
  });

  test("verifies flags after failed accountSetting are correctly set", () => {
    const error = {
      message: "Invalid authentication"
    };
    const state = initialState;
    const action = failureAccountSettings({}, error);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(false);
    expect(newState).toMatchSnapshot();
  });
});
