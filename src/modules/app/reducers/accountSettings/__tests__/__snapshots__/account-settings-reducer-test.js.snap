// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`account settings reducer verifies flags after failed accountSetting are correctly set 1`] = `
Object {
  "error": Object {
    "message": "Invalid authentication",
  },
  "isFetching": false,
  "isLoaded": false,
}
`;

exports[`account settings reducer verifies flags after success accountSetting are correctly set 1`] = `
Object {
  "email": "<EMAIL>",
  "error": null,
  "id": "1",
  "isFetching": false,
  "isLoaded": true,
  "menuLinks": Array [
    Object {
      "asterisk": false,
      "name": "Manage providers",
      "url": "/providers",
    },
    Object {
      "asterisk": false,
      "name": "Manage users",
      "url": "/users",
    },
    Object {
      "asterisk": false,
      "name": "Select facility",
      "url": "/facilities",
    },
    Object {
      "name": "Edit Feature Toggles",
      "url": "/facilities/1/feature_toggles",
    },
    Object {
      "fullUrl": true,
      "name": "Resources",
      "rel": "noopener noreferrer",
      "target": "_blank",
      "url": "https://qcentrix.sharepoint.com/prod_dev/SitePages/Q-Apps.aspx",
    },
    Object {
      "asterisk": false,
      "name": "Known Issues",
      "url": "/known_issues",
    },
    Object {
      "asterisk": false,
      "name": "Release Notes",
      "url": "/releases",
    },
    Object {
      "asterisk": false,
      "name": "Manage case detail users",
      "url": "/system/case_detail/users",
    },
  ],
}
`;
