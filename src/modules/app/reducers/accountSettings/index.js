import createReducer from "utils/createReducer";
import actionTypes from "../../constants/actionTypes";
import { always, assoc, evolve } from "ramda";

const initialState = {
  isFetching: false,
  isLoaded: false,
  error: null
};
const handlers = {
  [actionTypes.REQUEST_ACCOUNT_SETTINGS]: requestAccountSettings,
  [actionTypes.SUCCESS_ACCOUNT_SETTINGS]: getAccountSettings,
  [actionTypes.FAILURE_ACCOUNT_SETTINGS]: errorAccountSettings,
  [actionTypes.SET_ACCOUNT_SETTINGS]: setAccountSettings
};

function requestAccountSettings(state) {
  return evolve({ isFetching: always(true), isLoaded: always(false) }, state);
}

function getAccountSettings(state, { payload }) {
  const value = payload.accountSettings;

  value.id = payload.id;
  value.isFetching = false;
  value.isLoaded = true;
  return { ...state, ...value };
}

function errorAccountSettings(state, { payload }) {
  return evolve(
    {
      isFetching: always(false),
      isLoaded: always(false),
      error: always(payload)
    },
    state
  );
}

function setAccountSettings(state, { payload }) {
  return assoc("fullName", payload)(state);
}

export default createReducer(initialState, handlers);
