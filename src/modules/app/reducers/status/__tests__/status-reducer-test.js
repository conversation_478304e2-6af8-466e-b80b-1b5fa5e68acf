import nullAction from "utils/test/nullAction";

import { setMutationLoading } from "../../../actions";
import reducer from "..";

describe("Loading status reducer", () => {
  const initialState = reducer(undefined, nullAction);
  const mutationName = "validation";

  test("verifies flags for action after setMutationLoading are correctly set to true and then false", () => {
    const state = initialState;
    const action = setMutationLoading(mutationName, true);
    let newState = reducer(state, action);

    expect(newState.mutationLoading[mutationName]).toBe(true);

    const action2 = setMutationLoading(mutationName, false);

    // New action on already existing state
    newState = reducer(newState, action2);
    expect(newState.mutationLoading[mutationName]).toBe(false);

    // New action on initial state
    newState = reducer(state, action2);
    expect(newState.mutationLoading[mutationName]).toBe(false);
  });
});
