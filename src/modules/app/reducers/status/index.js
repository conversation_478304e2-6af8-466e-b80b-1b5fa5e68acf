import { lensPath, set } from "ramda";
import createReducer from "utils/createReducer";
import actionTypes from "../../constants/actionTypes";

const initialState = { mutationLoading: {} };
const handlers = {
  [actionTypes.SET_MUTATION_LOADING]: setMutationLoading
};

function setMutationLoading(state, { payload }) {
  return set(
    lensPath(["mutationLoading", payload.action]),
    payload.loading,
    state
  );
}

export default createReducer(initialState, handlers);
