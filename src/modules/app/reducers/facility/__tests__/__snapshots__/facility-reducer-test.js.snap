// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`facilities reducer verifies flags after failed facilities are correctly set 1`] = `
Object {
  "error": Object {
    "message": "Invalid authentication",
  },
  "isFetching": false,
  "isLoaded": false,
}
`;

exports[`facilities reducer verifies flags after success facilities are correctly set 1`] = `
Object {
  "id": "1",
  "isFetching": false,
  "isLoaded": true,
  "name": "My Facility",
}
`;
