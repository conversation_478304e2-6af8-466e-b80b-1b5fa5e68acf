import nullAction from "utils/test/nullAction";

import {
  requestFacility,
  successFacility,
  failureFacility
} from "../../../actions";
import reducer from "..";

describe("facilities reducer", () => {
  const initialState = reducer(undefined, nullAction);

  test("verifies flags for request accountSetting are correctly set", () => {
    const state = initialState;
    const action = requestFacility();
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(true);
    expect(newState.isLoaded).toBe(false);
  });

  test("verifies flags after success facilities are correctly set", () => {
    const json = {
      data: {
        id: "1",
        type: "facility",
        attributes: {
          name: "My Facility"
        }
      }
    };
    const state = initialState;
    const action = successFacility({}, json);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(true);
    expect(newState).toMatchSnapshot();
  });

  test("verifies flags after failed facilities are correctly set", () => {
    const error = {
      message: "Invalid authentication"
    };
    const state = initialState;
    const action = failureFacility({}, error);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(false);
    expect(newState).toMatchSnapshot();
  });
});
