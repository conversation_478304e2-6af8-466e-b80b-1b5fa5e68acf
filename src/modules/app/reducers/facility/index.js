import createReducer from "utils/createReducer";
import actionTypes from "../../constants/actionTypes";
import { always, evolve } from "ramda";

const initialState = {
  isFetching: false,
  isLoaded: false,
  error: null
};
const handlers = {
  [actionTypes.REQUEST_FACILITY]: requestFacility,
  [actionTypes.SUCCESS_FACILITY]: getFacility,
  [actionTypes.FAILURE_FACILITY]: errorFacility,
  [actionTypes.SET_SELECTED_FACILITY]: setFacility
};

function requestFacility(state) {
  return evolve({ isFetching: always(true), isLoaded: always(false) }, state);
}

function getFacility(state, { payload }) {
  const value = payload.facility;

  value.id = payload.id;
  value.isFetching = false;
  value.isLoaded = true;

  return value;
}

function setFacility(state, { payload }) {
  const value = payload.selectedFacility;

  value.isFetching = false;
  value.isLoaded = true;

  return value;
}

function errorFacility(state, { payload }) {
  return evolve(
    {
      isFetching: always(false),
      isLoaded: always(false),
      error: always(payload)
    },
    state
  );
}

export default createReducer(initialState, handlers);
