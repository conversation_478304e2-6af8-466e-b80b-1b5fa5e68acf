// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`enabled feature toggles reducer verifies flags after failed enabledFeatureToggles are correctly set 1`] = `
Object {
  "enabledFeatureToggles": Array [],
  "error": Object {
    "message": "Invalid authentication",
  },
  "isFetching": false,
  "isLoaded": false,
}
`;

exports[`enabled feature toggles reducer verifies flags after success enabldeFeatureToggles are correctly set 1`] = `
Object {
  "enabledFeatureToggles": Array [
    "Toggle 1",
    "Toggle 2",
  ],
  "error": null,
  "isFetching": false,
  "isLoaded": true,
}
`;
