import nullAction from "utils/test/nullAction";

import {
  requestEnabledFeatureToggles,
  successEnabledFeatureToggles,
  failureEnabledFeatureToggles
} from "../../../actions";
import reducer from "..";

describe("enabled feature toggles reducer", () => {
  const initialState = reducer(undefined, nullAction);

  test("verifies flags for request accountSetting are correctly set", () => {
    const state = initialState;
    const action = requestEnabledFeatureToggles();
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(true);
    expect(newState.isLoaded).toBe(false);
  });

  test("verifies flags after success enabldeFeatureToggles are correctly set", () => {
    const json = {
      data: {
        id: "1",
        type: "enabled_feature_toggles",
        attributes: {
          enabledFeatureToggles: ["Toggle 1", "Toggle 2"]
        }
      }
    };
    const state = initialState;
    const action = successEnabledFeatureToggles({}, json);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(true);
    expect(newState).toMatchSnapshot();
  });

  test("verifies flags after failed enabledFeatureToggles are correctly set", () => {
    const error = {
      message: "Invalid authentication"
    };
    const state = initialState;
    const action = failureEnabledFeatureToggles({}, error);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(false);
    expect(newState).toMatchSnapshot();
  });
});
