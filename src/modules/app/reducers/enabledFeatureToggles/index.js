import createReducer from "utils/createReducer";
import actionTypes from "../../constants/actionTypes";
import { always, evolve } from "ramda";

const initialState = {
  isFetching: false,
  isLoaded: false,
  error: null,
  enabledFeatureToggles: []
};

const handlers = {
  [actionTypes.REQUEST_ENABLED_FEATURE_TOGGLES]: requestEnabledFeatureToggles,
  [actionTypes.SUCCESS_ENABLED_FEATURE_TOGGLES]: getEnabledFeatureToggles,
  [actionTypes.FAILURE_ENABLED_FEATURE_TOGGLES]: errorEnabledFeatureToggles
};

function requestEnabledFeatureToggles(state) {
  return evolve({ isFetching: always(true), isLoaded: always(false) }, state);
}

function getEnabledFeatureToggles(state, { payload }) {
  const value = payload.enabledFeatureToggles;
  const newState = evolve(
    {
      isFetching: always(false),
      isLoaded: always(true),
      enabledFeatureToggles: always(value)
    },
    state
  );

  return newState;
}

function errorEnabledFeatureToggles(state, { payload }) {
  return evolve(
    {
      isFetching: always(false),
      isLoaded: always(false),
      error: always(payload)
    },
    state
  );
}

export default createReducer(initialState, handlers);
