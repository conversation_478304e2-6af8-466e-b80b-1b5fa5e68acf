import createReducer from "utils/createReducer";
import actionTypes from "../../constants/actionTypes";
import { always, evolve } from "ramda";

const initialState = {
  isFetching: false,
  isLoaded: false,
  error: null
};
const handlers = {
  [actionTypes.REQUEST_HAMBURGER]: requestHamburger,
  [actionTypes.SUCCESS_HAMBURGER]: getHamburger,
  [actionTypes.FAILURE_HAMBURGER]: errorHamburger
};

function requestHamburger(state) {
  return evolve({ isFetching: always(true), isLoaded: always(false) }, state);
}

function getHamburger(state, { payload }) {
  const value = payload.hamburger;

  value.isFetching = false;
  value.isLoaded = true;
  return value;
}

function errorHamburger(state, { payload }) {
  return evolve(
    {
      isFetching: always(false),
      isLoaded: always(false),
      error: always(payload)
    },
    state
  );
}

export default createReducer(initialState, handlers);
