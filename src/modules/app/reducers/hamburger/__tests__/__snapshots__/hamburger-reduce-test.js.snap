// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`account settings reducer verifies flags after failed hamburger are correctly set 1`] = `
Object {
  "error": Object {
    "message": "Invalid authentication",
  },
  "isFetching": false,
  "isLoaded": false,
}
`;

exports[`account settings reducer verifies flags after success hamburger are correctly set 1`] = `
Object {
  "apps": Array [
    Object {
      "name": "Case Detail",
      "url": "/case_detail",
    },
    Object {
      "name": "Case Upload",
      "url": "/qapps/cases_upload",
    },
    Object {
      "name": "Q-Card",
      "url": "/client_dashboard",
    },
    Object {
      "name": "Concurrent review",
      "url": "/concurrent",
    },
    Object {
      "name": "eCQM",
      "url": "http://localhost:4200/ecqm",
    },
    Object {
      "name": "Infection control",
      "url": "/infection_control",
    },
    Object {
      "name": "NSQIP Reporting",
      "url": "http://localhost:4200/nsqip",
    },
    Object {
      "name": "Q-Apps",
      "url": "/qapps",
    },
    Object {
      "name": "Readmissions",
      "url": "/readmissions/#/",
    },
    Object {
      "name": "Registry Abstraction",
      "url": "/admin/patients",
    },
  ],
  "isFetching": false,
  "isLoaded": true,
}
`;
