import nullAction from "utils/test/nullAction";

import {
  requestH<PERSON>burger,
  successHamburger,
  failureHamburger
} from "../../../actions";
import reducer from "..";

describe("account settings reducer", () => {
  const initialState = reducer(undefined, nullAction);

  test("verifies flags for request hamburger are correctly set", () => {
    const state = initialState;
    const action = requestHamburger();
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(true);
    expect(newState.isLoaded).toBe(false);
  });

  test("verifies flags after success hamburger are correctly set", () => {
    const json = {
      data: {
        id: "1",
        type: "hamburger",
        attributes: {
          apps: [
            { name: "Case Detail", url: "/case_detail" },
            { name: "Case Upload", url: "/qapps/cases_upload" },
            { name: "Q-Card", url: "/client_dashboard" },
            { name: "Concurrent review", url: "/concurrent" },
            { name: "eCQM", url: "http://localhost:4200/ecqm" },
            { name: "Infection control", url: "/infection_control" },
            { name: "NSQIP Reporting", url: "http://localhost:4200/nsqip" },
            { name: "Q-Apps", url: "/qapps" },
            { name: "Readmissions", url: "/readmissions/#/" },
            { name: "Registry Abstraction", url: "/admin/patients" }
          ]
        }
      }
    };
    const state = initialState;
    const action = successHamburger({}, json);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(true);
    expect(newState).toMatchSnapshot();
  });

  test("verifies flags after failed hamburger are correctly set", () => {
    const error = {
      message: "Invalid authentication"
    };
    const state = initialState;
    const action = failureHamburger({}, error);
    const newState = reducer(state, action);

    expect(newState.isFetching).toBe(false);
    expect(newState.isLoaded).toBe(false);
    expect(newState).toMatchSnapshot();
  });
});
