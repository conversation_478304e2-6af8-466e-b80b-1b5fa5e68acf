import localizeSelectors from "utils/localizeSelectors";
import { path } from "ramda";

const localState = path(["app", "enabledFeatureToggles"]);

export function getEnabledFeatureToggles(state) {
  return state.enabledFeatureToggles;
}

/**
 * Method in all selectors that scopes the state for given fuction to the path in this case
 * app.questionnaire
 */
export default localizeSelectors(localState, {
  getEnabledFeatureToggles
});
