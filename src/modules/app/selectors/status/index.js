import localizeSelectors from "utils/localizeSelectors";
import { path } from "ramda";

const localState = path(["app", "status"]);

export function isMutationLoading(valueName, state) {
  return path(["mutationLoading", valueName], state);
}

/**
 * Method in all selectors that scopes the state for given fuction to the path in this case
 * app.status
 */
export default localizeSelectors(localState, {
  isMutationLoading
});
