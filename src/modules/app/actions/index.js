import ActionType from "../constants/actionTypes";
import { fetchData } from "utils/serverRequest";

export function requestHamburger() {
  return {
    type: ActionType.REQUEST_HAMBURGER,
    payload: {}
  };
}

export function requestAccountSettings() {
  return {
    type: ActionType.REQUEST_ACCOUNT_SETTINGS,
    payload: {}
  };
}

export function requestEnabledFeatureToggles() {
  return {
    type: ActionType.REQUEST_ENABLED_FEATURE_TOGGLES,
    payload: {}
  };
}

export function requestFacility() {
  return {
    type: ActionType.REQUEST_FACILITY,
    payload: {}
  };
}

export function successHamburger(requestData, json) {
  return {
    type: ActionType.SUCCESS_HAMBURGER,
    payload: { hamburger: json.data.attributes }
  };
}

export function successAccountSettings(requestData, json) {
  return {
    type: ActionType.SUCCESS_ACCOUNT_SETTINGS,
    payload: { accountSettings: json.data.attributes, id: json.data.id }
  };
}

export function successEnabledFeatureToggles(requestData, json) {
  return {
    type: ActionType.SUCCESS_ENABLED_FEATURE_TOGGLES,
    payload: {
      enabledFeatureToggles: json.data.attributes.enabledFeatureToggles
    }
  };
}

export function successFacility(requestData, json) {
  return {
    type: ActionType.SUCCESS_FACILITY,
    payload: { facility: json.data.attributes, id: json.data.id }
  };
}

export function failureHamburger(requestData, json) {
  return {
    type: ActionType.FAILURE_HAMBURGER,
    payload: json
  };
}

export function failureAccountSettings(requestData, json) {
  return {
    type: ActionType.FAILURE_ACCOUNT_SETTINGS,
    payload: json
  };
}

export function failureEnabledFeatureToggles(requestData, json) {
  return {
    type: ActionType.FAILURE_ENABLED_FEATURE_TOGGLES,
    payload: json
  };
}

export function failureFacility(requestData, json) {
  return {
    type: ActionType.FAILURE_FACILITY,
    payload: json
  };
}

export function fetchHamburger() {
  return fetchData({
    uri: "/api/user/v2/hamburger",
    requestFunction: requestHamburger,
    successFunction: successHamburger,
    failureFunction: failureHamburger
  });
}

export function fetchAccountSettings() {
  return fetchData({
    uri: "/api/user/v2/account_settings",
    requestFunction: requestAccountSettings,
    successFunction: successAccountSettings,
    failureFunction: failureAccountSettings
  });
}

export function fetchFacility() {
  return fetchData({
    uri: "/api/user/v2/facility",
    requestFunction: requestFacility,
    successFunction: successFacility,
    failureFunction: failureFacility
  });
}

export const setSelectedFacility =
  refetch => (requestData, selectedFacility) => {
    refetch();
    return {
      type: ActionType.SET_SELECTED_FACILITY,
      payload: { selectedFacility }
    };
  };

export const setAccountSettings = payload => ({
  type: ActionType.SET_ACCOUNT_SETTINGS,
  payload
});

export function changeCurrentFacility(facilityId, refetch) {
  return fetchData({
    method: "PUT",
    uri: `/facilities/${facilityId}/set`,
    // eslint-disable-next-line camelcase
    requestData: { no_redirect: "true" },
    requestFunction: requestFacility,
    successFunction: setSelectedFacility(refetch),
    failureFunction: failureFacility
  });
}

export function fetchEnabledFeatureToggles() {
  return fetchData({
    uri: "/api/user/v2/feature_toggles",
    requestFunction: requestEnabledFeatureToggles,
    successFunction: successEnabledFeatureToggles,
    failureFunction: failureEnabledFeatureToggles
  });
}

export function pushFlashMessage(message) {
  return {
    type: ActionType.PUSH_FLASH_MESSAGE,
    payload: { message }
  };
}

export function clearFlashMessage() {
  return {
    type: ActionType.CLEAR_FLASH_MESSAGE,
    payload: {}
  };
}

export function popFlashMessage() {
  return {
    type: ActionType.POP_FLASH_MESSAGE,
    payload: {}
  };
}

export function logOut() {
  return fetchData({
    method: "DELETE",
    dataType: "text",
    uri: "/api/user/v1/session",
    requestFunction: logoutRequested,
    successFunction: successLogOut,
    failureFunction: failureLogOut
  });
}

export function logoutRequested() {
  return {
    type: ActionType.LOGOUT,
    payload: {}
  };
}

export function successLogOut() {
  return {
    type: ActionType.LOGOUT_SUCCESS,
    payload: {}
  };
}

export function failureLogOut() {
  return {
    type: ActionType.LOGOUT_FAILURE,
    payload: {}
  };
}

export function setMutationLoading(action, loading) {
  return {
    type: ActionType.SET_MUTATION_LOADING,
    payload: { action, loading }
  };
}

export function addChatMessage(message) {
  return {
    type: ActionType.ADD_CHAT_MESSAGE,
    payload: message
  };
}

export function cycleMessageCache(cmd) {
  return {
    type: ActionType.CYCLE_MESSAGE_CACHE,
    payload: cmd
  };
}

export function setMessageCyclingCache(payload) {
  return {
    type: ActionType.SET_MESSAGE_CYCLING_CACHE,
    payload
  };
}

export function setMessageModal(message) {
  return {
    type: ActionType.SET_MSG_MODAL_TEXT,
    payload: message
  };
}
