import { gql } from "@apollo/client";

export const DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES = gql`
  query dependentExternalQuestionEnumerables(
    $questionnaireResponseId: Int!
    $dependentAnswerData: [DependentAnswer]
  ) {
    dependentExternalQuestionEnumerables(
      questionnaireResponseId: $questionnaireResponseId
      dependentAnswerData: $dependentAnswerData
    )
  }
`;

export const DEPENDENT_FILTERED_QUESTION_ENUMERABLES = gql`
  query dependentFilteredQuestionEnumerables(
    $questionnaireResponseId: Int!
    $dependentAnswerData: [DependentAnswer]
    $questionId: Int!
  ) {
    dependentFilteredQuestionEnumerables(
      questionnaireResponseId: $questionnaireResponseId
      dependentAnswerData: $dependentAnswerData
      questionId: $questionId
    )
  }
`;

export default {
  DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES,
  DEPENDENT_FILTERED_QUESTION_ENUMERABLES
};
