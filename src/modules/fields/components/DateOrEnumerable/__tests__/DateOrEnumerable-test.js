import TestRenderer, { act, create } from "react-test-renderer";
import {
  decorated<PERSON><PERSON><PERSON>,
  decoratedApolloWithDispatch
} from "utils/test/decorated";
import moment from "moment";
import DateOrEnumerable from "..";
import DatePicker from "react-datepicker";
import { setFieldValue } from "modules/question/actions";

describe("DateOrEnumerable", () => {
  const appState = {
    questions: {
      values: {
        "2|1": { value: moment("12/12/2012") },
        "3|1": { selected: "1" }
      }
    },
    status: {},
    questionnaire: {}
  };
  const activeEnumerables = [
    { id: "1", description: "Yes" },
    { id: "2", description: "No" },
    { id: "3", description: "NA" }
  ];
  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: DateOrEnumerable,
        props: {
          name,
          disabled,
          question: { id: "1", activeEnumerables, type: "DateOrEnumerable" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on inputs when true", () => {
    const open = render({ disabled: true });

    expect(open).toMatchSnapshot();
  });

  test("disabled is properly set on inputs when false", () => {
    const open = render({ disabled: false });

    expect(open).toMatchSnapshot();
  });

  test("text value is set from prestored values", () => {
    const open = render({
      disabled: false,
      name: "2|1"
    });

    expect(open).toMatchSnapshot();
  });

  test("radio value is set from prestored values", () => {
    const open = render({
      disabled: false,
      name: "3|1"
    });

    expect(open).toMatchSnapshot();
  });

  test("DatePicker onChange setting value", () => {
    const { component, dispatch } = decoratedApolloWithDispatch({
      component: DateOrEnumerable,
      props: {
        disabled: false,
        name: "2|1",
        question: { id: "1", activeEnumerables, type: "DateOrEnumerable" },
        questionAnswerGroup: { id: "1" }
      },
      initialAppValues: appState,
      apolloMocks: []
    });

    const mountedComponent = create(component);
    const instance = mountedComponent.root;

    const input = instance.findByType(DatePicker);
    const newDate = moment("5/12/2024");

    act(() => input.props.onChange(newDate));

    expect(dispatch).toHaveBeenCalledWith(
      setFieldValue("2|1", { date: newDate, selected: "" })
    );
  });
});
