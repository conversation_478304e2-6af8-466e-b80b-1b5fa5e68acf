// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateOrEnumerable disabled is properly set on inputs when false 1`] = `
Array [
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="input-icon"
      >
        <input
          autoComplete="off"
          className="input-field"
          disabled={false}
          name="1|1"
          onBlur={[Function]}
          onChange={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mmddyyyy, mm/dd/yyyy"
          type="text"
          value=""
        />
        <i
          className="fa fa-calendar icon"
          onClick={[Function]}
        />
      </div>
    </div>
  </div>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`DateOrEnumerable disabled is properly set on inputs when true 1`] = `
Array [
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="input-icon"
      >
        <input
          autoComplete="off"
          className="input-field"
          disabled={true}
          name="1|1"
          onBlur={[Function]}
          onChange={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mmddyyyy, mm/dd/yyyy"
          type="text"
          value=""
        />
        <i
          className="fa fa-calendar icon"
          onClick={[Function]}
        />
      </div>
    </div>
  </div>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`DateOrEnumerable radio value is set from prestored values 1`] = `
Array [
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="input-icon"
      >
        <input
          autoComplete="off"
          className="input-field"
          disabled={false}
          name="3|1"
          onBlur={[Function]}
          onChange={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mmddyyyy, mm/dd/yyyy"
          type="text"
          value=""
        />
        <i
          className="fa fa-calendar icon"
          onClick={[Function]}
        />
      </div>
    </div>
  </div>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={true}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`DateOrEnumerable text value is set from prestored values 1`] = `
Array [
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="input-icon"
      >
        <input
          autoComplete="off"
          className="input-field"
          disabled={false}
          name="2|1"
          onBlur={[Function]}
          onChange={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mmddyyyy, mm/dd/yyyy"
          type="text"
          value=""
        />
        <i
          className="fa fa-calendar icon"
          onClick={[Function]}
        />
      </div>
    </div>
  </div>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio"
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;
