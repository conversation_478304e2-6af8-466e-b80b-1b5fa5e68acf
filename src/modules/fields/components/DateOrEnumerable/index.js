import autoCompleteVal from "utils/getAutoCompleteValue";
import DatePicker from "react-datepicker";
import dateFormats from "modules/question/constants/dateFormats";
import "react-datepicker/dist/react-datepicker.css";
import "../../styles/date.scss";
import { useComponentLogic } from "./hooks";

const DateInternalInput = props => {
  const {
    value: inputValue,
    name: inputName,
    disabled: inputDisabled,
    onChange,
    onFocus,
    onBlur,
    setTextInputRef,
    onKeyDown,
    placeholder
  } = props;

  return (
    <div className="input-icon">
      <input
        type="text"
        value={inputValue || ""}
        name={inputName}
        autoComplete={autoCompleteVal}
        disabled={inputDisabled}
        className="input-field"
        ref={setTextInputRef}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        placeholder={placeholder}
      />
      <i className="fa fa-calendar icon" onClick={onFocus} />
    </div>
  );
};

export const DateOrEnumerable = props => {
  const { disabled, name, question } = props;
  const { activeEnumerables } = question;
  const {
    value,
    focusRef,
    onFieldFocus,
    handleBlur,
    handleChange,
    handleSelect,
    pickerRef,
    handleEnumerableChange
  } = useComponentLogic({
    ...props,
    answerType: "date",
    enumerableAnswerType: "enumerable"
  });

  return (
    <>
      <DatePicker
        customInput={<DateInternalInput setTextInputRef={focusRef} />}
        disabled={disabled}
        name={name}
        selected={value?.date || null}
        placeholderText="mmddyyyy, mm/dd/yyyy"
        dateFormat={dateFormats}
        popperPlacement="right"
        onBlur={handleBlur}
        onFocus={onFieldFocus}
        onChange={handleChange}
        onSelect={handleSelect}
        ref={pickerRef}
        popperModifiers={[
          {
            name: "offset",
            options: {
              offset: [0, 10]
            }
          }
        ]}
      />

      {activeEnumerables &&
        activeEnumerables.map(enumerable => (
          <label className="radio-button enumerable-radio" key={enumerable.id}>
            <input
              disabled={disabled}
              name={name}
              type="radio"
              value={enumerable.id}
              checked={value?.selected === enumerable.id}
              onChange={handleEnumerableChange}
              onFocus={onFieldFocus}
            />
            <span>{enumerable.description}</span>
          </label>
        ))}
    </>
  );
};

export default DateOrEnumerable;
