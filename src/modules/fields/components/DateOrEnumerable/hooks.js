import { useCallback, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  prop,
  ifElse,
  always,
  propEq,
  assoc,
  __,
  pipe,
  either,
  complement
} from "ramda";
import { isNullOrEmpty } from "utils/fp";
import Questionnaire from "modules/questionnaire/selectors";
import Question from "modules/question/selectors";
import { setFieldValue } from "modules/question/actions";
import { useTab } from "../hooks/useTab";
import {
  cleanAndFocus,
  formatTextInput,
  validateInput
} from "modules/question/components/Question/shared/DateInput/enhancers";
import {
  getInitialDate,
  saveDate
} from "modules/question/components/Question/shared/DateInput/DateTransformations";
import { useSaveSingleAnswer } from "../hooks/useSaveAnswer";
import { debouncedAnswerQuestion } from "../hooks/useValue";

export const useInitCompoundValue = ({ name, fieldName }) => {
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const dispatch = useDispatch();
  const setValue = useCallback(
    value => dispatch(setFieldValue(name, value)),
    [dispatch, name]
  );
  const value = useSelector(state => Question.getFieldValue(name, state));

  useEffect(() => {
    if (value === undefined) {
      const formattedValueForInput = ifElse(
        either(isNullOrEmpty, complement(prop("answerData"))),
        always({}),
        ifElse(
          propEq("answerType", "enumerable"),
          pipe(prop(fieldName), assoc("selected", __, {})),
          pipe(
            prop(fieldName),
            answerData => getInitialDate(answerData),
            assoc("date", __, {})
          )
        )
      )(answer);

      setValue(formattedValueForInput);
    }
  }, [value, answer, fieldName, setValue]);

  return { answer, value, setValue };
};

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { name, enumerableAnswerType } = props;
  const pickerRef = useRef(null);

  const { focusRef, onFieldFocus, focus } = useTab(props);
  const { answer, value, setValue } = useInitCompoundValue({
    name,
    fieldName: "answerData"
  });

  const { answerQuestion } = useSaveSingleAnswer({ ...props, answer });

  const handleSave = useCallback(
    date => {
      saveDate(answerQuestion, date);
    },
    [answerQuestion]
  );

  const handleChangeDateInput = useCallback(
    date => {
      setValue({ date, selected: "" });
    },
    [value, setValue]
  );

  const handleBlur = useCallback(() => {
    if (
      validateInput({
        setValue,
        value: value.date,
        textInput: focusRef.current
      })
    ) {
      handleSave(value.date);
      formatTextInput({ value: value.date, datepicker: pickerRef.current });
    } else {
      cleanAndFocus({ setValue, textInput: focusRef.current, value: {} });
    }
  }, [focusRef, pickerRef, handleSave, setValue, value]);

  const handleEnumerableChange = useCallback(
    event => {
      setValue({ date: null, selected: event.target.value });
      pickerRef.current.setState({ inputValue: "" });
      debouncedAnswerQuestion({
        saveFunction: answerQuestion,
        event,
        value: event.target.value,
        type: enumerableAnswerType
      });
    },
    [answerQuestion, enumerableAnswerType, setValue, pickerRef]
  );

  const handleSelect = useCallback(() => {
    setTimeout(() => focus(), 1);
  }, [focus]);

  return {
    value,
    focusRef,
    pickerRef,
    onFieldFocus,
    handleChange: handleChangeDateInput,
    handleBlur,
    handleSelect,
    handleEnumerableChange
  };
};
