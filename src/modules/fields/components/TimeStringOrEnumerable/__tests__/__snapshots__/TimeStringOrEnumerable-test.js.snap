// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TimeStringOrEnumerable disabled is properly set on inputs when false 1`] = `
Array [
  <div
    className="time-string-input"
  >
    <input
      className="time-string"
      disabled={false}
      name="1|1"
      onBlur={[Function]}
      onChange={[Function]}
      onFocus={[Function]}
      placeholder="hh:mm hhmm"
      type="text"
      value=""
    />
    <i
      className="fa-solid fa-clock"
      onFocus={[Function]}
    />
  </div>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`TimeStringOrEnumerable disabled is properly set on inputs when true 1`] = `
Array [
  <div
    className="time-string-input"
  >
    <input
      className="time-string"
      disabled={true}
      name="1|1"
      onBlur={[Function]}
      onChange={[Function]}
      onFocus={[Function]}
      placeholder="hh:mm hhmm"
      type="text"
      value=""
    />
    <i
      className="fa-solid fa-clock"
      onFocus={[Function]}
    />
  </div>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`TimeStringOrEnumerable input value is set from prestored values 1`] = `
Array [
  <div
    className="time-string-input"
  >
    <input
      className="time-string"
      disabled={false}
      name="2|1"
      onBlur={[Function]}
      onChange={[Function]}
      onFocus={[Function]}
      placeholder="hh:mm hhmm"
      type="text"
      value="02:15"
    />
    <i
      className="fa-solid fa-clock"
      onFocus={[Function]}
    />
  </div>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`TimeStringOrEnumerable radio value is set from prestored values 1`] = `
Array [
  <div
    className="time-string-input"
  >
    <input
      className="time-string"
      disabled={false}
      name="3|1"
      onBlur={[Function]}
      onChange={[Function]}
      onFocus={[Function]}
      placeholder="hh:mm hhmm"
      type="text"
      value=""
    />
    <i
      className="fa-solid fa-clock"
      onFocus={[Function]}
    />
  </div>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={true}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;
