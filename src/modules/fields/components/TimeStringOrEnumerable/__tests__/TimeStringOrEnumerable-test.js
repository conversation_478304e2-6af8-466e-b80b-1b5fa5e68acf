import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import TimeStringOrEnumerable from "..";

describe("TimeStringOrEnumerable", () => {
  const appState = {
    questions: {
      values: { "2|1": { value: "02:15" }, "3|1": { selected: "1" } }
    },
    status: {},
    questionnaire: {}
  };
  const activeEnumerables = [
    { id: "1", description: "Yes" },
    { id: "2", description: "No" },
    { id: "3", description: "NA" }
  ];

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: TimeStringOrEnumerable,
        props: {
          name,
          disabled,
          question: {
            id: "1",
            activeEnumerables,
            type: "TimeStringOrEnumerable"
          },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on inputs when true", () => {
    const field = render({ disabled: true });

    expect(field).toMatchSnapshot();
  });

  test("disabled is properly set on inputs when false", () => {
    const field = render({ disabled: false });

    expect(field).toMatchSnapshot();
  });

  test("input value is set from prestored values", () => {
    const field = render({
      disabled: false,
      name: "2|1"
    });

    expect(field).toMatchSnapshot();
  });

  test("radio value is set from prestored values", () => {
    const field = render({
      disabled: false,
      name: "3|1"
    });

    expect(field).toMatchSnapshot();
  });
});
