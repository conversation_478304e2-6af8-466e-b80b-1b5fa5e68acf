import { useTab } from "../hooks/useTab";
import { useCompoundValue } from "../hooks/useValue";
import "../../styles/time-string.scss";

export const TimeStringOrEnumerable = props => {
  const { disabled, name, question } = props;
  const { activeEnumerables } = question;
  const { focusRef, onFieldFocus } = useTab(props);
  const { selected, handleEnumerableChange, value, handleChange, handleSave } =
    useCompoundValue({
      ...props,
      answerType: "time-string",
      enumerableAnswerType: "enumerable"
    });

  return (
    <>
      <div className="time-string-input">
        <input
          className="time-string"
          type="text"
          disabled={disabled}
          name={name}
          placeholder="hh:mm hhmm"
          value={value || ""}
          onBlur={handleSave}
          onFocus={onFieldFocus}
          onChange={handleChange}
          ref={focusRef}
        />
        <i className="fa-solid fa-clock" onFocus={onFieldFocus} />
      </div>
      {activeEnumerables &&
        activeEnumerables.map(enumerable => (
          <label className="radio-button enumerable-radio " key={enumerable.id}>
            <input
              disabled={disabled}
              name={name}
              type="radio"
              value={enumerable.id}
              checked={selected === enumerable.id}
              onChange={handleEnumerableChange}
              onFocus={onFieldFocus}
            />
            <span>{enumerable.description}</span>
          </label>
        ))}
    </>
  );
};

export default TimeStringOrEnumerable;
