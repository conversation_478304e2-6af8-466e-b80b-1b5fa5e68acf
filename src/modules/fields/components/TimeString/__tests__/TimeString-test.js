import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import TimeString from "..";

describe("TimeString", () => {
  const appState = {
    questions: { values: { "2|1": "02:15" } },
    status: {},
    questionnaire: {}
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: TimeString,
        props: {
          name,
          disabled,
          question: { id: "1" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on input when true", () => {
    const timeString = render({ disabled: true });

    expect(timeString).toMatchSnapshot();
  });

  test("disabled is not set on input when false", () => {
    const timeString = render({ disabled: false });

    expect(timeString).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const timeString = render({
      disabled: false,
      name: "2|1"
    });

    expect(timeString).toMatchSnapshot();
  });
});
