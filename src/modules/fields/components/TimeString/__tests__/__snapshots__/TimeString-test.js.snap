// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TimeString disabled is not set on input when false 1`] = `
<div
  className="time-string-input"
>
  <input
    className="time-string"
    disabled={false}
    name="1|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    placeholder="hh:mm hhmm"
    type="text"
    value=""
  />
  <i
    className="fa-solid fa-clock"
    onFocus={[Function]}
  />
</div>
`;

exports[`TimeString disabled is properly set on input when true 1`] = `
<div
  className="time-string-input"
>
  <input
    className="time-string"
    disabled={true}
    name="1|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    placeholder="hh:mm hhmm"
    type="text"
    value=""
  />
  <i
    className="fa-solid fa-clock"
    onFocus={[Function]}
  />
</div>
`;

exports[`TimeString value is set from prestored values 1`] = `
<div
  className="time-string-input"
>
  <input
    className="time-string"
    disabled={false}
    name="2|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    placeholder="hh:mm hhmm"
    type="text"
    value="02:15"
  />
  <i
    className="fa-solid fa-clock"
    onFocus={[Function]}
  />
</div>
`;
