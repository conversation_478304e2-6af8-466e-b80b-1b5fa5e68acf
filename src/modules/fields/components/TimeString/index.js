import { useSingleValue } from "../hooks/useValue";
import { useTab } from "../hooks/useTab";
import "../../styles/time-string.scss";

export const TimeString = props => {
  const { name, disabled } = props;
  const { focusRef, onFieldFocus } = useTab(props);
  const { value, handleSave, handleChange } = useSingleValue({
    ...props,
    answerType: "time-string"
  });

  return (
    <div className="time-string-input">
      <input
        className="time-string"
        type="text"
        disabled={disabled}
        name={name}
        placeholder="hh:mm hhmm"
        value={value || ""}
        onBlur={handleSave}
        onFocus={onFieldFocus}
        onChange={handleChange}
        ref={focusRef}
      />
      <i className="fa-solid fa-clock" onFocus={onFieldFocus} />
    </div>
  );
};

export default TimeString;
