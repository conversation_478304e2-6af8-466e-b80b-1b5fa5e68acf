// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumerableSearchable disabled is not set input when false 1`] = `
<Searchable
  answerType="enumerable-searchable"
  className="question-enumerable-searchable"
  disabled={false}
  isFavoritable={true}
  name="1|1"
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
/>
`;

exports[`EnumerableSearchable disabled is properly set on input when true 1`] = `
<Searchable
  answerType="enumerable-searchable"
  className="question-enumerable-searchable"
  disabled={true}
  isFavoritable={true}
  name="1|1"
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
/>
`;
