import TestRenderer from "react-test-renderer";
import EnumerableSearchable from "..";

jest.mock("modules/fields/components/shared/Searchable", () => "Searchable");

describe("EnumerableSearchable", () => {
  const question = { id: "1", columns: ["name", "code"] };
  const questionAnswerGroup = { id: "1" };

  const getComponent = ({ disabled, name = "1|1" }) => (
    <EnumerableSearchable
      {...{
        disabled,
        question,
        name,
        questionAnswerGroup,
        isFavoritable: true
      }}
    />
  );

  test("disabled is properly set on input when true", () => {
    const enumSearch = TestRenderer.create(getComponent({ disabled: true }));

    expect(enumSearch).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const enumSearch = TestRenderer.create(getComponent({ disabled: false }));

    expect(enumSearch).toMatchSnapshot();
  });
});
