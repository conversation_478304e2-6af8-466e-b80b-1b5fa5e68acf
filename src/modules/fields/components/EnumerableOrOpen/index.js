// import { HotKeys } from "react-hotkeys";
import autoCompleteVal from "utils/getAutoCompleteValue";
import { getFieldSuffix } from "modules/questionnaire/services/specialFields";
import { useTab } from "../hooks/useTab";
import { useCompoundValue } from "../hooks/useValue";

export const EnumerableOrOpen = props => {
  const { disabled, name, question } = props;
  const { activeEnumerables, type } = question;
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);
  const { value, selected, handleSave, handleChange, handleEnumerableChange } =
    useCompoundValue({
      ...props,
      answerType: "open",
      enumerableAnswerType: "enumerable"
    });

  return (
    <>
      <input
        className="question-open"
        disabled={disabled}
        name={`${name}${getFieldSuffix(type)}`}
        autoComplete={autoCompleteVal}
        value={value || ""}
        type="text"
        onBlur={handleSave}
        onFocus={onFieldFocus}
        onChange={handleChange}
        ref={focusRef}
      />
      {activeEnumerables &&
        activeEnumerables.map(enumerable => (
          <label className="radio-button enumerable-radio " key={enumerable.id}>
            <input
              disabled={disabled}
              name={name}
              type="radio"
              value={enumerable.id}
              checked={selected === enumerable.id}
              onChange={handleEnumerableChange}
              onFocus={onFieldFocus}
            />{" "}
            <span>{enumerable.description}</span>
          </label>
        ))}
    </>
  );
};

export default EnumerableOrOpen;
