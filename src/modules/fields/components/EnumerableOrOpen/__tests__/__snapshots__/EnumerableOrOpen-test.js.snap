// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumerableOrOpen disabled is properly set on inputs when false 1`] = `
Array [
  <input
    autoComplete="off"
    className="question-open"
    disabled={false}
    name="1|1-other"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value=""
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
     
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
     
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
     
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`EnumerableOrOpen disabled is properly set on inputs when true 1`] = `
Array [
  <input
    autoComplete="off"
    className="question-open"
    disabled={true}
    name="1|1-other"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value=""
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
     
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
     
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
     
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`EnumerableOrOpen radio value is set from prestored values 1`] = `
Array [
  <input
    autoComplete="off"
    className="question-open"
    disabled={false}
    name="3|1-other"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value=""
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={true}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
     
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
     
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
     
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`EnumerableOrOpen text value is set from prestored values 1`] = `
Array [
  <input
    autoComplete="off"
    className="question-open"
    disabled={false}
    name="2|1-other"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value="test value"
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
     
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
     
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
     
    <span>
      NA
    </span>
  </label>,
]
`;
