import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import EnumerableOrOpen from "..";

describe("EnumerableOrOpen", () => {
  const appState = {
    questions: {
      values: { "2|1": { value: "test value" }, "3|1": { selected: "1" } }
    },
    status: {},
    questionnaire: {}
  };
  const activeEnumerables = [
    { id: "1", description: "Yes" },
    { id: "2", description: "No" },
    { id: "3", description: "NA" }
  ];

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: EnumerableOrOpen,
        props: {
          name,
          disabled,
          question: { id: "1", activeEnumerables, type: "EnumerableOrOpen" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on inputs when true", () => {
    const open = render({ disabled: true });

    expect(open).toMatchSnapshot();
  });

  test("disabled is properly set on inputs when false", () => {
    const open = render({ disabled: false });

    expect(open).toMatchSnapshot();
  });

  test("text value is set from prestored values", () => {
    const open = render({
      disabled: false,
      name: "2|1"
    });

    expect(open).toMatchSnapshot();
  });

  test("radio value is set from prestored values", () => {
    const open = render({
      disabled: false,
      name: "3|1"
    });

    expect(open).toMatchSnapshot();
  });
});
