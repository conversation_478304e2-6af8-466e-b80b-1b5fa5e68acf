import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Number from "..";

describe("Number", () => {
  const appState = {
    questions: { values: { "2|1": "123" } },
    status: {},
    questionnaire: {}
  };

  const render = ({ questionId = "1", answerGroupId = "1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: Number,
        props: {
          name: `${questionId}|${answerGroupId}`,
          disabled,
          question: { id: questionId },
          answerGroup: { id: answerGroupId }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on input when true", () => {
    const number = render({ disabled: true });

    expect(number).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const number = render({ disabled: false });

    expect(number).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const number = render({
      disabled: false,
      questionId: "2"
    });

    expect(number).toMatchSnapshot();
  });
});
