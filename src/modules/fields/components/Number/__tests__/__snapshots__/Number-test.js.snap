// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Number disabled is not set input when false 1`] = `
<input
  className="question-number"
  disabled={false}
  inputMode="numeric"
  name="1|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  onKeyDown={[Function]}
  onMouseUp={[Function]}
  type="text"
  value=""
/>
`;

exports[`Number disabled is properly set on input when true 1`] = `
<input
  className="question-number"
  disabled={true}
  name="1|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  onKeyDown={[Function]}
  onMouseUp={[Function]}
  type="text"
  value=""
/>
`;

exports[`Number value is set from prestored values 1`] = `
<input
  className="question-number"
  disabled={false}
  name="2|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  onKeyDown={[Function]}
  onMouseUp={[Function]}
  type="text"
  value="123"
/>
`;
