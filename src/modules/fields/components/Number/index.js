// import { HotKeys } from "react-hotkeys";
import { NumericFormat } from "react-number-format";
import { useTab } from "../hooks/useTab";
import { useSingleValue } from "../hooks/useValue";

export const Number = props => {
  const { disabled, name } = props;
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);
  const { value, handleSave, handleChange } = useSingleValue({
    ...props,
    answerType: "number"
  });

  return (
    <NumericFormat
      className="question-number"
      disabled={disabled}
      name={name}
      value={value || ""}
      onBlur={handleSave}
      onFocus={onFieldFocus}
      onChange={handleChange}
      valueIsNumericString
      ref={focusRef}
    />
  );
};

export default Number;
