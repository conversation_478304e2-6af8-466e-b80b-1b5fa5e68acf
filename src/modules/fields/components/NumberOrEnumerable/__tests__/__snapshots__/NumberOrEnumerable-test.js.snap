// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NumberOrEnumerable disabled is properly set on inputs when false 1`] = `
Array [
  <input
    className="question-number"
    disabled={false}
    inputMode="numeric"
    name="1|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    onMouseUp={[Function]}
    type="text"
    value=""
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`NumberOrEnumerable disabled is properly set on inputs when true 1`] = `
Array [
  <input
    className="question-number"
    disabled={true}
    name="1|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    onMouseUp={[Function]}
    type="text"
    value=""
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={true}
      name="1|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`NumberOrEnumerable input value is set from prestored values 1`] = `
Array [
  <input
    className="question-number"
    disabled={false}
    name="2|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    onMouseUp={[Function]}
    type="text"
    value="12345"
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="2|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;

exports[`NumberOrEnumerable radio value is set from prestored values 1`] = `
Array [
  <input
    className="question-number"
    disabled={false}
    inputMode="numeric"
    name="3|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    onMouseUp={[Function]}
    type="text"
    value=""
  />,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={true}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="1"
    />
    <span>
      Yes
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="2"
    />
    <span>
      No
    </span>
  </label>,
  <label
    className="radio-button enumerable-radio "
  >
    <input
      checked={false}
      disabled={false}
      name="3|1"
      onChange={[Function]}
      onFocus={[Function]}
      type="radio"
      value="3"
    />
    <span>
      NA
    </span>
  </label>,
]
`;
