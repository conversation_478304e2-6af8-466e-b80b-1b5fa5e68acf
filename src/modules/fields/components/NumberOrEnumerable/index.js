import { NumericFormat } from "react-number-format";
import { useTab } from "../hooks/useTab";
import { useCompoundValue } from "../hooks/useValue";

export const NumberOrEnumerable = props => {
  const { disabled, name, question } = props;
  const { activeEnumerables } = question;
  const { focusRef, onFieldFocus } = useTab(props);
  const { selected, handleEnumerableChange, value, handleChange, handleSave } =
    useCompoundValue({
      ...props,
      answerType: "number",
      enumerableAnswerType: "enumerable"
    });

  return (
    <>
      <NumericFormat
        className="question-number"
        disabled={disabled}
        name={name}
        value={value || ""}
        onBlur={handleSave}
        onFocus={onFieldFocus}
        onChange={handleChange}
        valueIsNumericString
        getInputRef={focusRef}
      />
      {activeEnumerables &&
        activeEnumerables.map(enumerable => (
          <label className="radio-button enumerable-radio " key={enumerable.id}>
            <input
              disabled={disabled}
              name={name}
              type="radio"
              value={enumerable.id}
              checked={selected === enumerable.id}
              onChange={handleEnumerableChange}
              onFocus={onFieldFocus}
            />
            <span>{enumerable.description}</span>
          </label>
        ))}
    </>
  );
};

export default NumberOrEnumerable;
