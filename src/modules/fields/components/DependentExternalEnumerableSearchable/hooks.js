import {
  useDynamicSearchableOptions,
  useSearchableEvents,
  useSearchableState
} from "../hooks/useSearchableOptions";

export const useComponentLogic = (props, filteredType = false) => {
  const { savesValue = true } = props;
  const context = useSearchableState({
    ...props,
    answerType: filteredType
      ? "dependent-filtered-enumerable-searchable"
      : "dependent-external-enumerable-searchable"
  });
  const { options, clearOptions, columns } = useDynamicSearchableOptions({
    ...props,
    ...context,
    filteredType
  });
  const {
    controlRef,
    handleBlur,
    handleChange,
    handlePickValue,
    handleValueClick
  } = useSearchableEvents({
    ...context,
    options,
    clearOptions,
    savesValue
  });

  return {
    ...context,
    options,
    columns,
    controlRef,
    handleInputBlur: savesValue ? handleBlur : undefined,
    handlePickValue,
    handleChange,
    handleValueClick
  };
};
