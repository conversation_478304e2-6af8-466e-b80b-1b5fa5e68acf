// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DependentExternalEnumerableSearchable disabled is not set input when false 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
      "partenId": "1",
    }
  }
  questionnaireResponseId={1}
  savesValue={true}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentExternalEnumerableSearchable disabled is properly set on input when true 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={true}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
      "partenId": "1",
    }
  }
  questionnaireResponseId={1}
  savesValue={true}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentExternalEnumerableSearchable doesn't load options from backend on render 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
      "partenId": "1",
    }
  }
  questionnaireResponseId={1}
  savesValue={true}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentExternalEnumerableSearchable loads correct options and columns (question with no columns) from backend 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "key",
      "id",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={true}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={
    Array [
      Object {
        "description": "test|001",
        "enumerableDescription": "test|001",
        "id": "001",
        "key": "test",
        "questionId": "1",
      },
    ]
  }
  question={
    Object {
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
      "partenId": "1",
    }
  }
  questionnaireResponseId={1}
  savesValue={true}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentExternalEnumerableSearchable loads correct options from backend 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={true}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={
    Array [
      Object {
        "code": "001",
        "description": "test|001",
        "enumerableDescription": "test|001",
        "name": "test",
        "questionId": "1",
      },
    ]
  }
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
      "partenId": "1",
    }
  }
  questionnaireResponseId={1}
  savesValue={true}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;
