import { act, create } from "react-test-renderer";
import wait from "waait";
import { DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES } from "modules/fields/graphql/query";
import { <PERSON><PERSON><PERSON><PERSON> } from "utils/test/decorated";
import DependentExternalEnumerableSearchable from "..";
import SearchableControl from "../../shared/Searchable/SearchableControl";

jest.mock(
  "../../shared/Searchable/SearchableControl",
  () => "SearchableControl"
);

describe("DependentExternalEnumerableSearchable", () => {
  const mocks = [
    {
      request: {
        query: DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES,
        variables: {
          questionnaireResponseId: 1,
          dependentAnswerData: [{ questionId: "3", answerData: "12" }]
        }
      },
      result: {
        data: {
          dependentExternalQuestionEnumerables:
            '{"1": {"columns": ["key", "id"], "values": ["test|001"]}, "2":["test2|002"]}'
        }
      }
    }
  ];
  const question1 = {
    id: "1",
    columns: ["name", "code"],
    dependentQuestionIds: ["3"]
  };
  const questionNoColumns = {
    id: "1",
    dependentQuestionIds: ["3"]
  };
  const questionAnswerGroup1 = { id: "2", partenId: "1" };
  const questionAnswerGroup2 = { id: "3" };
  const sectionAnswerGroup = { id: "1", descendants: [{ id: "2" }] };

  const appState = {
    questions: {
      values: { "1|2": "123", "3|1": "12", "3|3": "13" },
      focusedFieldName: "1|1"
    },
    status: {},
    questionnaire: {
      visit: { id: "1" },
      answerGroups: [
        sectionAnswerGroup,
        questionAnswerGroup1,
        questionAnswerGroup2
      ],
      answers: {
        "1|2": {
          id: "1",
          questionId: "1",
          answerGroupId: "2",
          answerData: "123"
        },
        "3|1": {
          id: "2",
          questionId: "3",
          answerGroupId: "1",
          answerData: "12"
        },
        "3|3": {
          id: "3",
          questionId: "3",
          answerGroupId: "3",
          answerData: "13"
        }
      }
    }
  };

  const getComponent = ({ disabled, name = "1|1", question = question1 }) =>
    create(
      decoratedApollo({
        component: DependentExternalEnumerableSearchable,
        props: {
          disabled,
          name,
          question,
          questionAnswerGroup: questionAnswerGroup1,
          isFavoritable: true,
          questionnaireResponseId: 1,
          sectionAnswerGroup
        },
        initialAppValues: appState,
        apolloMocks: mocks
      })
    );

  test("disabled is properly set on input when true", () => {
    let enumSearch = null;

    act(() => {
      enumSearch = getComponent({ disabled: true });
    });
    expect(enumSearch).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    let enumSearch = null;

    act(() => {
      enumSearch = getComponent({ disabled: false });
    });
    expect(enumSearch).toMatchSnapshot();
  });

  test("doesn't load options from backend on render", async () => {
    const enumSearch = getComponent({ disabled: false });

    await act(() => wait(100));
    expect(enumSearch).toMatchSnapshot();
  });

  test("loads correct options from backend", async () => {
    const enumSearch = getComponent({ disabled: false });
    const instance = enumSearch.root;
    const [searchable] = instance.findAllByType(SearchableControl);

    act(() => {
      searchable.props.handleValueClick();
    });

    await act(() => wait(100));

    expect(enumSearch).toMatchSnapshot();
  });

  test("loads correct options and columns (question with no columns) from backend", async () => {
    const enumSearch = getComponent({
      disabled: false,
      question: questionNoColumns
    });
    const instance = enumSearch.root;
    const [searchable] = instance.findAllByType(SearchableControl);

    act(() => {
      searchable.props.handleValueClick();
    });

    await act(() => wait(100));
    expect(enumSearch).toMatchSnapshot();
  });
});
