import SearchableControl from "../shared/Searchable/SearchableControl";
import { useComponentLogic } from "./hooks";

export const DependentExternalEnumerableSearchable = props => {
  const additionalProps = useComponentLogic(props);

  return (
    <SearchableControl
      {...props}
      {...additionalProps}
      savesValue
      className="dependent-question-searchable"
    />
  );
};

export default DependentExternalEnumerableSearchable;
