import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import { F, ifElse, isNil, propEq } from "ramda";

export const useComponentLogic = props => {
  const { name } = props;
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const isCalculated = ifElse(
    isNil,
    F,
    propEq("answerType", "calculated")
  )(answer);

  return { isCalculated };
};
