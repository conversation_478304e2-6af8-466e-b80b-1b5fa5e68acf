import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Calculated from "..";

describe("Calculated", () => {
  const appState = {
    questions: { values: { "2|1": "test value", "3|1": "calculated value" } },
    status: {},
    questionnaire: {
      answers: {
        "2|1": { answerData: "test value", answerType: "open" },
        "3|1": { answerData: "calculated value", answerType: "calculated" }
      }
    }
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: Calculated,
        props: {
          name,
          disabled
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on input when true", () => {
    const calc = render({ disabled: true });

    expect(calc).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const calc = render({ disabled: false });

    expect(calc).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const calc = render({
      disabled: false,
      name: "2|1"
    });

    expect(calc).toMatchSnapshot();
  });

  test("renders clalculated answer / read only with value", () => {
    const calc = render({
      disabled: false,
      name: "3|1"
    });

    expect(calc).toMatchSnapshot();
  });
});
