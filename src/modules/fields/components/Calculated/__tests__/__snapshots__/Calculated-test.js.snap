// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Calculated disabled is not set input when false 1`] = `
<input
  autoComplete="off"
  className="question-calculated"
  disabled={false}
  name="1|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  type="text"
  value=""
/>
`;

exports[`Calculated disabled is properly set on input when true 1`] = `
<input
  autoComplete="off"
  className="question-calculated"
  disabled={true}
  name="1|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  type="text"
  value=""
/>
`;

exports[`Calculated renders clalculated answer / read only with value 1`] = `
<div
  className="calculated-value"
>
  calculated value
</div>
`;

exports[`Calculated value is set from prestored values 1`] = `
<input
  autoComplete="off"
  className="question-calculated"
  disabled={false}
  name="2|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  type="text"
  value="test value"
/>
`;
