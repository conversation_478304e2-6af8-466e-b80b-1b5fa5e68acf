import autoCompleteVal from "utils/getAutoCompleteValue";
// import { HotKeys } from "react-hotkeys";
import { useTab } from "../hooks/useTab";
import { useSingleValue } from "../hooks/useValue";
import { useComponentLogic } from "./hooks";
import "../../styles/calculated.scss";

export const Calculated = props => {
  const { disabled, name } = props;
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);
  const { value, handleSave, handleChange } = useSingleValue({
    ...props,
    answerType: "open"
  });
  const { isCalculated } = useComponentLogic(props);

  if (isCalculated) return <div className="calculated-value">{value}</div>;

  return (
    <input
      className="question-calculated"
      autoComplete={autoCompleteVal}
      disabled={disabled}
      name={name}
      value={value || ""}
      type="text"
      onBlur={handleSave}
      onFocus={onFieldFocus}
      onChange={handleChange}
      ref={focusRef}
    />
  );
};

export default Calculated;
