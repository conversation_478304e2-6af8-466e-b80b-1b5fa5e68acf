import { useMutation } from "@apollo/client";
import { setUpdateUserLastModifiedTimer } from "modules/abstraction/redux/actions";
import Engine from "modules/engine";
import Question from "modules/question/components/Question/mutations";
import Abstraction from "modules/abstraction/redux/selectors";
import Questionnaire from "modules/questionnaire/selectors";
import {
  assocPath,
  complement,
  defaultTo,
  includes,
  path,
  pipe,
  propOr,
  when
} from "ramda";
import { useDispatch, useSelector } from "react-redux";

export const usePartialUpdateMutation = () => {
  const [updateAnswerGroups] = useMutation(Question.updateAnswerGroupsMutation);

  return { updateAnswerGroups };
};

export const useFullUpdateMutation = () => {
  const [refreshData] = useMutation(Question.refreshDataMutation);

  return { refreshData };
};

export const useCalculateValueMutation = () => {
  const [valueCalculations] = useMutation(Question.valueCalculationsMutation);

  return { valueCalculations };
};

export const useNonEntryPointSaveMutations = props => ({
  ...usePartialUpdateMutation(props),
  ...useFullUpdateMutation(props),
  ...useCalculateValueMutation(props)
});

const useUpdateUserLastModified = ({
  questionnaireResponseId,
  query,
  objectName
}) => {
  const [updateUserLastModifiedMutate] = useMutation(
    Question.updateUserLastModified
  );
  const updateUserLastModifiedTimer = useSelector(state =>
    Abstraction.getUpdateUserLastModifiedTimer(state)
  );
  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );
  const isOncology = pipe(
    defaultTo({}),
    propOr("", "name"),
    includes("Oncology")
  )(questionnaire);
  const dispatch = useDispatch();

  const handleUpdateUserLastModified = () => {
    if (isOncology) {
      clearTimeout(updateUserLastModifiedTimer);

      const newUpdateUserLastModifiedTimer = setTimeout(() => {
        updateUserLastModifiedMutate({
          variables: {
            questionnaireResponseId: Number(questionnaireResponseId)
          },
          update: (store, { data: serverData }) => {
            when(
              complement(path(["updateUserLastModified", "errors"])),
              pipe(
                () =>
                  store.readQuery({
                    query,
                    variables: { id: Number(questionnaireResponseId) }
                  }),
                assocPath(
                  [objectName, "userLastModifiedAt"],
                  path(
                    ["updateUserLastModified", "userLastModifiedAt"],
                    serverData
                  )
                ),
                newData => {
                  store.writeQuery({
                    query,
                    variables: { id: Number(questionnaireResponseId) },
                    data: newData
                  });
                }
              )
            )(serverData);
          }
        });
        dispatch(setUpdateUserLastModifiedTimer(null));
      }, 5000);

      dispatch(setUpdateUserLastModifiedTimer(newUpdateUserLastModifiedTimer));
    }
  };

  return { handleUpdateUserLastModified };
};

export const useUpdateSingleAnswer = props => {
  const { handleUpdateUserLastModified } = useUpdateUserLastModified(props);
  const [answerMutate] = useMutation(Question.answerMutation);
  const mutationHandler = context => {
    Engine.handleSaveAnswerMutation({
      mutate: answerMutate,
      ownProps: props,
      context: { ...context, handleUpdateUserLastModified }
    });
  };

  return { mutationHandler };
};

export const useUpdateArrayAnswer = props => {
  const { handleUpdateUserLastModified } = useUpdateUserLastModified(props);
  const [answerMutate] = useMutation(Question.answerArrayMutation);
  const mutationHandler = context => {
    Engine.handleSaveAnswerMutation({
      mutate: answerMutate,
      ownProps: props,
      context: { ...context, handleUpdateUserLastModified }
    });
  };

  return { mutationHandler };
};
