import { useCallback, useLayoutEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setFocusedField, setFocusToField } from "modules/question/actions";
import {
  openHelpInfo,
  toggleAnswerSource
} from "modules/questionnaire/actions";
import Questionnaire from "modules/questionnaire/selectors";
import Questions from "modules/question/selectors";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import { both, cond, equals, identity, or, prop } from "ramda";
import { shouldFocus } from "modules/question/components/Question/Focusable";

const { helpPanel, prepopPanel } = panelTypes;

export const useOnFieldFocus = props => {
  const { name, question, helpInfo } = props;
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const activePanel = useSelector(state =>
    Questionnaire.getCurrentActivePanel(state)
  );
  const dispatch = useDispatch();
  const onFieldFocus = useCallback(() => {
    dispatch(setFocusedField(name));
    cond([
      [
        equals(helpPanel),
        () => {
          if (or(helpInfo, question.hasHelpInformation))
            dispatch(openHelpInfo({ helpInfo, question }));
        }
      ],
      [
        equals(prepopPanel),
        () => {
          if (answer && answer.source) {
            dispatch(toggleAnswerSource(answer));
          } else {
            dispatch(toggleAnswerSource(null));
          }
        }
      ]
    ])(activePanel);
  }, [activePanel, name, dispatch, helpInfo, question, answer]);

  return onFieldFocus;
};

export const useIsFocused = name => {
  const focusedField = useSelector(state => Questions.getFocusedField(state));
  const isFocused = focusedField === name;

  return isFocused;
};

export const useFocus = (props = {}) => {
  const { focusCallback } = props;
  const focusRef = useRef();
  const focus = useCallback(() => {
    if (both(identity, prop("current"))(focusRef)) {
      if (focusCallback) focusCallback(focusRef);
      focusRef.current.focus();
    }
  }, [focusRef, focusCallback]);

  return { focusRef, focus };
};

export const useManageQuestionnaireFocus = props => {
  const { name, question } = props;
  const fieldToBefocused = useSelector(state =>
    Questions.getFieldToBeFocused(state)
  );
  const dispatch = useDispatch();
  const setFocus = useCallback(
    field => {
      dispatch(setFocusToField(field));
    },
    [dispatch]
  );
  const { focusRef, focus } = useFocus(props);
  const onFieldFocus = useOnFieldFocus(props);

  useLayoutEffect(() => {
    if (shouldFocus({ fieldToBefocused, name, question })) {
      focus();
      setFocus(null);
    }
  }, [fieldToBefocused, focus, name, question, setFocus]);

  return { focusRef, onFieldFocus, focus };
};
