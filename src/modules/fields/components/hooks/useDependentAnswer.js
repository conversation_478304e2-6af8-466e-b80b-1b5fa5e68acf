import {
  always,
  curry,
  filter,
  map,
  pipe,
  prop,
  propEq,
  propOr,
  values,
  when
} from "ramda";
import { useSelector } from "react-redux";
import { isAnswerGroupOnSameAncestor } from "modules/question/components/Question/DependentEnumerable/enhancers";
import Questionnaire from "modules/questionnaire/selectors";

export const findDependentAnswer = curry(
  ({ answers, sectionAnswerGroup, filteredType }, dependentOnQuestionId) => {
    const filterAnswersByQuestion = filter(
      propEq("questionId", dependentOnQuestionId.toString())
    );
    const filterAnswersBySameAncestor = filter(
      pipe(
        prop("answerGroupId"),
        isAnswerGroupOnSameAncestor(sectionAnswerGroup)
      )
    );

    const createArgument = value => ({
      questionId: dependentOnQuestionId,
      answerData: value
    });

    return pipe(
      values,
      filterAnswersByQuestion,
      when(always(!filteredType), filterAnswersBySameAncestor),
      propOr({}, 0), // Get first answer or default to {}
      propOr(null, "answerData"),
      createArgument
    )(answers);
  }
);

export const useDependentAnswers = props => {
  const { sectionAnswerGroup, question, filteredType } = props;
  const answers = useSelector(state => Questionnaire.getAnswers(state));
  const currentSectionAnswerGroup = useSelector(state =>
    Questionnaire.getAnswerGroupById(sectionAnswerGroup.id, state)
  );
  const dependentAnswers = map(
    findDependentAnswer({
      answers,
      sectionAnswerGroup: currentSectionAnswerGroup,
      filteredType
    }),
    question.dependentQuestionIds
  );

  return { dependentAnswers };
};
