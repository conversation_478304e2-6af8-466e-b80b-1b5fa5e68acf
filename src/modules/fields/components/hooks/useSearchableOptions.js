import { useState, useCallback, useEffect, useRef } from "react";
import {
  always,
  both,
  complement,
  equals,
  identity,
  ifElse,
  not,
  pipe,
  trim
} from "ramda";
import { formatEnumerables } from "modules/question/components/Question/EnumerableSearchable/SearchModal/SearchResult/enhancers";
import {
  useActiveEnumerables,
  useDynamicActiveEnumerables
} from "./useQuestionEnumerables";
import { useTab } from "./useTab";
import { useSingleValue } from "./useValue";
import useOnClickOutside from "utils/hooks/useOnClickOutside";
import { useIsFocused } from "./useFocus";
import { isNullOrEmpty } from "utils/fp";
import { useDependentAnswers } from "./useDependentAnswer";

const useFormatEnumerables = ({ activeEnumerables, setOptions, columns }) => {
  useEffect(() => {
    pipe(formatEnumerables(columns), formattedEnums =>
      setOptions(o =>
        ifElse(equals(formattedEnums), identity, always(formattedEnums))(o)
      )
    )(activeEnumerables);
  }, [activeEnumerables, setOptions, columns]);
};

export const useSearchableOptions = ({ isEditMode, props, value }) => {
  const { question } = props;
  const [options, setOptions] = useState(question.activeEnumerables);
  const { activeEnumerables } = useActiveEnumerables({
    ...props,
    isEditMode,
    searchTerm: value
  });
  const clearOptions = useCallback(() => {
    setOptions([]);
  }, [setOptions]);
  const { columns } = question;

  useFormatEnumerables({ activeEnumerables, setOptions, columns });
  return { options, clearOptions, columns };
};

export const useDynamicSearchableOptions = props => {
  const { question, isEditMode, filteredType } = props;
  const [options, setOptions] = useState(
    filteredType ? question.activeEnumerables : []
  );
  const { dependentAnswers: args } = useDependentAnswers(props);
  const { activeEnumerables, columns: headerColumns } =
    useDynamicActiveEnumerables({
      ...props,
      skip: !isEditMode,
      args
    });
  const columns = isNullOrEmpty(question.columns)
    ? headerColumns
    : question.columns;
  const clearOptions = useCallback(() => {
    setOptions([]);
  }, [setOptions]);

  useFormatEnumerables({ activeEnumerables, setOptions, columns });
  return { options, clearOptions, columns };
};

export const useSearchableState = props => {
  const { name } = props;
  const valueRef = useRef(null);
  const focusCallback = useCallback(
    ref => {
      if (valueRef && valueRef.current) {
        valueRef.current.focus();
        setTimeout(() => {
          ref.current.focus();
        }, 100);
      }
    },
    [valueRef]
  );
  const { focusRef, onFieldFocus, focus } = useTab({ ...props, focusCallback });
  const { value, setValue, handleSave } = useSingleValue(props);
  const [inputValue, setInputValue] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);

  return {
    name,
    focusRef,
    valueRef,
    inputValue,
    value,
    isEditMode,
    setIsEditMode,
    setInputValue,
    onFieldFocus,
    focus,
    setValue,
    handleSave
  };
};

// eslint-disable-next-line max-statements
export const useSearchableEvents = context => {
  const {
    clearOptions,
    focus,
    name,
    handleSave,
    inputValue,
    savesValue,
    setInputValue,
    setIsEditMode,
    setValue
  } = context;
  const handleChange = useCallback(
    e => {
      setInputValue(e.target.value);
    },
    [setInputValue]
  );
  const handleLeaveControl = useCallback(() => {
    setIsEditMode(false);
    setInputValue("");
    clearOptions();
  }, [clearOptions, setIsEditMode, setInputValue]);
  const handleSaveValue = val => {
    setValue(val);
    handleSave({ target: { value: val } });
  };
  const handlePickValue = val => {
    handleSaveValue(val);
    handleLeaveControl();
  };
  const handleValueClick = useCallback(
    e => {
      if (e) e.preventDefault();
      setIsEditMode(true);
      setInputValue("");
      setTimeout(() => focus(), 300);
    },
    [setIsEditMode, setInputValue, focus]
  );
  const handleBlur = () => {
    if (both(complement(isNullOrEmpty), pipe(isNaN, not))(trim(inputValue))) {
      handleSaveValue(inputValue);
    }
  };
  const controlRef = useRef(null);
  const isFocused = useIsFocused(name);
  const handleClickOutSide = () => {
    if (isFocused) {
      if (savesValue) handleBlur();
      handleLeaveControl();
    }
  };

  useOnClickOutside(controlRef, handleClickOutSide);
  useEffect(() => {
    if (!isFocused) {
      handleLeaveControl();
    }
  }, [isFocused, handleLeaveControl]);

  return {
    controlRef,
    handleBlur,
    handlePickValue,
    handleChange,
    handleValueClick
  };
};
