import { useCallback, useRef, useState } from "react";
import { __, always, dec, gt, ifElse, inc, lt, prop, multiply } from "ramda";
import useKeyPress from "./useKeyPress";

const decTo0 = limit => ifElse(gt(__, 0), dec, always(limit));
const incToLimit = limit => ifElse(lt(__, limit), inc, always(0));
const multiplyI = multiply(28);
const scroll = (ref, val) => {
  ref.scroll({ top: multiplyI(val) });
};

export const useSearchableMenuKeyCommands = ({
  options,
  primaryColumn,
  submit
}) => {
  const [currentOptionIndex, setCurrentOptionIndex] = useState(0);
  const searchResultsRef = useRef();

  const nextOption = useCallback(() => {
    setCurrentOptionIndex(prev => {
      const newVal = incToLimit(options.length - 1)(prev);

      scroll(searchResultsRef.current, newVal);
      return newVal;
    });
  }, [options.length, searchResultsRef.current, setCurrentOptionIndex]);

  const prevOption = useCallback(() => {
    setCurrentOptionIndex(prev => {
      const newVal = decTo0(options.length - 1)(prev);

      scroll(searchResultsRef.current, newVal);
      return newVal;
    });
  }, [options.length, searchResultsRef.current, setCurrentOptionIndex]);

  const handleKeyboardSubmit = useCallback(
    e => {
      if (e) e.preventDefault();
      if (submit) {
        submit(prop(primaryColumn, options[currentOptionIndex]));
      }
    },
    [currentOptionIndex, options, primaryColumn, submit]
  );

  useKeyPress(nextOption, "ArrowDown");
  useKeyPress(prevOption, "ArrowUp");
  useKeyPress(handleKeyboardSubmit, "Enter");

  return { currentOptionIndex, searchResultsRef };
};

export default useSearchableMenuKeyCommands;
