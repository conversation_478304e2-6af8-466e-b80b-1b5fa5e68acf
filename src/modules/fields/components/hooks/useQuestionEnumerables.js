import { useMemo } from "react";
import { useSelector } from "react-redux";
import {
  allPass,
  always,
  applySpec,
  both,
  filter,
  identity,
  ifElse,
  includes,
  length,
  map,
  not,
  path,
  pipe,
  prop,
  propOr,
  when,
  __,
  toLower
} from "ramda";
import { useQuery } from "@apollo/client";
import Questionnaire from "modules/questionnaire/selectors";
import query from "modules/question/components/Question/EnumerableSearchable/query";
import {
  DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES,
  DEPENDENT_FILTERED_QUESTION_ENUMERABLES
} from "modules/fields/graphql/query";
import getActiveEnumerables from "modules/question/components/Question/Enumerable/activeEnumerables";
import { isNullOrEmpty } from "utils/fp";

const shouldPreloadEnumerables = allPass([
  prop("isEditMode"),
  pipe(prop("questionEnumerables"), length, not),
  path(["question", "loadEnums"])
]);

const usePreloadedEnumerables = props => ({
  preloadedEnumerables: ifElse(
    shouldPreloadEnumerables,
    path(["question", "activeEnumerables"]),
    prop("questionEnumerables")
  )(props)
});

export const useActiveEnumerables = props => {
  const { isEditMode, question = {}, searchTerm, skip = false } = props;
  const visit = useSelector(state => Questionnaire.getVisit(state));
  const {
    loading,
    data: { questionEnumerables } = { questionEnumerables: [] }
  } = useQuery(query, {
    variables: {
      questionId: Number(question.id),
      description: searchTerm,
      visitId: Number(visit.id),
      skip
    },
    skip: isNullOrEmpty(searchTerm)
  });
  const { preloadedEnumerables } = usePreloadedEnumerables({
    isEditMode,
    question,
    questionEnumerables
  });
  const activeEnumerables = useMemo(
    () => getActiveEnumerables(visit, preloadedEnumerables),
    [visit, preloadedEnumerables]
  );

  return { activeEnumerables, loading };
};

const parseServerData = propertyName => serverData =>
  JSON.parse(propOr("{}", propertyName, serverData));

const buildEnumDescription = questionId =>
  applySpec({
    enumerableDescription: identity,
    description: identity,
    questionId: always(questionId)
  });
const createDynamicEnumerables = (question, visit) =>
  pipe(
    propOr([], question.id),
    applySpec({
      activeEnumerables: pipe(
        when(val => !Array.isArray(val), prop("values")),
        map(buildEnumDescription(question.id)),
        getActiveEnumerables(visit)
      ),
      columns: ifElse(
        val => Array.isArray(val),
        always(undefined),
        prop("columns")
      )
    })
  );
const createFilteredEnumerables = (question, inputValue) =>
  applySpec({
    activeEnumerables: ifElse(
      prop("descriptions"),
      questionEnumerablesData =>
        pipe(
          propOr([], "activeEnumerables"),
          filter(
            pipe(
              prop("description"),
              both(
                includes(__, questionEnumerablesData.descriptions),
                pipe(toLower, includes(toLower(inputValue)))
              )
            )
          )
        )(question),
      always([])
    ),
    columns: always(question.columns)
  });

// eslint-disable-next-line complexity
export const useDynamicActiveEnumerables = props => {
  const {
    question,
    questionnaireResponseId,
    args,
    skip = false,
    filteredType = false,
    inputValue
  } = props;

  const searchQuery = filteredType
    ? DEPENDENT_FILTERED_QUESTION_ENUMERABLES
    : DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES;
  const objectName = filteredType
    ? "dependentFilteredQuestionEnumerables"
    : "dependentExternalQuestionEnumerables";
  const variables = {
    questionnaireResponseId: Number(questionnaireResponseId),
    questionId: filteredType ? Number(question.id) : undefined,
    dependentAnswerData: args
  };

  const visit = useSelector(state => Questionnaire.getVisit(state));
  const { loading, data = {} } = useQuery(searchQuery, {
    variables,
    skip
  });

  const questionEnumerables = useMemo(
    () =>
      pipe(
        parseServerData(objectName),
        ifElse(
          always(filteredType),
          createFilteredEnumerables(question, inputValue),
          createDynamicEnumerables(question, visit)
        )
      )(data),
    [data, objectName, filteredType, visit, question, inputValue]
  );

  return { ...questionEnumerables, loading };
};
