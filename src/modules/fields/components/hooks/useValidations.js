import { useMemo } from "react";
import { any, either, join, map, pipe, propEq, filter, length } from "ramda";
import { useSelector } from "react-redux";
import Questions from "modules/question/selectors";
import { processValidationNotifications } from "modules/question/components/Question/ValidationNotification/enhancers";
import { isNullOrEmpty } from "utils/fp";

export const useValidationFailures = name => {
  const validationFailures = useSelector(state =>
    Questions.getValidationFailures(name, state)
  );
  const responseValidationFailures = useSelector(state =>
    Questions.getResponseValidationFailures(name, state)
  );

  return { validationFailures, responseValidationFailures };
};

const prefixes = ["must ", "", "should "];

export const useValidation = name => {
  const { validationFailures, responseValidationFailures } =
    useValidationFailures(name);
  const validationNotifications = useMemo(
    () =>
      processValidationNotifications(
        validationFailures,
        responseValidationFailures
      ),
    [validationFailures, responseValidationFailures]
  );
  const hasValidationFailures = !isNullOrEmpty(validationNotifications);
  const hasValidationErrors = useMemo(
    () =>
      any(
        either(propEq("type", 0), propEq("type", 1)),
        validationNotifications
      ),
    [validationNotifications]
  );
  const validationErrorsCount = useMemo(
    () =>
      pipe(
        filter(either(propEq("type", 0), propEq("type", 1))),
        length
      )(validationNotifications),
    [validationNotifications]
  );
  const hasValidationWarnings = useMemo(
    () => any(propEq("type", 2), validationNotifications),
    [validationNotifications]
  );
  const validationWarningsCount = useMemo(
    () => pipe(filter(propEq("type", 2)), length)(validationNotifications),
    [validationNotifications]
  );
  const validationText = useMemo(
    () =>
      pipe(
        map(
          notification =>
            `${
              notification.isCustom ? prefixes[1] : prefixes[notification.type]
            }${notification.failureMessage}`
        ),
        join("<br />")
      )(validationNotifications),
    [validationNotifications]
  );

  return {
    validationFailures,
    responseValidationFailures,
    hasValidationFailures,
    hasValidationErrors,
    validationErrorsCount,
    hasValidationWarnings,
    validationWarningsCount,
    validationText
  };
};
