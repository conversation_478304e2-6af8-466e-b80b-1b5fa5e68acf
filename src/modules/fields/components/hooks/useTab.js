import { useCallback, useEffect, useMemo, useState } from "react";
import { and, both, identity, or, pathEq } from "ramda";
import { useDispatch, useSelector } from "react-redux";
import Question from "modules/question/selectors";
import { focusNextField } from "modules/question/components/Question/shared/HoldTab";
import { setHoldingSave } from "modules/question/actions";
import { useManageQuestionnaireFocus } from "./useFocus";

// eslint-disable-next-line max-statements
export const useTab = props => {
  const { question, name } = props;
  const [onTab, setOnTab] = useState(false);
  const dispatch = useDispatch();
  const isSaving = useSelector(
    state =>
      state.app.status.mutationLoading && state.app.status.mutationLoading[name]
  );
  const isHoldingSave = useSelector(state =>
    Question.getIsHoldingSave(name, state)
  );
  const isOptimistic = useSelector(state =>
    Question.getIsHoldingSave(name, state)
  );
  const turnOnHoldingSave = useCallback(
    () => dispatch(setHoldingSave(name, true)),
    [dispatch, name]
  );
  const turnOffHoldingSave = useCallback(
    () => dispatch(setHoldingSave(name, false)),
    [dispatch, name]
  );
  const moveNextField = useCallback(() => {
    focusNextField(name);
  }, [name]);
  const { focusRef, onFieldFocus, focus } = useManageQuestionnaireFocus(props);
  const handlers = useMemo(
    () => ({
      holdTab: event => {
        if (question.edgeTarget) {
          let timeOffset = 0;

          if (both(identity, pathEq(["target", "type"], "radio"))(event)) {
            event.preventDefault();
            timeOffset = 200;
            setOnTab(true);
          } else {
            // we wait small time to make sure the saving (if any) has started by the time
            // the component refresh is done
            window.setTimeout(() => {
              focus();
            }, 20);
          }
          window.setTimeout(() => {
            turnOnHoldingSave();
          }, 50 + timeOffset);
        }
      },
      enumNavigation: event => {
        if (
          and(
            and(question.edgeTarget, onTab),
            both(identity, pathEq(["target", "type"], "radio"))(event)
          )
        ) {
          event.preventDefault();
        }
      }
    }),
    [question, focus, onTab, setOnTab, turnOnHoldingSave]
  );

  useEffect(() => {
    if (and(isHoldingSave, or(isOptimistic, !isSaving))) {
      setOnTab(false);
      turnOffHoldingSave();
      window.setTimeout(() => {
        moveNextField();
      }, 50);
    }
  }, [
    isOptimistic,
    isHoldingSave,
    isSaving,
    setOnTab,
    turnOffHoldingSave,
    moveNextField
  ]);

  return { focus, focusRef, handlers, onFieldFocus };
};
