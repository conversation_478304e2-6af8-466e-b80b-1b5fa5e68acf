import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { compose } from "ramda";
import {
  setOptimistic as setOptimisticState,
  savePendingFields,
  changeFocusToRealField,
  addFieldToPending,
  setFieldValue
} from "modules/question/actions";
import { setMutationLoading } from "modules/app/actions";
import { pushError, pushWarning } from "modules/questionnaire/actions";
import Questions from "modules/question/selectors";
import Questionnaire from "modules/questionnaire/selectors";
import { useProcessAnswer } from "modules/question/components/Question/processAnswer";
import {
  useNonEntryPointSaveMutations,
  useUpdateArrayAnswer,
  useUpdateSingleAnswer
} from "./useSaveAnswerMutation";
import { isNullOrEmpty } from "utils/fp";
import query from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";

export const useIsSaving = name => {
  const isSaving = useSelector(
    state =>
      state.app.status.mutationLoading && state.app.status.mutationLoading[name]
  );

  return isSaving;
};
export const useStateChangingFunctions = ({ name, question }) => {
  const dispatch = useDispatch();
  const answerError = useCallback(
    ({ errors, warnings }) => {
      const formatMessage = messages => ({
        fieldName: name,
        messages,
        question
      });

      if (!isNullOrEmpty(errors)) {
        const sendError = error => dispatch(pushError(error));

        compose(sendError, formatMessage)(errors);
      }
      if (!isNullOrEmpty(warnings)) {
        const sendWarning = warning => dispatch(pushWarning(warning));

        compose(sendWarning, formatMessage)(warnings);
      }
    },
    [name, question, dispatch]
  );
  const setSaving = useCallback(
    saving => {
      dispatch(setMutationLoading(name, saving));
    },
    [name, dispatch]
  );
  const setOptimistic = useCallback(
    isOptimistic => {
      dispatch(setOptimisticState(name, isOptimistic));
    },
    [name, dispatch]
  );
  const focusRealField = useCallback(
    addedVisibleQuestions => {
      dispatch(changeFocusToRealField(addedVisibleQuestions));
    },
    [dispatch]
  );
  const savePendingAnswers = useCallback(
    addedVisibleQuestions => {
      dispatch(savePendingFields(addedVisibleQuestions));
    },
    [dispatch]
  );
  const addToPendingSave = useCallback(
    fieldData => {
      dispatch(addFieldToPending(fieldData));
    },
    [dispatch]
  );
  const setFormValue = useCallback(
    (fieldName, value) => {
      dispatch(setFieldValue(fieldName, value));
    },
    [dispatch]
  );

  return {
    answerError,
    setSaving,
    setOptimistic,
    focusRealField,
    savePendingAnswers,
    addToPendingSave,
    setFormValue
  };
};

const useProps = props => {
  const allAnswers = useSelector(state => Questionnaire.getAnswers(state));
  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );
  const visibleQuestions = useSelector(state =>
    Questions.getVisibleQuestions(state)
  );
  const allAnswerGroups = useSelector(state =>
    Questionnaire.getAnswerGroups(state)
  );
  // const query = useSelector(state => Questions.getResponseQuery(state));
  // const objectName = useSelector(state =>
  //   Questions.getResponseObjectName(state)
  // );
  const errors = useSelector(state => Questionnaire.getErrors(state));
  const stateFunctions = useStateChangingFunctions(props);
  const mutationsCallbacks = useNonEntryPointSaveMutations(props);

  return {
    ...props,
    allAnswers,
    questionnaire,
    visibleQuestions,
    allAnswerGroups,
    query,
    objectName: "questionnaireResponse",
    errors,
    ...stateFunctions,
    ...mutationsCallbacks
  };
};

export const useSaveSingleAnswer = props => {
  const ownProps = useProps(props);
  const { mutationHandler } = useUpdateSingleAnswer(ownProps);
  const answerQuestion = useProcessAnswer({
    dataFieldName: "answerData",
    mutationName: "answerQuestion",
    mutationHandler,
    ownProps
  });

  return { answerQuestion };
};

export const useSaveArrayAnswer = props => {
  const ownProps = useProps(props);
  const { mutationHandler } = useUpdateArrayAnswer(ownProps);
  const answerArrayQuestion = useProcessAnswer({
    dataFieldName: "answerArray",
    mutationName: "answerArrayQuestion",
    mutationHandler,
    ownProps
  });

  return { answerArrayQuestion };
};
