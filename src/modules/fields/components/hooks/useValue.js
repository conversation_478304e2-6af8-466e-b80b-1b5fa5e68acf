import { useCallback, useEffect } from "react";
import debounce from "debounce";
import { useDispatch, useSelector } from "react-redux";
import {
  __,
  always,
  and,
  assoc,
  both,
  complement,
  either,
  identity,
  ifElse,
  or,
  pipe,
  prop,
  propEq,
  propOr,
  test as ramTest,
  equals
} from "ramda";
import Questionnaire from "modules/questionnaire/selectors";
import Question from "modules/question/selectors";
import { useSaveArrayAnswer, useSaveSingleAnswer } from "./useSaveAnswer";
import { setFieldValue } from "modules/question/actions";
import { normalizeCheckboxValues } from "modules/question/components/Question/EnumerableCollection/convertCheckboxValues";
import { isNullOrEmpty } from "utils/fp";
import moment from "moment";

export const debouncedAnswerQuestion = ({
  saveFunction,
  event,
  value,
  type,
  debounceDelayInMilliseconds = 300
}) => {
  const debouncedSaveFunction = debounce(
    saveFunction,
    debounceDelayInMilliseconds
  );

  debouncedSaveFunction(event, value, type);
};
const convertTimeString = value => {
  const timeFormats = ["HH:mm", "H:mm", "HHmm", "Hmm", "mm", "m"];

  if (ramTest(/^([01]?\d|2[0-3]):?([0-5]\d)$/, value)) {
    const momentTime = moment(value, timeFormats, true).format("HH:mm");

    return momentTime;
  }

  return "";
};

export const useInitValue = ({ name, fieldName, defaultValue = "" }) => {
  // DB/Backend saved answer
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const dispatch = useDispatch();
  const setValue = useCallback(
    value => dispatch(setFieldValue(name, value)),
    [dispatch, name]
  );

  const resetValue = useCallback(() => {
    setValue(propOr(defaultValue, fieldName, answer || {}));
  }, [setValue, answer]);
  // FE stored answer data, can be partial a value
  const value = useSelector(state => Question.getFieldValue(name, state));

  // Initialize FE store with answer value when value is undefined
  useEffect(() => {
    if (and(value === undefined, both(identity, prop(fieldName))(answer))) {
      setValue(prop(fieldName, answer));
    }
  }, [value, answer, fieldName, setValue]);

  return { answer, value, resetValue, setValue };
};

export const useInitCompoundValue = ({ name, fieldName }) => {
  // DB/Backend saved answer
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const dispatch = useDispatch();
  const setValue = useCallback(
    value => dispatch(setFieldValue(name, value)),
    [dispatch, name]
  );
  // FE stored answer data, can be partial a value
  const value = useSelector(state => Question.getFieldValue(name, state));

  // Initialize FE store with answer value when value is undefined
  useEffect(() => {
    if (value === undefined) {
      const formattedValue = ifElse(
        either(isNullOrEmpty, complement(prop("answerData"))),
        always({}),
        ifElse(
          propEq("answerType", "enumerable"),
          pipe(prop("answerData"), assoc("selected", __, {})),
          pipe(prop("answerData"), assoc("value", __, {}))
        )
      )(answer);

      setValue(formattedValue);
    }
  }, [answer, fieldName, setValue, value]);

  return { answer, value, setValue };
};

export const useSingleValue = props => {
  const { name, answerType, debounceDelayInMilliseconds } = props;
  const { answer, value, resetValue, setValue } = useInitValue({
    name,
    fieldName: "answerData"
  });

  // Saves partial values as user changes field
  const handleChange = useCallback(
    event => setValue(event.target.value),
    [setValue]
  );
  const { answerQuestion } = useSaveSingleAnswer({ ...props, answer });
  // Saves final value and sends Answer to be saved on Server as user leaves field
  const handleSave = useCallback(
    event => {
      const inputValue = ifElse(
        equals("time-string"),
        always(convertTimeString(event.target.value)),
        always(event.target.value)
      )(answerType);

      setValue(inputValue);
      debouncedAnswerQuestion({
        saveFunction: answerQuestion,
        event,
        value: inputValue,
        type: answerType,
        debounceDelayInMilliseconds
      });
    },
    [answerQuestion, answerType, setValue]
  );

  return { value, handleChange, handleSave, resetValue, setValue };
};

export const useArrayValue = props => {
  const { name, answerType } = props;
  const { answer, value, resetValue, setValue } = useInitValue({
    name,
    fieldName: "answerArray",
    defaultValue: []
  });
  const { answerArrayQuestion } = useSaveArrayAnswer({ ...props, answer });
  // Saves final value and sends Answer to be saved on Server as user leaves field
  const handleSave = useCallback(
    event => {
      const newValue = normalizeCheckboxValues(event.target.value, value);

      setValue(newValue);
      debouncedAnswerQuestion({
        saveFunction: answerArrayQuestion,
        event,
        value: newValue,
        type: answerType
      });
    },
    [answerArrayQuestion, answerType, setValue, value]
  );

  return { value, handleSave, resetValue };
};

export const useCompoundValue = props => {
  const { name, answerType, enumerableAnswerType } = props;
  const {
    answer,
    value = {},
    setValue
  } = useInitCompoundValue({
    name,
    fieldName: "answerData"
  });
  // Saves partial values as user changes field
  const handleChange = useCallback(
    event => setValue({ value: event.target.value }),
    [setValue]
  );
  const { answerQuestion } = useSaveSingleAnswer({ ...props, answer });
  // Saves final value and sends Answer to be saved on Server as user leaves field
  const handleSave = useCallback(
    event => {
      if (
        !answer ||
        or(answer.answerType !== "enumerable", event.target.value)
      ) {
        const inputValue = ifElse(
          equals("time-string"),
          always(convertTimeString(event.target.value)),
          always(event.target.value)
        )(answerType);

        setValue({ value: inputValue });
        debouncedAnswerQuestion({
          saveFunction: answerQuestion,
          event,
          value: inputValue,
          type: answerType
        });
      }
    },
    [answerQuestion, answerType, setValue, answer]
  );
  const handleEnumerableChange = useCallback(
    event => {
      setValue({ selected: event.target.value });
      debouncedAnswerQuestion({
        saveFunction: answerQuestion,
        event,
        value: event.target.value,
        type: enumerableAnswerType
      });
    },
    [answerQuestion, enumerableAnswerType, setValue]
  );

  return {
    ...value,
    handleChange,
    handleSave,
    handleEnumerableChange,
    setValue
  };
};

export const useClearAnswer = props => {
  const { name, question } = props;
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const dispatch = useDispatch();
  const { answerQuestion } = useSaveSingleAnswer({ ...props, answer });
  const handleClear = useCallback(
    event => {
      if (event) {
        event.preventDefault();
      }
      answerQuestion(event, "", question.type);
      dispatch(setFieldValue(name, ""));
    },
    [answerQuestion, question.type, name, dispatch]
  );

  return {
    handleClear
  };
};
