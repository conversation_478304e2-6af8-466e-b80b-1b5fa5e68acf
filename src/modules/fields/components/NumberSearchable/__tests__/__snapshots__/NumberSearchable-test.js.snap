// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NumberSearchable disabled is not set input when false 1`] = `
<Searchable
  answerType="number-searchable"
  className="question-number-searchable"
  disabled={false}
  isFavoritable={true}
  name="1|1"
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  savesValue={true}
/>
`;

exports[`NumberSearchable disabled is properly set on input when true 1`] = `
<Searchable
  answerType="number-searchable"
  className="question-number-searchable"
  disabled={true}
  isFavoritable={true}
  name="1|1"
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  savesValue={true}
/>
`;
