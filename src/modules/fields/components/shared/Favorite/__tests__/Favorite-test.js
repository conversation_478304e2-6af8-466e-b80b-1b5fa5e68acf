import TestRenderer from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Favorite from "..";

describe("Favorite", () => {
  const mocks = [];
  const optionNoFavorite = {
    id: "3",
    description: "test1|23",
    name: "test1",
    code: "23"
  };
  const optionFavorite = {
    id: "4",
    description: "test2|24",
    name: "test2",
    code: "24",
    favorites: true
  };

  const getComponent = ({ option }) =>
    decoratedApollo({
      component: Favorite,
      props: {
        option
      },
      initialAppValues: {},
      apolloMocks: mocks
    });

  test("renders correctly when not favorite", async () => {
    const favorite = TestRenderer.create(
      getComponent({
        option: optionNoFavorite
      })
    );

    await wait(3);

    expect(favorite).toMatchSnapshot();
  });

  test("renders correctly when is favorite", async () => {
    const favorite = TestRenderer.create(
      getComponent({
        option: optionFavorite
      })
    );

    await wait(3);

    expect(favorite).toMatchSnapshot();
  });
});
