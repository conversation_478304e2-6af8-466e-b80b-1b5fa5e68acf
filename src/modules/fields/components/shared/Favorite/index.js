import classnames from "classnames";
import { useComponentLogic } from "./hooks";

export const Favorite = props => {
  const { option } = props;
  const { loading, handleUpdateFavoritable } = useComponentLogic(props);
  const favoriteClass = classnames("fa pci-fav", {
    "fa-star": option.favorites && !loading,
    "fa-star-o": !option.favorites && !loading,
    "fa-spinner": loading
  });

  return (
    <td className="favorite-cell">
      <i className={favoriteClass} onClick={handleUpdateFavoritable} />
    </td>
  );
};

export default Favorite;
