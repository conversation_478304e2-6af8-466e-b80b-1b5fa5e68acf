import { useCallback } from "react";
import { useMutation } from "@apollo/client";
import GET_FAVORITES from "modules/question/components/Question/EnumerableSearchable/SearchModal/query";
import { UPDATE_ENUMERABLE } from "modules/question/components/Question/EnumerableSearchable/SearchModal/SearchResult/SearchResultRow/Favorite/mutation";

export const useComponentLogic = props => {
  const { option, questionId } = props;
  const [updateFavoriteEnumerable, { loading }] =
    useMutation(UPDATE_ENUMERABLE);
  const handleUpdateFavoritable = useCallback(
    e => {
      e.preventDefault();
      e.stopPropagation();

      updateFavoriteEnumerable({
        variables: { id: option.id },
        refetchQueries: [{ query: GET_FAVORITES, variables: { questionId } }]
      });
    },
    [updateFavoriteEnumerable, option.id, questionId]
  );

  return { loading, handleUpdateFavoritable };
};
