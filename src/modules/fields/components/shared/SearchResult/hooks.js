import { prop } from "ramda";
import { useCallback } from "react";

export const useComponentLogic = props => {
  const { option, primaryColumn = "id", onClick } = props;
  const handleClick = useCallback(
    e => {
      if (e) e.preventDefault();
      if (onClick) {
        onClick(prop(primaryColumn, option));
      }
    },
    [onClick, option, primaryColumn]
  );

  return { handleClick };
};
