import classnames from "classnames";
import { prop } from "ramda";
import Favorite from "../Favorite";
import { useComponentLogic } from "./hooks";

export const SearchResult = props => {
  const { active, option, question, isFavoritable, columns = [] } = props;
  const { handleClick } = useComponentLogic(props);
  const className = classnames("result", { active });

  return (
    <tr className={className} onClick={handleClick}>
      {option.isOneColumn ? (
        <td colSpan={columns.length}>{option.description}</td>
      ) : (
        columns.map(column => <td key={column}>{prop(column, option)}</td>)
      )}
      {isFavoritable && <Favorite questionId={question.id} option={option} />}
    </tr>
  );
};

export default SearchResult;
