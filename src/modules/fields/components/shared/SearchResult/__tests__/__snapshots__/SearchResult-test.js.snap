// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SearchResult when question has column 1`] = `
<tr
  className="result"
  onClick={[Function]}
>
  <td>
    test1
  </td>
  <td>
    23
  </td>
</tr>
`;

exports[`SearchResult when question has column and isFavoritable 1`] = `
<tr
  className="result"
  onClick={[Function]}
>
  <td>
    test1
  </td>
  <td>
    23
  </td>
  <Favorite
    option={
      Object {
        "code": "23",
        "description": "test1|23",
        "id": "3",
        "name": "test1",
      }
    }
  />
</tr>
`;

exports[`SearchResult when question has columns and option isOneColumn 1`] = `
<tr
  className="result"
  onClick={[Function]}
>
  <td
    colSpan={2}
  >
    this option is not formatted correctly for columns
  </td>
</tr>
`;

exports[`SearchResult when question has no columns 1`] = `
<tr
  className="result"
  onClick={[Function]}
/>
`;
