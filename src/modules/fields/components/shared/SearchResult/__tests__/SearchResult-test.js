import { act, create } from "react-test-renderer";
import SearchResult from "..";

jest.mock("modules/fields/components/shared/Favorite", () => "Favorite");

describe("SearchResult", () => {
  const questionNoColumns = {};
  const questionWithColumns = { columns: ["name", "code"] };
  const someOption = {
    id: "3",
    description: "test1|23",
    name: "test1",
    code: "23"
  };
  const oneColumnOption = {
    id: "3",
    description: "this option is not formatted correctly for columns",
    isOneColumn: true
  };

  const render = ({ option, question, isFavoritable }) =>
    create(
      <SearchResult
        option={option}
        question={question}
        columns={question.columns}
        primaryColumn="id"
        onClick={jest.fn()}
        isFavoritable={isFavoritable}
      />
    );

  test("when question has no columns", () => {
    const results = render({
      option: someOption,
      question: questionNoColumns
    });

    expect(results).toMatchSnapshot();
  });

  test("when question has column", () => {
    const results = render({
      option: someOption,
      question: questionWithColumns
    });

    expect(results).toMatchSnapshot();
  });

  test("when question has column and isFavoritable", () => {
    const results = render({
      option: someOption,
      question: questionWithColumns,
      isFavoritable: true
    });

    expect(results).toMatchSnapshot();
  });

  test("when question has columns and option isOneColumn", () => {
    const results = render({
      option: oneColumnOption,
      question: questionWithColumns
    });

    expect(results).toMatchSnapshot();
  });

  test("send correct value when clicked", () => {
    const onClick = jest.fn();
    const wrapper = create(
      <SearchResult
        option={someOption}
        question={questionWithColumns}
        primaryColumn="name"
        onClick={onClick}
      />
    );
    const instance = wrapper.root;
    const [row] = instance.findAllByType("tr");

    act(() => {
      row.props.onClick();
    });

    expect(onClick).toHaveBeenCalledWith("test1");
  });
});
