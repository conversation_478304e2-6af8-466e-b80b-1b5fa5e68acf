import {
  useSearchableEvents,
  useSearchableState,
  useSearchableOptions
} from "../../hooks/useSearchableOptions";

export const useComponentLogic = props => {
  const { savesValue } = props;
  const context = useSearchableState(props);
  const { inputValue, isEditMode } = context;
  const { options, clearOptions, columns } = useSearchableOptions({
    isEditMode,
    props,
    value: inputValue
  });
  const {
    controlRef,
    handleBlur,
    handleChange,
    handlePickValue,
    handleValueClick
  } = useSearchableEvents({ ...context, options, clearOptions, savesValue });

  return {
    ...context,
    options,
    columns,
    controlRef,
    handleInputBlur: savesValue ? handleBlur : undefined,
    handlePickValue,
    handleChange,
    handleValueClick
  };
};
