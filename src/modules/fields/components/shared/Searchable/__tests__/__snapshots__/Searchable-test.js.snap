// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Searchable disabled is not set input when false 1`] = `
<SearchableControl
  className="question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`Searchable disabled is properly set on input when true 1`] = `
<SearchableControl
  className="question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={true}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`Searchable renders correctly when savesValue is true 1`] = `
<SearchableControl
  className="question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  savesValue={true}
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`Searchable renders correctly when savesValue is true and question has primaryColumn 1`] = `
<SearchableControl
  className="question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
      "primaryColumn": "code",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  savesValue={true}
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`Searchable value is set from prestored values 1`] = `
<SearchableControl
  className="question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="2|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "columns": Array [
        "name",
        "code",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  value="123"
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;
