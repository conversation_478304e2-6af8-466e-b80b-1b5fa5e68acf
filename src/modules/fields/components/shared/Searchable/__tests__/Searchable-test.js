import TestRenderer from "react-test-renderer";
import wait from "waait";
import query from "modules/question/components/Question/EnumerableSearchable/query";
import { decoratedApollo } from "utils/test/decorated";
import Searchable from "..";

jest.mock("../SearchableControl", () => "SearchableControl");

describe("Searchable", () => {
  const mocks = [
    {
      request: {
        query,
        variables: { questionId: 1, description: "test", visitId: 1 }
      },
      result: {
        data: {
          questionEnumerables: [
            {
              id: "123",
              description: "test1",
              expirationOn: 0,
              displayOrder: 0,
              effectiveOn: 0,
              __typename: "QuestionEnumerable"
            }
          ]
        }
      }
    },
    {
      request: {
        query,
        variables: { id: 123 }
      },
      result: {
        data: {
          questionEnumerables: [
            {
              id: "123",
              description: "test1",
              expirationOn: 0,
              displayOrder: 0,
              effectiveOn: 0,
              __typename: "QuestionEnumerable"
            }
          ]
        }
      }
    }
  ];
  const question1 = { id: "1", columns: ["name", "code"] };
  const question2 = {
    id: "1",
    columns: ["name", "code"],
    primaryColumn: "code"
  };
  const questionAnswerGroup = { id: "1" };
  const appState = {
    questions: { values: { "2|1": "123" } },
    status: {},
    questionnaire: { visit: { id: "1" } }
  };

  const getComponent = ({
    disabled,
    name = "1|1",
    savesValue,
    question = question1
  }) =>
    decoratedApollo({
      component: Searchable,
      props: {
        disabled,
        name,
        question,
        questionAnswerGroup,
        isFavoritable: true,
        savesValue,
        className: "question-searchable"
      },
      initialAppValues: appState,
      apolloMocks: mocks
    });

  test("disabled is properly set on input when true", () => {
    const enumSearch = TestRenderer.create(getComponent({ disabled: true }));

    expect(enumSearch).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const enumSearch = TestRenderer.create(getComponent({ disabled: false }));

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when savesValue is true", () => {
    const enumSearch = TestRenderer.create(
      getComponent({ disabled: false, savesValue: true })
    );

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when savesValue is true and question has primaryColumn", () => {
    const enumSearch = TestRenderer.create(
      getComponent({ disabled: false, savesValue: true, question: question2 })
    );

    expect(enumSearch).toMatchSnapshot();
  });

  test("value is set from prestored values", async () => {
    const enumSearch = TestRenderer.create(
      getComponent({
        disabled: false,
        name: "2|1"
      })
    );

    await wait(3);
    expect(enumSearch).toMatchSnapshot();
  });
});
