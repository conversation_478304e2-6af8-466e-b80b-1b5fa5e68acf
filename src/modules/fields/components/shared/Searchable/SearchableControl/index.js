import autoCompleteVal from "utils/getAutoCompleteValue";
import SearchResults from "../../SearchResults";
import SelectedValue from "../../SelectedValue";

export const SearchableControl = props => {
  const {
    disabled,
    name,
    question,
    isFavoritable,
    savesValue,
    className,
    focusRef,
    controlRef,
    valueRef,
    isEditMode,
    inputValue,
    options = [],
    columns = [],
    value,
    onFieldFocus,
    handleInputBlur,
    handlePickValue,
    handleChange,
    handleValueClick
  } = props;

  return (
    <div className={`${className} searchable-container`} ref={controlRef}>
      <input
        disabled={disabled}
        name={name}
        autoComplete={autoCompleteVal}
        value={inputValue}
        type="text"
        onFocus={onFieldFocus}
        onChange={handleChange}
        onBlur={handleInputBlur}
        ref={focusRef}
        style={{ display: isEditMode ? "" : "none" }}
      />
      <SelectedValue
        value={value}
        onClick={handleValueClick}
        isEditMode={isEditMode}
        useValue={savesValue}
        valueRef={valueRef}
      />
      <SearchResults
        options={options}
        columns={columns}
        question={question}
        onResultSelected={handlePickValue}
        primaryColumn={question.primaryColumn || "id"}
        isFavoritable={isFavoritable}
        isEditMode={isEditMode}
      />
    </div>
  );
};
export default SearchableControl;
