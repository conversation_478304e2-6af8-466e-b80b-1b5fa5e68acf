// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SearchableControl disabled is not set input when false 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={false}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "none",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={false}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={false}
    isFavoritable={true}
    onResultSelected={[MockFunction]}
    options={Array []}
    primaryColumn="id"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
      }
    }
  />
</div>
`;

exports[`SearchableControl disabled is properly set on input when true 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={true}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "none",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={false}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={false}
    isFavoritable={true}
    onResultSelected={[MockFunction]}
    options={Array []}
    primaryColumn="id"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
      }
    }
  />
</div>
`;

exports[`SearchableControl renders correctly when isEditMode is true 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={false}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={true}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={true}
    isFavoritable={true}
    onResultSelected={[MockFunction]}
    options={Array []}
    primaryColumn="id"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
      }
    }
  />
</div>
`;

exports[`SearchableControl renders correctly when isFavoritable is false 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={false}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "none",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={false}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={false}
    isFavoritable={false}
    onResultSelected={[MockFunction]}
    options={Array []}
    primaryColumn="id"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
      }
    }
  />
</div>
`;

exports[`SearchableControl renders correctly when options passed 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={false}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "none",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={false}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={false}
    isFavoritable={true}
    onResultSelected={[MockFunction]}
    options={
      Array [
        Object {
          "description": "test1",
          "displayOrder": 0,
          "effectiveOn": 0,
          "expirationOn": 0,
          "id": "123",
        },
      ]
    }
    primaryColumn="id"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
      }
    }
  />
</div>
`;

exports[`SearchableControl renders correctly when savesValue is true 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={false}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "none",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={false}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={false}
    isFavoritable={true}
    onResultSelected={[MockFunction]}
    options={Array []}
    primaryColumn="id"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
      }
    }
  />
</div>
`;

exports[`SearchableControl renders correctly when savesValue is true and question has primaryColumn 1`] = `
<div
  className="question-searchable searchable-container"
>
  <input
    autoComplete="off"
    disabled={false}
    name="1|1"
    onBlur={[MockFunction]}
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    style={
      Object {
        "display": "none",
      }
    }
    type="text"
    value=""
  />
  <SelectedValue
    isEditMode={false}
    onClick={[MockFunction]}
    useValue={true}
    value=""
  />
  <SearchResults
    columns={Array []}
    isEditMode={false}
    isFavoritable={true}
    onResultSelected={[MockFunction]}
    options={Array []}
    primaryColumn="code"
    question={
      Object {
        "columns": Array [
          "name",
          "code",
        ],
        "id": "1",
        "primaryColumn": "code",
      }
    }
  />
</div>
`;
