import TestRenderer from "react-test-renderer";
import SearchableControl from "..";

jest.mock(
  "modules/fields/components/shared/SearchResults",
  () => "SearchResults"
);
jest.mock(
  "modules/fields/components/shared/SelectedValue",
  () => "SelectedValue"
);

describe("SearchableControl", () => {
  const question1 = { id: "1", columns: ["name", "code"] };
  const question2 = {
    id: "1",
    columns: ["name", "code"],
    primaryColumn: "code"
  };

  const getComponent = ({
    disabled,
    name = "1|1",
    savesValue = true,
    question = question1,
    isFavoritable = true,
    isEditMode = false,
    options = [],
    onFieldFocus = jest.fn(),
    handleInputBlur = jest.fn(),
    handlePickValue = jest.fn(),
    handleChange = jest.fn(),
    handleValueClick = jest.fn()
  }) => (
    <SearchableControl
      disabled={disabled}
      name={name}
      question={question}
      isFavoritable={isFavoritable}
      savesValue={savesValue}
      className="question-searchable"
      isEditMode={isEditMode}
      inputValue=""
      options={options}
      value=""
      onFieldFocus={onFieldFocus}
      handleInputBlur={handleInputBlur}
      handlePickValue={handlePickValue}
      handleChange={handleChange}
      handleValueClick={handleValueClick}
    />
  );

  test("disabled is properly set on input when true", () => {
    const enumSearch = TestRenderer.create(getComponent({ disabled: true }));

    expect(enumSearch).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const enumSearch = TestRenderer.create(getComponent({ disabled: false }));

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when savesValue is true", () => {
    const enumSearch = TestRenderer.create(
      getComponent({ disabled: false, savesValue: true })
    );

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when savesValue is true and question has primaryColumn", () => {
    const enumSearch = TestRenderer.create(
      getComponent({ disabled: false, savesValue: true, question: question2 })
    );

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when isFavoritable is false", () => {
    const enumSearch = TestRenderer.create(
      getComponent({ disabled: false, isFavoritable: false })
    );

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when isEditMode is true", () => {
    const enumSearch = TestRenderer.create(
      getComponent({ disabled: false, isEditMode: true })
    );

    expect(enumSearch).toMatchSnapshot();
  });

  test("renders correctly when options passed", () => {
    const enumSearch = TestRenderer.create(
      getComponent({
        disabled: false,
        options: [
          {
            id: "123",
            description: "test1",
            expirationOn: 0,
            displayOrder: 0,
            effectiveOn: 0
          }
        ]
      })
    );

    expect(enumSearch).toMatchSnapshot();
  });
});
