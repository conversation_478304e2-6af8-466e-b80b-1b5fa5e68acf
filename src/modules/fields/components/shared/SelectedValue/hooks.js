import { useCallback, useMemo } from "react";
import { useQuery } from "@apollo/client";
import query from "modules/question/components/Question/EnumerableSearchable/query";
import { isNullOrEmpty } from "utils/fp";
import { always, head, ifElse, pipe, prop } from "ramda";

export const useComponentLogic = props => {
  const { value, useValue, onClick } = props;
  const { data = {} } = useQuery(query, {
    variables: { id: Number(value) },
    skip: !value || useValue
  });
  const { questionEnumerables } = data;
  const selectedValue = useMemo(
    () =>
      ifElse(
        isNullOrEmpty,
        always(useValue ? value : ""),
        pipe(head, prop("description"))
      )(questionEnumerables),
    [questionEnumerables, value]
  );
  const handleFocus = useCallback(() => {
    onClick();
  }, [onClick]);

  return {
    selectedValue,
    handleFocus
  };
};
