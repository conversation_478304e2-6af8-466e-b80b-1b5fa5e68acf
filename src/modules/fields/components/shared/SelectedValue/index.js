import classNames from "classnames";
import { and } from "ramda";
import { useComponentLogic } from "./hooks";
import { isNullOrEmpty } from "utils/fp";

export const SelectedValue = props => {
  const { onClick, isEditMode, valueRef } = props;
  const { selectedValue, handleFocus } = useComponentLogic(props);
  const valueClasses = classNames("value-holder", { "is-edit": isEditMode });

  if (and(isEditMode, isNullOrEmpty(selectedValue))) return null;

  return (
    <div
      className={valueClasses}
      onClick={onClick}
      onFocus={handleFocus}
      tabIndex={isEditMode ? undefined : "0"}
      title={selectedValue}
      ref={valueRef}
    >
      {selectedValue}
    </div>
  );
};
export default SelectedValue;
