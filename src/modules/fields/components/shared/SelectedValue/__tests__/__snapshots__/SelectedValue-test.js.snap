// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectedValue if no value passed 1`] = `
<div
  className="value-holder"
  onClick={[MockFunction]}
  onFocus={[Function]}
  tabIndex="0"
  title=""
/>
`;

exports[`SelectedValue if value doesn't match a backend value 1`] = `
<div
  className="value-holder"
  onClick={[MockFunction]}
  onFocus={[Function]}
  tabIndex="0"
  title=""
/>
`;

exports[`SelectedValue if value is passed and useValue is true shows value 1`] = `
<div
  className="value-holder"
  onClick={[MockFunction]}
  onFocus={[Function]}
  tabIndex="0"
  title="123"
>
  123
</div>
`;

exports[`SelectedValue if value match a backend value 1`] = `
<div
  className="value-holder"
  onClick={[MockFunction]}
  onFocus={[Function]}
  tabIndex="0"
  title="test1"
>
  test1
</div>
`;

exports[`SelectedValue renders in edit mode with value 1`] = `
<div
  className="value-holder is-edit"
  onClick={[MockFunction]}
  onFocus={[Function]}
  title="test1"
>
  test1
</div>
`;

exports[`SelectedValue renders null when in edit mode without value 1`] = `null`;
