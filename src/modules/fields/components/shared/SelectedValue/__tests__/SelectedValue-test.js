import { act, create } from "react-test-renderer";
import wait from "waait";
import query from "modules/question/components/Question/EnumerableSearchable/query";
import { decoratedApollo } from "utils/test/decorated";
import SelectedValue from "..";

describe("SelectedValue", () => {
  const mocks = [
    {
      request: {
        query,
        variables: { id: 123 }
      },
      result: {
        data: {
          questionEnumerables: [
            {
              id: "123",
              description: "test1",
              expirationOn: 0,
              displayOrder: 0,
              effectiveOn: 0,
              comparand: "discharge",
              __typename: "QuestionEnumerable"
            }
          ]
        }
      }
    },
    {
      request: {
        query,
        variables: { id: -1 }
      },
      result: { data: { questionEnumerables: [] } }
    }
  ];

  const render = ({ value, isEditMode, useValue }) =>
    create(
      decoratedApollo({
        component: SelectedValue,
        props: { onClick: jest.fn(), value, isEditMode, useValue },
        initialAppValues: {},
        apolloMocks: mocks
      })
    );

  test("if no value passed", async () => {
    const selValue = render({});

    await act(() => wait(100));
    expect(selValue).toMatchSnapshot();
  });

  test("if value match a backend value", async () => {
    const selValue = render({
      value: "123"
    });

    await act(() => wait(100));
    expect(selValue).toMatchSnapshot();
  });

  test("if value doesn't match a backend value", async () => {
    const selValue = render({
      value: "-1"
    });

    await act(() => wait(100));
    expect(selValue).toMatchSnapshot();
  });

  test("if value is passed and useValue is true shows value", async () => {
    const selValue = render({ useValue: true, value: "123" });

    await act(() => wait(100));
    expect(selValue).toMatchSnapshot();
  });

  test("renders in edit mode with value", async () => {
    const selValue = render({ isEditMode: true, value: "123" });

    await act(() => wait(100));
    expect(selValue).toMatchSnapshot();
  });

  test("renders null when in edit mode without value", async () => {
    const selValue = render({ isEditMode: true });

    await act(() => wait(100));
    expect(selValue).toMatchSnapshot();
  });

  test("send correct value when clicked", async () => {
    const onClick = jest.fn();
    const wrapper = create(
      decoratedApollo({
        component: SelectedValue,
        props: { onClick, value: "123" },
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
    const instance = wrapper.root;

    await act(() => wait(100));
    const [element] = instance.findAllByType("div");

    act(() => {
      element.props.onClick();
    });
    expect(onClick).toHaveBeenCalled();
  });
});
