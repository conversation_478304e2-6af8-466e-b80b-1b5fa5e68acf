import { equals, and } from "ramda";
import SearchResult from "../SearchResult";
import "../../../styles/searchable-results.scss";
import { useComponentLogic } from "./hooks";
import { isNullOrEmpty } from "utils/fp";

export const SearchResults = props => {
  const { isEditMode, options } = props;

  return and(isEditMode, !isNullOrEmpty(options)) ? (
    <SearchResultsContent {...props} />
  ) : null;
};

export const SearchResultsContent = props => {
  const {
    question,
    primaryColumn,
    onResultSelected,
    isFavoritable,
    columns = []
  } = props;
  const { currentOptionIndex, mergedOptions, searchResultsRef } =
    useComponentLogic(props);

  return (
    <div className="search-results" ref={searchResultsRef}>
      <table>
        <thead>
          <tr>
            {columns.map(column => (
              <th key={column}>{column}</th>
            ))}
            {isFavoritable && <th>Favorites</th>}
          </tr>
        </thead>
        <tbody>
          {mergedOptions.map((option, i) => (
            <SearchResult
              active={equals(currentOptionIndex, i)}
              key={option.id || option[primaryColumn]}
              option={option}
              primaryColumn={primaryColumn}
              columns={columns}
              question={question}
              isFavoritable={isFavoritable}
              onClick={onResultSelected}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SearchResults;
