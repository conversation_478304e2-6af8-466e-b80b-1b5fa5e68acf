// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SearchResults if no options available and !isEditMode returns null 1`] = `null`;

exports[`SearchResults if no options available and !isEditMode returns null even when is favoritable true 1`] = `null`;

exports[`SearchResults when options available but question with no columns 1`] = `
<div
  className="search-results"
>
  <table>
    <thead>
      <tr />
    </thead>
    <tbody>
      <SearchResult
        active={true}
        columns={Array []}
        onClick={[MockFunction]}
        option={
          Object {
            "code": "23",
            "description": "test1|23",
            "id": "3",
            "name": "test1",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "id": "1",
          }
        }
      />
      <SearchResult
        active={false}
        columns={Array []}
        onClick={[MockFunction]}
        option={
          Object {
            "code": "24",
            "description": "test2|24",
            "id": "4",
            "name": "test2",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "id": "1",
          }
        }
      />
      <SearchResult
        active={false}
        columns={Array []}
        onClick={[MockFunction]}
        option={
          Object {
            "code": "25",
            "description": "test3|25",
            "id": "5",
            "name": "test3",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "id": "1",
          }
        }
      />
    </tbody>
  </table>
</div>
`;

exports[`SearchResults when question and options available 1`] = `
<div
  className="search-results"
>
  <table>
    <thead>
      <tr>
        <th>
          name
        </th>
        <th>
          code
        </th>
      </tr>
    </thead>
    <tbody>
      <SearchResult
        active={true}
        columns={
          Array [
            "name",
            "code",
          ]
        }
        onClick={[MockFunction]}
        option={
          Object {
            "code": "23",
            "description": "test1|23",
            "id": "3",
            "name": "test1",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "columns": Array [
              "name",
              "code",
            ],
            "id": "1",
          }
        }
      />
      <SearchResult
        active={false}
        columns={
          Array [
            "name",
            "code",
          ]
        }
        onClick={[MockFunction]}
        option={
          Object {
            "code": "24",
            "description": "test2|24",
            "id": "4",
            "name": "test2",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "columns": Array [
              "name",
              "code",
            ],
            "id": "1",
          }
        }
      />
      <SearchResult
        active={false}
        columns={
          Array [
            "name",
            "code",
          ]
        }
        onClick={[MockFunction]}
        option={
          Object {
            "code": "25",
            "description": "test3|25",
            "id": "5",
            "name": "test3",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "columns": Array [
              "name",
              "code",
            ],
            "id": "1",
          }
        }
      />
    </tbody>
  </table>
</div>
`;

exports[`SearchResults when question and options available and isFavoritable 1`] = `
<div
  className="search-results"
>
  <table>
    <thead>
      <tr>
        <th>
          name
        </th>
        <th>
          code
        </th>
        <th>
          Favorites
        </th>
      </tr>
    </thead>
    <tbody>
      <SearchResult
        active={true}
        columns={
          Array [
            "name",
            "code",
          ]
        }
        isFavoritable={true}
        onClick={[MockFunction]}
        option={
          Object {
            "__typename": "QuestionEnumerable",
            "code": "23",
            "description": "test1|23",
            "favorites": true,
            "id": "3",
            "name": "test1",
            "questionId": "1",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "columns": Array [
              "name",
              "code",
            ],
            "id": "1",
          }
        }
      />
      <SearchResult
        active={false}
        columns={
          Array [
            "name",
            "code",
          ]
        }
        isFavoritable={true}
        onClick={[MockFunction]}
        option={
          Object {
            "code": "24",
            "description": "test2|24",
            "id": "4",
            "name": "test2",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "columns": Array [
              "name",
              "code",
            ],
            "id": "1",
          }
        }
      />
      <SearchResult
        active={false}
        columns={
          Array [
            "name",
            "code",
          ]
        }
        isFavoritable={true}
        onClick={[MockFunction]}
        option={
          Object {
            "code": "25",
            "description": "test3|25",
            "id": "5",
            "name": "test3",
          }
        }
        primaryColumn="id"
        question={
          Object {
            "columns": Array [
              "name",
              "code",
            ],
            "id": "1",
          }
        }
      />
    </tbody>
  </table>
</div>
`;
