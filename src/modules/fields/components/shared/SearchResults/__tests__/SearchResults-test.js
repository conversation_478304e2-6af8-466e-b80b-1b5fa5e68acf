import TestRenderer from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import GET_FAVORITES from "modules/question/components/Question/EnumerableSearchable/SearchModal/query";
import SearchResults from "..";

jest.mock(
  "modules/fields/components/shared/SearchResult",
  () => "SearchResult"
);

describe("SearchResults", () => {
  const mocks = [
    {
      request: {
        query: GET_FAVORITES,
        variables: { questionId: "1" }
      },
      result: {
        data: {
          favoriteEnumerables: [
            {
              id: "3",
              questionId: "1",
              description: "test1|23",
              expirationOn: 0,
              displayOrder: 0,
              effectiveOn: 0,
              __typename: "QuestionEnumerable"
            }
          ]
        }
      }
    }
  ];
  const questionNoColumns = { id: "1" };
  const questionWithColumns = { id: "1", columns: ["name", "code"] };
  const emptyOptions = [];
  const someOptions = [
    { id: "3", description: "test1|23", name: "test1", code: "23" },
    { id: "4", description: "test2|24", name: "test2", code: "24" },
    { id: "5", description: "test3|25", name: "test3", code: "25" }
  ];
  const getComponent = ({
    question,
    options,
    isEditMode = true,
    isFavoritable
  }) =>
    decoratedApollo({
      component: SearchResults,
      props: {
        isEditMode,
        isFavoritable,
        options,
        question,
        columns: question.columns,
        primaryColumn: "id",
        onResultSelected: jest.fn()
      },
      initialAppValues: {},
      apolloMocks: mocks
    });

  test("if no options available and !isEditMode returns null", async () => {
    const results = TestRenderer.create(
      getComponent({
        isEditMode: false,
        options: emptyOptions,
        question: questionWithColumns
      })
    );

    await wait(100);

    expect(results).toMatchSnapshot();
  });

  test("if no options available and !isEditMode returns null even when is favoritable true", async () => {
    const results = TestRenderer.create(
      getComponent({
        isEditMode: false,
        options: emptyOptions,
        question: questionWithColumns,
        isFavoritable: true
      })
    );

    await wait(100);

    expect(results).toMatchSnapshot();
  });

  test("when options available but question with no columns", async () => {
    const results = TestRenderer.create(
      getComponent({
        options: someOptions,
        question: questionNoColumns
      })
    );

    await wait(100);

    expect(results).toMatchSnapshot();
  });

  test("when question and options available", async () => {
    const results = TestRenderer.create(
      getComponent({
        options: someOptions,
        question: questionWithColumns
      })
    );

    await wait(100);

    expect(results).toMatchSnapshot();
  });

  test("when question and options available and isFavoritable", async () => {
    const results = TestRenderer.create(
      getComponent({
        options: someOptions,
        question: questionWithColumns,
        isFavoritable: true
      })
    );

    await wait(100);

    expect(results).toMatchSnapshot();
  });
});
