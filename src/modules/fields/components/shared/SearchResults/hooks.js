import { useMemo } from "react";
import { useQuery } from "@apollo/client";
import { assoc, map, pipe } from "ramda";
import { mergeFavorites } from "modules/question/components/Question/EnumerableSearchable/SearchModal/enhancers";
import { formatEnumerables } from "modules/question/components/Question/EnumerableSearchable/SearchModal/SearchResult/enhancers";
import GET_FAVORITES from "modules/question/components/Question/EnumerableSearchable/SearchModal/query";
import useSearchableMenuKeyCommands from "../../hooks/useSearchableMenuKeyCommands";

export const useComponentLogic = props => {
  const {
    isFavoritable,
    onResultSelected,
    options,
    primaryColumn = "id",
    columns,
    question
  } = props;
  const { data: { favoriteEnumerables } = { favoriteEnumerables: [] } } =
    useQuery(GET_FAVORITES, {
      variables: { questionId: question.id },
      skip: !isFavoritable
    });
  const mergedOptions = useMemo(
    () =>
      pipe(
        formatEnumerables(columns),
        map(assoc("favorites", true)),
        formattedOptions =>
          mergeFavorites(options, formattedOptions, isFavoritable)
      )(favoriteEnumerables),
    [options, columns, favoriteEnumerables, isFavoritable]
  );

  const { currentOptionIndex, searchResultsRef } = useSearchableMenuKeyCommands(
    {
      options: mergedOptions,
      primaryColumn,
      submit: onResultSelected
    }
  );

  return { currentOptionIndex, mergedOptions, searchResultsRef };
};
