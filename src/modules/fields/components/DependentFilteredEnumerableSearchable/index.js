import SearchableControl from "../shared/Searchable/SearchableControl";
import { useComponentLogic } from "../DependentExternalEnumerableSearchable/hooks";

export const DependentFilteredEnumerableSearchable = props => {
  const additionalProps = useComponentLogic(props, true);

  return (
    <SearchableControl
      {...props}
      {...additionalProps}
      savesValue={false}
      className="dependent-question-searchable"
    />
  );
};

export default DependentFilteredEnumerableSearchable;
