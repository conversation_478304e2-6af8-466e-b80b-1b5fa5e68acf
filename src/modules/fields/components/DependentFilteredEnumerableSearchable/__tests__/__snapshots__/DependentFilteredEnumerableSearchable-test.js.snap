// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DependentFilteredEnumerableSearchable disabled is not set input when false 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "testLoad|0001L",
        },
        Object {
          "description": "testLoad2|0002L",
        },
        Object {
          "description": "testLoad3|0003L",
        },
      ],
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
    }
  }
  questionnaireResponseId={1}
  savesValue={false}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentFilteredEnumerableSearchable disabled is properly set on input when true 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={true}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "testLoad|0001L",
        },
        Object {
          "description": "testLoad2|0002L",
        },
        Object {
          "description": "testLoad3|0003L",
        },
      ],
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
    }
  }
  questionnaireResponseId={1}
  savesValue={false}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentFilteredEnumerableSearchable doesn't load options from backend on render 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={false}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={Array []}
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "testLoad|0001L",
        },
        Object {
          "description": "testLoad2|0002L",
        },
        Object {
          "description": "testLoad3|0003L",
        },
      ],
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
    }
  }
  questionnaireResponseId={1}
  savesValue={false}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;

exports[`DependentFilteredEnumerableSearchable loads correct options from backend 1`] = `
<SearchableControl
  className="dependent-question-searchable"
  columns={
    Array [
      "name",
      "code",
    ]
  }
  controlRef={
    Object {
      "current": null,
    }
  }
  disabled={false}
  dispatch={[Function]}
  focus={[Function]}
  focusRef={
    Object {
      "current": undefined,
    }
  }
  handleChange={[Function]}
  handleInputBlur={[Function]}
  handlePickValue={[Function]}
  handleSave={[Function]}
  handleValueClick={[Function]}
  inputValue=""
  isEditMode={true}
  isFavoritable={true}
  name="1|1"
  onFieldFocus={[Function]}
  options={
    Array [
      Object {
        "code": "0001L",
        "description": "testLoad|0001L",
        "name": "testLoad",
      },
    ]
  }
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "testLoad|0001L",
        },
        Object {
          "description": "testLoad2|0002L",
        },
        Object {
          "description": "testLoad3|0003L",
        },
      ],
      "columns": Array [
        "name",
        "code",
      ],
      "dependentQuestionIds": Array [
        "3",
      ],
      "id": "1",
    }
  }
  questionAnswerGroup={
    Object {
      "id": "2",
    }
  }
  questionnaireResponseId={1}
  savesValue={false}
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "2",
        },
      ],
      "id": "1",
    }
  }
  setInputValue={[Function]}
  setIsEditMode={[Function]}
  setValue={[Function]}
  valueRef={
    Object {
      "current": null,
    }
  }
/>
`;
