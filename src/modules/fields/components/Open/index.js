// import { HotKeys } from "react-hotkeys";
import autoCompleteVal from "utils/getAutoCompleteValue";
import { useTab } from "../hooks/useTab";
import { useSingleValue } from "../hooks/useValue";

export const Open = props => {
  const { disabled, name } = props;
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);
  const { value, handleSave, handleChange } = useSingleValue({
    ...props,
    answerType: "open",
    debounceDelayInMilliseconds: 0
  });

  return (
    <input
      className="question-open"
      disabled={disabled}
      name={name}
      autoComplete={autoCompleteVal}
      value={value || ""}
      type="text"
      onBlur={handleSave}
      onFocus={onFieldFocus}
      onChange={handleChange}
      ref={focusRef}
    />
  );
};

export default Open;
