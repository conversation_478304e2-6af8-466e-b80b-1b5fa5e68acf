// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Open disabled is not set input when false 1`] = `
<input
  autoComplete="off"
  className="question-open"
  disabled={false}
  name="1|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  type="text"
  value=""
/>
`;

exports[`Open disabled is properly set on input when true 1`] = `
<input
  autoComplete="off"
  className="question-open"
  disabled={true}
  name="1|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  type="text"
  value=""
/>
`;

exports[`Open value is set from prestored values 1`] = `
<input
  autoComplete="off"
  className="question-open"
  disabled={false}
  name="2|1"
  onBlur={[Function]}
  onChange={[Function]}
  onFocus={[Function]}
  type="text"
  value="test value"
/>
`;
