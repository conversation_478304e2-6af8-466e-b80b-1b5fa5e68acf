import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Open from "..";

describe("Open", () => {
  const appState = {
    questions: { values: { "2|1": "test value" } },
    status: {},
    questionnaire: {}
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: Open,
        props: {
          name,
          disabled,
          question: { id: "1" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on input when true", () => {
    const open = render({ disabled: true });

    expect(open).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const open = render({ disabled: false });

    expect(open).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const open = render({
      disabled: false,
      name: "2|1"
    });

    expect(open).toMatchSnapshot();
  });
});
