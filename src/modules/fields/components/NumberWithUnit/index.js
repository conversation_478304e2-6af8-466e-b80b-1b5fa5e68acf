import { useSingleValue } from "../hooks/useValue";
import { useTab } from "../hooks/useTab";
import { convertUnitDisplay } from "../../../question/components/Question/NumberWithUnit/convertUnit";
import "../../styles/number-with-unit.scss";

export const NumberWithUnit = props => {
  const { name, disabled, question } = props;
  const { focusRef, onFieldFocus } = useTab(props);
  const { value, handleSave, handleChange } = useSingleValue({
    ...props,
    answerType: "number-with-unit"
  });

  return (
    <>
      <input
        type="text"
        disabled={disabled}
        name={name}
        value={value || ""}
        onBlur={handleSave}
        onFocus={onFieldFocus}
        onChange={handleChange}
        ref={focusRef}
      />
      <span className="conversion-prompt">
        {convertUnitDisplay(question, value)}
      </span>
    </>
  );
};

export default NumberWithUnit;
