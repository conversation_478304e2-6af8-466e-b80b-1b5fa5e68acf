import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import NumberWithUnit from "..";

describe("NumberWithUnit", () => {
  const appState = {
    questions: { values: { "2|1": "145", "3|1": "56" } },
    status: {},
    questionnaire: {}
  };

  const questionHeight = {
    id: "1",
    unit: "cm"
  };

  const questionWeight = {
    id: "2",
    unit: "kg"
  };

  const render = ({ name, disabled, question }) =>
    TestRenderer.create(
      decoratedApollo({
        component: NumberWithUnit,
        props: {
          disabled,
          question,
          name
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("it renders the correct value with units(cm)", () => {
    const component = render({
      disabled: false,
      name: "2|1",
      question: questionHeight
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders the correct value with units(kg)", () => {
    const component = render({
      disabled: false,
      name: "3|1",
      question: questionWeight
    });

    expect(component).toMatchSnapshot();
  });
});
