// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NumberWithUnit it renders the correct value with units(cm) 1`] = `
Array [
  <input
    disabled={false}
    name="2|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value="145"
  />,
  <span
    className="conversion-prompt"
  >
    145 cm
  </span>,
]
`;

exports[`NumberWithUnit it renders the correct value with units(kg) 1`] = `
Array [
  <input
    disabled={false}
    name="3|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value="56"
  />,
  <span
    className="conversion-prompt"
  >
    56 kg
  </span>,
]
`;
