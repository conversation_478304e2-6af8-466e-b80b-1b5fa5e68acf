import shallowRender from "utils/shallowRender";
import EnumerableItem from "..";

describe("EnumerableItem", () => {
  const render = ({ value, disabled }) =>
    shallowRender(
      <EnumerableItem
        disabled={disabled}
        enumerable={{ id: "3", description: "NA" }}
        focusRef={{}}
        name="1|1"
        value={value}
        onChange={jest.fn()}
        onFocus={jest.fn()}
      />
    );

  test("disabled is properly set to input when true", () => {
    const enumerable = render({ disabled: true });

    expect(enumerable).toMatchSnapshot();
  });

  test("disabled is not set to input when false", () => {
    const enumerable = render({ disabled: false });

    expect(enumerable).toMatchSnapshot();
  });

  test("input checked when id equals value", () => {
    const enumerable = render({ disabled: false, value: "3" });

    expect(enumerable).toMatchSnapshot();
  });

  test("input not checked when id different value", () => {
    const enumerable = render({ disabled: false, value: "2" });

    expect(enumerable).toMatchSnapshot();
  });
  test("input text is highlighted when selected", () => {
    const enumerable = render({
      disabled: false,
      value: "3"
    });

    expect(enumerable).toMatchSnapshot();
  });
});
