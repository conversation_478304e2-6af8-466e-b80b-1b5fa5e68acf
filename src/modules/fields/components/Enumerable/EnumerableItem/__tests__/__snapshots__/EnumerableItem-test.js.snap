// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumerableItem disabled is not set to input when false 1`] = `
<label
  className="radio-button"
>
  <input
    checked={false}
    disabled={false}
    name="1|1"
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    type="radio"
    value="3"
  />
  <span
    className=""
  >
    NA
  </span>
</label>
`;

exports[`EnumerableItem disabled is properly set to input when true 1`] = `
<label
  className="radio-button"
>
  <input
    checked={false}
    disabled={true}
    name="1|1"
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    type="radio"
    value="3"
  />
  <span
    className=""
  >
    NA
  </span>
</label>
`;

exports[`EnumerableItem input checked when id equals value 1`] = `
<label
  className="radio-button"
>
  <input
    checked={true}
    disabled={false}
    name="1|1"
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    type="radio"
    value="3"
  />
  <span
    className="highlight"
  >
    NA
  </span>
</label>
`;

exports[`EnumerableItem input not checked when id different value 1`] = `
<label
  className="radio-button"
>
  <input
    checked={false}
    disabled={false}
    name="1|1"
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    type="radio"
    value="3"
  />
  <span
    className=""
  >
    NA
  </span>
</label>
`;

exports[`EnumerableItem input text is highlighted when selected 1`] = `
<label
  className="radio-button"
>
  <input
    checked={true}
    disabled={false}
    name="1|1"
    onChange={[MockFunction]}
    onFocus={[MockFunction]}
    type="radio"
    value="3"
  />
  <span
    className="highlight"
  >
    NA
  </span>
</label>
`;
