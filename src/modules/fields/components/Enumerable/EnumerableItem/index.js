import classnames from "classnames";

export const EnumerableItem = props => {
  const { disabled, enumerable, focusRef, name, value, onChange, onFocus } =
    props;

  const shouldHighlightText = classnames({
    highlight: value === enumerable.id
  });

  return (
    <label className="radio-button">
      <input
        disabled={disabled}
        name={name}
        type="radio"
        value={enumerable.id}
        checked={value === enumerable.id}
        onChange={onChange}
        onFocus={onFocus}
        ref={focusRef}
      />
      <span className={shouldHighlightText}>{enumerable.description}</span>
    </label>
  );
};

export default EnumerableItem;
