import { FormattedMessage } from "react-intl";
import { pipe, prop, sortWith, defaultTo, ascend } from "ramda";
import { parseNumber } from "utils/fp/parse";
import { useManageQuestionnaireFocus } from "../../hooks/useFocus";
import { useSingleValue } from "../../hooks/useValue";

export const DropList = props => {
  const {
    disabled,
    name,
    question: { activeEnumerables }
  } = props;
  const { focusRef, onFieldFocus } = useManageQuestionnaireFocus(props);
  const { value, handleSave } = useSingleValue({
    ...props,
    answerType: "enumerable"
  });
  const sortedEnumerables = sortWith(
    [
      ascend(pipe(prop("displayOrder"), defaultTo("0"), parseNumber)),
      ascend(pipe(prop("description")))
    ],
    activeEnumerables
  );

  return (
    <select
      className="question-enumerable question-droplist"
      disabled={disabled}
      name={name}
      value={value}
      onChange={handleSave}
      onFocus={onFieldFocus}
      ref={focusRef}
    >
      <FormattedMessage
        id="application.select_text"
        defaultMessage="Please select ..."
      >
        {txt => <option value="">{txt}</option>}
      </FormattedMessage>
      {sortedEnumerables &&
        sortedEnumerables.map(enumerable => (
          <option key={enumerable.id} value={enumerable.id}>
            {enumerable.description}
          </option>
        ))}
    </select>
  );
};

export default DropList;
