// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DropList disabled is properly passed select when true 1`] = `
<select
  className="question-enumerable question-droplist"
  disabled={true}
  name="1|1"
  onChange={[Function]}
  onFocus={[Function]}
>
  <option
    value=""
  >
    Please select ...
  </option>
  <option
    value="3"
  >
    NA
  </option>
  <option
    value="2"
  >
    No
  </option>
  <option
    value="1"
  >
    Yes
  </option>
</select>
`;

exports[`DropList disabled is properly passed to select when false 1`] = `
<select
  className="question-enumerable question-droplist"
  disabled={false}
  name="1|1"
  onChange={[Function]}
  onFocus={[Function]}
>
  <option
    value=""
  >
    Please select ...
  </option>
  <option
    value="3"
  >
    NA
  </option>
  <option
    value="2"
  >
    No
  </option>
  <option
    value="1"
  >
    Yes
  </option>
</select>
`;

exports[`DropList value is set from prestored values 1`] = `
<select
  className="question-enumerable question-droplist"
  disabled={false}
  name="2|1"
  onChange={[Function]}
  onFocus={[Function]}
  value="3"
>
  <option
    value=""
  >
    Please select ...
  </option>
  <option
    value="3"
  >
    NA
  </option>
  <option
    value="2"
  >
    No
  </option>
  <option
    value="1"
  >
    Yes
  </option>
</select>
`;
