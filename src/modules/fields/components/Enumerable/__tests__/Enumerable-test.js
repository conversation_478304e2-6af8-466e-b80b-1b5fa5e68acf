import shallowRender from "utils/shallowRender";
import Enumerable from "..";

describe("Enumerable", () => {
  const render = ({ activeEnumerables }) =>
    shallowRender(<Enumerable question={{ id: "1", activeEnumerables }} />);

  test("renders Boolean if only 2 enumerables that are yes and no", () => {
    const activeEnumerables = [{ description: "Yes" }, { description: "No" }];

    expect(render({ activeEnumerables })).toMatchSnapshot();
  });

  test("renders Standard if only 2 enumerables that are not yes and no", () => {
    const activeEnumerables = [{ description: "Yes" }, { description: "NA" }];

    expect(render({ activeEnumerables })).toMatchSnapshot();
  });

  test("renders Standard if less than 10 enumerables", () => {
    const activeEnumerables = [
      { description: "Yes" },
      { description: "No" },
      { description: "NA" },
      { description: "4" },
      { description: "5" },
      { description: "6" },
      { description: "7" },
      { description: "8" },
      { description: "9" }
    ];

    expect(render({ activeEnumerables })).toMatchSnapshot();
  });

  test("renders DropList if 10 or more enumerables", () => {
    const activeEnumerables = [
      { description: "Yes" },
      { description: "No" },
      { description: "NA" },
      { description: "4" },
      { description: "5" },
      { description: "6" },
      { description: "7" },
      { description: "8" },
      { description: "9" },
      { description: "10" }
    ];

    expect(render({ activeEnumerables })).toMatchSnapshot();
  });
});
