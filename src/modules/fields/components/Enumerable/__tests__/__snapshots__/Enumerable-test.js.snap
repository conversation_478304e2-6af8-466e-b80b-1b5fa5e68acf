// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Enumerable renders Boolean if only 2 enumerables that are yes and no 1`] = `
<Boolean
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "Yes",
        },
        Object {
          "description": "No",
        },
      ],
      "id": "1",
    }
  }
/>
`;

exports[`Enumerable renders DropList if 10 or more enumerables 1`] = `
<DropList
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "Yes",
        },
        Object {
          "description": "No",
        },
        Object {
          "description": "NA",
        },
        Object {
          "description": "4",
        },
        Object {
          "description": "5",
        },
        Object {
          "description": "6",
        },
        Object {
          "description": "7",
        },
        Object {
          "description": "8",
        },
        Object {
          "description": "9",
        },
        Object {
          "description": "10",
        },
      ],
      "id": "1",
    }
  }
/>
`;

exports[`Enumerable renders Standard if less than 10 enumerables 1`] = `
<Standard
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "Yes",
        },
        Object {
          "description": "No",
        },
        Object {
          "description": "NA",
        },
        Object {
          "description": "4",
        },
        Object {
          "description": "5",
        },
        Object {
          "description": "6",
        },
        Object {
          "description": "7",
        },
        Object {
          "description": "8",
        },
        Object {
          "description": "9",
        },
      ],
      "id": "1",
    }
  }
/>
`;

exports[`Enumerable renders Standard if only 2 enumerables that are not yes and no 1`] = `
<Standard
  question={
    Object {
      "activeEnumerables": Array [
        Object {
          "description": "Yes",
        },
        Object {
          "description": "NA",
        },
      ],
      "id": "1",
    }
  }
/>
`;
