import {
  always,
  all,
  both,
  cond,
  either,
  equals,
  length,
  lte,
  pipe,
  prop,
  toLower,
  T
} from "ramda";
import Boolean from "./Boolean";
import Standard from "./Standard";
import DropList from "./DropList";
import "../../styles/enumerable.scss";
// import TableRow from "./TableRow";
// import Inline from "./Inline";

const getEnumerableType = enumerables => {
  const isBoolean = both(
    pipe(length, equals(2)),
    all(pipe(prop("description"), toLower, either(equals("yes"), equals("no"))))
  );

  return cond([
    [isBoolean, always(Boolean)],
    [pipe(length, lte(10)), always(DropList)],
    [T, always(Standard)]
  ])(enumerables);
};

export const Enumerable = props => {
  const {
    question: { activeEnumerables }
  } = props;
  const EnumerableType = getEnumerableType(activeEnumerables);

  return <EnumerableType {...props} />;
};

export default Enumerable;
