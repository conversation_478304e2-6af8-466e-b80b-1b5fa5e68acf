// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumerableItems disabled is properly passed to EnumerableItem when false 1`] = `
<div
  className="enumerable-items"
>
  <EnumerableItem
    disabled={false}
    enumerable={
      Object {
        "description": "Yes",
        "id": "1",
      }
    }
    focusRef={
      Object {
        "current": undefined,
      }
    }
    name="1|1"
    onChange={[Function]}
    onFocus={[Function]}
  />
  <EnumerableItem
    disabled={false}
    enumerable={
      Object {
        "description": "No",
        "id": "2",
      }
    }
    name="1|1"
    onChange={[Function]}
    onFocus={[Function]}
  />
  <EnumerableItem
    disabled={false}
    enumerable={
      Object {
        "description": "NA",
        "id": "3",
      }
    }
    name="1|1"
    onChange={[Function]}
    onFocus={[Function]}
  />
</div>
`;

exports[`EnumerableItems disabled is properly passed to EnumerableItem when true 1`] = `
<div
  className="enumerable-items"
>
  <EnumerableItem
    disabled={true}
    enumerable={
      Object {
        "description": "Yes",
        "id": "1",
      }
    }
    focusRef={
      Object {
        "current": undefined,
      }
    }
    name="1|1"
    onChange={[Function]}
    onFocus={[Function]}
  />
  <EnumerableItem
    disabled={true}
    enumerable={
      Object {
        "description": "No",
        "id": "2",
      }
    }
    name="1|1"
    onChange={[Function]}
    onFocus={[Function]}
  />
  <EnumerableItem
    disabled={true}
    enumerable={
      Object {
        "description": "NA",
        "id": "3",
      }
    }
    name="1|1"
    onChange={[Function]}
    onFocus={[Function]}
  />
</div>
`;

exports[`EnumerableItems value is set from prestored values 1`] = `
<div
  className="enumerable-items"
>
  <EnumerableItem
    disabled={false}
    enumerable={
      Object {
        "description": "Yes",
        "id": "1",
      }
    }
    focusRef={
      Object {
        "current": undefined,
      }
    }
    name="2|1"
    onChange={[Function]}
    onFocus={[Function]}
    value="3"
  />
  <EnumerableItem
    disabled={false}
    enumerable={
      Object {
        "description": "No",
        "id": "2",
      }
    }
    name="2|1"
    onChange={[Function]}
    onFocus={[Function]}
    value="3"
  />
  <EnumerableItem
    disabled={false}
    enumerable={
      Object {
        "description": "NA",
        "id": "3",
      }
    }
    name="2|1"
    onChange={[Function]}
    onFocus={[Function]}
    value="3"
  />
</div>
`;
