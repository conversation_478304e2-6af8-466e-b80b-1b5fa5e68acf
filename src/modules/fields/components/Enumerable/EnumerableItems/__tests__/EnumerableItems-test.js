import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import EnumerableItems from "..";

jest.mock(
  "modules/fields/components/Enumerable/EnumerableItem",
  () => "EnumerableItem"
);

describe("EnumerableItems", () => {
  const appState = {
    questions: { values: { "2|1": "3" } },
    status: {},
    questionnaire: {}
  };
  const activeEnumerables = [
    { id: "1", description: "Yes" },
    { id: "2", description: "No" },
    { id: "3", description: "NA" }
  ];

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: EnumerableItems,
        props: {
          name,
          disabled,
          question: { id: "1", activeEnumerables },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly passed to EnumerableItem when true", () => {
    const enumerable = render({ disabled: true });

    expect(enumerable).toMatchSnapshot();
  });

  test("disabled is properly passed to EnumerableItem when false", () => {
    const enumerable = render({ disabled: false });

    expect(enumerable).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const enumerable = render({
      disabled: false,
      name: "2|1"
    });

    expect(enumerable).toMatchSnapshot();
  });
});
