import { useManageQuestionnaireFocus } from "../../hooks/useFocus";
import { useSingleValue } from "../../hooks/useValue";
import EnumerableItem from "../EnumerableItem";

export const EnumerableItems = props => {
  const {
    question: { activeEnumerables },
    disabled,
    name
  } = props;
  const { focusRef, onFieldFocus } = useManageQuestionnaireFocus(props);
  const { value, handleSave } = useSingleValue({
    ...props,
    answerType: "enumerable"
  });

  return (
    <div className="enumerable-items">
      {activeEnumerables &&
        activeEnumerables.map((enumerable, index) => (
          <EnumerableItem
            key={enumerable.id}
            name={name}
            disabled={disabled}
            enumerable={enumerable}
            value={value}
            focusRef={index === 0 ? focusRef : undefined}
            onFocus={onFieldFocus}
            onChange={handleSave}
          />
        ))}
    </div>
  );
};

export default EnumerableItems;
