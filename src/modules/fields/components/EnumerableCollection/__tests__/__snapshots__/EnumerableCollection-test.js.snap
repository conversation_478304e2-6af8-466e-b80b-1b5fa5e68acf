// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumerableCollection disabled is properly passed to input when false 1`] = `
<div
  className="question-enumerable-collection"
>
  <div
    className="enumerable-items"
  >
    <div
      className="label-radio-checkbox"
    >
      <input
        disabled={false}
        name="1|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="1"
      />
      <span>
        Yes
      </span>
    </div>
    <div
      className="label-radio-checkbox"
    >
      <input
        disabled={false}
        name="1|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="2"
      />
      <span>
        No
      </span>
    </div>
    <div
      className="label-radio-checkbox"
    >
      <input
        disabled={false}
        name="1|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="3"
      />
      <span>
        NA
      </span>
    </div>
  </div>
</div>
`;

exports[`EnumerableCollection disabled is properly passed to input when true 1`] = `
<div
  className="question-enumerable-collection"
>
  <div
    className="enumerable-items"
  >
    <div
      className="label-radio-checkbox"
    >
      <input
        disabled={true}
        name="1|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="1"
      />
      <span>
        Yes
      </span>
    </div>
    <div
      className="label-radio-checkbox"
    >
      <input
        disabled={true}
        name="1|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="2"
      />
      <span>
        No
      </span>
    </div>
    <div
      className="label-radio-checkbox"
    >
      <input
        disabled={true}
        name="1|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="3"
      />
      <span>
        NA
      </span>
    </div>
  </div>
</div>
`;

exports[`EnumerableCollection value is set from prestored values and correct inputs checked 1`] = `
<div
  className="question-enumerable-collection"
>
  <div
    className="enumerable-items"
  >
    <div
      className="label-radio-checkbox"
    >
      <input
        checked={true}
        disabled={false}
        name="2|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="1"
      />
      <span>
        Yes
      </span>
    </div>
    <div
      className="label-radio-checkbox"
    >
      <input
        checked={false}
        disabled={false}
        name="2|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="2"
      />
      <span>
        No
      </span>
    </div>
    <div
      className="label-radio-checkbox"
    >
      <input
        checked={true}
        disabled={false}
        name="2|1"
        onChange={[Function]}
        onFocus={[Function]}
        type="checkbox"
        value="3"
      />
      <span>
        NA
      </span>
    </div>
  </div>
</div>
`;
