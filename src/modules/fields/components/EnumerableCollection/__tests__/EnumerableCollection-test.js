import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import EnumerableCollection from "..";

describe("EnumerableCollection", () => {
  const appState = {
    questions: { values: { "2|1": ["3", "1"] } },
    status: {},
    questionnaire: {}
  };
  const activeEnumerables = [
    { id: "1", description: "Yes" },
    { id: "2", description: "No" },
    { id: "3", description: "NA" }
  ];

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: EnumerableCollection,
        props: {
          name,
          disabled,
          question: { id: "1", activeEnumerables },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly passed to input when true", () => {
    const enumerable = render({ disabled: true });

    expect(enumerable).toMatchSnapshot();
  });

  test("disabled is properly passed to input when false", () => {
    const enumerable = render({ disabled: false });

    expect(enumerable).toMatchSnapshot();
  });

  test("value is set from prestored values and correct inputs checked", () => {
    const enumerable = render({
      disabled: false,
      name: "2|1"
    });

    expect(enumerable).toMatchSnapshot();
  });
});
