// import { HotKeys } from "react-hotkeys";
import { isChecked } from "modules/question/components/Question/EnumerableCollection/convertCheckboxValues";
import { useManageQuestionnaireFocus } from "../hooks/useFocus";
import { useArrayValue } from "../hooks/useValue";
import "../../styles/enumerable.scss";
import classnames from "classnames";

export const EnumerableCollection = props => {
  const {
    question: { activeEnumerables },
    disabled,
    name,
    columnSpan
  } = props;
  const { focusRef, onFieldFocus } = useManageQuestionnaireFocus(props);
  const { value, handleSave } = useArrayValue({
    ...props,
    answerType: "enumerable-collection"
  });
  const columnRadioCheckboxClass = classnames("label-radio-checkbox", {
    [`col-${columnSpan}`]: columnSpan
  });

  return (
    <div className="question-enumerable-collection">
      <div className="enumerable-items">
        {activeEnumerables &&
          activeEnumerables.map((enumerable, index) => (
            <div key={enumerable.id} className={columnRadioCheckboxClass}>
              <input
                type="checkbox"
                name={name}
                value={enumerable.id}
                onChange={handleSave}
                checked={isChecked(enumerable.id, value)}
                onFocus={onFieldFocus}
                disabled={disabled}
                ref={index === 0 ? focusRef : null}
              />
              <span>{enumerable.description}</span>
            </div>
          ))}
      </div>
    </div>
  );
};

export default EnumerableCollection;
