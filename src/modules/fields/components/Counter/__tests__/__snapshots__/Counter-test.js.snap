// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Counter component renders correctly when saving 1`] = `
<div
  className="question-counter counter-header"
>
  <div
    className="counter-saving"
  >
    <i
      className="fa fa-spinner fa-spin"
    />
     Loading...
  </div>
</div>
`;

exports[`Counter component renders correctly when value set 1`] = `
<div
  className="question-counter counter-header"
>
  <div
    className="counter-header-left"
  >
    <div
      className="counter-value"
    >
      1
    </div>
     Counter
  </div>
  <div
    className="counter-header-right"
  >
    <RemoveInstance
      disabled={false}
      dispatch={[Function]}
      name="2|1"
      question={
        Object {
          "id": "1",
        }
      }
      questionAnswerGroup={
        Object {
          "id": "1",
        }
      }
    />
  </div>
</div>
`;

exports[`Counter disabled is properly set on when true 1`] = `
<div
  className="question-counter counter-header"
>
  <AddInstance
    disabled={true}
    dispatch={[Function]}
    name="1|1"
    question={
      Object {
        "id": "1",
      }
    }
    questionAnswerGroup={
      Object {
        "id": "1",
      }
    }
  />
</div>
`;

exports[`Counter disabled is properly set when false 1`] = `
<div
  className="question-counter counter-header"
>
  <AddInstance
    disabled={false}
    dispatch={[Function]}
    name="1|1"
    question={
      Object {
        "id": "1",
      }
    }
    questionAnswerGroup={
      Object {
        "id": "1",
      }
    }
  />
</div>
`;
