import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Counter from "..";

jest.mock("modules/fields/components/Counter/AddInstance", () => "AddInstance");
jest.mock(
  "modules/fields/components/Counter/RemoveInstance",
  () => "RemoveInstance"
);

describe("Counter", () => {
  const appState = {
    status: { mutationLoading: { "3|1": true } },
    questionnaire: { answers: { "2|1": { answerData: 1 } } }
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: Counter,
        props: {
          name,
          disabled,
          question: { id: "1" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on when true", () => {
    const counter = render({ disabled: true });

    expect(counter).toMatchSnapshot();
  });

  test("disabled is properly set when false", () => {
    const counter = render({ disabled: false });

    expect(counter).toMatchSnapshot();
  });

  test("component renders correctly when value set", () => {
    const counter = render({
      disabled: false,
      name: "2|1"
    });

    expect(counter).toMatchSnapshot();
  });

  test("component renders correctly when saving", () => {
    const counter = render({
      disabled: false,
      name: "3|1"
    });

    expect(counter).toMatchSnapshot();
  });
});
