import { useComponentLogic } from "./hooks";
import AddInstance from "./AddInstance";
import { useIsSaving } from "../hooks/useSaveAnswer";
import RemoveInstance from "./RemoveInstance";
import "../../styles/counter.scss";
import { identity, ifElse } from "ramda";

const Saving = () => (
  <div className="counter-saving">
    <i className="fa fa-spinner fa-spin" /> Loading...
  </div>
);

export function Counter(props) {
  const { name } = props;
  const isSaving = useIsSaving(name);
  const { isLast, value } = useComponentLogic(props);

  return (
    <div className="question-counter counter-header">
      {ifElse(
        identity,
        () => <Saving />,
        () =>
          isLast ? (
            <AddInstance {...props} />
          ) : (
            <>
              <div className="counter-header-left">
                <div className="counter-value">{value}</div> Counter
              </div>
              <div className="counter-header-right">
                <RemoveInstance {...props} />
              </div>
            </>
          )
      )(isSaving)}
    </div>
  );
}

export default Counter;
