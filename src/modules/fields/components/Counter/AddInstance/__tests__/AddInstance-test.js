import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import AddInstance from "..";

describe("AddInstance", () => {
  const appState = {
    status: { mutationLoading: { "3|1": true } },
    questionnaire: { answers: { "2|1": { answerData: 1 } } },
    questions: {}
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: AddInstance,
        props: {
          name,
          disabled,
          question: { id: "1" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on when true", () => {
    const addInstance = render({ disabled: true });

    expect(addInstance).toMatchSnapshot();
  });

  test("disabled is properly set when false", () => {
    const addInstance = render({ disabled: false });

    expect(addInstance).toMatchSnapshot();
  });
});
