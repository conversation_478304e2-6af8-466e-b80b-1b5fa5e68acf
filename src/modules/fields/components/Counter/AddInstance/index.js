import { useTab } from "../../hooks/useTab";
import { useComponentLogic } from "./hooks";

export const AddInstance = props => {
  const { disabled, question } = props;
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);
  const { addInstance } = useComponentLogic(props);

  return (
    <div
      className="counter-button"
      disabled={disabled}
      onClick={addInstance}
      onFocus={onFieldFocus}
      ref={focusRef}
    >
      <i className="fal fa-plus-circle" /> {question.prompt}
    </div>
  );
};

export default AddInstance;
