import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import {
  getCounterAnswers,
  getNextValue
} from "modules/question/components/Question/Counter/enhancers";
import { useSingleValue } from "../../hooks/useValue";

export const useComponentLogic = props => {
  const { answerGroup, question } = props;
  const answers = useSelector(state => Questionnaire.getAnswers(state));
  const counterAnswers = getCounterAnswers(question, answers, answerGroup);
  const nextValue = getNextValue(counterAnswers);
  const { handleSave } = useSingleValue({ ...props, answerType: "counter" });

  const addInstance = event => {
    if (event) {
      event.preventDefault();
    }
    handleSave({ target: { value: nextValue } });
  };

  return { addInstance };
};
