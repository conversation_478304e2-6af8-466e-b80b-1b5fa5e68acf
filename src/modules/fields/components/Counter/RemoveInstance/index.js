import { useTab } from "../../hooks/useTab";
import { useComponentLogic } from "./hooks";

export const RemoveInstance = props => {
  const { deleteInstance } = useComponentLogic(props);
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);

  return (
    <i
      className="fal fa-trash-alt action"
      onClick={deleteInstance}
      onFocus={onFieldFocus}
      ref={focusRef}
    />
  );
};

export default RemoveInstance;
