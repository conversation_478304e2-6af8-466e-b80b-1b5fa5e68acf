import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import RemoveInstance from "..";

describe("RemoveInstance", () => {
  const appState = {
    status: {},
    questionnaire: {},
    questions: {}
  };

  const render = () =>
    TestRenderer.create(
      decoratedApollo({
        component: RemoveInstance,
        props: {
          question: { id: "1" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("renders correctly", () => {
    const addInstance = render();

    expect(addInstance).toMatchSnapshot();
  });
});
