import Test<PERSON>enderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import TextArea from "..";

describe("TextArea", () => {
  const appState = {
    questions: { values: { "2|1": "test value" } },
    status: {},
    questionnaire: {}
  };

  const render = ({ name = "1|1", disabled, maxCharacters }) =>
    TestRenderer.create(
      decoratedApollo({
        component: TextArea,
        props: {
          name,
          disabled,
          question: { id: "1", maxCharacters },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on input when true", () => {
    const field = render({ disabled: true });

    expect(field).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const field = render({ disabled: false });

    expect(field).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const field = render({
      disabled: false,
      name: "2|1"
    });

    expect(field).toMatchSnapshot();
  });

  test("shows character counter when maxCharacter defined", () => {
    const field = render({
      disabled: false,
      name: "2|1",
      maxCharacters: 100
    });

    expect(field).toMatchSnapshot();
  });

  test("shows character counter as error when maxCharacters is smaller", () => {
    const field = render({
      disabled: false,
      name: "2|1",
      maxCharacters: 5
    });

    expect(field).toMatchSnapshot();
  });
});
