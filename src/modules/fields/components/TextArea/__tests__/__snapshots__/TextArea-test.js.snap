// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextArea disabled is not set input when false 1`] = `
<div
  className="question-text-area"
>
  <textarea
    disabled={false}
    name="1|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value=""
  />
</div>
`;

exports[`TextArea disabled is properly set on input when true 1`] = `
<div
  className="question-text-area"
>
  <textarea
    disabled={true}
    name="1|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value=""
  />
</div>
`;

exports[`TextArea shows character counter as error when maxCharacters is smaller 1`] = `
<div
  className="question-text-area"
>
  <div
    className="counter error"
  >
    10
    /
    5
  </div>
  <textarea
    disabled={false}
    maxLength={5}
    name="2|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value="test value"
  />
</div>
`;

exports[`Text<PERSON>rea shows character counter when maxCharacter defined 1`] = `
<div
  className="question-text-area"
>
  <div
    className="counter"
  >
    10
    /
    100
  </div>
  <textarea
    disabled={false}
    maxLength={100}
    name="2|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value="test value"
  />
</div>
`;

exports[`TextArea value is set from prestored values 1`] = `
<div
  className="question-text-area"
>
  <textarea
    disabled={false}
    name="2|1"
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    type="text"
    value="test value"
  />
</div>
`;
