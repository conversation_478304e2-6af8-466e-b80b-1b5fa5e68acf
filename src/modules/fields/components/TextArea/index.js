import classnames from "classnames";
// import { HotKeys } from "react-hotkeys";
import { useTab } from "../hooks/useTab";
import { useSingleValue } from "../hooks/useValue";
import "../../styles/textarea.scss";
import { defaultTo, length } from "ramda";

export const TextArea = props => {
  const { disabled, name, question } = props;
  const { maxCharacters } = question;
  const { focusRef, /* handlers, */ onFieldFocus } = useTab(props);
  const { value, handleSave, handleChange } = useSingleValue({
    ...props,
    answerType: "text-area"
  });
  const realValue = defaultTo("", value);
  const currentCharacters = length(realValue);
  const counterClasses = classnames("counter", {
    error: maxCharacters && currentCharacters > maxCharacters
  });

  return (
    <div className="question-text-area">
      {maxCharacters && (
        <div className={counterClasses}>
          {currentCharacters}/{maxCharacters}
        </div>
      )}
      <textarea
        disabled={disabled}
        name={name}
        value={realValue}
        type="text"
        onBlur={handleSave}
        onFocus={onFieldFocus}
        onChange={handleChange}
        ref={focusRef}
        maxLength={maxCharacters}
      />
    </div>
  );
};

export default TextArea;
