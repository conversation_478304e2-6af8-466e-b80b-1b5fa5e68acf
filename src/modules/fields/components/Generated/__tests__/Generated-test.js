import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import Generated from "..";

describe("Generated", () => {
  const appState = {
    questions: { values: { "2|1": "test value", "3|1": "generated value" } },
    status: {},
    questionnaire: {
      answers: {
        "2|1": { answerData: "test value", answerType: "open" },
        "3|1": { answerData: "generated value", answerType: "generated" }
      }
    }
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: Generated,
        props: {
          name,
          disabled
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("renders answer value as read only", () => {
    const calc = render({
      disabled: false,
      name: "3|1"
    });

    expect(calc).toMatchSnapshot();
  });
});
