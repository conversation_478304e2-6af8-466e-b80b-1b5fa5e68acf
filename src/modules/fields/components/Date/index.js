// import { HotKeys } from "react-hotkeys";
import autoCompleteVal from "utils/getAutoCompleteValue";
import DatePicker from "react-datepicker";
import dateFormats from "modules/question/constants/dateFormats";
import { useComponentLogic } from "./hooks";
import "react-datepicker/dist/react-datepicker.css";
import "../../styles/date.scss";

const DateInternalInput = props => {
  const {
    value: inputValue,
    name: inputName,
    disabled: inputDisabled,
    onChange,
    onFocus,
    onBlur,
    setTextInputRef,
    onKeyDown,
    placeholder
  } = props;

  return (
    <div className="input-icon">
      <input
        type="text"
        value={inputValue || ""}
        name={inputName}
        autoComplete={autoCompleteVal}
        disabled={inputDisabled}
        className="input-field"
        ref={setTextInputRef}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        placeholder={placeholder}
      />
      <i className="fa fa-calendar icon" onClick={onFocus} />
    </div>
  );
};

export const Date = props => {
  const { disabled, name } = props;

  const {
    value,
    focusRef,
    onFieldFocus,
    handleBlur,
    handleChange,
    handleSelect,
    pickerRef
  } = useComponentLogic(props);

  return (
    <DatePicker
      customInput={<DateInternalInput setTextInputRef={focusRef} />}
      disabled={disabled}
      name={name}
      selected={value || null}
      placeholderText="mmddyyyy, mm/dd/yyyy"
      dateFormat={dateFormats}
      popperPlacement="right"
      onBlur={handleBlur}
      onFocus={onFieldFocus}
      onChange={handleChange}
      onSelect={handleSelect}
      ref={pickerRef}
      popperModifiers={[
        {
          name: "offset",
          options: {
            offset: [5, 10]
          }
        }
      ]}
    />
  );
};

export default Date;
