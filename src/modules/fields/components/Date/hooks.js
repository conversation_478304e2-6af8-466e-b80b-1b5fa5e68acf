import { useCallback, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { and, both, identity, prop } from "ramda";
import Questionnaire from "modules/questionnaire/selectors";
import Question from "modules/question/selectors";
import { setFieldValue } from "modules/question/actions";
import { useTab } from "../hooks/useTab";
import {
  cleanAndFocus,
  formatTextInput,
  validateInput
} from "modules/question/components/Question/shared/DateInput/enhancers";
import {
  getInitialDate,
  saveDate
} from "modules/question/components/Question/shared/DateInput/DateTransformations";
import { useSaveSingleAnswer } from "../hooks/useSaveAnswer";

export const useInitDate = ({ name, fieldName }) => {
  // DB/Backend saved answer
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const dispatch = useDispatch();
  const setValue = useCallback(
    value => dispatch(setFieldValue(name, value)),
    [dispatch, name]
  );
  // FE stored answer data, can be partial a value
  const value = useSelector(state => Question.getFieldValue(name, state));

  // Initialize FE store with answer value when value is undefined
  useEffect(() => {
    if (and(value === undefined, both(identity, prop(fieldName))(answer))) {
      const initialValue = getInitialDate(prop(fieldName, answer));

      setValue(initialValue);
    }
  }, [value, answer, fieldName, setValue]);

  return { answer, value, setValue };
};

export const useComponentLogic = props => {
  const pickerRef = useRef(null);
  const { focusRef, /* handlers, */ onFieldFocus, focus } = useTab(props);
  const { name } = props;
  const { answer, value, setValue } = useInitDate({
    name,
    fieldName: "answerData"
  });
  const { answerQuestion } = useSaveSingleAnswer({ ...props, answer });
  const handleSave = useCallback(
    date => {
      saveDate(answerQuestion, date);
    },
    [answerQuestion]
  );
  const handleChangeDateInput = useCallback(
    date => {
      setValue(date);
    },
    [setValue]
  );
  const handleBlur = useCallback(() => {
    if (validateInput({ setValue, value, textInput: focusRef.current })) {
      handleSave(value);
      formatTextInput({ value, datepicker: pickerRef.current });
    } else {
      // If input is invalid, clean value and set focus on field so user can correct it
      cleanAndFocus({ setValue, textInput: focusRef.current });
    }
  }, [focusRef, pickerRef, handleSave, setValue, value]);

  const handleSelect = useCallback(() => {
    setTimeout(() => focus(), 1);
  }, [focus]);

  return {
    value,
    focusRef,
    pickerRef,
    onFieldFocus,
    handleChange: handleChangeDateInput,
    handleBlur,
    handleSelect
  };
};
