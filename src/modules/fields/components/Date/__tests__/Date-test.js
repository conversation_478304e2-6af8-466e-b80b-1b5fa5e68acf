import moment from "moment";
import TestRenderer from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Date from "..";

describe("Date", () => {
  const appState = {
    questions: { values: { "2|1": moment("12/12/2012") } },
    status: {},
    questionnaire: {}
  };

  const render = ({ name = "1|1", disabled }) =>
    TestRenderer.create(
      decoratedApollo({
        component: Date,
        props: {
          name,
          disabled,
          question: { id: "1" },
          questionAnswerGroup: { id: "1" }
        },
        initialAppValues: appState,
        apolloMocks: []
      })
    );

  test("disabled is properly set on input when true", () => {
    const date = render({ disabled: true });

    expect(date).toMatchSnapshot();
  });

  test("disabled is not set input when false", () => {
    const date = render({ disabled: false });

    expect(date).toMatchSnapshot();
  });

  test("value is set from prestored values", () => {
    const date = render({
      disabled: false,
      name: "2|1"
    });

    expect(date).toMatchSnapshot();
  });
});
