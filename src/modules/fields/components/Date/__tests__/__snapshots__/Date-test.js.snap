// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Date disabled is not set input when false 1`] = `
<div
  className="react-datepicker-wrapper"
>
  <div
    className="react-datepicker__input-container"
  >
    <div
      className="input-icon"
    >
      <input
        autoComplete="off"
        className="input-field"
        disabled={false}
        name="1|1"
        onBlur={[Function]}
        onChange={[Function]}
        onFocus={[Function]}
        onKeyDown={[Function]}
        placeholder="mmddyyyy, mm/dd/yyyy"
        type="text"
        value=""
      />
      <i
        className="fa fa-calendar icon"
        onClick={[Function]}
      />
    </div>
  </div>
</div>
`;

exports[`Date disabled is properly set on input when true 1`] = `
<div
  className="react-datepicker-wrapper"
>
  <div
    className="react-datepicker__input-container"
  >
    <div
      className="input-icon"
    >
      <input
        autoComplete="off"
        className="input-field"
        disabled={true}
        name="1|1"
        onBlur={[Function]}
        onChange={[Function]}
        onFocus={[Function]}
        onKeyDown={[Function]}
        placeholder="mmddyyyy, mm/dd/yyyy"
        type="text"
        value=""
      />
      <i
        className="fa fa-calendar icon"
        onClick={[Function]}
      />
    </div>
  </div>
</div>
`;

exports[`Date value is set from prestored values 1`] = `
<div
  className="react-datepicker-wrapper"
>
  <div
    className="react-datepicker__input-container"
  >
    <div
      className="input-icon"
    >
      <input
        autoComplete="off"
        className="input-field"
        disabled={false}
        name="2|1"
        onBlur={[Function]}
        onChange={[Function]}
        onFocus={[Function]}
        onKeyDown={[Function]}
        placeholder="mmddyyyy, mm/dd/yyyy"
        type="text"
        value="12/12/2012"
      />
      <i
        className="fa fa-calendar icon"
        onClick={[Function]}
      />
    </div>
  </div>
</div>
`;
