.enumerable-items {
  padding: 15px 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .radio-button,
  .label-radio-checkbox {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    flex-basis: 31%;
    margin-right: 2%;
    input {
      margin-top: 3px;
      margin-right: 10px;
      width: auto;
    }
    span {
      font-weight: 500;
      font-size: 15px;
      line-height: 18px;
    }
  }
  .col-2 {
    flex-basis: calc(80% / 2);
  }
  .col-3 {
    flex-basis: calc(80% / 3);
  }
  .col-4 {
    flex-basis: calc(80% / 4);
  }
}

.field-holder {
  .radio-button {
    display: flex;
    margin-right: 8%;
    &.enumerable-radio {
      padding-top: 10px;
      width: fit-content;
    }
    input {
      margin-top: 3px;
      margin-right: 10px;
      width: auto;
      align-self: first baseline;
    }
    span {
      font-weight: 500;
      font-size: 15px;
      line-height: 18px;
      &.highlight {
        color: #005f86;
      }
    }
  }
}
