.searchable-container {
  position: relative;
  .value-holder {
    background: #ffffff;
    border: 1px solid #d8d7d7;
    box-sizing: border-box;
    border-radius: 10px;
    font-size: 14px;
    height: 49px;
    padding: 13px 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &.is-edit {
      position: absolute;
      right: 0;
      top: 0;
      max-width: 30%;
      background: #f3f3f3 !important;
      border: 1px solid #d8d7d7 !important;
      box-shadow: none !important;
      padding: 5px 8px !important;
      margin: 8px !important;
      border-radius: 10px;
      height: 33px;
    }
  }
}

.search-results {
  position: absolute;
  top: 50px;
  background: #ffffff;
  border: 1px solid #a4a4a4;
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 5px;
  padding: 0px 10px;
  z-index: 1000;
  max-height: 200px;
  overflow-y: scroll;
  overflow-x: scroll;
  max-width: 125%;
  table {
    width: 100%;
    border: none;
    thead {
      tr {
        th {
          padding: 5px 10px;
          font-style: normal;
          font-weight: 800;
          font-size: 11px;
          line-height: 18px;
          color: #0e5873;
          text-align: left;
          border-bottom: 1px solid #d8d7d7;
        }
      }
    }
    tbody {
      tr {
        td {
          padding: 5px 10px;
          font-style: normal;
          font-weight: 800;
          font-size: 11px;
          line-height: 18px;
          color: #828282;
        }
      }
      tr:nth-child(even) {
        background-color: #f4f7fd;
      }
      tr.active,
      tr:hover {
        background-color: #227b9c;
        td {
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }
  .favorite-cell {
    text-align: center;
  }
}
