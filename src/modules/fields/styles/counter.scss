.counter-header {
  display: flex;
  flex-direction: row;
  background: #f4f4f4;
  border-bottom: 1px solid #d8d7d7;
  box-sizing: border-box;
  width: calc(100% + 20px);
  padding: 12px;
  margin: -10px -10px 0px;
  font-weight: 800;
  font-size: 16px;
  line-height: 18px;
  .counter-saving {
    font-style: italic;
    font-weight: 800;
    font-size: 16px;
    line-height: 18px;
    color: #828282;

    i {
      color: #7da1b4;
    }
  }
  .counter-button {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    font-weight: 800;
    font-size: 16px;
    line-height: 18px;
    color: #5faa40;
    i {
      margin-right: 4px;
    }
  }
  .counter-value {
    border: 2px solid #227b9c;
    box-sizing: border-box;
    border-radius: 50%;
    padding: 0px 4px;
    margin-right: 4px;
  }
  .counter-header-left,
  .counter-header-right {
    flex-grow: 0;
    flex-basis: 50%;
  }
  .counter-header-left {
    display: flex;
    flex-direction: row;
    color: #227b9c;
    align-items: center;
  }
  .counter-header-right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    i {
      color: #828282;
    }
  }

  .action {
    cursor: pointer;
  }
}

.registry-repeatable{
  border: 1px solid #D8D7D7;
  margin: 0px -10px 10px;
  padding: 0px 10px;
}
