import {
  and,
  any,
  always,
  append,
  compose,
  equals,
  find,
  ifElse,
  isNil,
  keys,
  pipe,
  prop,
  propEq,
  propOr,
  reduce,
  reject,
  either,
  both,
  identity,
  complement
} from "ramda";
import { isNullOrEmpty } from "utils/fp";

export const rules = {
  DateComparison: always("unimplemented"),
  EnumerableEquality: enumerableEquality,
  EnumerableInclusion: enumerableInclusion,
  NumericEquality: always("unimplemented"),
  NumericRange: always("unimplemented"),
  Presence: presence,
  Unconditional: always("passing")
};

export const ruleTypeExists = rule =>
  compose(any(equals(rule.type)), keys)(rules);

const getEnumerablesFromAnswer = (answer, context) => {
  const {
    questionnaire: { questions }
  } = context;

  const enumerables = pipe(
    prop(answer.questionId),
    prop("questionEnumerables")
  )(questions);
  const findEnumerable = currentAnswerValue =>
    find(propEq("id", currentAnswerValue), enumerables);
  const appendEnumerable = (acc, currentAnswerValue) =>
    append(findEnumerable(currentAnswerValue), acc);

  return ifElse(
    isNullOrEmpty,
    pipe(always([findEnumerable(answer.answerData)]), reject(isNil)),
    pipe(reduce(appendEnumerable, []), reject(isNil))
  )(answer.answerArray);
};

const answerPresent = both(
  identity,
  either(
    prop("answerData"),
    pipe(prop("answerArray"), complement(isNullOrEmpty))
  )
);

function enumerableEquality(rule, [answer]) {
  if (isNullOrEmpty(answer)) return "failing";
  const { equalityValue, questionIds } = rule;
  const value = propOr(undefined, "answerData", answer);

  return and(questionIds[0] === answer.questionId, value === equalityValue)
    ? "passing"
    : "failing";
}

function enumerableInclusion(rule, [answer], context) {
  if (isNullOrEmpty(answer)) return "failing";
  const { includedIn, questionIds } = rule;
  const enumerableValues = getEnumerablesFromAnswer(answer, context);
  const matchDescription = equalityValue =>
    any(propEq("description", equalityValue), enumerableValues);

  return and(
    any(matchDescription, includedIn),
    answer.questionId === questionIds[0]
  )
    ? "passing"
    : "failing";
}

function presence(_rule, answers) {
  return ifElse(
    any(answerPresent),
    always("passing"),
    always("failing")
  )(answers);
}

export default rules;
