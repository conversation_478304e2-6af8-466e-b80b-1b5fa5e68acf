import {
  __,
  all,
  always,
  append,
  applySpec,
  assoc,
  both,
  concat,
  curry,
  F,
  filter,
  ifElse,
  includes,
  indexBy,
  map,
  none,
  not,
  nth,
  path,
  pipe,
  prop,
  propEq,
  reduce,
  reject,
  split
} from "ramda";
import {
  checkMutationErrors,
  handleGraphQLErrors,
  processAnswersForStore
} from "./saveAnswers";
import { isNullOrEmpty } from "utils/fp";
import { calculateValues } from "./saveAnswer";

/**
 * Method to find whether an answer is in the answersWithoutErrors list
 * @param {[object]} answersWithoutErrors currently in store with no errors
 * @param {[object]} answer to evaluate
 * @returns {[object]} returns true if answer is not in the list (if has error)
 */
export const answerHasError = answersWithoutErrors => answer =>
  none(
    both(
      propEq("answerGroupId", answer.answerGroupId),
      propEq("questionId", answer.questionId)
    ),
    answersWithoutErrors
  );

/**
 * Method to remove answers from deleted answer groups from the store -
 * some child and grandchild answer groups are deleted on back end,
 * but on client store the answers become orphans that are not shown but still
 * considered for nodewalks
 * @param {[object]} answers currently in store
 * @param {[object]} deletedAnswerGroupIds list sent by server
 * @returns {[object]} the list of answers remaining after removing answers from deleted answer groups
 */
export const removeAnswersFromDeletedAnswerGroups = (
  answers,
  deletedAnswerGroupIds
) => {
  const isAnswerGroupDeleted = pipe(
    prop("answerGroupId"),
    includes(__, deletedAnswerGroupIds)
  );

  return reject(isAnswerGroupDeleted, answers);
};

/**
 * Method that updates store data according to added answer groups
 * @param {object} serverData data that comes in response from server
 * @param {object} answerGroups data stored in the apollo store
 * @return {object} new data with the updated answer groups
 */
export const addNewAnswerGroups = curry((serverData, answerGroups) => {
  const { createdAnswerGroups } = serverData.refreshData;
  const addAnswerGroups = concat(createdAnswerGroups);

  return pipe(addAnswerGroups)(answerGroups);
});

/**
 * Method that updates store data according to removed answer groups
 * @param {object} serverData data that comes in response from server
 * @param {object} answerGroups data stored in the apollo store
 * @return {object} new data with the deleted answer groups removed
 */
export const removeDeletedAnswerGroups = curry((serverData, answerGroups) => {
  const { deletedAnswerGroupIds } = serverData.refreshData;
  const removeAnswerGroupsById = pipe(
    prop("id"),
    includes(__, deletedAnswerGroupIds)
  );

  return reject(removeAnswerGroupsById, answerGroups);
});

/**
 * Method that updates store data according to added visible questions
 * @param {object} serverData data that comes in response from server
 * @param {object} visibleQuestions data stored in the apollo store
 * @return {object} new data with the updated visible questions
 */
export const addNewVisibleQuestions = curry((serverData, visibleQuestions) => {
  const { newVisibleQuestions } = serverData.refreshData;
  const addVisibleQuestions = concat(newVisibleQuestions);

  return pipe(addVisibleQuestions)(visibleQuestions);
});

/**
 * Method that updates store data according to removed visible questions
 * @param {object} deletedAnswerGroupIds data that comes in response from server
 * @param {object} visibleQuestions data stored in the apollo store
 * @return {object} new data with the deleted visible questions removed
 */
export const removeDeletedVisibleQuestions = curry(
  (serverData, visibleQuestions) => {
    const { deletedAnswerGroupIds } = serverData.refreshData;
    // get the answer group ID from the visible question ID
    // and check whether it exists on the deleted answer group IDs
    const removeVisibleQuestions = pipe(
      prop("id"),
      split("|"),
      nth(1),
      includes(__, deletedAnswerGroupIds)
    );

    return reject(removeVisibleQuestions, visibleQuestions);
  }
);

export const runCalculations = (context, newData, serverData) => {
  const { questionnaire, answers, answerGroups } = context;
  const addQuestion = answer =>
    assoc("question", prop(answer.questionId, questionnaire.questions), answer);
  const indexedAnswerGroups = indexBy(prop("id"), answerGroups);
  const addAnswerGroup = answer =>
    assoc(
      "answerGroup",
      prop(answer.answerGroupId, indexedAnswerGroups),
      answer
    );

  // remove errored answers and add questions/answer group objects to the new answers
  const answersToCalculate = pipe(
    reject(answerHasError(serverData.refreshData.createdAnswers)),
    map(addQuestion),
    map(addAnswerGroup)
  )(answers);

  calculateValues({
    isOptimistic: false,
    props: { ...context, allAnswerGroups: answerGroups },
    answers: answersToCalculate,
    allNewAnswers: newData.questionnaireResponse.answers
  });
};

// check whether all answers have errors so that if they do, we don't update anything
export const allHasErrors = curry((pathToAnswers, data) =>
  pipe(
    prop(pathToAnswers),
    ifElse(isNullOrEmpty, F, all(pipe(prop("errors"), isNullOrEmpty, not)))
  )(data)
);

const isEmptySentAnswer = both(
  pipe(prop("value"), isNullOrEmpty),
  pipe(prop("valueArray"), isNullOrEmpty)
);

const addDeletedAnswersToArray = (serverAnswers, sentAnswer) =>
  ifElse(
    isEmptySentAnswer,
    pipe(
      applySpec({
        answerGroupId: prop("answerGroupId"),
        questionId: prop("questionId")
      }),
      append(__, serverAnswers)
    ),
    always(serverAnswers)
  )(sentAnswer);

/**
 * Method that will add the deleted answers to server answers - the deleted
 * answers are answers with now empty values and were sent to server to be
 * deleted so they are not returned in the array, but they will be needed
 * for calculation on processAnswersForStore
 * after the deleted answers are added, we remove all answers with errors that
 * should be ignored by processAnswersForStore
 * @param {object} serverData data that comes in response from server
 * @param {object} pathToAnswers on the server data
 * @param {sentAnswers} sentAnswers answers sent to server for deletion
 * @return {object} all answers (including ones to be deleted) with no errors
 */
export const prepareServerAnswers = (serverData, pathToAnswers, sentAnswers) =>
  pipe(
    reduce(addDeletedAnswersToArray, path(pathToAnswers, serverData)),
    filter(pipe(prop("errors"), isNullOrEmpty))
  )(sentAnswers);

// eslint-disable-next-line max-statements
export const updateAfterMutation = ({ store, context, serverData }) => {
  const {
    questionnaireResponseId,
    answers,
    query,
    objectName,
    shouldRunCalculations = true
  } = context;

  if (!allHasErrors("createdAnswers", serverData.refreshData)) {
    // get current visible questions through questionnaire response from Apollo store
    const data = store.readQuery({
      query,
      variables: { id: Number(questionnaireResponseId) }
    });

    // update answers

    // prepare for deletion and remove answers with errors so they don't get processed
    const serverAnswers = prepareServerAnswers(
      serverData,
      ["refreshData", "createdAnswers"],
      answers
    );

    // create a new store data object with new/updated answers and deleted answers removed
    const newData = processAnswersForStore({
      answers: serverAnswers,
      objectName,
      data
    });

    // remove answers from removed child answer groups
    newData.questionnaireResponse.answers =
      removeAnswersFromDeletedAnswerGroups(
        newData.questionnaireResponse.answers,
        serverData.refreshData.deletedAnswerGroupIds
      );

    // update answer groups

    newData.questionnaireResponse.answerGroups = pipe(
      addNewAnswerGroups(serverData),
      removeDeletedAnswerGroups(serverData)
    )(newData.questionnaireResponse.answerGroups);

    // update visible questions

    newData.questionnaireResponse.visibleQuestions = pipe(
      addNewVisibleQuestions(serverData),
      removeDeletedVisibleQuestions(serverData)
    )(newData.questionnaireResponse.visibleQuestions);

    // save changes in the Apollo store
    store.writeQuery({
      query,
      variables: { id: Number(questionnaireResponseId) },
      data: newData
    });

    if (shouldRunCalculations) {
      runCalculations(context, newData, serverData);
    }
  }
};

/**
 * Method that calls the mutation for partial/full refresh. Response will return:
 * createdAnswerGroups, deletedAnswerGroupIds, createdAnswers, newVisibleQuestions
 * We use out of the box Apollo funtionality to update, as structure is same
 * as used in previous queries
 * @param {Object} props Hash with all the properties utilized to call refresh
 * {Int} questionnaireResponseId Current Questionnaire Response id
 * {object} sectionAnswerGroup Current section Answer Group
 * {function} refreshData Mutation function to be called
 * {function} setSaving Function to turn on flag to mark field as saving
 * @return {Void} No value returned as only calls the mutation
 */
export const fullServerRefresh = props => {
  const {
    questionnaireResponseId,
    refreshData,
    answers,
    answerError,
    setSaving
  } = props;

  // A timeout was added as is called within other mutation and throws error if done synchronously
  setTimeout(() => {
    setSaving(true);
    const variables = {
      id: Number(questionnaireResponseId),
      answers
    };

    // Mutation call for data refresh
    refreshData({
      variables,
      // Method called after the mutation response is received
      update: (store, { data: serverData }) => {
        updateAfterMutation({ store, context: props, serverData });
      }
    })
      .then(response => {
        checkMutationErrors({
          data: response.data.refreshData,
          mutationName: "createdAnswers",
          answerError
        });
      })
      .catch(response => {
        handleGraphQLErrors({
          graphQLErrors: response.graphQLErrors,
          answerError
        });
      })
      .finally(() => setSaving(false));
  }, 1);
};
