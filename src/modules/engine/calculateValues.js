import {
  always,
  assoc,
  both,
  complement,
  evolve,
  find,
  forEach,
  identity,
  ifElse,
  indexBy,
  isEmpty,
  lensPath,
  map,
  pathOr,
  pipe,
  prop,
  propEq,
  set,
  values,
  view
} from "ramda";
import Engine from ".";
import { isNullOrEmpty } from "utils/fp";

/**
 * Method to update the questionnaire response with a set of modified answers
 * @param {Object} anonymous Hash with the parameters needed, for style more
 *                           than 3 params need to be passed as hash
 * HASH CONTENTS
 * {[Object]} answers      Modified answers to update on store
 * {Object} objectName     Path name to use to get current response object
 * {Object} data           The data from store
 * @return {[Object]}      All answers, including updated ones, from the store.
This is a copy of ProcessAnswersForStore from engine/saveAnswers.
Will want to integrate functions at one point. Jira ticket to complete has been
created.*/
export const processAnswersForStore = ({ answers, objectName, data }) => {
  // Create a lens to answers object
  const answersLens = lensPath([objectName, "answers"]);
  const answerId = answer => `${answer.questionId}|${answer.answerGroupId}`;

  if (!isNullOrEmpty(answers)) {
    // index current answers
    let indexedAnswers = pipe(view(answersLens), indexBy(answerId))(data);
    const addRemoveAnswersQR = answer => {
      const addAnswer = () => assoc(answerId(answer), answer, indexedAnswers);
      const cleannedAnswer = evolve(
        { answerData: always(null), answerArray: always([]) },
        answer
      );
      const cleanAnswer = () =>
        assoc(answerId(answer), cleannedAnswer, indexedAnswers);
      const isEmptyAnswer = both(
        pipe(prop("answerData"), isNullOrEmpty),
        pipe(prop("answerArray"), isNullOrEmpty)
      );

      indexedAnswers = ifElse(
        identity,
        ifElse(isEmptyAnswer, cleanAnswer, addAnswer),
        always(indexedAnswers)
      )(answer);
    };

    // merge response answers with existing list
    forEach(addRemoveAnswersQR, answers);
    // Returns new data object with merged/removed answers
    return set(answersLens, values(indexedAnswers), data);
  }
  return data;
};

/**
 * Method that evaluates answer to find if it's a calculation target, and if it is, call server;
 * when result comes back it checks if any of answers is an edge target to be evaluated
 * @param {Object} context  Hash with the parameters needed, for style more
 *                           than 3 params need to be passed as hash
 * HASH CONTENTS
 * {Object} answerGroup          Answer's answer group
 * {Object} question             Answer's question
 * {Object} value                Answer's value
 * {String} answerType           Answer's type
 * {Object} context              Group of values needed for the NodeWalk process but not for calculation
 * @return {Void} No value returned as only calls the mutation
 */
export const calculateValues = context => {
  const {
    answerGroup,
    question,
    value,
    answerType,
    valueCalculations,
    allAnswerGroups,
    responseQuery,
    objectName
  } = context;

  if (question.calculationTarget) {
    const { questionnaireResponseId, setSaving, setFormValue } = context;

    // A timeout was added as is called within other mutation and throws error if done synchronously
    setTimeout(() => {
      setSaving(true);

      // Mutation call for value calculation, this will get calculated answers that depend on current answer
      valueCalculations({
        variables: {
          answerGroupId: Number(answerGroup.id),
          questionId: Number(question.id),
          value,
          answerType
        },
        // Method called after the mutation response is received, just add data to
        // Questionnaire Response. This is inside the mutation redux action
        // eslint-disable-next-line max-statements
        update: (store, { data: serverData }) => {
          const answersLens = lensPath([objectName, "answers"]);
          // Gets Answers from response
          const getCalculatedAnswers = prop("valueCalculations");
          const answers = getCalculatedAnswers(serverData);
          const data = store.readQuery({
            query: responseQuery,
            variables: { id: Number(questionnaireResponseId) }
          });
          const newData = processAnswersForStore({
            answers,
            objectName,
            data
          });

          // Save changes in the Apollo store
          store.writeQuery({
            query: responseQuery,
            variables: { id: Number(questionnaireResponseId) },
            data: newData
          });

          // change values on the form
          ifElse(
            both(always(setFormValue), complement(isNullOrEmpty)),
            forEach(answer =>
              setFormValue(
                `${answer.questionId}|${answer.answerGroupId}`,
                answer.answerData
              )
            ),
            identity
          )(answers);
          const addAnswerGroup = answer =>
            ifElse(
              prop("answerGroup"),
              identity,
              assoc(
                "answerGroup",
                find(propEq("id", answer.answerGroupId), allAnswerGroups)
              )
            )(answer);

          const newAllAnswers = map(addAnswerGroup, view(answersLens, newData));

          const answersMap = map(
            answer => ({
              answerData: isEmpty(answer.answerArray)
                ? answer.answerData
                : answer.answerArray,
              question: answer.question,
              answerGroup: answer.answerGroup,
              answerType: answer.answerType
            }),
            answers
          );

          Engine.evaluateAnswersForEdges(
            {
              ...context,
              allAnswerGroups: pathOr(
                context.allAnswerGroups,
                ["questionnaireResponse", "answerGroups"],
                newData
              ),
              allAnswers: newAllAnswers,
              query: responseQuery
            },
            answersMap
          );
        }
      })
        .then(() => setSaving(false))
        .catch(() => setSaving(false));
    }, 1);
  }
};
