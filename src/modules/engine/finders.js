import { any, both, compose, either, equals, find, keys, pathEq } from "ramda";

const Null = ({ contextAnswerGroupId, allAnswers, question }) =>
  find(
    both(
      pathEq(["questionId"], question.id),
      either(
        pathEq(["answerGroup", "id"], contextAnswerGroupId),
        pathEq(["answerGroup", "parentId"], contextAnswerGroupId)
      )
    ),
    allAnswers
  );

const Any = ({ allAnswers, question }) =>
  find(pathEq(["questionId"], question.id), allAnswers);

export const finderStrategies = {
  Null,
  Any
};
// Checks that a provided finder type exists
export const finderTypeExists = finder =>
  compose(any(equals(finder.type)), keys)(finderStrategies);

export default finderStrategies;
