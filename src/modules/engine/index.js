import {
  all,
  always,
  any,
  append,
  assoc,
  both,
  chain,
  compose,
  concat,
  curry,
  defaultTo,
  dissoc,
  either,
  equals,
  filter,
  find,
  groupBy,
  identity,
  ifElse,
  isEmpty,
  isNil,
  map,
  merge,
  mergeWith,
  not,
  path,
  pipe,
  prop,
  propEq,
  reduce,
  reject,
  values
} from "ramda";
import { rules, ruleTypeExists } from "./rules";
import { logic, logicTypeExists } from "./logic";
import { finderStrategies, finderTypeExists } from "./finders";
import { partialServerUpdate } from "./partialUpdate";
import { fullServerRefresh } from "./fullRefresh";
import { calculateValues } from "./calculateValues";
import {
  saveAnswer,
  handleSaveAnswerMutation,
  updateAnswersForNodeWalk
} from "./saveAnswer";
import {
  saveAnswers,
  handleSaveAnswersMutation,
  formatAnswer as formatAnswers
} from "./saveAnswers";
import { isNullOrEmpty } from "utils/fp";
import { parseNumber } from "utils/fp/parse";

const defaultGroups = { passing: [], failing: [], unimplemented: [] };
const findQuestion = (questionnaire, finder) =>
  prop(finder.questionId, questionnaire.questions);

/**
 * Curried function that returns all found answers for a finder type given a
 * changing answer
 * @param {Object} context hash object with values the provide context to calculation
 * HASH CONTENTS *
 * {Object} questionnaire  Current Questionnaire
 * {Object} contextAnswerGroupId  Current Answer Group id
 * {String} allAnswers List of all answers indexed by agId|questionId
 * @return {function(Object): Object[]} function that accepts a finder and
 *         returns a status string
 */
const findAnswers =
  ({ allAnswers, ...context }) =>
  finder => {
    const { questionnaire } = context;
    const { type } = finder;
    const question = findQuestion(questionnaire, finder);

    if (question === undefined) return "unimplemented";

    return finderTypeExists(finder)
      ? finderStrategies[type]({
          question,
          allAnswers: values(allAnswers),
          ...context
        })
      : "unimplemented";
  };

/**
 * Function that returns whether a rule passes in the context of a
 * passed changing answer
 * @param {Object} context hash object with values the provide context to calculation
 * HASH CONTENTS *
 * {Object} questionnaire  Current Questionnaire
 * {Object} contextAnswerGroupId  Current Answer Group id
 * {String} all Answers List of all answers indexed by agId|questionId
 * @return {function(Object): string} function that accepts a rule to be evaluated and returns a
 *         status string
 */
const evaluateRule = context => rule => {
  const answers = map(findAnswers(context), rule.finders);

  if (any(equals("unimplemented"), answers)) {
    return "unimplemented";
  }

  return ruleTypeExists(rule)
    ? rules[rule.type](rule, answers, context)
    : "unimplemented";
};

/**
 * Function that returns whether an edge passes in the context of a
 * passed changing answer
 * @param {Object} context Hash object with values the provide context to calculation
 * HASH CONTENTS *
 * {Object} questionnaire  Current Questionnaire
 * {Object} contextAnswerGroupId  Current Answer Group id
 * {String} allAnswers List of all answers indexed by agId|questionId
 * @return {function(Object): Object} function that accepts an edge and returns
 *         an object with status, group and parent answer group
 */
const processEdge = context => edge => {
  const { contextAnswerGroupId } = context;
  const { logicType, rules: edgeRules, endNode, startNode } = edge;

  if (!contextAnswerGroupId) return null;
  const evaluateEdge = rulesResult =>
    logicTypeExists(logicType)
      ? logic[logicType](rulesResult)
      : "unimplemented";
  const result = compose(evaluateEdge, map(evaluateRule(context)))(edgeRules);

  return {
    status: result,
    parentId: Number(contextAnswerGroupId),
    groupId: Number(endNode.groupId),
    parentGroupId: Number(startNode.groupId)
  };
};

/**
 * Given a result it returns the descendant edges based on the result group
 * @param {Object} questionnaire Current questionnaire
 * @return {function(Object): Object[]} function that accepts a nodewalk result
 *         and returns list descendant edges of the result group nodes
 */
const getDescendantEdges = questionnaire => result => {
  const relatedNodes = pipe(
    filter(propEq("groupId", result.groupId)),
    values
  )(questionnaire.nodes);
  const relatedEdges = startNode =>
    filter(
      propEq("startNodeId", parseNumber(startNode.id)),
      questionnaire.edges
    );
  const descendantEdges = chain(relatedEdges, relatedNodes);
  const descendantEdgeObject = parentId => edge => ({
    parentId,
    edge
  });

  return map(descendantEdgeObject(result.parentId), descendantEdges);
};

/**
 * Based on the passing results get the child edges and evaluate them. We remove the failing
 * results as the answer groups for them haven't being created yet, so nothing to delete.
 * @param {Object} questionnaire Current questionnaire
 * @param {[Object]} passingResults Array with the passing results from the parent edge
 * @param {Object} allAnswers List with all answers indexed by agId|questionId
 * @return {Object} The results from evaluating descendant edges
 */
const processDescendantsPassing = (questionnaire, passingResults, allAnswers) =>
  pipe(
    defaultTo([]),
    chain(getDescendantEdges(questionnaire)),
    chain(pair =>
      processEdge({
        contextAnswerGroupId: pair.parentId,
        questionnaire,
        allAnswers
      })(pair.edge)
    ),
    reject(isNullOrEmpty),
    map(assoc("isDescendant", true)),
    groupBy(prop("status")),
    dissoc("failing")
  )(passingResults);

/**
 * Check recursively edges based on the results of the affected edges. unimplemented results
 * are not checked as a full server request will be done. Failing results are not checked either
 * as answer groups of failed childs will be deleted with the parent.
 * @param {Object} questionnaire The current questionnaire object
 * @param {Object} results Node walk results for the parent edge
 * @param {Object} allAnswers List with all answers indexed by agId|questionId
 * @return {Object} Results of the nodewalk for this edge merged with result from descendants
 */
const processDescendantEdges = (questionnaire, results, allAnswers) => {
  const getDescendantResults = () =>
    processDescendantsPassing(questionnaire, results.passing, allAnswers);

  const isEmptyResults = either(
    isNullOrEmpty,
    pipe(values, all(isNullOrEmpty))
  );

  return ifElse(
    // If no results available return default results
    isEmptyResults,
    always(defaultGroups),
    ifElse(
      // If any edge is already unimplemented let server caculate all descendant edges
      pipe(prop("unimplemented"), isNullOrEmpty, not),
      always(defaultGroups),
      pipe(getDescendantResults, descendantResults =>
        mergeWith(
          concat,
          descendantResults,
          processDescendantEdges(questionnaire, descendantResults, allAnswers)
        )
      )
    )
  )(results);
};

/**
 * Method to find the answerGroup for the answer context
 *
 * @param {Object} anonymous hash with context values
 * @param {Object} answer changed answer
 * @param {Object} edge affected Edge
 * @returns {Int} Start AnswerGroup Id that belongs to context
 */
const getContextAnswerGroupId = (
  { questionnaire, answerGroupsByGroup, allAnswers },
  answer,
  edge
) => {
  const finders = pipe(
    chain(prop("finders")),
    filter(propEq("questionId", answer.question.id))
  )(edge.rules);

  if (isNullOrEmpty(finders)) return null;

  const getAnswers = startAG =>
    pipe(
      chain(
        findAnswers({
          questionnaire,
          allAnswers,
          contextAnswerGroupId: startAG.id
        })
      ),
      reject(isNil)
    )(finders);
  const isAffectedAG = pipe(
    getAnswers,
    any(
      either(
        equals("unimplemented"),
        both(
          propEq("answerGroupId", answer.answerGroup.id),
          propEq("questionId"),
          answer.question.id
        )
      )
    )
  );

  return pipe(
    prop(edge.startNode.groupId),
    ifElse(isNullOrEmpty, identity, find(isAffectedAG)),
    ifElse(isNullOrEmpty, identity, prop("id"))
  )(answerGroupsByGroup);
};

const createBlankCounter = (answer, allAnswerGroups, _questionnaire) => {
  const { question, answerGroup, answerData } = answer;

  if (question.type === "Counter" && !isNullOrEmpty(answerData)) {
    const parentAnswerGroup = find(
      propEq("id", answerGroup.parentId),
      allAnswerGroups
    );

    return {
      status: "passing",
      parentId: Number(answerGroup.parentId),
      groupId: Number(answerGroup.groupId),
      parentGroupId: Number(parentAnswerGroup.groupId)
    };
  }
  return null;
};

/**
 * Method will get all edges from questionnaire nodes, then filter only the ones
 * that the answer affects based on the finder question ids. After that we will
 * add the full end node object to the edge (formerly only had the id), then
 * each edge rules is evaluated based on the answer and results are group by
 * status (passing, failing and unimplemnted)
 * @param {Object} answer  answer that is changing prompting the walk
 * @param {Object} context Hash with the current Question info
 * HASH CONTENTS *
 * {Object} questionnaire Current Questionnaire
 * {Object} allAnswers List with all answers indexed by agId|questionId
 * {[Object]} allAnswerGroups Array with all answerGroups
 * @return {Object} Returns a hash with results of node walk as passing, failing
 *                  and unimplemented
 *
 */
function nodeWalk(answer, context) {
  if (either(isEmpty, isNil)(answer)) return defaultGroups;

  const { questionnaire, allAnswers, allAnswerGroups } = context;
  const affectedEdges = filter(
    pipe(
      prop("rules"),
      any(
        pipe(
          prop("finders"),
          any(propEq("questionId", path(["question", "id"], answer)))
        )
      )
    )
  );
  const answerGroupsByGroup = groupBy(prop("groupId"), allAnswerGroups);

  return pipe(
    affectedEdges,
    map(edge =>
      processEdge({
        contextAnswerGroupId: getContextAnswerGroupId(
          { questionnaire, answerGroupsByGroup, allAnswers },
          answer,
          edge
        ),
        questionnaire,
        allAnswers
      })(edge)
    ),
    append(createBlankCounter(answer, allAnswerGroups, questionnaire)),
    // Add blank Counter
    reject(isNullOrEmpty),
    groupBy(prop("status")),
    merge(defaultGroups),
    results =>
      mergeWith(
        concat,
        processDescendantEdges(questionnaire, results, allAnswers),
        results
      )
  )(questionnaire.edges);
}

/**
 * Method that runs the nodewalk to calculate affected
 * groups based on a given Answer *
 * @param {Object} context Hash with the current Question info
 * HASH CONTENTS *
 * {Object} questionnaire Current Questionnaire
 * {Object} allAnswers List with all answers indexed by agId|questionId
 * {[Object]} allAnswerGroups Array list with all answerGroups
 * @param {Object} anonymous Hash with the answer properties
 * HASH CONTENTS *
 * {Object} question  Current Answer's Question
 * {Object} answerGroup  Current Answer's Answer Group
 * {String} answerData Answer's data to be saved *
 * @return {Object} Returns a hash with results of node walk as passing,
 *                   failing and unimplemented
 */
const evaluateAnswer = curry(
  (context, { question, answerGroup, answerData }) => {
    const answerField = {
      answerData,
      question,
      answerGroup
    };

    if (question.edgeTarget) {
      // eslint-disable-next-line no-use-before-define
      return Engine.nodeWalk(answerField, context);
    }
    return defaultGroups;
  }
);

const formatAnswer = answer => ({
  answerData: answer.answerData,
  question: answer.question,
  answerGroup: answer.answerGroup
});

/**
 * Method that calls the nodewalk (through evaluate answer) and merge all
 * result in 1 hash. Answers are formatted on the way nodewalk method expect them *
 * @param {Object} context Hash with the current Question info
 * HASH CONTENTS *
 * {Object} questionnaire Current Questionnaire
 * {Object} allAnswers List with all answers indexed by agId|questionId
 * {Object} allAnswerGroups Array with all answerGroups indexed by Id
 * @param  {Object[]} answers Array wilth all the answers that changed
 * @return {Object} Hash with merged results as passing, failing and unimplemented
 */
const evaluateAnswers = curry((context, answers) =>
  pipe(
    map(formatAnswer),
    // eslint-disable-next-line no-use-before-define
    map(Engine.evaluateAnswer(context)),
    reduce(mergeWith(concat), defaultGroups)
  )(answers)
);

/**
 * This method checks from walk results if there are unimplemented results to
 * do a full refresh. if not it checks that there are affected nodes (passing
 * or failing) to trigger a partial refresh to create/delete answer groups.
 * @param {Object} anonymous  Hash with the parameters needed, for style more
 *                            than 3 params need to be passed as hash *
 * HASH CONTENTS
 * {hash} walkResult Hash with parent answerGroup and groups for nodes passing,
 *                   failing and unimplemented
 * {Int} questionnaireResponseId Current Questionnaire ResponseId
 * {object} sectionAnswerGroup  Current section answer group
 * {function} refreshData Function to trigger a full refresh mutation
 * {function} updateAnswerGroups Function to trigger a partial refresh for
 *                               given answerGroups
 * {function} setSaving Function to turn on flag to mark field as saving
 * @return {Void} No value returned
 */
// eslint-disable-next-line complexity
const processNodeWalkResults = ({
  walkResult,
  answers,
  answerGroups,
  ...props
}) =>
  ifElse(
    pipe(prop("unimplemented"), isNullOrEmpty),
    ifElse(
      both(
        pipe(prop("failing"), isNullOrEmpty),
        pipe(prop("passing"), isNullOrEmpty)
      ),
      identity,
      () => {
        // Call Mutation with arrays for passing and failing groups
        partialServerUpdate({
          walkResult,
          answers,
          answerGroups,
          ...props
        });
      }
    ),
    () => {
      // As we cannot determine certain rules on client side we do full refresh
      fullServerRefresh({
        answers,
        answerGroups,
        ...props
      });
    }
  )(walkResult);

/**
 * Method to check each returned answer to see if is an edge target, and
 * if it is, evaluates it and call server to update AGs as needed.
 * Uses same logic that answerQuestion on question component for edge evaluation.
 * @param {Object} anonymous Hash with the parameters needed, for style more
 *                           than 3 params need to be passed as hash
 * HASH CONTENTS
 * {Object} questionnaire        Current Questionnaire
 * {Int} questionnaireResponseId Current Questionnaire Response Id
 * {Function} refreshData        Function to trigger a full refresh mutation
 * {Function} updateAnswerGroups Function to trigger a partial refresh
 *                               (through a mutation declared on caller) for
 *                               given answerGroups
 * {Function} setSaving Function to turn on flag to mark field as saving
 * @param {[Object]} answers Array with answers to be evaluated
 * @return {Void} No value returned as only calls the mutation
 */
const evaluateAnswersForEdges = curry((context, answers) => {
  const { allAnswerGroups, allAnswers, questionnaire } = context;
  const formattedAnswers = map(formatAnswers, answers);
  const createProcessObject = walkResult => ({
    ...context,
    walkResult,
    answerGroups: allAnswerGroups,
    answers: formattedAnswers
  });
  // Evaluate answers and obtains the node walk results, then processes the result

  pipe(
    // eslint-disable-next-line no-use-before-define
    Engine.evaluateAnswers({ questionnaire, allAnswers, allAnswerGroups }),
    ifElse(
      pipe(values, all(isNullOrEmpty)),
      identity,
      pipe(
        createProcessObject,
        // eslint-disable-next-line no-use-before-define
        Engine.processNodeWalkResults
      )
    )
  )(answers);
});

const Engine = {
  findAnswers,
  defaultGroups,
  nodeWalk,
  evaluateAnswer,
  evaluateAnswers,
  processNodeWalkResults,
  updateAnswersForNodeWalk,
  calculateValues,
  evaluateAnswersForEdges,
  saveAnswer,
  saveAnswers,
  handleSaveAnswerMutation,
  handleSaveAnswersMutation,
  fullServerRefresh
};

export default Engine;
