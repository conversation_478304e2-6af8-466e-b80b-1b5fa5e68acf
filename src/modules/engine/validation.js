import {
  always,
  and,
  applySpec,
  both,
  complement,
  concat,
  cond,
  defaultTo,
  equals,
  has,
  identity,
  ifElse,
  mergeWith<PERSON>ey,
  not,
  pipe,
  prop,
  reduce,
  T
} from "ramda";
import { isNullOrEmpty } from "utils/fp";

const formatResult = validator => result => {
  const { severity, failureMessage } = validator;
  const isError = equals(severity, "error");

  return applySpec({
    valid: identity,
    errors: ifElse(
      both(not, always(isError)),
      always([failureMessage]),
      always(undefined)
    ),
    warnings: ifElse(
      both(not, always(!isError)),
      always([failureMessage]),
      always(undefined)
    )
  })(result);
};
const processValidation = fn => (validator, value) =>
  pipe(fn, formatResult(validator))(value);
const realTimePresence = complement(isNullOrEmpty);

export const validation = {
  RealTimePresence: processValidation(realTimePresence)
};

export const validationExists = validationType =>
  has(validationType, validation);

const concatMessages = (left, right) =>
  ifElse(isNullOrEmpty, always(right), concat(right))(left);

const mergeValidationResults = (key, left, right) =>
  cond([
    [equals("valid"), () => and(left, right)],
    [equals("errors"), () => concatMessages(left, right)],
    [equals("warnings"), () => concatMessages(left, right)],
    [T, always(left)]
  ])(key);

export const validateAnswer = (question, value) => {
  const validate = (state, validator) =>
    pipe(
      prop("validationType"),
      ifElse(
        validationExists,
        validationType =>
          mergeWithKey(
            mergeValidationResults,
            validation[validationType](validator, value),
            state
          ),
        always(state)
      )
    )(validator);

  return reduce(
    validate,
    { valid: true, errors: [], warnings: [] },
    defaultTo([], question.validators)
  );
};

export default validation;
