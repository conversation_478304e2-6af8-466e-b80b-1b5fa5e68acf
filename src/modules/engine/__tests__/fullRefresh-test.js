import {
  addNewAnswerGroups,
  addNewVisibleQuestions,
  answerHasError,
  removeAnswersFromDeletedAnswerGroups,
  removeDeletedAnswerGroups,
  removeDeletedVisibleQuestions,
  updateAfterMutation
} from "../fullRefresh";
import * as saveAnswer from "../saveAnswer";

describe("fullRefresh", () => {
  const answer1 = { answerGroupId: "1", questionId: "1" };
  const answer2 = { answerGroupId: "2", questionId: "1" };
  const answersWithoutErrors = [{ answerGroupId: "1", questionId: "1" }];

  describe("answerHasError", () => {
    test("returns false if answer is in answersWithoutErrors list", () => {
      expect(answerHasError(answersWithoutErrors)(answer1)).toBeFalsy();
    });
    test("returns true if answer is not in answersWithoutErrors list", () => {
      expect(answerHasError(answersWithoutErrors)(answer2)).toBeTruthy();
    });
  });

  describe("removeAnswersFromDeletedAnswerGroups", () => {
    test("removes the answers for the deleted answer groups", () => {
      const answers = [
        { id: "1", answerGroupId: "1", questionId: "1" },
        { id: "2", answerGroupId: "2", questionId: "1" },
        { id: "3", answerGroupId: "3", questionId: "2" }
      ];
      const deletedAnswerGroupIds = ["1", "3"];

      expect(
        removeAnswersFromDeletedAnswerGroups(answers, deletedAnswerGroupIds)
      ).toMatchSnapshot();
    });
  });

  describe("addNewAnswerGroups", () => {
    const serverData = {
      refreshData: {
        createdAnswerGroups: [{ id: "1" }, { id: "2" }]
      }
    };
    const answerGroups = [{ id: "3" }, { id: "4" }];

    test("returns updated answer groups array", () => {
      expect(addNewAnswerGroups(serverData, answerGroups)).toMatchSnapshot();
    });
  });

  describe("removeDeletedAnswerGroups", () => {
    const serverData = {
      refreshData: { deletedAnswerGroupIds: ["1", "3"] }
    };
    const answerGroups = [{ id: "1" }, { id: "2" }, { id: "3" }, { id: "4" }];

    test("returns updated answer groups array", () => {
      expect(
        removeDeletedAnswerGroups(serverData, answerGroups)
      ).toMatchSnapshot();
    });
  });

  describe("addNewVisibleQuestions", () => {
    const serverData = {
      refreshData: {
        newVisibleQuestions: [{ id: "1|13" }, { id: "2|4" }]
      }
    };
    const visibleQuestions = [
      { id: "1|1" },
      { id: "1|2" },
      { id: "1|3" },
      { id: "2|1" },
      { id: "2|2" }
    ];

    test("returns updated visible questions array", () => {
      expect(
        addNewVisibleQuestions(serverData, visibleQuestions)
      ).toMatchSnapshot();
    });
  });

  describe("removeDeletedVisibleQuestions", () => {
    const serverData = {
      refreshData: { deletedAnswerGroupIds: ["1", "3"] }
    };
    const visibleQuestions = [
      { id: "1|1" },
      { id: "1|2" },
      { id: "1|3" },
      { id: "2|1" },
      { id: "2|2" }
    ];

    test("returns updated visible questions array", () => {
      expect(
        removeDeletedVisibleQuestions(serverData, visibleQuestions)
      ).toMatchSnapshot();
    });
  });

  describe("updateAfterMutation", () => {
    const answerGroups = [{ id: "1" }];
    const questions = { 1: { id: "1" } };
    const answerNoError = {
      id: "1",
      answerData: "test",
      answerGroupId: "1",
      questionId: "1",
      errors: []
    };
    const answerWithError = {
      id: null,
      answerData: "test",
      answerGroupId: "1",
      questionId: "1",
      errors: ["error"]
    };
    const getContext = (answers = []) => ({
      questionnaireResponseId: "1",
      questionnaire: { answerGroups, questions },
      answers,
      query: {},
      objectName: "questionnaireResponse",
      answerGroups
    });
    const getServerData = (
      answers,
      createdAnswerGroups = [],
      deletedAnswerGroupIds = [],
      newVisibleQuestions = []
      // eslint-disable-next-line max-params
    ) => ({
      refreshData: {
        createdAnswerGroups,
        deletedAnswerGroupIds,
        createdAnswers: answers,
        newVisibleQuestions
      }
    });
    const getStore = (storedAnswers, testFunction) => ({
      readQuery: () => ({
        questionnaireResponse: {
          answers: storedAnswers,
          answerGroups,
          visibleQuestions: [{ id: "1|1" }, { id: "1|3" }]
        }
      }),
      writeQuery: testFunction || jest.fn()
    });
    const answersToCalculate = [
      {
        answerData: "test",
        answerGroupId: "1",
        questionId: "1",
        answerGroup: { id: "1" },
        question: { id: "1" }
      }
    ];

    test("calculateValues is called with correct params when no answers have errors", () => {
      const testFunction = jest.fn();

      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = obj =>
        testFunction(
          obj.isOptimistic,
          obj.props,
          obj.allNewAnswers,
          obj.answers
        );

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "test" }
      ]);
      const serverData = getServerData([answerNoError]);
      const store = getStore([]);

      updateAfterMutation({ context, store, serverData });

      expect(testFunction).toHaveBeenCalledWith(
        false,
        { ...context, allAnswerGroups: answerGroups },
        [],
        answersToCalculate
      );
    });

    test("calculateValues is not called when all answers have errors", () => {
      const testFunction = jest.fn();

      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = obj =>
        testFunction(
          obj.isOptimistic,
          obj.props,
          obj.allNewAnswers,
          obj.answers
        );

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "test" }
      ]);
      const serverData = getServerData([answerWithError]);
      const store = getStore([]);

      updateAfterMutation({ context, store, serverData });

      expect(testFunction).not.toHaveBeenCalled();
    });

    test("calculateValues is called with correct params when some answers have errors", () => {
      const testFunction = jest.fn();

      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = obj =>
        testFunction(
          obj.isOptimistic,
          obj.props,
          obj.allNewAnswers,
          obj.answers
        );

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "test" }
      ]);
      const serverData = getServerData([answerNoError, answerWithError]);
      const store = getStore([]);

      updateAfterMutation({ context, store, serverData });

      expect(testFunction).toHaveBeenCalledWith(
        false,
        { ...context, allAnswerGroups: answerGroups },
        [],
        answersToCalculate
      );
    });

    test("createdAnswerGroups get added to the store", () => {
      const testFunction = jest.fn();

      // eslint-disable-next-line import/namespace
      const onWriteQuery = obj =>
        testFunction(obj.data.questionnaireResponse.answerGroups);

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "test" }
      ]);
      const newAnswerGroup = { id: "2" };
      const serverData = getServerData([answerNoError], [newAnswerGroup]);
      const store = getStore([], onWriteQuery);

      updateAfterMutation({ context, store, serverData });

      expect(testFunction).toHaveBeenCalledWith([
        newAnswerGroup,
        ...answerGroups
      ]);
    });

    test("deletedAnswerGroups and deleted visibleQuestions get removed from the store", () => {
      const testFunction = jest.fn();

      // eslint-disable-next-line import/namespace
      const onWriteQuery = obj =>
        testFunction(
          obj.data.questionnaireResponse.answerGroups,
          obj.data.questionnaireResponse.visibleQuestions
        );

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "test" }
      ]);
      const serverData = getServerData([answerNoError], [], ["1"]);
      const store = getStore([], onWriteQuery);

      updateAfterMutation({ context, store, serverData });

      expect(testFunction).toHaveBeenCalledWith([], [{ id: "1|3" }]);
    });

    test("newVisibleQuestions get added to the store", () => {
      const testFunction = jest.fn();

      // eslint-disable-next-line import/namespace
      const onWriteQuery = obj =>
        testFunction(obj.data.questionnaireResponse.visibleQuestions);

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "test" }
      ]);
      const newAnswerGroup = { id: "2" };
      const serverData = getServerData(
        [answerNoError],
        [newAnswerGroup],
        [],
        [{ id: "1|2" }]
      );
      const store = getStore([], onWriteQuery);

      updateAfterMutation({ context, store, serverData });

      expect(testFunction).toHaveBeenCalledWith([
        { id: "1|2" },
        { id: "1|1" },
        { id: "1|3" }
      ]);
    });

    test("DeletedAnswer is removed even when createdAnswer is empty", () => {
      const testFunction = jest.fn();
      // eslint-disable-next-line import/namespace
      const onWriteQuery = obj =>
        testFunction(obj.data.questionnaireResponse.answers);

      const context = getContext([
        { answerGroupId: "1", questionId: "1", answerData: "" }
      ]);

      const serverData = getServerData([], [], [], []);
      const store = getStore(
        [{ id: 1, answerGroupId: "1", questionId: "1", answerData: "test" }],
        onWriteQuery
      );

      updateAfterMutation({ context, store, serverData });
      expect(testFunction).toHaveBeenCalledWith([]);
    });
  });
});
