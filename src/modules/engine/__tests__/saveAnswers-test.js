import {
  checkMutationErrors,
  cleanServerAnswers,
  formatAnswer,
  getAnswersField,
  handleGraphQLErrors,
  hasErrors,
  hasErrorsOrWarnings,
  hasWarnings,
  processAnswersForStore,
  saveAnswers
} from "../saveAnswers";
import { append, values } from "ramda";
import Engine from "..";

const answerGroup1 = { id: "1" };
const answerGroup2 = { id: "2" };
const answerGroup3 = { id: "3" };
const answerGroup4 = { id: "4" };

describe("saveAnswers", () => {
  const allAnswers = {
    "1|1": {
      id: "1",
      answerGroupId: "1",
      questionId: "1",
      answerData: "test"
    },
    "2|1": {
      id: "2",
      answerGroupId: "1",
      questionId: "2",
      answerData: "test2"
    },
    "3|2": {
      id: "3",
      answerGroupId: "2",
      questionId: "3",
      answerData: "test3"
    },
    "4|1": {
      id: "4",
      answerGroupId: "3",
      questionId: "4",
      answerData: "test4",
      errors: ["error"]
    },
    "1|4": {
      id: "5",
      answerGroupId: "4",
      questionId: "1",
      answerData: "test5"
    }
  };
  const allAnswerGroups = [
    answerGroup1,
    answerGroup2,
    answerGroup3,
    answerGroup4
  ];

  describe("ProcessAnswersForStore", () => {
    const oldData = {
      questionnaireResponse: {
        answers: values(allAnswers)
      }
    };
    const objectName = "questionnaireResponse";

    test("return same data object if changed answers array is empty", () => {
      const answers = [];

      const newData = processAnswersForStore({
        objectName,
        answers,
        data: oldData
      });

      expect(newData).toBe(oldData);
    });

    test("return updated data object based on changed answers", () => {
      const answers = [
        { id: "1", answerGroupId: "1", questionId: "1", answerData: "updated" },
        { id: "4", answerGroupId: "4", questionId: "1", answerData: "" },
        { id: "6", answerGroupId: "3", questionId: "2", answerData: "added" }
      ];

      const newData = processAnswersForStore({
        objectName,
        answers,
        data: oldData
      });

      expect(newData).toMatchSnapshot();
    });
  });

  describe("getAnswersField", () => {
    const data = {
      answerQuestions: values(allAnswers)
    };
    const errors = getAnswersField("errors", "answerQuestions", data);

    test("returns all the errors in answer", () => {
      expect(errors).toEqual(["error"]);
    });
  });

  describe("hasErrors", () => {
    const data = {
      answerQuestions: values(allAnswers)
    };

    test("returns true when there are errors imn answers", () => {
      expect(hasErrors("answerQuestions", data)).toBeTruthy();
    });
    test("returns false when no errors in answers", () => {
      const newData = {
        answerQuestions: []
      };

      expect(hasErrors("answerQuestions", newData)).toBeFalsy();
    });
  });

  describe("hasWarnings", () => {
    const data = {
      answerQuestions: [{ warnings: ["warning"] }]
    };

    test("returns true when there are errors in answers", () => {
      expect(hasWarnings("answerQuestions", data)).toBeTruthy();
    });
    test("returns false when no errors in answers", () => {
      const newData = {
        answerQuestions: []
      };

      expect(hasWarnings("answerQuestions", newData)).toBeFalsy();
    });
  });

  describe("hasErrorsOrWarnings", () => {
    test("returns true when there are warnings in answers", () => {
      const data = {
        answerQuestions: [{ warnings: ["warning"] }]
      };

      expect(hasErrorsOrWarnings("answerQuestions", data)).toBeTruthy();
    });
    test("returns true when there are errors in answers", () => {
      const data = {
        answerQuestions: values(allAnswers)
      };

      expect(hasErrorsOrWarnings("answerQuestions", data)).toBeTruthy();
    });
    test("returns false when no errors or warnings in answers", () => {
      const newData = {
        answerQuestions: []
      };

      expect(hasErrorsOrWarnings("answerQuestions", newData)).toBeFalsy();
    });
  });

  describe("checkMutationErrors", () => {
    const testFunction = jest.fn();
    const answerError = errorsAnsWarnings => testFunction(errorsAnsWarnings);
    const mutationName = "answerQuestions";

    test("returns true when there are no warnings or erros", () => {
      const data = {
        answerQuestions: []
      };

      expect(
        checkMutationErrors({ data, mutationName, answerError })
      ).toBeTruthy();
    });
    test("returns errors and warnings when there are errors in answers", () => {
      const data = {
        answerQuestions: append({ warnings: ["warning"] }, values(allAnswers))
      };

      checkMutationErrors({ data, mutationName, answerError });
      expect(testFunction).toHaveBeenCalledWith({
        errors: ["error"],
        warnings: ["warning"]
      });
    });
  });

  describe("handleGraphQLErrors", () => {
    const testFunction = jest.fn();
    const answerError = errors => testFunction(errors);

    test("calls answerError with error", () => {
      const graphQLErrors = ["error 1", "error 2"];

      handleGraphQLErrors({ graphQLErrors, answerError });
      expect(testFunction).toHaveBeenCalled();
    });
  });

  describe("cleanServerAnswers", () => {
    const savedAnswers = [
      { answerGroupId: "1", questionId: "1", answerData: "updated" },
      { answerGroupId: "4", questionId: "1", answerData: "" },
      { answerGroupId: "3", questionId: "2", answerData: "added" }
    ];
    const pathToAnswers = ["questionAnswers"];
    const serverData = {
      questionAnswers: [
        { id: "1", answerGroupId: "1", questionId: "1", answerData: "updated" },
        null,
        { id: "6", answerGroupId: "3", questionId: "2", answerData: "added" }
      ]
    };

    test("remove null answers and replace it with answer", () => {
      expect(
        cleanServerAnswers(serverData, pathToAnswers, savedAnswers)
      ).toMatchSnapshot();
    });
  });

  describe("formatAnswer", () => {
    const answer = {
      answerData: "1",
      answerArray: ["1"],
      question: { id: "1" },
      answerGroup: { id: "2" },
      answerType: "open"
    };
    const expectedFormattedAnswer = {
      value: "1",
      valueArray: ["1"],
      questionId: "1",
      answerGroupId: "2",
      answerType: "open"
    };

    test("returns correctly formatted answer", () => {
      expect(formatAnswer(answer)).toEqual(expectedFormattedAnswer);
    });
  });

  describe("main method", () => {
    const props = {
      questionnaire: {},
      allAnswers,
      allAnswerGroups,
      visibleQuestions: [],
      answerQuestions: null
    };
    const answers = [
      {
        answerGroupId: "1",
        answerGroup: { id: "1" },
        questionId: "1",
        question: { id: "1" },
        answerData: "updated"
      },
      {
        answerGroupId: "4",
        answerGroup: { id: "4" },
        questionId: "1",
        question: { id: "1" },
        answerData: ""
      },
      {
        answerGroupId: "3",
        answerGroup: { id: "3" },
        questionId: "2",
        question: { id: "2" },
        answerData: "added"
      }
    ];

    Engine.processNodeWalkResults = jest.fn();

    test("if default group returned from node walk mutationName called", () => {
      const testFunction = jest.fn();
      const answerQuestions = () => testFunction();

      props.answerQuestions = answerQuestions;
      Engine.evaluateAnswers = jest.fn();
      Engine.evaluateAnswers.mockReturnValue(Engine.defaultGroups);

      saveAnswers(props, answers);

      expect(testFunction).toHaveBeenCalled();
      expect(Engine.processNodeWalkResults).not.toHaveBeenCalled();
    });

    test("if a result returned from node walk processNodeWalkResults called", () => {
      const testFunction = jest.fn();
      const answerQuestions = () => testFunction();

      props.answerQuestions = answerQuestions;
      Engine.evaluateAnswers = jest.fn();
      Engine.evaluateAnswers.mockReturnValue({ passing: [true] });

      saveAnswers(props, answers);

      expect(testFunction).not.toHaveBeenCalled();
      expect(Engine.processNodeWalkResults).toHaveBeenCalled();
    });
  });
});
