import validation, { validationExists, validateAnswer } from "../validation";

describe("validations", () => {
  const validator = {
    validationType: "RealTimePresence",
    failureMessage: "be present error",
    severity: "error"
  };
  const validator2 = {
    validationType: "RealTimePresence",
    failureMessage: "be present warning",
    severity: "warning"
  };
  const validator3 = {
    validationType: "RealTimePresence",
    failureMessage: "be present error 2",
    severity: "error"
  };
  const validator4 = {
    validationType: "RealTimePresence",
    failureMessage: "be present warning 2",
    severity: "warning"
  };

  test("if Validation doesn't exists returns false ", () => {
    expect(validationExists({ type: "InvalidValidation" })).toBeFalsy();
  });
  test("RealTimePresence validation", () => {
    expect(validationExists(validator.validationType)).toBeTruthy();
    expect(validation[validator.validationType](validator, "")).toEqual({
      valid: false,
      errors: [validator.failureMessage]
    });
    expect(validation[validator.validationType](validator, "test")).toEqual({
      valid: true
    });
    expect(validation[validator2.validationType](validator2, "")).toEqual({
      valid: false,
      warnings: [validator2.failureMessage]
    });
  });
  test("validate answer returns valid = true when question has no validators", () => {
    const question = { id: "1", validators: [] };
    const result = validateAnswer(question, "");

    expect(result.valid).toBeTruthy();
    expect(result.errors).toEqual([]);
    expect(result.warnings).toEqual([]);
  });

  test("validate answer returns valid = true when question has only unimplemented validator", () => {
    const question = { id: "1", validators: [{ type: "Unimplemented" }] };
    const result = validateAnswer(question, "");

    expect(result.valid).toBeTruthy();
    expect(result.errors).toEqual([]);
    expect(result.warnings).toEqual([]);
  });

  test("validate answer returns valid = false when question has one failing validator", () => {
    const question = {
      id: "1",
      validators: [
        validator,
        validator2,
        validator3,
        validator4,
        { type: "Unimplemented" }
      ]
    };
    const result = validateAnswer(question, "");

    expect(result.valid).toBeFalsy();
    expect(result.errors).toEqual(["be present error", "be present error 2"]);
    expect(result.warnings).toEqual([
      "be present warning",
      "be present warning 2"
    ]);
  });

  test("validate answer returns valid = true when all question validators pass", () => {
    const question = {
      id: "1",
      validators: [
        validator,
        validator2,
        validator3,
        validator4,
        { type: "Unimplemented" }
      ]
    };

    const result = validateAnswer(question, "test");

    expect(result.valid).toBeTruthy();
    expect(result.errors).toEqual([]);
    expect(result.warnings).toEqual([]);
  });
});
