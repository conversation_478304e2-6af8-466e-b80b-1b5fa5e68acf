import Rule, { ruleTypeExists } from "../rules";

describe("Rule strategies", () => {
  test("if Rule doesn't exists returns false ", () => {
    expect(ruleTypeExists({ type: "InvalidRule" })).toBeFalsy();
  });
  test("enumerableEquality rule", () => {
    const rule = {
      type: "EnumerableEquality",
      questionIds: ["66"],
      equalityValue: "156"
    };

    expect(ruleTypeExists(rule)).toBeTruthy();
    expect(Rule[rule.type](rule, [null])).toBe("failing");
    expect(Rule[rule.type](rule, [{ question: { id: "66" } }])).toBe("failing");
    expect(
      Rule[rule.type](rule, [{ answerData: "157", questionId: "66" }])
    ).toBe("failing");
    expect(
      Rule[rule.type](rule, [{ answerData: "156", questionId: "65" }])
    ).toBe("failing");
    expect(
      Rule[rule.type](rule, [{ answerData: "156", questionId: "66" }])
    ).toBe("passing");
  });
  test("unconditional Rule", () => {
    const rule = { type: "Unconditional" };

    expect(ruleTypeExists(rule)).toBeTruthy();
    expect(Rule[rule.type](rule, [null])).toBe("passing");
    expect(Rule[rule.type](rule, [{ questionId: "66" }])).toBe("passing");
    expect(
      Rule[rule.type](rule, [{ answerData: "157", questionId: "66" }])
    ).toBe("passing");
    expect(
      Rule[rule.type](rule, [{ answerData: "156", questionId: "65" }])
    ).toBe("passing");
    expect(
      Rule[rule.type](rule, [{ answerData: "156", questionId: "66" }])
    ).toBe("passing");
  });
  // test("EnumerableInclusion Rule", () => {
  //   const questions = {
  //     "66": {
  //       id: "66",
  //       questionEnumerables: [
  //         { id: "154", description: "Yes" },
  //         { id: "155", description: "No" }
  //       ]
  //     },
  //     "67": {
  //       id: "67",
  //       questionEnumerables: [
  //         { id: "156", description: "Yes" },
  //         { id: "157", description: "No" }
  //       ]
  //     }
  //   };
  //   const questionnaire = { questions };
  //   const rule = {
  //     type: "EnumerableInclusion",
  //     includedIn: ["Yes"],
  //     questionIds: ["66"]
  //   };

  //   expect(ruleTypeExists(rule)).toBeTruthy();
  //   expect(Rule[rule.type](rule, [null], { questions })).toBe("failing");
  //   expect(
  //     Rule[rule.type](rule, [{ answerArray: ["154"], questionId: "66" }], {
  //       questionnaire
  //     })
  //   ).toBe("passing");
  //   expect(
  //     Rule[rule.type](rule, [{ answerArray: ["155"], questionId: "66" }], {
  //       questionnaire
  //     })
  //   ).toBe("failing");
  //   expect(
  //     Rule[rule.type](rule, [{ answerArray: ["156"], questionId: "67" }], {
  //       questionnaire
  //     })
  //   ).toBe("failing");
  //   expect(
  //     Rule[rule.type](rule, [{ answerArray: ["157"], questionId: "67" }], {
  //       questionnaire
  //     })
  //   ).toBe("failing");
  // });
});
