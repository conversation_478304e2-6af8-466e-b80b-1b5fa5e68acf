import PartialUpdate, {
  updateVisibleQuestions,
  filterAnswersFromDeletedAnswerGroups,
  updateAfterMutation,
  formatDeletedAnswersForNodeWalk
} from "../partialUpdate";
import * as saveAnswer from "../saveAnswer";
import * as saveAnswers from "../saveAnswers";

describe("partialUpdate", () => {
  describe("updateVisibleQuestions", () => {
    const serverData = {
      updateAnswerGroups: {
        addedVisibleQuestions: [{ id: "1|13" }, { id: "2|4" }],
        removedVisibleQuestions: [{ id: "1|1" }, { id: "2|1" }]
      }
    };
    const storeData = {
      questionnaireResponse: {
        visibleQuestions: [
          { id: "1|1" },
          { id: "1|2" },
          { id: "1|3" },
          { id: "2|1" },
          { id: "2|2" }
        ]
      }
    };

    test("returns updated visible questions array", () => {
      expect(updateVisibleQuestions(serverData, storeData)).toMatchSnapshot();
    });
  });

  describe("updateDescendantAnswerGroups", () => {
    const descendantsWalkResults = [
      {
        status: "passing",
        parentId: "1",
        parentGroupId: "1",
        isDescendant: true
      }
    ];
    const context = {};
    const response = {
      updateAnswerGroups: {
        answerGroups: [
          { id: "4", parentId: "1", groupId: "2" },
          { id: "3", parentId: "1", groupId: "1" },
          { id: "5", parentId: "2", groupId: "1" }
        ]
      }
    };

    test("calls partial update with updated result with parentId as 3 ", () => {
      PartialUpdate.partialServerUpdate = newResult =>
        expect(newResult).toMatchSnapshot();
      PartialUpdate.updateDescendantAnswerGroups(
        descendantsWalkResults,
        context,
        response
      );
    });
  });

  describe("filterAnswersFromDeletedAnswerGroups", () => {
    test("removes the answers from non existent answergroups", () => {
      const answers = [
        { id: "1", answerGroupId: "1", questionId: "1" },
        { id: "2", answerGroupId: "2", questionId: "1" },
        { id: "3", answerGroupId: "3", questionId: "2" }
      ];
      const answerGroups = [{ id: 1 }, { id: 3 }];

      expect(
        filterAnswersFromDeletedAnswerGroups(answers, answerGroups)
      ).toMatchSnapshot();
    });
  });

  describe("formatDeletedAnswersForNodeWalk", () => {
    test("adds answer group and question and clean data from answer", () => {
      const answers = [
        { id: "1", answerGroupId: "1", questionId: "1" },
        { id: "2", answerGroupId: "2", questionId: "1" },
        { id: "3", answerGroupId: "3", questionId: "2" }
      ];
      const answerGroups = [
        { id: 1, parentId: 5 },
        { id: 3, parentId: 6 }
      ];
      const questions = { 1: { id: 1 }, 2: { id: 2 } };

      expect(
        formatDeletedAnswersForNodeWalk(answers, answerGroups, questions)
      ).toMatchSnapshot();
    });
  });

  describe("updateAfterMutation", () => {
    const getContext = ({
      savePendingAnswers,
      focusRealField,
      answers = []
    }) => ({
      questionnaireResponseId: "1",
      setOptimistic: jest.fn(),
      objectName: "questionnaireResponse",
      savePendingAnswers,
      focusRealField,
      allAnswerGroups: [{ id: "1" }, { id: "2" }],
      questionnaire: { questions: { 1: { id: "1" }, 2: { id: "2" } } },
      answers
    });
    const getServerData = ({ isOptimistic, errors = [] }) => ({
      updateAnswerGroups: {
        isOptimistic,
        answerGroups: [{ id: "1" }],
        answers: [
          {
            id: "1",
            answerData: "test",
            answerGroupId: "1",
            questionId: "1",
            errors
          }
        ],
        addedVisibleQuestions: [{ id: "1|1" }]
      }
    });
    const deletedAnswers = [
      { id: "2", answerData: null },
      {
        id: "3",
        answerData: "will be deleted",
        answerGroupId: "2",
        questionId: "2"
      }
    ];
    const getStore = storedAnswers => ({
      readQuery: () => ({
        questionnaireResponse: {
          answers: storedAnswers
        }
      }),
      writeQuery: jest.fn()
    });

    test("all the functions are called on update and isOptimistic undefined", () => {
      PartialUpdate.updateDescendantAnswerGroups = jest.fn();
      PartialUpdate.updateVisibleQuestions = jest.fn();
      PartialUpdate.formatDeletedAnswersForNodeWalk = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswers.runNodeWalkAndProcess = jest.fn();
      const savePendingAnswers = jest.fn();
      const focusRealField = jest.fn();
      const context = getContext({
        savePendingAnswers,
        focusRealField,
        answers: [{ answerGroupId: "1", questionId: "1", answerData: "test" }]
      });
      const serverData = getServerData({});
      const descendantsWalkResult = [];
      const store = getStore(deletedAnswers);

      updateAfterMutation({
        context,
        store,
        serverData,
        descendantsWalkResult
      });
      expect(PartialUpdate.updateVisibleQuestions).toHaveBeenCalled();
      expect(PartialUpdate.updateDescendantAnswerGroups).toHaveBeenCalled();
      expect(savePendingAnswers).toHaveBeenCalled();
      expect(focusRealField).toHaveBeenCalled();
      expect(saveAnswer.calculateValues).toHaveBeenCalled();
      expect(PartialUpdate.formatDeletedAnswersForNodeWalk).toHaveBeenCalled();
      expect(saveAnswers.runNodeWalkAndProcess).toHaveBeenCalled();
    });

    test("calculation functions are not called on update and isOptimistic true", () => {
      PartialUpdate.updateDescendantAnswerGroups = jest.fn();
      PartialUpdate.updateVisibleQuestions = jest.fn();
      PartialUpdate.formatDeletedAnswersForNodeWalk = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswers.runNodeWalkAndProcess = jest.fn();
      const savePendingAnswers = jest.fn();
      const focusRealField = jest.fn();
      const context = getContext({
        savePendingAnswers,
        focusRealField,
        answers: [{ answerGroupId: "1", questionId: "1", answerData: "test" }]
      });
      const serverData = getServerData({ isOptimistic: true });
      const descendantsWalkResult = [];
      const store = getStore(deletedAnswers);

      updateAfterMutation({
        context,
        store,
        serverData,
        descendantsWalkResult
      });
      expect(PartialUpdate.updateVisibleQuestions).toHaveBeenCalled();
      expect(PartialUpdate.updateDescendantAnswerGroups).not.toHaveBeenCalled();
      expect(savePendingAnswers).not.toHaveBeenCalled();
      expect(focusRealField).not.toHaveBeenCalled();
      expect(saveAnswer.calculateValues).not.toHaveBeenCalled();
      expect(PartialUpdate.formatDeletedAnswersForNodeWalk).toHaveBeenCalled();
      expect(saveAnswers.runNodeWalkAndProcess).toHaveBeenCalled();
    });

    test("calculation functions are called on update and isOptimistic undefined, but not deleted answers nodewalk when none exist", () => {
      PartialUpdate.updateDescendantAnswerGroups = jest.fn();
      PartialUpdate.updateVisibleQuestions = jest.fn();
      PartialUpdate.formatDeletedAnswersForNodeWalk = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswers.runNodeWalkAndProcess = jest.fn();
      const savePendingAnswers = jest.fn();
      const focusRealField = jest.fn();
      const context = getContext({
        savePendingAnswers,
        focusRealField,
        answers: [{ answerGroupId: "1", questionId: "1", answerData: "test" }]
      });
      const serverData = getServerData({});
      const descendantsWalkResult = [];
      const store = getStore([]);

      updateAfterMutation({
        context,
        store,
        serverData,
        descendantsWalkResult
      });
      expect(PartialUpdate.updateVisibleQuestions).toHaveBeenCalled();
      expect(PartialUpdate.updateDescendantAnswerGroups).toHaveBeenCalled();
      expect(savePendingAnswers).toHaveBeenCalled();
      expect(focusRealField).toHaveBeenCalled();
      expect(saveAnswer.calculateValues).toHaveBeenCalled();
      expect(
        PartialUpdate.formatDeletedAnswersForNodeWalk
      ).not.toHaveBeenCalled();
      expect(saveAnswers.runNodeWalkAndProcess).not.toHaveBeenCalled();
    });

    test("no function is called on update and serverData with errors", () => {
      PartialUpdate.updateDescendantAnswerGroups = jest.fn();
      PartialUpdate.updateVisibleQuestions = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswer.calculateValues = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswers.cleanServerAnswers = jest.fn();
      // eslint-disable-next-line import/namespace
      saveAnswers.runNodeWalkAndProcess = jest.fn();
      const savePendingAnswers = jest.fn();
      const focusRealField = jest.fn();
      const context = getContext({
        savePendingAnswers,
        focusRealField
      });
      const serverData = getServerData({
        isOptimistic: true,
        errors: [{ message: "test" }]
      });
      const descendantsWalkResult = [];
      const store = getStore(deletedAnswers);

      updateAfterMutation({
        context,
        store,
        serverData,
        descendantsWalkResult
      });
      expect(PartialUpdate.updateVisibleQuestions).not.toHaveBeenCalled();
      expect(PartialUpdate.updateDescendantAnswerGroups).not.toHaveBeenCalled();
      expect(savePendingAnswers).not.toHaveBeenCalled();
      expect(focusRealField).not.toHaveBeenCalled();
      expect(saveAnswer.calculateValues).not.toHaveBeenCalled();
      expect(saveAnswers.runNodeWalkAndProcess).not.toHaveBeenCalled();
    });
  });
});
