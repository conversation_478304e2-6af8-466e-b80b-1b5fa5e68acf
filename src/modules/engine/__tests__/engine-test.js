import { assoc, evolve, indexBy, pipe, prop } from "ramda";
import Engine from "..";
import { processEdges } from "modules/questionnaire/services/questionnaire";

describe("rules engine", () => {
  let questionnaire = {
    questions: {
      64: { id: "64" },
      66: { id: "66" },
      72: { id: "72" },
      193: { id: "193" },
      197: { id: "197" }
    },
    nodes: [
      {
        id: "1",
        groupId: null,
        edges: [
          {
            id: "1",
            logicType: "All",
            startNodeId: "1",
            endNodeId: "2",
            rules: [
              {
                id: "1-0",
                type: "Unconditional",
                finders: []
              }
            ]
          },
          {
            id: "2",
            logicType: "All",
            startNodeId: "1",
            endNodeId: "3",
            rules: [
              {
                id: "2-0",
                type: "Unconditional",
                finders: []
              }
            ]
          },
          {
            id: "3",
            logicType: "All",
            startNodeId: "1",
            endNodeId: "4",
            rules: [
              {
                id: "3-0",
                type: "Unconditional",
                finders: []
              }
            ]
          },
          {
            id: "7",
            logicType: "All",
            startNodeId: "1",
            endNodeId: "8",
            rules: [
              {
                id: "7-0",
                type: "Unconditional",
                finders: []
              }
            ]
          },
          {
            id: "59",
            logicType: "All",
            startNodeId: "1",
            endNodeId: "61",
            rules: [
              {
                id: "59-0",
                type: "Unconditional",
                finders: []
              }
            ]
          }
        ]
      },
      {
        id: "2",
        groupId: "1",
        edges: []
      },
      {
        id: "3",
        groupId: "2",
        edges: []
      },
      {
        id: "4",
        groupId: "3",
        edges: [
          {
            id: "4",
            logicType: "All",
            startNodeId: "4",
            endNodeId: "5",
            rules: [
              {
                id: "4-0",
                type: "EnumerableEquality",
                finders: [
                  {
                    id: "4-0-0",
                    type: "Null",
                    questionId: "64"
                  }
                ],
                equalityValue: "154",
                questionIds: ["64"]
              }
            ]
          },
          {
            id: "5",
            logicType: "All",
            startNodeId: "4",
            endNodeId: "6",
            rules: [
              {
                id: "5-0",
                type: "EnumerableEquality",
                finders: [
                  {
                    id: "5-0-0",
                    type: "Null",
                    questionId: "66"
                  }
                ],
                equalityValue: "156",
                questionIds: ["66"]
              }
            ]
          },
          {
            id: "6",
            logicType: "All",
            startNodeId: "6",
            endNodeId: "7",
            rules: [
              {
                id: "6-0",
                type: "EnumerableEquality",
                finders: [
                  {
                    id: "6-0-0",
                    type: "Null",
                    questionId: "72"
                  }
                ],
                equalityValue: "166",
                questionIds: ["72"]
              }
            ]
          }
        ]
      },
      {
        id: "5",
        groupId: "4",
        edges: []
      },
      {
        id: "6",
        groupId: "5",
        edges: []
      },
      {
        id: "7",
        groupId: "6",
        edges: []
      },
      {
        id: "34",
        groupId: "33",
        edges: [
          {
            id: "33",
            logicType: "Any",
            startNodeId: "34",
            endNodeId: "35",
            rules: [
              {
                id: "33-0",
                type: "DateComparison",
                finders: [
                  {
                    id: "33-0-0",
                    type: "DateFinder::Any",
                    questionId: "197"
                  },
                  {
                    id: "33-0-1",
                    type: "DateFinder::DependentAnswer",
                    questionId: "193"
                  }
                ]
              },
              {
                id: "33-1",
                type: "EnumerableEquality",
                finders: [
                  {
                    id: "33-1-0",
                    type: "Any",
                    questionId: "66"
                  }
                ],
                equalityValue: "156",
                questionIds: ["66"]
              }
            ]
          }
        ]
      },
      {
        id: "35",
        groupId: "34",
        edges: []
      },
      {
        id: "51",
        groupId: "44",
        edges: [
          {
            id: "51",
            logicType: "All",
            startNodeId: "51",
            endNodeId: "52",
            rules: [
              {
                id: "51-0",
                type: "EnumerableEquality",
                finders: [
                  {
                    id: "51-0-0",
                    type: "Null",
                    questionId: "177"
                  }
                ],
                equalityValue: "166",
                questionIds: ["177"]
              }
            ]
          }
        ]
      },
      {
        id: "52",
        groupId: "45",
        edges: []
      }
    ]
  };

  questionnaire = pipe(evolve({ nodes: indexBy(prop("id")) }), q =>
    assoc("edges", processEdges(q), q)
  )(questionnaire);

  test("nodewalk results passing", () => {
    const answer = {
      question: { id: "64" },
      questionId: "64",
      answerGroup: { id: "675", parentId: null, groupId: "3" },
      answerGroupId: "675",
      answerData: "154"
    };
    const result = Engine.nodeWalk(answer, {
      questionnaire,
      allAnswers: [answer],
      allAnswerGroups: [answer.answerGroup]
    });

    expect(result).toMatchSnapshot();
  });

  test("nodewalk results failing", () => {
    const answer = {
      question: { id: "64" },
      questionId: "64",
      answerGroup: { id: "675", parentId: null, groupId: "3" },
      answerGroupId: "675",
      answerData: "155"
    };
    const result = Engine.nodeWalk(answer, {
      questionnaire,
      allAnswers: [answer],
      allAnswerGroups: [answer.answerGroup]
    });

    expect(result).toMatchSnapshot();
  });

  test("nodewalk results unimplemented", () => {
    const answer = {
      question: { id: "66" },
      questionId: "66",
      answerGroup: { id: "675", parentId: null, groupId: "33" },
      answerGroupId: "675",
      answerData: "157"
    };
    const result = Engine.nodeWalk(answer, {
      questionnaire,
      allAnswers: [answer],
      allAnswerGroups: [answer.answerGroup]
    });

    expect(result).toMatchSnapshot();
  });

  test("edgeTarget is inactive", () => {
    const answer = {
      question: { id: "66" },
      questionId: "66",
      answerGroup: { id: "51", parentId: null, groupId: "44" },
      answerGroupId: "51",
      answerData: "166"
    };

    const result = Engine.findAnswers({
      questionnaire,
      allAnswers: [answer],
      contextAnswerGroupId: "52"
    })([
      {
        id: "51-0-0",
        type: "Null",
        questionId: "177"
      }
    ]);

    expect(result).toBe("unimplemented");
  });

  test("when question has calculationTarget true the value calculations is called", () => {
    jest.useFakeTimers();
    const testFunction = jest.fn();
    const valueCalculations = () =>
      new Promise(resolve => {
        testFunction();
        process.nextTick(() =>
          resolve({
            data: { valueCalculations: [{ question: { edgeTarget: false } }] }
          })
        );
      });
    const question = { id: "1", calculationTarget: true };
    const answerGroup = { id: "1" };

    Engine.calculateValues({
      valueCalculations,
      question,
      answerGroup,
      setSaving: jest.fn()
    });
    jest.runAllTimers();
    expect(testFunction).toHaveBeenCalled();
  });

  test("when question has calculationTarget false the value calculations is not called", () => {
    jest.useFakeTimers();
    const testFunction = jest.fn();
    const valueCalculations = () =>
      new Promise(resolve => {
        testFunction();
        resolve({ question: { edgeTarget: false } });
      });
    const question = { id: "1", calculationTarget: false };
    const answerGroup = { id: "1" };

    Engine.calculateValues({ valueCalculations, question, answerGroup });
    jest.runAllTimers();
    expect(testFunction).not.toHaveBeenCalled();
  });

  test("when question has edgeTarget true the nodeWalk is called", () => {
    Engine.nodeWalk = jest.fn();
    Engine.nodeWalk.mockReturnValue(Engine.defaultGroups);
    Engine.processNodeWalkResults = jest.fn();

    const question = { id: "1", edgeTarget: true };
    const question2 = { id: "2", edgeTarget: false };
    const answerGroup = { id: "1" };
    const answerGroup2 = { id: "2" };
    const answers = [
      { id: "1", answerData: "11", answerGroup, question },
      { id: "2", answerData: "12", answerGroup, question: question2 },
      { id: "3", answerData: "13", answerGroup: answerGroup2, question }
    ];

    Engine.evaluateAnswersForEdges({
      questionnaire: { nodes: [] },
      questionnaireResponseId: "1",
      setSaving: jest.fn()
    })(answers);
    expect(Engine.nodeWalk).toHaveBeenCalledTimes(2);
  });
});
