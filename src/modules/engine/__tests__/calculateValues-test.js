import { processAnswersForStore, calculateValues } from "../calculateValues";
import { values } from "ramda";
import Engine from "..";

describe("calculateValues", () => {
  const allAnswers = {
    "1|1": {
      id: "1",
      answerGroupId: "1",
      questionId: "1",
      answerData: "test",
      answerArray: ["test"]
    },
    "2|1": {
      id: "2",
      answerGroupId: "1",
      questionId: "2",
      answerData: "test2",
      answerArray: []
    },
    "3|2": {
      id: "3",
      answerGroupId: "2",
      questionId: "3",
      answerData: "1",
      answerArray: []
    }
  };

  describe("ProcessAnswersForStore", () => {
    const oldData = {
      questionnaireResponse: {
        answers: values(allAnswers)
      }
    };
    const objectName = "questionnaireResponse";

    test("return same data object if changed answers array is empty", () => {
      const answers = [];

      const newData = processAnswersForStore({
        objectName,
        answers,
        data: oldData
      });

      expect(newData).toBe(oldData);
    });

    test("cleans data object with new answer data and answer array if values are empty", () => {
      const answers = [
        {
          id: "1",
          answerGroupId: "1",
          questionId: "1",
          answerData: "update",
          answerArray: ["test"]
        },
        {
          id: "2",
          answerGroupId: "1",
          questionId: "2",
          answerData: "add",
          answerArray: []
        },
        {
          id: "3",
          answerGroupId: "2",
          questionId: "3",
          answerData: null,
          answerArray: []
        },
        {
          id: "4",
          answerGroupId: "3",
          questionId: "4",
          answerData: null,
          answerArray: []
        }
      ];

      const newData = processAnswersForStore({
        objectName,
        answers,
        data: oldData
      });

      expect(newData).toMatchSnapshot();
    });
  });
  describe("calculateValues", () => {
    let testFunction = jest.fn();
    const sourceAnswer = {
      id: "1",
      questionId: "1",
      answerGroupId: "1",
      answerType: "open",
      answerData: "source answer"
    };
    const store = {
      readQuery: () => ({
        questionnaireResponse: {
          answers: [sourceAnswer],
          answerGroups: [{ id: "1" }, { id: "2" }]
        }
      }),
      writeQuery: jest.fn()
    };
    const data = {
      valueCalculations: [
        {
          id: "2",
          answerGroupId: "2",
          questionId: "2",
          answerData: "calculated value",
          answerType: "open"
        }
      ]
    };
    const valueCalculations = options =>
      new Promise((resolve, _reject) => {
        const { update } = options;

        testFunction();
        if (update) {
          update(store, { data });
        }
        resolve();
      });

    test("it calls mutation when question is a calculation target", () => {
      jest.useFakeTimers();
      testFunction = jest.fn();
      const context = {
        setSaving: jest.fn(),
        valueCalculations,
        question: { id: 1, calculationTarget: true },
        answerGroup: { id: 1 }
      };

      calculateValues(context);
      jest.runAllTimers();
      expect(testFunction).toHaveBeenCalled();
    });
    test("it doesn't call mutation when question is not a calculation target", () => {
      jest.useFakeTimers();
      testFunction = jest.fn();
      const context = {
        setSaving: jest.fn(),
        valueCalculations,
        question: { calculationTarget: false }
      };

      calculateValues(context);
      jest.runAllTimers();
      expect(testFunction).not.toHaveBeenCalled();
    });
    test("calls evaluateAnswersForEdges with the new answers", () => {
      jest.useFakeTimers();
      Engine.evaluateAnswersForEdges = context => {
        expect(context).toMatchSnapshot();
      };
      const context = {
        setSaving: jest.fn(),
        valueCalculations,
        question: { id: "1", calculationTarget: true },
        answerGroup: { id: "1" },
        allAnswers: [sourceAnswer],
        allAnswerGroups: [{ id: "1" }, { id: "2" }],
        objectName: "questionnaireResponse",
        responseQuery: { id: "1", text: "text" }
      };

      calculateValues(context);
      jest.runAllTimers();
    });
    test("it calls setFormValue when question is a calculation target and function set", () => {
      jest.useFakeTimers();
      const testSetFormValue = jest.fn();

      const context = {
        setSaving: jest.fn(),
        valueCalculations,
        question: { id: 1, calculationTarget: true },
        answerGroup: { id: 1 },
        allAnswers: [sourceAnswer],
        allAnswerGroups: [{ id: "1" }, { id: "2" }],
        objectName: "questionnaireResponse",
        responseQuery: { id: "1", text: "text" },
        setFormValue: testSetFormValue
      };

      calculateValues(context);
      jest.runAllTimers();
      expect(testSetFormValue).toHaveBeenCalledWith("2|2", "calculated value");
    });
  });
});
