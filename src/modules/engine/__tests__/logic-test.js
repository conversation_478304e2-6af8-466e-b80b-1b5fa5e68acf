import Logic, { logicTypeExists } from "../logic";

describe("logic strategies", () => {
  test("if logic doesn't exists returns false ", () => {
    expect(logicTypeExists("InvalidLogic")).toBeFalsy();
  });
  test("all logic", () => {
    const logicType = "All";

    expect(logicTypeExists(logicType)).toBeTruthy();
    expect(Logic[logicType](["passing", "failing", "unimplemented"])).toBe(
      "failing"
    );
    expect(Logic[logicType](["passing", "passing", "unimplemented"])).toBe(
      "unimplemented"
    );
    expect(Logic[logicType](["passing", "passing", "passing"])).toBe("passing");
  });
  test("any logic", () => {
    const logicType = "Any";

    expect(logicTypeExists(logicType)).toBeTruthy();
    expect(Logic[logicType](["passing", "failing", "unimplemented"])).toBe(
      "passing"
    );
    expect(Logic[logicType](["failing", "failing", "unimplemented"])).toBe(
      "unimplemented"
    );
    expect(Logic[logicType](["failing", "failing", "failing"])).toBe("failing");
  });
  test("none logic", () => {
    const logicType = "None";

    expect(logicTypeExists(logicType)).toBeTruthy();
    expect(Logic[logicType](["passing", "failing", "unimplemented"])).toBe(
      "failing"
    );
    expect(Logic[logicType](["failing", "failing", "unimplemented"])).toBe(
      "unimplemented"
    );
    expect(Logic[logicType](["failing", "failing", "failing"])).toBe("passing");
  });
});
