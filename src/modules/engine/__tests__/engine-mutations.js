import { updateVisibleQuestions } from "../partialUpdate";

describe("updateVisibleQuestions", () => {
  test("adds and removes visble questions based on data from server", () => {
    const serverData = {
      updateAnswerGroups: {
        addedVisibleQuestions: [
          { id: "68|1223", __typename: "VisibleQuestion" }
        ],
        removedVisibleQuestions: [
          { id: "65|1232", __typename: "VisibleQuestion" }
        ],
        __typename: "UpdateResult"
      }
    };

    const data = {
      questionnaireResponse: {
        visibleQuestions: [
          { id: "86|830", __typename: "VisibleQuestion" },
          { id: "256|1059", __typename: "VisibleQuestion" },
          { id: "65|1232", __typename: "VisibleQuestion" },
          { id: "53|674", __typename: "VisibleQuestion" },
          { id: "54|674", __typename: "VisibleQuestion" },
          { id: "55|674", __typename: "VisibleQuestion" },
          { id: "56|674", __typename: "VisibleQuestion" },
          { id: "191|674", __typename: "VisibleQuestion" },
          { id: "200|674", __typename: "VisibleQuestion" },
          { id: "228|674", __typename: "VisibleQuestion" },
          { id: "43|673", __typename: "VisibleQuestion" },
          { id: "44|673", __typename: "VisibleQuestion" },
          { id: "45|673", __typename: "VisibleQuestion" },
          { id: "46|673", __typename: "VisibleQuestion" },
          { id: "47|673", __typename: "VisibleQuestion" },
          { id: "48|673", __typename: "VisibleQuestion" },
          { id: "49|673", __typename: "VisibleQuestion" },
          { id: "50|673", __typename: "VisibleQuestion" },
          { id: "51|673", __typename: "VisibleQuestion" },
          { id: "51|673", __typename: "VisibleQuestion" },
          { id: "52|673", __typename: "VisibleQuestion" },
          { id: "52|673", __typename: "VisibleQuestion" },
          { id: "189|673", __typename: "VisibleQuestion" },
          { id: "199|673", __typename: "VisibleQuestion" }
        ]
      }
    };
    const visibleQuestions = updateVisibleQuestions(serverData, data);

    expect(visibleQuestions).toMatchSnapshot();
  });
});
