import {
  processAnswerForStore,
  updateAnswersForNodeWalk,
  saveAnswer,
  calculateValues
} from "../saveAnswer";
import { values } from "ramda";
import Engine from "..";

const answerGroup1 = { id: "1" };
const answerGroup2 = { id: "2" };
const answerGroup3 = { id: "3" };
const answerGroup4 = { id: "4" };

describe("saveAnswer", () => {
  const allAnswers = {
    "1|1": {
      id: "1",
      answerGroupId: "1",
      questionId: "1",
      answerData: "test"
    },
    "2|1": {
      id: "2",
      answerGroupId: "1",
      questionId: "2",
      answerData: "test2"
    },
    "3|2": {
      id: "3",
      answerGroupId: "2",
      questionId: "3",
      answerData: "test3"
    },
    "4|1": {
      id: "4",
      answerGroupId: "3",
      questionId: "4",
      answerData: "test4"
    },
    "1|4": {
      id: "5",
      answerGroupId: "4",
      questionId: "1",
      answerData: "test5"
    }
  };
  const allAnswerGroups = [
    answerGroup1,
    answerGroup2,
    answerGroup3,
    answerGroup4
  ];

  describe("ProcessAnswerForStore", () => {
    const testFunction = jest.fn();
    const oldData = {
      questionnaireResponse: {
        answers: values(allAnswers)
      }
    };
    const store = {
      writeQuery: newData => {
        testFunction(newData);
        return newData;
      },
      readQuery: () => oldData
    };
    const responseId = 1;
    const responseQuery = {};
    const objectName = "questionnaireResponse";
    const mutationName = "answerQuestion";

    test("removes answer if value coming back is null", () => {
      const serverData = {
        answerQuestion: null
      };
      const questionAnswerGroup = { id: "1" };
      const question = { id: "1" };

      const newAnswers = processAnswerForStore({
        store,
        serverData,
        responseId,
        questionAnswerGroup,
        question,
        responseQuery,
        objectName,
        mutationName
      });

      expect(newAnswers).toMatchSnapshot();
    });

    test("updates answer if value coming back for a existing answer", () => {
      const serverData = {
        answerQuestion: {
          id: "2",
          answerGroupId: "1",
          questionId: "2",
          answerData: "no test",
          answerType: "open"
        }
      };
      const questionAnswerGroup = { id: "1" };
      const question = { id: "2" };

      const newAnswers = processAnswerForStore({
        store,
        serverData,
        responseId,
        questionAnswerGroup,
        question,
        responseQuery,
        objectName,
        mutationName
      });

      expect(newAnswers).toMatchSnapshot();
    });

    test("add answer if value coming back for a new answer", () => {
      const serverData = {
        answerQuestion: {
          id: "5",
          answerGroupId: "4",
          questionId: "1",
          answerData: "test5",
          answerType: "open"
        }
      };
      const questionAnswerGroup = { id: "4" };
      const question = { id: "1" };

      const newAnswers = processAnswerForStore({
        store,
        serverData,
        responseId,
        questionAnswerGroup,
        question,
        responseQuery,
        objectName,
        mutationName
      });

      expect(newAnswers).toMatchSnapshot();
    });

    test("calls the setFormValue function when the answer has been updated in the server", () => {
      const serverData = {
        answerQuestion: {
          id: "5",
          answerGroupId: "4",
          questionId: "1",
          answerData: "R413",
          answerType: "open"
        }
      };
      const questionAnswerGroup = { id: "4" };
      const question = { id: "1" };
      const setFormValue = jest.fn();
      const newAnswers = processAnswerForStore({
        store,
        serverData,
        responseId,
        questionAnswerGroup,
        question,
        responseQuery,
        objectName,
        mutationName,
        setFormValue,
        value: "r413"
      });

      expect(setFormValue).toHaveBeenCalledWith("1|4", "R413");
      expect(newAnswers).toMatchSnapshot();
    });

    test("doesn't call the setFormValue function when the answer has been updated in the server", () => {
      const serverData = {
        answerQuestion: {
          id: "5",
          answerGroupId: "4",
          questionId: "1",
          answerData: "R413",
          answerType: "open"
        }
      };
      const questionAnswerGroup = { id: "4" };
      const question = { id: "1" };
      const setFormValue = jest.fn();

      processAnswerForStore({
        store,
        serverData,
        responseId,
        questionAnswerGroup,
        question,
        responseQuery,
        objectName,
        mutationName,
        setFormValue,
        value: "R413"
      });
      expect(setFormValue).not.toHaveBeenCalled();
    });
  });

  describe("updateAnswersForNodeWalk", () => {
    test("if empty changedAnswers will return same allANswers", () => {
      const changedAnswers = [];
      const result = updateAnswersForNodeWalk({
        allAnswers,
        allAnswerGroups,
        changedAnswers
      });

      expect(result).toEqual(allAnswers);
    });

    test("will update existing answer, add new answer and leave deleted answer as empty value", () => {
      const changedAnswers = [
        { answerGroupId: "1", questionId: "1", answerData: "updated" },
        { answerGroupId: "4", questionId: "1", answerData: "" },
        { answerGroupId: "3", questionId: "2", answerData: "added" }
      ];
      const result = updateAnswersForNodeWalk({
        allAnswers,
        allAnswerGroups,
        changedAnswers
      });

      expect(result).toMatchSnapshot();
    });
  });

  describe("main method", () => {
    const props = {
      question: { id: "1" },
      questionAnswerGroup: { id: "1" },
      questionnaire: {},
      allAnswers,
      allAnswerGroups,
      answerQuestion: null
    };
    const context = {
      value: "new value",
      answerType: "open",
      answerGroupOverride: null,
      dataFieldName: "answerData",
      mutationName: "answerQuestion"
    };

    Engine.processNodeWalkResults = jest.fn();

    test("if default group returned from node walk mutationName called", () => {
      const testFunction = jest.fn();
      const answerQuestion = () => testFunction();

      props.answerQuestion = answerQuestion;
      Engine.evaluateAnswer = jest.fn();
      Engine.evaluateAnswer.mockReturnValue(Engine.defaultGroups);

      saveAnswer(props, context);

      expect(testFunction).toHaveBeenCalled();
      expect(Engine.processNodeWalkResults).not.toHaveBeenCalled();
    });

    test("if a result returned from node walk processNodeWalkResults called", () => {
      const testFunction = jest.fn();
      const answerQuestion = () => testFunction();

      props.answerQuestion = answerQuestion;
      Engine.evaluateAnswer = jest.fn();
      Engine.evaluateAnswer.mockReturnValue({ passing: [true] });

      saveAnswer(props, context);

      expect(testFunction).not.toHaveBeenCalled();
      expect(Engine.processNodeWalkResults).toHaveBeenCalled();
    });
  });

  describe("calculateValues", () => {
    let isOptimistic = false;
    const props = { questionnaire: {} };
    const answers = [
      {
        answerGroupId: "1",
        answerGroup: answerGroup1,
        questionId: "1",
        question: { id: "1" },
        value: "test",
        answerType: "open"
      },
      {
        answerGroupId: "2",
        answerGroup: answerGroup2,
        questionId: "1",
        question: { id: "1" },
        value: "test2",
        answerType: "open"
      }
    ];

    test("if is optimistic is true the calculations is not called", () => {
      Engine.calculateValues = jest.fn();
      isOptimistic = true;
      calculateValues({
        isOptimistic,
        props,
        answers,
        allNewAnswers: allAnswers
      });
      expect(Engine.calculateValues).not.toHaveBeenCalled();
    });

    test("if is optimistic is false the calculations is called by answers", () => {
      Engine.calculateValues = jest.fn();
      isOptimistic = false;
      calculateValues({
        isOptimistic,
        props,
        answers,
        allNewAnswers: allAnswers
      });
      expect(Engine.calculateValues).toHaveBeenCalledTimes(answers.length);
    });
  });
});
