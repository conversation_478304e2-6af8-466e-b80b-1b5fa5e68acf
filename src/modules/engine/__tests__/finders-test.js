import Finder, { finderTypeExists } from "../finders";

describe("Finder strategies", () => {
  const allAnswers = [
    {
      id: "1",
      answerData: "1",
      answerGroup: { id: "1" },
      questionId: "66"
    },
    {
      id: "2",
      answerData: "1",
      answerGroup: { id: "2" },
      questionId: "66"
    },
    {
      id: "3",
      answerData: "1",
      answerGroup: { id: "1" },
      questionId: "67"
    },
    {
      id: "4",
      answerData: "1",
      answerGroup: { id: "3", parentId: "1" },
      questionId: "68"
    },
    {
      id: "5",
      answerData: "1",
      answerGroup: { id: "5", parentId: "2" },
      questionId: "69"
    },
    {
      id: "6",
      answerData: "1",
      answerGroup: { id: "6" },
      questionId: "66"
    },
    {
      id: "7",
      answerData: "1",
      answerGroup: { id: "7" },
      questionId: "67"
    },
    {
      id: "8",
      answerData: "1",
      answerGroup: { id: "4" },
      questionId: "66"
    }
  ];

  test("if Finder doesn't exists returns false ", () => {
    expect(finderTypeExists({ type: "InvalidFinder" })).toBeFalsy();
  });
  test("null finder", () => {
    const finder = {
      type: "Null"
    };

    expect(finderTypeExists(finder)).toBeTruthy();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "66" },
        contextAnswerGroupId: "1"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "67" },
        contextAnswerGroupId: "1"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "68" },
        contextAnswerGroupId: "1"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "69" },
        contextAnswerGroupId: "1"
      })
    ).toMatchSnapshot();

    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "66" },
        contextAnswerGroupId: "2"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "67" },
        contextAnswerGroupId: "2"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "68" },
        contextAnswerGroupId: "2"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "69" },
        contextAnswerGroupId: "2"
      })
    ).toMatchSnapshot();

    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "66" },
        contextAnswerGroupId: "3"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "67" },
        contextAnswerGroupId: "3"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "68" },
        contextAnswerGroupId: "3"
      })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({
        allAnswers,
        question: { id: "69" },
        contextAnswerGroupId: "3"
      })
    ).toMatchSnapshot();
  });
  test("any finder", () => {
    const finder = { type: "Any" };

    expect(finderTypeExists(finder)).toBeTruthy();
    expect(
      Finder[finder.type]({ allAnswers, question: { id: "66" } })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({ allAnswers, question: { id: "67" } })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({ allAnswers, question: { id: "68" } })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({ allAnswers, question: { id: "69" } })
    ).toMatchSnapshot();
    expect(
      Finder[finder.type]({ allAnswers, question: { id: "1" } })
    ).toMatchSnapshot();
  });
});
