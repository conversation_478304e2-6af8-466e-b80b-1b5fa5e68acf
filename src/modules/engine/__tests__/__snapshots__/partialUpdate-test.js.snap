// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`partialUpdate filterAnswersFromDeletedAnswerGroups removes the answers from non existent answergroups 1`] = `
Array [
  Array [
    Object {
      "answerGroupId": "1",
      "id": "1",
      "questionId": "1",
    },
    Object {
      "answerGroupId": "3",
      "id": "3",
      "questionId": "2",
    },
  ],
  Array [
    Object {
      "answerGroupId": "2",
      "id": "2",
      "questionId": "1",
    },
  ],
]
`;

exports[`partialUpdate formatDeletedAnswersForNodeWalk adds answer group and question and clean data from answer 1`] = `
Array [
  Object {
    "answerGroup": Object {
      "id": 1,
      "parentId": 5,
    },
    "answerGroupId": "1",
    "id": "1",
    "question": Object {
      "id": 1,
    },
    "questionId": "1",
  },
  Object {
    "answerGroup": undefined,
    "answerGroupId": "2",
    "id": "2",
    "question": Object {
      "id": 1,
    },
    "questionId": "1",
  },
  Object {
    "answerGroup": Object {
      "id": 3,
      "parentId": 6,
    },
    "answerGroupId": "3",
    "id": "3",
    "question": Object {
      "id": 2,
    },
    "questionId": "2",
  },
]
`;

exports[`partialUpdate updateDescendantAnswerGroups calls partial update with updated result with parentId as 3  1`] = `
Object {
  "answers": Array [],
  "walkResult": Object {
    "passing": Array [
      Object {
        "parentGroupId": "1",
        "parentId": 3,
        "status": "passing",
      },
    ],
  },
}
`;

exports[`partialUpdate updateVisibleQuestions returns updated visible questions array 1`] = `
Array [
  Object {
    "id": "1|13",
  },
  Object {
    "id": "2|4",
  },
  Object {
    "id": "1|2",
  },
  Object {
    "id": "1|3",
  },
  Object {
    "id": "2|2",
  },
]
`;
