// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`saveAnswer ProcessAnswerForStore add answer if value coming back for a new answer 1`] = `
Array [
  Object {
    "answerData": "test",
    "answerGroupId": "1",
    "id": "1",
    "questionId": "1",
  },
  Object {
    "answerData": "test2",
    "answerGroupId": "1",
    "id": "2",
    "questionId": "2",
  },
  Object {
    "answerData": "test3",
    "answerGroupId": "2",
    "id": "3",
    "questionId": "3",
  },
  Object {
    "answerData": "test4",
    "answerGroupId": "3",
    "id": "4",
    "questionId": "4",
  },
  Object {
    "answerData": "test5",
    "answerGroupId": "4",
    "answerType": "open",
    "id": "5",
    "questionId": "1",
  },
]
`;

exports[`saveAnswer ProcessAnswerForStore calls the setFormValue function when the answer has been updated in the server 1`] = `
Array [
  Object {
    "answerData": "test",
    "answerGroupId": "1",
    "id": "1",
    "questionId": "1",
  },
  Object {
    "answerData": "test2",
    "answerGroupId": "1",
    "id": "2",
    "questionId": "2",
  },
  Object {
    "answerData": "test3",
    "answerGroupId": "2",
    "id": "3",
    "questionId": "3",
  },
  Object {
    "answerData": "test4",
    "answerGroupId": "3",
    "id": "4",
    "questionId": "4",
  },
  Object {
    "answerData": "R413",
    "answerGroupId": "4",
    "answerType": "open",
    "id": "5",
    "questionId": "1",
  },
]
`;

exports[`saveAnswer ProcessAnswerForStore removes answer if value coming back is null 1`] = `
Array [
  Object {
    "answerData": "test2",
    "answerGroupId": "1",
    "id": "2",
    "questionId": "2",
  },
  Object {
    "answerData": "test3",
    "answerGroupId": "2",
    "id": "3",
    "questionId": "3",
  },
  Object {
    "answerData": "test4",
    "answerGroupId": "3",
    "id": "4",
    "questionId": "4",
  },
  Object {
    "answerData": "test5",
    "answerGroupId": "4",
    "id": "5",
    "questionId": "1",
  },
]
`;

exports[`saveAnswer ProcessAnswerForStore updates answer if value coming back for a existing answer 1`] = `
Array [
  Object {
    "answerData": "test",
    "answerGroupId": "1",
    "id": "1",
    "questionId": "1",
  },
  Object {
    "answerData": "no test",
    "answerGroupId": "1",
    "answerType": "open",
    "id": "2",
    "questionId": "2",
  },
  Object {
    "answerData": "test3",
    "answerGroupId": "2",
    "id": "3",
    "questionId": "3",
  },
  Object {
    "answerData": "test4",
    "answerGroupId": "3",
    "id": "4",
    "questionId": "4",
  },
  Object {
    "answerData": "test5",
    "answerGroupId": "4",
    "id": "5",
    "questionId": "1",
  },
]
`;

exports[`saveAnswer updateAnswersForNodeWalk will update existing answer, add new answer and leave deleted answer as empty value 1`] = `
Object {
  "1|1": Object {
    "answerData": "updated",
    "answerGroupId": "1",
    "id": "1",
    "questionId": "1",
  },
  "1|4": Object {
    "answerData": "",
    "answerGroupId": "4",
    "id": "5",
    "questionId": "1",
  },
  "2|1": Object {
    "answerData": "test2",
    "answerGroupId": "1",
    "id": "2",
    "questionId": "2",
  },
  "2|3": Object {
    "answerData": "added",
    "answerGroup": Object {
      "id": "3",
    },
    "answerGroupId": "3",
    "questionId": "2",
  },
  "3|2": Object {
    "answerData": "test3",
    "answerGroupId": "2",
    "id": "3",
    "questionId": "3",
  },
  "4|1": Object {
    "answerData": "test4",
    "answerGroupId": "3",
    "id": "4",
    "questionId": "4",
  },
}
`;
