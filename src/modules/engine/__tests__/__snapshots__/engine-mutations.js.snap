// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`updateVisibleQuestions adds and removes visble questions based on data from server 1`] = `
Array [
  Object {
    "__typename": "VisibleQuestion",
    "id": "68|1223",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "86|830",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "256|1059",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "53|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "54|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "55|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "56|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "191|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "200|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "228|674",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "43|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "44|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "45|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "46|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "47|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "48|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "49|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "50|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "51|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "52|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "189|673",
  },
  Object {
    "__typename": "VisibleQuestion",
    "id": "199|673",
  },
]
`;
