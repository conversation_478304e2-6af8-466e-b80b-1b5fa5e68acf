// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`calculateValues ProcessAnswersForStore cleans data object with new answer data and answer array if values are empty 1`] = `
Object {
  "questionnaireResponse": Object {
    "answers": Array [
      Object {
        "answerArray": Array [
          "test",
        ],
        "answerData": "update",
        "answerGroupId": "1",
        "id": "1",
        "questionId": "1",
      },
      Object {
        "answerArray": Array [],
        "answerData": "add",
        "answerGroupId": "1",
        "id": "2",
        "questionId": "2",
      },
      Object {
        "answerArray": Array [],
        "answerData": null,
        "answerGroupId": "2",
        "id": "3",
        "questionId": "3",
      },
      Object {
        "answerArray": Array [],
        "answerData": null,
        "answerGroupId": "3",
        "id": "4",
        "questionId": "4",
      },
    ],
  },
}
`;

exports[`calculateValues calculateValues calls evaluateAnswersForEdges with the new answers 1`] = `
Object {
  "allAnswerGroups": Array [
    Object {
      "id": "1",
    },
    Object {
      "id": "2",
    },
  ],
  "allAnswers": Array [
    Object {
      "answerData": "source answer",
      "answerGroup": Object {
        "id": "1",
      },
      "answerGroupId": "1",
      "answerType": "open",
      "id": "1",
      "questionId": "1",
    },
    Object {
      "answerData": "calculated value",
      "answerGroup": Object {
        "id": "2",
      },
      "answerGroupId": "2",
      "answerType": "open",
      "id": "2",
      "questionId": "2",
    },
  ],
  "answerGroup": Object {
    "id": "1",
  },
  "objectName": "questionnaireResponse",
  "query": Object {
    "id": "1",
    "text": "text",
  },
  "question": Object {
    "calculationTarget": true,
    "id": "1",
  },
  "responseQuery": Object {
    "id": "1",
    "text": "text",
  },
  "setSaving": [MockFunction] {
    "calls": Array [
      Array [
        true,
      ],
    ],
    "results": Array [
      Object {
        "type": "return",
        "value": undefined,
      },
    ],
  },
  "valueCalculations": [Function],
}
`;

exports[`calculateValues calculateValues it calls setFormValue when question is a calculation target and function set 1`] = `
Object {
  "allAnswerGroups": Array [
    Object {
      "id": "1",
    },
    Object {
      "id": "2",
    },
  ],
  "allAnswers": Array [
    Object {
      "answerData": "source answer",
      "answerGroup": Object {
        "id": "1",
      },
      "answerGroupId": "1",
      "answerType": "open",
      "id": "1",
      "questionId": "1",
    },
    Object {
      "answerData": "calculated value",
      "answerGroup": Object {
        "id": "2",
      },
      "answerGroupId": "2",
      "answerType": "open",
      "id": "2",
      "questionId": "2",
    },
  ],
  "answerGroup": Object {
    "id": 1,
  },
  "objectName": "questionnaireResponse",
  "query": Object {
    "id": "1",
    "text": "text",
  },
  "question": Object {
    "calculationTarget": true,
    "id": 1,
  },
  "responseQuery": Object {
    "id": "1",
    "text": "text",
  },
  "setFormValue": [MockFunction] {
    "calls": Array [
      Array [
        "2|2",
        "calculated value",
      ],
    ],
    "results": Array [
      Object {
        "type": "return",
        "value": undefined,
      },
    ],
  },
  "setSaving": [MockFunction] {
    "calls": Array [
      Array [
        true,
      ],
    ],
    "results": Array [
      Object {
        "type": "return",
        "value": undefined,
      },
    ],
  },
  "valueCalculations": [Function],
}
`;
