// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`fullRefresh addNewAnswerGroups returns updated answer groups array 1`] = `
Array [
  Object {
    "id": "1",
  },
  Object {
    "id": "2",
  },
  Object {
    "id": "3",
  },
  Object {
    "id": "4",
  },
]
`;

exports[`fullRefresh addNewVisibleQuestions returns updated visible questions array 1`] = `
Array [
  Object {
    "id": "1|13",
  },
  Object {
    "id": "2|4",
  },
  Object {
    "id": "1|1",
  },
  Object {
    "id": "1|2",
  },
  Object {
    "id": "1|3",
  },
  Object {
    "id": "2|1",
  },
  Object {
    "id": "2|2",
  },
]
`;

exports[`fullRefresh removeAnswersFromDeletedAnswerGroups removes the answers for the deleted answer groups 1`] = `
Array [
  Object {
    "answerGroupId": "2",
    "id": "2",
    "questionId": "1",
  },
]
`;

exports[`fullRefresh removeDeletedAnswerGroups returns updated answer groups array 1`] = `
Array [
  Object {
    "id": "2",
  },
  Object {
    "id": "4",
  },
]
`;

exports[`fullRefresh removeDeletedVisibleQuestions returns updated visible questions array 1`] = `
Array [
  Object {
    "id": "1|2",
  },
  Object {
    "id": "2|2",
  },
]
`;
