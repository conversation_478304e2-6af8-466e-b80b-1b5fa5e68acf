// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`rules engine nodewalk results failing 1`] = `
Object {
  "failing": Array [
    Object {
      "groupId": 4,
      "parentGroupId": 3,
      "parentId": 675,
      "status": "failing",
    },
  ],
  "passing": Array [],
  "unimplemented": Array [],
}
`;

exports[`rules engine nodewalk results passing 1`] = `
Object {
  "failing": Array [],
  "passing": Array [
    Object {
      "groupId": 4,
      "parentGroupId": 3,
      "parentId": 675,
      "status": "passing",
    },
  ],
  "unimplemented": Array [],
}
`;

exports[`rules engine nodewalk results unimplemented 1`] = `
Object {
  "failing": Array [],
  "passing": Array [],
  "unimplemented": Array [
    Object {
      "groupId": 34,
      "parentGroupId": 33,
      "parentId": 675,
      "status": "unimplemented",
    },
  ],
}
`;
