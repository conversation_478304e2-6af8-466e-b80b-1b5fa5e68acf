// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Finder strategies any finder 1`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "1",
  },
  "id": "1",
  "questionId": "66",
}
`;

exports[`Finder strategies any finder 2`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "1",
  },
  "id": "3",
  "questionId": "67",
}
`;

exports[`Finder strategies any finder 3`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "3",
    "parentId": "1",
  },
  "id": "4",
  "questionId": "68",
}
`;

exports[`Finder strategies any finder 4`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "5",
    "parentId": "2",
  },
  "id": "5",
  "questionId": "69",
}
`;

exports[`Finder strategies any finder 5`] = `undefined`;

exports[`Finder strategies null finder 1`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "1",
  },
  "id": "1",
  "questionId": "66",
}
`;

exports[`Finder strategies null finder 2`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "1",
  },
  "id": "3",
  "questionId": "67",
}
`;

exports[`Finder strategies null finder 3`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "3",
    "parentId": "1",
  },
  "id": "4",
  "questionId": "68",
}
`;

exports[`Finder strategies null finder 4`] = `undefined`;

exports[`Finder strategies null finder 5`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "2",
  },
  "id": "2",
  "questionId": "66",
}
`;

exports[`Finder strategies null finder 6`] = `undefined`;

exports[`Finder strategies null finder 7`] = `undefined`;

exports[`Finder strategies null finder 8`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "5",
    "parentId": "2",
  },
  "id": "5",
  "questionId": "69",
}
`;

exports[`Finder strategies null finder 9`] = `undefined`;

exports[`Finder strategies null finder 10`] = `undefined`;

exports[`Finder strategies null finder 11`] = `
Object {
  "answerData": "1",
  "answerGroup": Object {
    "id": "3",
    "parentId": "1",
  },
  "id": "4",
  "questionId": "68",
}
`;

exports[`Finder strategies null finder 12`] = `undefined`;
