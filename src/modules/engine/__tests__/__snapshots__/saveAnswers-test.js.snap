// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`saveAnswers ProcessAnswersForStore return updated data object based on changed answers 1`] = `
Object {
  "questionnaireResponse": Object {
    "answers": Array [
      Object {
        "answerData": "updated",
        "answerGroupId": "1",
        "id": "1",
        "questionId": "1",
      },
      Object {
        "answerData": "test2",
        "answerGroupId": "1",
        "id": "2",
        "questionId": "2",
      },
      Object {
        "answerData": "test3",
        "answerGroupId": "2",
        "id": "3",
        "questionId": "3",
      },
      Object {
        "answerData": "test4",
        "answerGroupId": "3",
        "errors": Array [
          "error",
        ],
        "id": "4",
        "questionId": "4",
      },
      Object {
        "answerData": "added",
        "answerGroupId": "3",
        "id": "6",
        "questionId": "2",
      },
    ],
  },
}
`;

exports[`saveAnswers cleanServerAnswers remove null answers and replace it with answer 1`] = `
Array [
  Object {
    "answerData": "updated",
    "answerGroupId": "1",
    "id": "1",
    "questionId": "1",
  },
  Object {
    "answerGroupId": "4",
    "questionId": "1",
  },
  Object {
    "answerData": "added",
    "answerGroupId": "3",
    "id": "6",
    "questionId": "2",
  },
]
`;
