import {
  all,
  always,
  assoc,
  both,
  chain,
  curry,
  dissoc,
  either,
  forEach,
  identity,
  ifElse,
  indexBy,
  lensPath,
  map,
  mapObjIndexed,
  not,
  nth,
  path,
  pipe,
  prop,
  reject,
  set,
  T,
  values,
  view
} from "ramda";
import Engine from ".";
import { isNullOrEmpty } from "utils/fp";
import { getMockAnswerQuestion, calculateValues } from "./saveAnswer";

/**
 * Method to update the questionnaire response with a set of modified answers
 * @param {Object} anonymous Hash with the parameters needed, for style more
 *                           than 3 params need to be passed as hash
 * HASH CONTENTS
 * {[Object]} answers      Modified answers to update on store
 * {Object} objectName     Path name to use to get current response object
 * {Object} data           The data from store
 * @return {[Object]}      All answers, including updated ones, from the store.
 */
export const processAnswersForStore = ({ answers, objectName, data }) => {
  // Create a lens to answers object (get answers from store)
  const answersLens = lensPath([objectName, "answers"]);
  const answerId = answer => `${answer.questionId}|${answer.answerGroupId}`;

  if (!isNullOrEmpty(answers)) {
    // index current answers
    let indexedAnswers = pipe(view(answersLens), indexBy(answerId))(data);
    const addRemoveAnswersQR = answer => {
      const addAnswer = () => assoc(answerId(answer), answer, indexedAnswers);
      const removeAnswer = () => dissoc(answerId(answer), indexedAnswers);
      const isEmptyAnswer = both(
        pipe(prop("answerData"), isNullOrEmpty),
        pipe(prop("answerArray"), isNullOrEmpty)
      );

      indexedAnswers = ifElse(
        identity,
        ifElse(isEmptyAnswer, removeAnswer, addAnswer),
        always(indexedAnswers)
      )(answer);
    };

    // merge response answers with existing store list
    forEach(addRemoveAnswersQR, answers);
    // Returns new data object with merged/removed answers
    // updates store with these new answers and removes the deleted ones
    return set(answersLens, values(indexedAnswers), data);
  }
  return data;
};

export const getAnswersField = curry((field, pathToAnswers, data) =>
  pipe(prop(pathToAnswers), chain(prop(field)), reject(isNullOrEmpty))(data)
);
export const hasErrors = curry((pathToAnswers, data) =>
  pipe(getAnswersField("errors", pathToAnswers), isNullOrEmpty, not)(data)
);
export const hasWarnings = curry((pathToAnswers, data) =>
  pipe(getAnswersField("warnings", pathToAnswers), isNullOrEmpty, not)(data)
);
export const hasErrorsOrWarnings = curry((pathToAnswers, data) =>
  either(hasErrors(pathToAnswers), hasWarnings(pathToAnswers))(data)
);

export const getMockAnswerQuestions = answers =>
  map(getMockAnswerQuestion, answers);

const getErrorsAndWarnings = curry((pathToAnswers, data) => {
  const errors = getAnswersField("errors", pathToAnswers, data);
  const warnings = getAnswersField("warnings", pathToAnswers, data);

  return { errors, warnings };
});

export const checkMutationErrors = ({ data, mutationName, answerError }) =>
  ifElse(
    hasErrorsOrWarnings(mutationName),
    pipe(getErrorsAndWarnings(mutationName), answerError),
    T
  )(data);

export const handleGraphQLErrors = ({ graphQLErrors, answerError }) => {
  const formatErrors = data => ({ errors: data });

  map(pipe(formatErrors, answerError), graphQLErrors);
};

// creating array of answers that are empty
const addAnswersToDelete = answers => (value, index) =>
  ifElse(
    always(isNullOrEmpty(value)),
    pipe(nth(index), answer => ({
      answerGroupId: answer.answerGroupId,
      questionId: answer.questionId
    })),
    always(value)
  )(answers);

export const formatAnswer = answer => ({
  value: answer.answerData,
  valueArray: answer.answerArray,
  questionId: answer.question.id,
  answerGroupId: answer.answerGroup.id,
  answerType: answer.answerType
});

const formatAnswerForCalculations = answer => ({
  value: answer.answerData || answer.answerArray,
  question: answer.question,
  questionId: answer.question.id,
  answerGroup: answer.answerGroup,
  answerGroupId: answer.answerGroup.id,
  answerType: answer.answerType
});

/**
 * Method to obtain the server answers by adding path to deleted answers
 * this will allow remove deleted answers from store. We remove the nil value
 * from server with the raw data answerGroup and question ids.
 * @param {Object} serverData is the data obtained from the server
 * @param {[String]} pathToAnswers the path to get answers from server data
 * @param {[Object]} savedAnswers raw answer data that was sent to server to be saved
 * @return {[Object]} A list of answers where we replace nulls with raw data
 */
export const cleanServerAnswers = (serverData, pathToAnswers, savedAnswers) =>
  pipe(
    mapObjIndexed(addAnswersToDelete(savedAnswers)),
    values
  )(path(pathToAnswers, serverData));

/**
 * Method to handles the mutation to save a single answer and calls actions to do after a
 * save
 *
 * @param {Function} mutate function with apollo mutation
 * @param {Object} props properties for the field where the save happened
 * @param {Object} context values of the event that triggered save
 * @return {Void} This only executes mutation no value return
 */
export const handleSaveAnswersMutation = ({ mutate, props, context }) => {
  const { setSaving, answerError, objectName, query, questionnaireResponseId } =
    props;
  const { answers, handleUpdateUserLastModified } = context;

  const formattedAnswers = map(formatAnswer, answers);

  setSaving(true, formattedAnswers);

  // Apollo mutation call (saves answers to server)
  mutate({
    variables: {
      answers: formattedAnswers
    },
    // Method called after the mutation response is received, serverData is
    // the data that comes from response. as we use optimistic response
    // this will be called twice, one with mockedAnswerQuestions and second
    // with server response. Both times the store will be the same so
    // changes from first pass won't exist on second
    update: (store, { data: serverData }) => {
      if (!hasErrors("answerQuestions", serverData)) {
        const answersLens = lensPath([objectName, "answers"]);
        const data = store.readQuery({
          query,
          variables: { id: Number(questionnaireResponseId) }
        });
        // Remove deleted answers from the answers object
        const serverAnswers = cleanServerAnswers(
          serverData,
          ["answerQuestions"],
          formattedAnswers
        );
        // Set new answers on the response object
        const newData = processAnswersForStore({
          answers: serverAnswers,
          objectName,
          data
        });

        // Save changes in the Apollo store
        store.writeQuery({
          query,
          variables: { id: Number(questionnaireResponseId) },
          data: newData
        });

        calculateValues({
          isOptimistic: serverData.answerQuestions.isOptimistic,
          props,
          answers: map(formatAnswerForCalculations, answers),
          allNewAnswers: view(answersLens, newData)
        });
      }
    },
    optimisticResponse: {
      answerQuestions: getMockAnswerQuestions(formattedAnswers)
    }
  })
    // This is called when mutation response returns in parallel to update above
    // cannot do this on update because, as is on a redux action you cannot dispatch
    // inside another
    .then(response => {
      if (handleUpdateUserLastModified)
        handleUpdateUserLastModified(questionnaireResponseId);

      return checkMutationErrors({
        data: response.data,
        mutationName: "answerQuestions",
        answerError
      });
    })
    // This will handle any graphqlError on the mutation and show an error message for user
    .catch(response => {
      handleGraphQLErrors({
        graphQLErrors: response.graphQLErrors,
        answerError
      });
    })
    .finally(() => setSaving(false, formattedAnswers));
};

export const runNodeWalkAndProcess = (context, answers, saveCallBack) => {
  const { questionnaire, allAnswers, allAnswerGroups } = context;
  const newAllAnswers = Engine.updateAnswersForNodeWalk({
    allAnswers,
    allAnswerGroups,
    changedAnswers: answers
  });
  const walkResult = Engine.evaluateAnswers(
    {
      questionnaire,
      allAnswers: newAllAnswers,
      allAnswerGroups
    },
    answers
  );

  ifElse(
    pipe(values, all(isNullOrEmpty)),
    // If there are no results from edges call save answer mutation
    saveCallBack,
    // Else we need to process results and check if full or partial refresh
    () =>
      Engine.processNodeWalkResults({
        walkResult,
        answerGroups: allAnswerGroups,
        answers: map(formatAnswer, answers),
        ...context
      })
  )(walkResult);
};

export const saveAnswers = (props, answers) => {
  const { answerQuestions } = props;

  runNodeWalkAndProcess(props, answers, () => answerQuestions(answers));
};
