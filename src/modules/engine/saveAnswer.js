import {
  __,
  all,
  always,
  and,
  assoc,
  both,
  cond,
  curry,
  defaultTo,
  either,
  equals,
  evolve,
  forEach,
  identity,
  ifElse,
  includes,
  indexBy,
  is,
  lensPath,
  map,
  path,
  pipe,
  prop,
  propEq,
  propOr,
  reduce,
  reject,
  set,
  T,
  tap,
  values,
  view
} from "ramda";
import Engine from ".";
import { isNullOrEmpty } from "utils/fp";
import generateRandomId from "utils/generateRandomId";
import { formatValueForInput as formatValueForInputCompound } from "modules/question/components/Question/shared/InitializeCompoundValue";
import { formatValueForInput as formatValueForInputCollection } from "modules/question/components/Question/EnumerableOrEnumerableCollection/enhancers";
import { formatValueForInput as formatValueForInputDate } from "modules/question/components/Question/DateOrEnumerable/enhancers";
import { formatValueForInput as formatValueForInputDateTime } from "modules/question/components/Question/DateTimeOrEnumerable/enhancers";

const COMPOUND_INCLUDING_TYPES = ["number-with-enumerable"];
const COMPOUND_FIELDS = ["EnumerableOrOpen", "NumberOrEnumerable"];

/**
 * Process the new answer by adding ir or removing it if deleted from the
 * response object in the store. Will return the new set of answers
 * @param {Object} anonymous     Hash with the parameters needed, for style more
 *                               than 3 params need to be passed as hash
 * HASH CONTENTS
 * {Object} store                Refrence to the apollo store object
 * {Object} serverData           Data returned by the apollo query
 * {Int}    responseId           The Id for the current response
 * {Object} questionAnswerGroup  Current question answergroup, used to find deleted answer
 * {Object} question             Current question, used to find deleted answer
 * {Object} responseQuery        Store query to get current response object
 * {Object} objectName           Path name to use to get current response object
 * @return {[Object]}           Array with answers in store
 */
export const processAnswerForStore = ({
  store,
  serverData,
  responseId,
  questionAnswerGroup,
  question,
  responseQuery,
  objectName,
  mutationName,
  setFormValue,
  value
}) => {
  // Get full response from store
  const data = store.readQuery({
    query: responseQuery,
    variables: { id: Number(responseId) }
  });

  // Create a lens to response answers
  const answersLens = lensPath([objectName, "answers"]);
  const isAnswerDeleted = pipe(prop(mutationName), isNullOrEmpty)(serverData);
  const removeAnswer = reject(
    both(
      propEq("answerGroupId", questionAnswerGroup.id),
      propEq("questionId", question.id)
    )
  );
  const addAnswer = pipe(
    indexBy(prop("id")),
    answers =>
      pipe(prop(mutationName), newAnswer => {
        const isWithType = pipe(
          prop("answerType"),
          includes(__, COMPOUND_INCLUDING_TYPES)
        );
        const sameAnswerData = propEq("answerData", value);
        const sameAnswerArray = propEq("answerArray", value);

        if (
          and(setFormValue, !either(sameAnswerData, sameAnswerArray)(newAnswer))
        ) {
          setFormValue(
            `${question.id}|${questionAnswerGroup.id}`,
            cond([
              [isWithType, pipe(prop("answerData"), val => JSON.parse(val))],
              [
                () => equals(question.type, "EnumerableOrEnumerableCollection"),
                formatValueForInputCollection
              ],
              [
                () => equals(question.type, "DateOrEnumerable"),
                formatValueForInputDate
              ],
              [
                () => equals(question.type, "DateTimeOrEnumerable"),
                formatValueForInputDateTime
              ],
              [
                () => includes(question.type, COMPOUND_FIELDS),
                formatValueForInputCompound
              ],
              [T, prop("answerData")]
            ])(newAnswer)
          );
        }

        return assoc(newAnswer.id, newAnswer, answers);
      })(serverData),
    values
  );
  const writeDataToStore = newData => {
    // Save changes in the Apollo store
    store.writeQuery({
      query: responseQuery,
      variables: { id: Number(responseId) },
      data: newData
    });
  };

  return pipe(
    view(answersLens),
    ifElse(
      always(isAnswerDeleted),
      // If answer is deleted (no value), remove it from store
      removeAnswer,
      // Else Add/Update answers
      addAnswer
    ),
    set(answersLens, __, data),
    tap(writeDataToStore),
    view(answersLens)
  )(data);
};

const getErrors = curry((mutationName, data) =>
  path([mutationName, "errors"], data)
);
const getWarnings = curry((mutationName, data) =>
  path([mutationName, "warnings"], data)
);
const hasErrorsOrWarnings = curry((mutationName, data) =>
  either(getErrors(mutationName), getWarnings(mutationName), data)
);

export const getMockAnswerQuestion = answer => ({
  __typename: "AnswerWithWarnings",
  answerData: answer.value || "",
  answerArray: answer.valueArray || [],
  answerGroupId: answer.answerGroupId,
  answerType: answer.answerType,
  errors: null,
  id: defaultTo(generateRandomId(), answer.id),
  questionId: answer.questionId,
  source: false,
  warnings: null,
  isOptimistic: true
});

const addAnswerGroup = allAnswerGroups => answer =>
  ifElse(
    prop("answerGroup"),
    identity,
    assoc("answerGroup", prop(answer.answerGroupId, allAnswerGroups))
  )(answer);

export const calculateValues = ({
  isOptimistic,
  props,
  answers,
  allNewAnswers
}) => {
  if (!isOptimistic) {
    const { query, allAnswerGroups } = props;

    pipe(
      map(answer =>
        assoc(
          "question",
          propOr(
            props.question,
            answer.questionId,
            props.questionnaire.questions
          ),
          answer
        )
      ),
      forEach(answer =>
        Engine.calculateValues({
          ...props,
          ...answer,
          responseQuery: query,
          allAnswers: map(addAnswerGroup(allAnswerGroups), allNewAnswers)
        })
      )
    )(answers);
  }
};

export const updateCache =
  ({ ownProps, context }) =>
  (store, { data: serverData }) => {
    const { answerGroup, value, answerType, mutationName } = context;

    // We check only errors because warnings already saved the answer on
    // server so we need to update store on client side.
    if (!getErrors(mutationName, serverData)) {
      const {
        question,
        questionnaireResponseId,
        questionAnswerGroup,
        query,
        objectName,
        setFormValue
      } = ownProps;
      const storeAnswers = processAnswerForStore({
        store,
        serverData,
        responseId: questionnaireResponseId,
        questionAnswerGroup,
        question,
        responseQuery: query,
        objectName,
        mutationName,
        setFormValue,
        value
      });
      const answer = {
        answerGroup,
        value,
        answerType,
        questionId: question.id
      };
      const isOptimistic = both(
        identity,
        path([mutationName, "isOptimistic"])
      )(serverData);

      calculateValues({
        isOptimistic,
        props: ownProps,
        answers: [answer],
        allNewAnswers: storeAnswers
      });
    }
  };
export const onMutationComplete =
  ({ setSaving, mutationName, answerError }) =>
  response => {
    setSaving(false);
    const getErrorsAndWarnings = data => ({
      errors: getErrors(mutationName, data),
      warnings: getWarnings(mutationName, data)
    });

    return ifElse(
      hasErrorsOrWarnings(mutationName),
      pipe(getErrorsAndWarnings, answerError),
      T
    )(response.data);
  };
export const onMutationError =
  ({ setSaving, answerError }) =>
  error => {
    setSaving(false);
    const formatErrors = data => ({ errors: data });

    map(pipe(formatErrors, answerError), error.graphQLErrors);
  };

/**
 * Method to handle the mutation to save a single answer and call actions to do after a
 * save
 *
 * @param {Function} mutate function with apollo mutation
 * @param {Object} ownProps properties for the field where the save happened
 * @param {Object} context values of the event that triggered save
 * @return {Void} This only executes mutation no value return
 */
export const handleSaveAnswerMutation = ({ mutate, ownProps, context }) => {
  const { question, setSaving, answerError, questionnaireResponseId } =
    ownProps;
  const {
    answerGroup,
    value,
    answerType,
    mutationName,
    handleUpdateUserLastModified
  } = context;

  setTimeout(() => {
    setSaving(true);

    mutate({
      variables: {
        value,
        questionId: question.id,
        answerGroupId: answerGroup.id,
        answerType,
        questionnaireResponseId
      },
      // Method called after the mutation response is received, just add data to
      // Questionnaire Response. This is inside the mutation redux action. serverData
      // represents data that comes from server response
      update: updateCache({ ownProps, context })
    })
      // This is called in parallel to update above when mutation response returns
      // cannot do this on update because, as is on a redux action you cannot dispatch
      // inside another
      .then(response => {
        setSaving(false);
        if (handleUpdateUserLastModified)
          handleUpdateUserLastModified(questionnaireResponseId);
        const getErrorsAndWarnings = data => ({
          errors: getErrors(mutationName, data),
          warnings: getWarnings(mutationName, data)
        });

        return ifElse(
          hasErrorsOrWarnings(mutationName),
          pipe(getErrorsAndWarnings, answerError),
          T
        )(response.data);
      })
      // This will handle any graphqlError on the mutation and show a error message for user
      .catch(response => {
        setSaving(false);
        const formatErrors = data => ({ errors: data });

        map(pipe(formatErrors, answerError), response.graphQLErrors);
      });
  }, 1);
};

/**
 * Method to make sure all the answers have the data needed for the node walk, specially
 * Answer Group Object.
 *
 * @param {Object} anonymous      Hash with the parameters needed, for style more
 *                                than 3 params need to be passed as hash
 * HASH CONTENTS *
 * {[Object]} allAnswers          Array with the Processed Answers before the save
 * {[Object]} allAnswerGroups     Array with all the processed AnswerGroups
 * {[Object]} changedAnswers      Array with the Answers changed that trigger the save
 * @return {[Object]}  Array with the answers, it will use the old answer data if not changed
 *                     or will add from old one the data needed
 */
export const updateAnswersForNodeWalk = ({
  allAnswers,
  allAnswerGroups,
  changedAnswers
}) => {
  const getAnswerId = answer => `${answer.questionId}|${answer.answerGroupId}`;
  const findAnswer = answer => prop(getAnswerId(answer), allAnswers);
  const indexAnswerGroups = indexBy(prop("id"), allAnswerGroups);
  // If answer already exists just update the values with new answer
  const updateAnswer = (newAnswer, oldAnswer) =>
    evolve(
      {
        answerData: always(newAnswer.answerData),
        answerArray: always(newAnswer.answerArray)
      },
      oldAnswer
    );
  // If is a new answer, complete the raw data that comes from server with the
  // one needed for node walk
  const addAnswer = newAnswer =>
    assoc(
      "answerGroup",
      prop(newAnswer.answerGroupId, indexAnswerGroups),
      newAnswer
    );

  // will add deleted answer for node calculation
  return reduce(
    (acc, answer) =>
      ifElse(
        isNullOrEmpty,
        always(acc),
        pipe(
          findAnswer,
          ifElse(
            isNullOrEmpty,
            () => assoc(getAnswerId(answer), addAnswer(answer), acc),
            oldAnswer =>
              assoc(
                getAnswerId(oldAnswer),
                updateAnswer(answer, oldAnswer),
                acc
              )
          )
        )
      )(answer),
    allAnswers,
    changedAnswers
  );
};

const answerQuestionHandler = (props, context) => {
  const {
    question,
    questionAnswerGroup,
    questionnaire,
    allAnswers,
    allAnswerGroups
  } = props;
  const {
    value,
    answerType,
    answerGroupOverride,
    dataFieldName,
    mutationName
  } = context;
  const answerGroup = answerGroupOverride || questionAnswerGroup;
  const newAnswer = assoc(dataFieldName, value, {
    answerType,
    questionId: question.id,
    answerGroupId: answerGroup.id,
    answerGroup,
    question
  });
  const newAllAnswers = updateAnswersForNodeWalk({
    allAnswers,
    allAnswerGroups,
    changedAnswers: [newAnswer]
  });
  const walkResult = Engine.evaluateAnswer(
    {
      questionnaire,
      allAnswers: newAllAnswers,
      allAnswerGroups
    },
    {
      question,
      answerGroup,
      answerData: value
    }
  );

  // Depending on the results of front-end nodewalking we either save the
  // answer or process a partial refresh or full refresh
  ifElse(
    pipe(values, all(isNullOrEmpty)),
    // If there are no results from edges call save answer mutation
    () =>
      pipe(
        prop(mutationName),
        ifElse(
          is(Function),
          fn => {
            fn({ answerGroup, ...context });
          },
          identity
        )
      )(props),
    // Else we need to process results and check if is full or partial refresh
    () =>
      Engine.processNodeWalkResults({
        walkResult,
        answerGroups: allAnswerGroups,
        answers: [
          {
            value: newAnswer.answerData,
            valueArray: newAnswer.answerArray,
            answerType,
            answerGroupId: answerGroup.id,
            questionId: question.id
          }
        ],
        ...props
      })
  )(walkResult);
};

export const saveAnswer = (props, context) =>
  answerQuestionHandler(props, context);
