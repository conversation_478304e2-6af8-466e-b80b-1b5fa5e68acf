import { always, any, compose, equals, keys, ifElse } from "ramda";

const allRule = ifElse(
  any(equals("failing")),
  always("failing"),
  ifElse(
    any(equals("unimplemented")),
    always("unimplemented"),
    always("passing")
  )
);

const anyRule = ifElse(
  any(equals("passing")),
  always("passing"),
  ifElse(
    any(equals("unimplemented")),
    always("unimplemented"),
    always("failing")
  )
);

const noneRule = ifElse(
  any(equals("passing")),
  always("failing"),
  ifElse(
    any(equals("unimplemented")),
    always("unimplemented"),
    always("passing")
  )
);

export const logic = {
  All: allRule,
  Any: anyRule,
  None: noneRule
};

export const logicTypeExists = logicType =>
  compose(any(equals(logicType)), keys)(logic);

export default logic;
