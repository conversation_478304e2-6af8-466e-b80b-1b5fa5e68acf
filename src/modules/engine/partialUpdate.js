import {
  __,
  always,
  any,
  assoc,
  both,
  chain,
  concat,
  curry,
  defaultTo,
  dissoc,
  differenceWith,
  endsWith,
  eqBy,
  evolve,
  filter,
  forEach,
  groupBy,
  identity,
  ifElse,
  innerJoin,
  isNil,
  indexBy,
  map,
  not,
  partition,
  path,
  pipe,
  prop,
  propEq,
  reject,
  toString,
  values
} from "ramda";
import { isNullOrEmpty } from "utils/fp";
import { query } from "../questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import {
  checkMutationErrors,
  cleanServerAnswers,
  hasErrors,
  handleGraphQLErrors,
  getMockAnswerQuestions,
  processAnswersForStore,
  runNodeWalkAndProcess
} from "./saveAnswers";
import generateRandomId from "utils/generateRandomId";
import { calculateValues } from "./saveAnswer";

/**
 * Method that updates store data according to added and removed visible questions
 * @param {object} serverData data that comes in response from server
 * @param {object} storeData data stored in the apollo store
 * @return {object} new data with the updated visible questions
 */
export const updateVisibleQuestions = (serverData, storeData) => {
  const newVisibleQuestions =
    serverData.updateAnswerGroups.addedVisibleQuestions;
  const deletedVisibleQuestions =
    serverData.updateAnswerGroups.removedVisibleQuestions;
  const addNewVisibleQuestions = concat(newVisibleQuestions);
  const removeVisibleQuestions = differenceWith(
    eqBy(prop("id")),
    __,
    deletedVisibleQuestions
  );

  return pipe(
    addNewVisibleQuestions,
    removeVisibleQuestions
  )(storeData.questionnaireResponse.visibleQuestions);
};

/**
 * Method to return the updated results once we know their real
 * parentId and groupId. This will be called for nested edges.
 * @param {[object]} descendantsWalkResults list of the results that still pending
 * @param {object} context object with the context data that started the call
 * @param {object} response data returned from server in last call
 * @returns {object}  result is not important it will call or not partial Update
 */
const updateDescendantAnswerGroups = (
  descendantsWalkResults,
  context,
  response
) => {
  const alternativeId = ag => `${ag.parentId}|${ag.groupId}`;
  const indexedAnswerGroups = indexBy(
    alternativeId,
    path(["updateAnswerGroups", "answerGroups"], response)
  );
  const updateParentValue = result => {
    const answerGroup = prop(
      `${result.parentId}|${result.parentGroupId}`,
      indexedAnswerGroups
    );

    return ifElse(
      always(isNullOrEmpty(answerGroup)),
      identity,
      pipe(assoc("parentId", Number(answerGroup.id)), dissoc("isDescendant"))
    )(result);
  };
  const createContext = walkResult => ({ walkResult, answers: [], ...context });
  const existsRootWalkResult = any(pipe(prop("isDescendant"), not));
  const groupedByStatus = groupBy(prop("status"));

  return pipe(
    map(updateParentValue),
    ifElse(
      existsRootWalkResult,
      pipe(
        groupedByStatus,
        createContext,
        // eslint-disable-next-line no-use-before-define
        PartialUpdate.partialServerUpdate
      ),
      identity
    )
  )(descendantsWalkResults);
};

const getStringValue = curry((fieldName, obj) =>
  pipe(prop(fieldName), toString)(obj)
);

const addTypeName = typename => list =>
  map(assoc("__typename", typename), list);
const doesAGExists = list => result =>
  any(
    both(
      propEq("parentId", result.parentId.toString()),
      propEq("groupId", result.groupId.toString())
    ),
    list
  );
/**
 * Method that creates a mock answer group to be used in optimistic result
 * @param {object} result The node result used to create mock data
 * @return {object} Mock answer group based on the result
 */
const createAG = result => ({
  id: generateRandomId(),
  groupId: result.groupId.toString(),
  parentId: result.parentId.toString()
});
/**
 * Method that creates a mock visible question to be used in optimistic result
 * @param {string} id id of the mocked Visible Question
 * @return {object} Mock visible question
 */
const createVQ = id => ({ id, __typename: "VisibleQuestion" });
/**
 * Method to create mock answer groups to be used in optimistic result based
 * on result
 * @param {object} anonymous Hash with the results from node walking and current
 *                           values
 * HASH CONTENTS
 * {[Object]} passing array with the passing results from node walk
 * {[Object]} failing array with the failing results from node walk
 * {[Object]} answerGroups array with the current answer groups
 * {[Object]} groups array with the questionnaire groups
 * {[object]]} visibleQuestions array with the current visible questions
 * @return {[object]} AN array with mock answer groups based on nodewalk result
 */
const createMockAnswerGroups = ({
  passing = [],
  failing = [],
  answerGroups = [],
  groups = [],
  visibleQuestions = []
}) => {
  const AGsToRemove = innerJoin(
    (ag, failingResult) =>
      both(
        propEq("parentId", getStringValue("parentId", failingResult)),
        propEq("groupId", getStringValue("groupId", failingResult))
      )(ag),
    answerGroups,
    failing
  );
  const AGsToAdd = pipe(
    map(ifElse(doesAGExists(answerGroups), always(null), createAG)),
    reject(isNil)
  )(passing);
  const removeAGsFromFailingGroups = ags => {
    let newAGs = ags;

    forEach(ag => {
      newAGs = dissoc(ag.id, newAGs);
    }, AGsToRemove);
    return newAGs;
  };
  const createAGsFromPassingGroups = ags => {
    let newAGs = ags;

    forEach(ag => {
      newAGs = assoc(ag.id, ag, newAGs);
    }, AGsToAdd);
    return newAGs;
  };
  const removedVisibleQuestions = pipe(
    filter(
      pipe(prop("id"), id => any(ag => endsWith(`|${ag.id}`, id), AGsToRemove))
    ),
    addTypeName("VisibleQuestion"),
    values
  )(visibleQuestions);
  const indexedGroups = indexBy(prop("id"), groups);
  const addedVisibleQuestions = pipe(
    chain(ag =>
      pipe(
        prop(ag.groupId),
        prop("questions"),
        map(q => `${q.id}|${ag.id}`)
      )(indexedGroups)
    ),
    map(createVQ)
  )(AGsToAdd);
  const updateAnswerGroups = pipe(
    indexBy(prop("id")),
    removeAGsFromFailingGroups,
    createAGsFromPassingGroups,
    addTypeName("AnswerGroup"),
    values
  );

  return {
    answerGroups: updateAnswerGroups(answerGroups),
    removedVisibleQuestions,
    addedVisibleQuestions
  };
};

/**
 * Method to remove answers from deleted answer groups from the store - when a partial
 * update happens some child and grandchild answer groups are deleted on back end,
 * but on client store the answers become orphans that are not shown but still
 * considered for nodewalks
 * @param {[object]} answers answers currently in store
 * @param {[object]} answerGroups new answer group list sent by server
 * @returns {[object]} the new filtered list of answers and the deleted answers
 */
export const filterAnswersFromDeletedAnswerGroups = (answers, answerGroups) => {
  const indexedAnswerGroups = indexBy(prop("id"), answerGroups);
  const answerGroupExists = pipe(
    prop("answerGroupId"),
    prop(__, indexedAnswerGroups)
  );

  return partition(answerGroupExists, answers);
};

/**
 * Method that add for the answers the question and answerGroup object before they are processed
 * @param {[object]} deletedAnswers group of deleted answers to be modified
 * @param {[object]} answerGroups array with all Answer Groups
 * @param {[object]} questions array with all Questions
 * @returns {[object]} new array of answers with its question and AG
 */
export const formatDeletedAnswersForNodeWalk = (
  deletedAnswers,
  answerGroups,
  questions
) => {
  const indexedAnswerGroups = indexBy(prop("id"), answerGroups);
  const addAnswerGroup = answer =>
    ifElse(
      prop("answerGroup"),
      identity,
      assoc("answerGroup", prop(answer.answerGroupId, indexedAnswerGroups))
    )(answer);
  const addQuestion = answer =>
    ifElse(
      prop("question"),
      identity,
      assoc("question", prop(answer.questionId, questions))
    )(answer);
  const cleanAnswer = evolve({
    answerData: always(null),
    answerArray: always([])
  });

  return map(pipe(addAnswerGroup, addQuestion, cleanAnswer), deletedAnswers);
};

/**
 * Method to manage the store and call nested calculations and edge targets
 * after the partial update mutation
 * @param {obj} Hash that contains following values
 * store {obj} the store object to access data from Apollo
 * context {obj} has the context values of the current call
 * serverData {obj} contains the data returned by mutation from server
 * descendantsWalkResult {[obj]} contains the result of child edge targets
 * @returns {void} no returned value
 */
// eslint-disable-next-line max-statements, complexity
export const updateAfterMutation = ({
  store,
  context,
  serverData,
  descendantsWalkResult
}) => {
  const {
    questionnaireResponseId,
    setOptimistic,
    objectName,
    answers,
    savePendingAnswers,
    focusRealField
  } = context;

  if (!hasErrors("answers", serverData.updateAnswerGroups)) {
    // Get current visible questions through questionnaire response from Apollo store
    const data = store.readQuery({
      query,
      variables: { id: Number(questionnaireResponseId) }
    });

    // remove deleted answers
    const serverAnswers = cleanServerAnswers(
      serverData,
      ["updateAnswerGroups", "answers"],
      answers
    );
    // creates a new store data object with new/updated answers
    const newData = pipe(
      storeData =>
        processAnswersForStore({
          answers: serverAnswers,
          objectName,
          data: storeData
        }),
      toString, // Removes read only store reference
      storeData => JSON.parse(storeData)
    )(data);

    // remove answers from removed child answer groups
    const [filteredAnswers, deletedAnswers] =
      filterAnswersFromDeletedAnswerGroups(
        newData.questionnaireResponse.answers,
        serverData.updateAnswerGroups.answerGroups
      );

    newData.questionnaireResponse.answers = filteredAnswers;

    newData.questionnaireResponse.visibleQuestions =
      // eslint-disable-next-line no-use-before-define
      PartialUpdate.updateVisibleQuestions(serverData, newData);

    newData.questionnaireResponse.answerGroups =
      serverData.updateAnswerGroups.answerGroups;

    // Save changes in the Apollo store
    store.writeQuery({
      query,
      variables: { id: Number(questionnaireResponseId) },
      data: newData
    });

    // This turns on/off the flag optimistic on redux so other processes know
    window.setTimeout(
      () => setOptimistic(serverData.updateAnswerGroups.isOptimistic),
      100
    );

    // run Nodewalk for deleted answers to see if there are more edges affected
    // this will call other mutation, that's why called after writing data to store
    const fieldId = answer => `${answer.questionId}|${answer.answerGroupId}`;
    const allAnswers = indexBy(fieldId, newData.questionnaireResponse.answers);
    const allAnswerGroups = newData.questionnaireResponse.answerGroups;
    const {
      updateAnswerGroups: { addedVisibleQuestions: newVisibleQuestions }
    } = serverData;
    const newContext = {
      ...context,
      allAnswers,
      allAnswerGroups,
      answerGroups: allAnswerGroups,
      visibleQuestions: newVisibleQuestions
    };

    if (!isNullOrEmpty(deletedAnswers)) {
      runNodeWalkAndProcess(
        newContext,
        // eslint-disable-next-line no-use-before-define
        PartialUpdate.formatDeletedAnswersForNodeWalk(
          deletedAnswers,
          context.allAnswerGroups,
          context.questionnaire.questions
        ),
        identity
      );
    }

    if (!serverData.updateAnswerGroups.isOptimistic) {
      // This produces a mutation so should be done only on non optimistic pass
      // eslint-disable-next-line no-use-before-define
      PartialUpdate.updateDescendantAnswerGroups(
        descendantsWalkResult,
        newContext,
        serverData
      );
      const props = {
        ...context,
        allAnswerGroups
      };

      // This produces a mutation so should be done only on non optimistic pass
      // eslint-disable-next-line no-use-before-define
      PartialUpdate.updateDescendantAnswerGroups(
        descendantsWalkResult,
        {
          ...context,
          answerGroups: allAnswerGroups,
          visibleQuestions: newData.questionnaireResponse.visibleQuestions
        },
        serverData
      );
      savePendingAnswers({
        allAnswers,
        allAnswerGroups,
        visibleQuestions: newVisibleQuestions
      });
      focusRealField(newVisibleQuestions);
      calculateValues({
        isOptimistic: serverData.updateAnswerGroups.isOptimistic,
        props,
        answers,
        allNewAnswers: newData.questionnaireResponse.answers
      });
    }
  }
};

/**
 * Method that calls the mutation for partial refresh. Response is expected
 * that an object containing new and deleted visible question is returned,
 * and also new hierarchy for the section answer group is expected. Visible
 * questions are updated on the Apollo store manually on the mutation's
 * update method called on response.
 * @param {Object} annonymus Hash with the parameters needed, for style more
 *                           than 3 params need to be passed as hash
 * HASH CONTENTS
 * {Object} walkResult Hash with parent answerGroup and groups for nodes passing,
 *                   failing and unimplemented
 * {Int} questionnaireResponseId Current Questionnaire Response Id
 * {Object} sectionAnswerGroup  Current section answer group
 * {Function} updateAnswerGroups Function to trigger a partial refresh
 *                               (through a mutation declared on caller) for
 *                               given answerGroups
 * {function} setSaving Function to turn on flag to mark field as saving
 * @return {Void} No value returned as only calls the mutation
 */
export const partialServerUpdate = ({ walkResult, ...context }) => {
  const {
    questionnaireResponseId,
    updateAnswerGroups,
    setSaving,
    answerError,
    answerGroups,
    questionnaire,
    visibleQuestions,
    answers
  } = context;

  // A timeout was added as is called within other mutation and throws error if done synchronously
  setTimeout(() => {
    setSaving(true);
    // Splits root results (we know the parent AnswerGroup) from descendants that we need to get
    // answerGroup first. We only do it for passing results as failing always are root
    const [descendantsWalkResult, rootWalkResults] = partition(
      prop("isDescendant"),
      walkResult.passing
    );
    const {
      addedVisibleQuestions,
      removedVisibleQuestions,
      answerGroups: newAllAnswerGroups
    } = createMockAnswerGroups({
      passing: rootWalkResults,
      failing: walkResult.failing,
      answerGroups,
      groups: questionnaire.groups,
      visibleQuestions
    });
    const clearResultsForServer = pipe(
      defaultTo([]),
      map(dissoc("parentGroupId"))
    );
    const variables = {
      questionnaireResponseId: Number(questionnaireResponseId),
      passing: clearResultsForServer(rootWalkResults),
      failing: clearResultsForServer(walkResult.failing),
      answers
    };

    // Mutation call for partial refresh, this will get new visible questions from server
    updateAnswerGroups({
      variables,
      optimisticResponse: {
        updateAnswerGroups: {
          addedVisibleQuestions,
          removedVisibleQuestions,
          answerGroups: newAllAnswerGroups,
          answers: getMockAnswerQuestions(answers),
          isOptimistic: true,
          __typename: "UpdateResult"
        }
      },
      // Method called after the mutation response is received
      // serverData is the data from the response. As we use optimistic update will be called
      // twice, one with mocked data and then with real data
      // eslint-disable-next-line max-statements
      update: (store, { data: serverData }) => {
        updateAfterMutation({
          store,
          context,
          serverData,
          descendantsWalkResult
        });
      }
    })
      .then(response =>
        checkMutationErrors({
          data: response.data.updateAnswerGroups,
          mutationName: "answers",
          answerError
        })
      )
      .catch(response => {
        handleGraphQLErrors({
          graphQLErrors: response.graphQLErrors,
          answerError
        });
      })
      .finally(() => {
        setSaving(false);
      });
  }, 1);
};

const PartialUpdate = {
  partialServerUpdate,
  updateDescendantAnswerGroups,
  updateVisibleQuestions,
  formatDeletedAnswersForNodeWalk
};

export default PartialUpdate;
