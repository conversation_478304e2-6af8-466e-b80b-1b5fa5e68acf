import { gql } from "@apollo/client";

export default {
  questionnaireNodes: gql`
    fragment QuestionnaireNodes on Questionnaire {
      nodes {
        id
        groupId
        edges {
          id
          logicType
          startNodeId
          endNodeId
          rules {
            id
            type
            finders {
              id
              type
              questionId
            }
            ... on EnumerableEqualityRule {
              equalityValue
              questionIds
            }
            ... on EnumerableInclusionRule {
              includedIn
              questionIds
            }
          }
        }
      }
    }
  `
};
