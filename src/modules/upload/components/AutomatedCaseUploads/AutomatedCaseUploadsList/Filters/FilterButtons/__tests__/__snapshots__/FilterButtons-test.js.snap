// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FilterButtons renders component 1`] = `
<div
  className="tw-sticky tw-bottom-0 tw-left-0 tw-mt-2.5 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md"
>
  <div
    className=" tw-flex tw-gap-2"
  >
    <Button
      bg="main"
      customStyle="tw-h-[40px]"
      disabled={false}
      onClick={[MockFunction]}
      type="submit"
    >
      <i
        className="fa-solid fa-filter tw-mr-2 tw-opacity-80"
      />
      Apply Filters

      (1)
    </Button>
    <Button
      bg="warning"
      customStyle="tw-h-[40px]"
      disabled={false}
      onClick={[MockFunction]}
      type="button"
    >
      <i
        className="fa-solid fa-xmark tw-mr-2"
      />
      Clear All Filters
    </Button>
  </div>
</div>
`;

exports[`FilterButtons renders component with Applied Filters button disabled 1`] = `
<div
  className="tw-sticky tw-bottom-0 tw-left-0 tw-mt-2.5 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md"
>
  <div
    className=" tw-flex tw-gap-2"
  >
    <Button
      bg="main"
      customStyle="tw-h-[40px]"
      disabled={true}
      onClick={[MockFunction]}
      type="submit"
    >
      <i
        className="fa-solid fa-filter tw-mr-2 tw-opacity-80"
      />
      Apply Filters

    </Button>
    <Button
      bg="warning"
      customStyle="tw-h-[40px]"
      disabled={false}
      onClick={[MockFunction]}
      type="button"
    >
      <i
        className="fa-solid fa-xmark tw-mr-2"
      />
      Clear All Filters
    </Button>
  </div>
</div>
`;

exports[`FilterButtons renders component with both Applied Filters and Clear All Filters buttons disabled 1`] = `
<div
  className="tw-sticky tw-bottom-0 tw-left-0 tw-mt-2.5 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md"
>
  <div
    className=" tw-flex tw-gap-2"
  >
    <Button
      bg="main"
      customStyle="tw-h-[40px]"
      disabled={true}
      onClick={[MockFunction]}
      type="submit"
    >
      <i
        className="fa-solid fa-filter tw-mr-2 tw-opacity-80"
      />
      Apply Filters

    </Button>
    <Button
      bg="warning"
      customStyle="tw-h-[40px]"
      disabled={true}
      onClick={[MockFunction]}
      type="button"
    >
      <i
        className="fa-solid fa-xmark tw-mr-2"
      />
      Clear All Filters
    </Button>
  </div>
</div>
`;
