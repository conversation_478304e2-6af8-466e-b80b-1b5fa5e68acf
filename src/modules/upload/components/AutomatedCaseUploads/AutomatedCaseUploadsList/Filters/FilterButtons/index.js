import { Button } from "@q-centrix/q-components-react";

const FilterButtons = ({
  handleApplyFilters,
  handleReset,
  countFiltersToBeApplied,
  countAppliedFilters
}) => (
  <div className="tw-sticky tw-bottom-0 tw-left-0 tw-mt-2.5 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md">
    <div className=" tw-flex tw-gap-2">
      <Button
        bg="main"
        type="submit"
        customStyle="tw-h-[40px]"
        disabled={countFiltersToBeApplied === 0}
        onClick={handleApplyFilters}
      >
        <i className="fa-solid fa-filter tw-mr-2 tw-opacity-80" />
        Apply Filters{" "}
        {countFiltersToBeApplied > 0 && `(${countFiltersToBeApplied})`}
      </Button>
      <Button
        bg="warning"
        type="button"
        onClick={handleReset}
        disabled={countAppliedFilters === 0}
        customStyle="tw-h-[40px]"
      >
        <i className="fa-solid fa-xmark tw-mr-2" />
        Clear All Filters
      </Button>
    </div>
  </div>
);

export default FilterButtons;
