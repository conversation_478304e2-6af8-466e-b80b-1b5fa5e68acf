// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Filters render correctly 1`] = `
<div
  className="tw-relative tw-basis-[414px] tw-border tw-border-gray-50 tw-bg-qc-blue-50 tw-p-5"
>
  <h3
    className="tw-mb-5 tw-mt-3 tw-text-base tw-font-semibold tw-text-black-70"
  >
    Filter Facility Group
  </h3>
  <form
    autoComplete="off"
    className="tw-flex tw-flex-col tw-gap-4"
    onSubmit={[MockFunction]}
  >
    <div
      className="tw-flex tw-flex-col tw-gap-2"
    >
      <label>
        Facility Group Name
        <input
          id="name"
          inputClassName={false}
          name="name"
          onChange={[Function]}
          placeholder="Facility Group Name"
          value=""
        />
      </label>
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-2"
    >
      <label>
        Facility Group ID
        <input
          id="id"
          inputClassName={false}
          name="id"
          onChange={[Function]}
          placeholder="Facility Group ID"
          value=""
        />
      </label>
    </div>
    <div
      className="tw-sticky tw-bottom-0 tw-left-0 tw-mt-2.5 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md"
    >
      <div
        className=" tw-flex tw-gap-2"
      >
        <button
          bg="main"
          customStyle="tw-h-[40px]"
          disabled={false}
          type="submit"
        >
          <i
            className="fa-solid fa-filter tw-mr-2 tw-opacity-80"
          />
          Apply Filters
        </button>
        <button
          bg="warning"
          customStyle="tw-h-[40px]"
          disabled={false}
          onClick={[Function]}
          type="button"
        >
          <i
            className="fa-solid fa-xmark tw-mr-2"
          />
          Clear All Filters
        </button>
      </div>
    </div>
  </form>
</div>
`;
