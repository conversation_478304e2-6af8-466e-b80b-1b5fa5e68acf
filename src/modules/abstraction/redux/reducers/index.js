import { assoc, mergeAll } from "ramda";
import createReducer from "utils/createReducer";
import ActionType from "../../constants/actionTypes";
import {
  getValidationFailureQuestionPrompts,
  groupByFn
} from "modules/abstraction/utils/helpers";
import { processQuestionnaireQuestions } from "modules/questionnaire/services/questionnaire";

export const initialState = {
  errorSidePanelActive: false,
  errorSidePanelActive2: false,
  errors: null,
  error: {},
  errorPrompt: null,
  errorUrl: null,
  updateUserLastModifiedTimer: null,
  abstractionQuestionnaire: null,
  abstractionAnswerGroups: []
};

const setErrorSidePanel = (state, { payload }) => {
  const { errorState } = payload;

  return assoc("errorSidePanelActive", errorState, state);
};

const setErrorSidePanel2 = (state, { payload }) => {
  const { errorState } = payload;

  return assoc("errorSidePanelActive2", errorState, state);
};

const setErrors = (state, { payload }) => {
  const { errors } = payload;

  if (errors) {
    const validationFailuresGroupedByPrompt =
      getValidationFailureQuestionPrompts(
        errors.validationFailures,
        state.abstractionQuestionnaire
      );
    const responseValidationFailuresGroupedByPrompt = groupByFn(
      errors.responseValidationFailures
    );

    const allFailuresGroupedByPrompt = mergeAll([
      validationFailuresGroupedByPrompt,
      responseValidationFailuresGroupedByPrompt
    ]);

    return assoc("errors", allFailuresGroupedByPrompt)(state);
  }

  return assoc("errors", null, state);
};

const setError = (state, { payload }) => {
  const { error } = payload;

  return assoc("error", error, state);
};

const setErrorPrompt = (state, { payload }) => {
  const { prompt } = payload;

  return assoc("errorPrompt", prompt, state);
};

const setErrorUrl = (state, { payload }) => {
  const { url } = payload;

  return assoc("errorUrl", url, state);
};

const setUpdateUserLastModifiedTimer = (state, { payload }) => {
  const { updateUserLastModifiedTimer } = payload;

  return assoc(
    "updateUserLastModifiedTimer",
    updateUserLastModifiedTimer,
    state
  );
};
const setAbstractionQuestionnaire = (state, { payload }) => {
  const { questionnaire, visit } = payload;

  const processedQuestionnaire = processQuestionnaireQuestions(
    questionnaire,
    visit
  );

  return assoc("abstractionQuestionnaire", processedQuestionnaire, state);
};

const setAbstractionAnswerGroups = (state, { payload }) => {
  const { answerGroups } = payload;

  return assoc("abstractionAnswerGroups", answerGroups, state);
};

const handlers = {
  [ActionType.SET_ERROR_SIDE_PANEL]: setErrorSidePanel,
  [ActionType.SET_ERROR_SIDE_PANEL2]: setErrorSidePanel2,
  [ActionType.SET_ERRORS]: setErrors,
  [ActionType.SET_ERROR]: setError,
  [ActionType.SET_ERROR_PROMPT]: setErrorPrompt,
  [ActionType.SET_ERROR_URL]: setErrorUrl,
  [ActionType.SET_UPDATE_USER_LAST_MODIFIED_TIMER]:
    setUpdateUserLastModifiedTimer,
  [ActionType.SET_ABSTRACTION_QUESTIONNAIRE]: setAbstractionQuestionnaire,
  [ActionType.SET_ABSTRACTION_ANSWER_GROUPS]: setAbstractionAnswerGroups
};

export const reducer = createReducer(initialState, handlers);

export default reducer;
