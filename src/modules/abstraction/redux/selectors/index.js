import { path } from "ramda";
import localizeSelectors from "utils/localizeSelectors";

const localState = path(["app", "abstractionValidationReport"]);

export function getErrorPanelState(state) {
  return state.errorSidePanelActive;
}

export function getErrorPanelState2(state) {
  return state.errorSidePanelActive2;
}

export function getErrors(state) {
  return state.errors;
}

export function getError(state) {
  return state.error;
}

export function getErrorPrompt(state) {
  return state.errorPrompt;
}

export function getErrorUrl(state) {
  return state.errorUrl;
}

export function getUpdateUserLastModifiedTimer(state) {
  return state?.updateUserLastModifiedTimer;
}

export default localizeSelectors(localState, {
  getErrorPanelState,
  getErrorPanelState2,
  getErrors,
  getError,
  getErrorPrompt,
  getErrorUrl,
  getUpdateUserLastModifiedTimer
});
