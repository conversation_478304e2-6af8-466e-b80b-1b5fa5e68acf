import ActionType from "../../constants/actionTypes";

export function setErrorSidePanel(errorState) {
  return {
    type: ActionType.SET_ERROR_SIDE_PANEL,
    payload: { errorState }
  };
}

export function setErrorSidePanel2(errorState) {
  return {
    type: ActionType.SET_ERROR_SIDE_PANEL2,
    payload: { errorState }
  };
}

export function setErrors(errors) {
  return {
    type: ActionType.SET_ERRORS,
    payload: { errors }
  };
}

export function setError(error) {
  return {
    type: ActionType.SET_ERROR,
    payload: { error }
  };
}

export function setErrorPrompt(prompt) {
  return {
    type: ActionType.SET_ERROR_PROMPT,
    payload: { prompt }
  };
}

export function setErrorUrl(url) {
  return {
    type: ActionType.SET_ERROR_URL,
    payload: { url }
  };
}

export function setUpdateUserLastModifiedTimer(updateUserLastModifiedTimer) {
  return {
    type: ActionType.SET_UPDATE_USER_LAST_MODIFIED_TIMER,
    payload: { updateUserLastModifiedTimer }
  };
}

export function setAbstractionQuestionnaire(questionnaire, visit) {
  return {
    type: ActionType.SET_ABSTRACTION_QUESTIONNAIRE,
    payload: { questionnaire, visit }
  };
}

export function setAbstractionAnswerGroups(answerGroups) {
  return {
    type: ActionType.SET_ABSTRACTION_ANSWER_GROUPS,
    payload: { answerGroups }
  };
}
