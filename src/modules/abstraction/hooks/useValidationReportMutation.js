import { useCallback } from "react";
import { useLazyQuery, useMutation } from "@apollo/client";
import { useDispatch, useSelector } from "react-redux";
import { lensPath, set } from "ramda";
import { setMutationLoading, pushFlashMessage } from "modules/app/actions";
import AppStatus from "modules/app/selectors/status";
import { query } from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import Main from "modules/questionnaire/components/Main/mutations";
import { GET_PATIENT_CARE_COMPLIANCE } from "modules/questionnaire/components/Questionnaire/SidePane/PanelContent/PatientCareCompliance/graphql/query";
import { rrmClient } from "modules/questionnaire/components/Questionnaire/SidePane/PanelContent/PatientCareCompliance/Table/hooks";

export const useValidationReportMutation = ({
  questionnaireResponseId,
  client = rrmClient
}) => {
  const isValidating = useSelector(state =>
    AppStatus.isMutationLoading("validation", state)
  );

  const dispatch = useDispatch();

  const [refetchPCC] = useLazyQuery(GET_PATIENT_CARE_COMPLIANCE, {
    client,
    fetchPolicy: "network-only"
  });

  // NOTE => replace with mutation that uses standards as a variable after ONC-227 is complete
  const [validationReport, { loading }] = useMutation(
    Main.validationReportMutation,
    {
      onCompleted: () => {
        // sendValidationReport(data.validationReport);
        dispatch(setMutationLoading("validation", false));
        refetchPCC({ variables: { id: Number(questionnaireResponseId) } });
      },
      onError: () => {
        dispatch(pushFlashMessage("Validation failed. Please try again."));
        dispatch(setMutationLoading("validation", false));
      },
      update: (store, { data: serverData }) => {
        const data = store.readQuery({
          query,
          variables: { id: Number(questionnaireResponseId) }
        });
        const validationReportLens = lensPath([
          "questionnaireResponse",
          "validationReport"
        ]);

        // Save changes in the Apollo store
        store.writeQuery({
          query,
          variables: { id: Number(questionnaireResponseId) },
          data: set(validationReportLens, serverData.validationReport, data)
        });
      }
    }
  );

  const validateReport = useCallback(
    (event, options) => {
      if (event) event.preventDefault();
      if (!loading) {
        dispatch(setMutationLoading("validation", true));
        validationReport({
          variables: { id: questionnaireResponseId, options }
        });
      }
    },
    [dispatch, loading, questionnaireResponseId]
  );

  return { validateReport, isValidating };
};
