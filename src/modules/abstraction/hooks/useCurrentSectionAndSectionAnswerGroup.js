import { useMemo } from "react";
import { defaultTo, find, pipe, prop, propEq } from "ramda";
import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";

export const getSectionAnswerGroup = (currentSection, id) =>
  pipe(
    prop("answerGroups"),
    defaultTo([]),
    find(propEq("id", id))
  )(currentSection);

export const useCurrentSectionAndSectionAnswerGroup = id => {
  const currentSection = useSelector(
    state => Questionnaire.getCurrentSection(id, state) || {}
  );
  const sectionAnswerGroup = useMemo(
    () => getSectionAnswerGroup(currentSection, id),
    [currentSection, id]
  );

  return {
    currentSection,
    sectionAnswerGroup
  };
};
