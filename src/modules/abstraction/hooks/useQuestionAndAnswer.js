import { pathOr } from "ramda";
import { useMemo } from "react";
import { useQuery } from "@apollo/client";
import { useSelector } from "react-redux";
import GET_QUESTION_ENUMERABLES from "modules/question/components/Question/EnumerableSearchable/query";
import Questionnaire from "modules/questionnaire/selectors";
import { getAnswerForQuestion } from "modules/questionnaire/components/Questionnaire/SidePane/PersistentData/enhancers";
import { isEnumerableQuestion } from "utils/isEnumerableQuestion";

export const useQuestionAndAnswer = ({ id }) => {
  const allAnswers = useSelector(state => Questionnaire.getAnswers(state));

  const { prompt, type } = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  ).questions[id];

  const skip = useMemo(() => !isEnumerableQuestion(type), [type]);

  const answerFromId = getAnswerForQuestion(allAnswers, id);

  const { data = {} } = useQuery(GET_QUESTION_ENUMERABLES, {
    variables: { id: Number(answerFromId) },
    skip
  });

  const answer = useMemo(
    () => pathOr(answerFromId, ["questionEnumerables", 0, "description"], data),
    [answerFromId, data]
  );

  return { answer, prompt };
};
