import { useCallback } from "react";
import { ifElse, both, either, propEq, identity } from "ramda";

export const useLinkButtonKeyboard = ({ onKeyboardAction, actionDisabled }) => {
  const handleKeyBoardAction = useCallback(
    e =>
      ifElse(
        both(
          () => !actionDisabled,
          either(propEq("keyCode", 32), propEq("keyCode", 13))
        ),
        event => {
          if (event) event.preventDefault();
          if (onKeyboardAction) onKeyboardAction(event);
        },
        identity
      )(e),
    [actionDisabled, onKeyboardAction]
  );

  return { handleKeyBoardAction };
};
