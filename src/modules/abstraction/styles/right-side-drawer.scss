$border: #d8d7d7;
$primary: #227b9c;
main.abstraction
  .body
  .content-wrapper
  .questionnaire-body
  .right-side-drawer-container {
  background-color: #fff;
  border-left: 1px solid $primary;
  flex-grow: 1;
  flex-basis: 25%;
  height: 100%;
  position: relative;
  .drawer-handle {
    background-color: $primary;
    cursor: grab;
    height: 4rem;
    left: -0.25rem;
    position: absolute;
    top: 40%;
    width: 0.5rem;
  }
  .right-side-drawer {
    flex-grow: 1;
    flex-basis: 25%;
    height: 100%;
    overflow-y: auto;
    position: relative;
    .abstraction-sidebar {
      padding: 0 1.5rem 0 0.75rem;
      .abstraction-sidebar-panel {
        border: 1px solid $border;
        margin-bottom: 0.375rem;
        padding-bottom: 0.125rem;
        position: relative;
        .abstraction-sidebar-panel-header {
          align-items: center;
          border-bottom: 2px solid $primary;
          color: $primary;
          display: flex;
          justify-content: flex-start;
          padding: 1rem;
          h4 {
            margin-left: 0.75rem;
          }
        }
        .abstraction-sidebar-panel-content {
          height: initial;
          padding-top: 0.125rem;
          .accordions-panel {
            .accordion {
              margin-bottom: 0.125rem;
              .accordion-header {
                align-items: center;
                background: #f3f9fb;
                border-bottom: 1px solid $border;
                border-top: 1px solid $border;
                color: #000000;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                padding: 0.5rem;
                padding-right: 0.75rem;
                &.open {
                  border-color: $primary;
                }
                &.red {
                  .character-count {
                    color: #ce0d0d;
                  }
                }
                h6 {
                  font-size: 0.875rem;
                  font-weight: normal;
                }
                .accordion-header-right-side {
                  align-items: center;
                  display: flex;
                  span {
                    color: rgba(0, 0, 0, 0.5);
                    font-size: 0.625rem;
                    font-weight: 800;
                    margin-right: 0.875rem;
                  }
                  i:before {
                    font-size: 14px;
                  }
                }
              }
              .accordion-content {
                overflow: hidden;
                padding: 0 0.625rem;
                textarea {
                  border: 1px solid #e2e2e2;
                  border-radius: 0.3125rem;
                  box-shadow: 2px 2px 2px 0px rgba(0, 0, 0, 0.1) inset;
                  font-size: 0.75rem;
                  line-height: 1.125rem;
                  min-height: 8rem;
                  resize: vertical !important;
                }
              }
            }
          }
        }
        .resize-handle {
          background: #7da1b4;
          bottom: -0.125rem;
          height: 0.25rem;
          left: 40%;
          position: absolute;
          width: 3rem;
        }
      }
    }
  }
}

.drawer-wrapper {
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  right: 0;
  &.active {
    overflow-y: auto;
    pointer-events: all;
  }
  .drawer-container {
    padding-left: 0.5rem;
    pointer-events: all;
  }
}
