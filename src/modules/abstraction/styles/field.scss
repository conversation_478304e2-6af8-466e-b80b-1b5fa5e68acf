.field-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px 0px;
  .disabled {
    opacity: 1;
  }
  .enumerable-items {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
  }

  .field-prompt-control {
    align-items: flex-start;
    display: flex;
    flex-direction: row;
    flex-basis: calc(100% - 60px);
    max-width: calc(100% - 60px);
    flex-wrap: wrap;
    flex-grow: 1;
    .question-prompt {
      align-items: flex-start;
      display: flex;
      font-size: 0.9375rem;
      justify-content: flex-start;
      line-height: 1.125rem;
      padding-top: 1rem;
      width: 250px;
      &.long-prompt {
        width: 50%;
      }

      .menu-container {
        position: relative;
        margin-right: 10px;
        .context-menu {
          position: absolute;
          width: 140px;
          border-radius: 0.4rem;
          border: 1.5px solid #005381;
          box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
          background-color: white;
          z-index: 10;
          padding-bottom: 0.5rem;
        }
        .help-button,
        .management-button,
        .question-help,
        .clear-button {
          color: #b0acad;
          padding-left: 1ch;
          padding-top: 1ch;
          cursor: pointer;
        }
        .text-content {
          font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 15px;
          color: #000;
          font-weight: 400;
          padding-left: 5px;
        }
      }

      .prompt {
        color: #000;
        font-weight: 500;
      }

      &.core-question {
        .pulse {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #cca92c;
          cursor: pointer;
          box-shadow: 0 0 0 rgba(204, 169, 44, 0.4);
          animation: pulse 2s infinite;
          position: relative;
          align-items: flex-end;
          margin-left: 10px;
        }

        .pulse:hover {
          animation: none;
        }

        @-webkit-keyframes pulse {
          0% {
            -webkit-box-shadow: 0 0 0 0 rgba(204, 169, 44, 0.4);
          }
          70% {
            -webkit-box-shadow: 0 0 0 10px rgba(204, 169, 44, 0);
          }
          100% {
            -webkit-box-shadow: 0 0 0 0 rgba(204, 169, 44, 0);
          }
        }
        @keyframes pulse {
          0% {
            -moz-box-shadow: 0 0 0 0 rgba(204, 169, 44, 0.4);
            box-shadow: 0 0 0 0 rgba(204, 169, 44, 0.4);
          }
          70% {
            -moz-box-shadow: 0 0 0 10px rgba(204, 169, 44, 0);
            box-shadow: 0 0 0 10px rgba(204, 169, 44, 0);
          }
          100% {
            -moz-box-shadow: 0 0 0 0 rgba(204, 169, 44, 0);
            box-shadow: 0 0 0 0 rgba(204, 169, 44, 0);
          }
        }
      }
    }

    .field-holder {
      flex-grow: 1;
      max-width: calc(100% - 250px);
      padding-left: 0.5rem;
      &.long-prompt {
        width: 50%;
        max-width: 50%;
        .enumerable-items {
          flex-direction: column;
        }
      }

      input[type="text"],
      select,
      textarea {
        background: #ffffff;
        border: 1px solid #d8d7d7;
        box-sizing: border-box;
        border-radius: 10px;
        font-size: 14px;
        height: 49px;
        padding: 13px 20px;
      }
      textarea {
        height: 111px;
      }
    }
  }

  .field-validation {
    flex-grow: 0;
    padding-left: 15px;
    padding-top: 2px;
    position: relative;
    width: 33px;
    margin-right: 20px;
    cursor: help;
    text-align: left;
    .good {
      color: #5faa40;
    }
    .validation-warning {
      color: #fa9917;
    }
    .validation-error {
      color: #ce0d0d;
    }
    .focused {
      color: #c2d8df;
    }

    .error-menu {
      position: absolute;
      top: 0.5rem;
      width: 175px;
      height: auto;
      max-height: 225px;
      overflow-y: scroll;
      right: 55%;
      border: 2px solid #912018;
      background-color: #ffffff;
      border-radius: 4px;
      z-index: 10;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      .error-menu-item {
        padding: 5px;
        color: #000;
        font-size: 14px;
        font-weight: 600;
        border-bottom: 1px solid #e2e2e2;
        &:last-of-type {
          border-bottom: none;
        }
        .error-menu-link {
          color: #005f86;
          font-size: 12px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          text-decoration: underline;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }

    .number-circle {
      border-radius: 50%;
      width: 15px;
      height: 15px;
      padding: 0px 1px;
      border: 1px solid #fa9917;
      background: #e7ca65;
      color: #000;
      text-align: center;
      font-size: 9px;
      font-weight: 700;
      position: relative;
      z-index: 5;
      top: 10px;
      left: 10px;
      margin-top: -10px;
    }
  }

  .styled-tool-tip {
    width: 200px;
    display: flex;
    flex-direction: column;
  }

  .multi-line {
    display: flex;
    align-self: flex-start;
  }

  i {
    font-size: 20px;
  }

  &.has-warnings {
    .field-holder {
      input[type="text"],
      textarea,
      select,
      .value-holder {
        background: #fbf8ed;
        border: 1px solid #cba82e;
      }
    }
  }

  &.has-errors {
    .field-holder {
      input[type="text"],
      textarea,
      select,
      .value-holder {
        background: #fff8f8;
        border: 1px solid #ce0d0d;
      }
    }
  }

  &.is-focused {
    .field-holder {
      input[type="text"],
      textarea,
      select,
      .value-holder {
        background: rgb(253, 253, 253);
        border: 1px solid #51bde4;
        box-shadow: inset 0px 0px 3px 3px #bddefd;
      }
    }
  }
}
