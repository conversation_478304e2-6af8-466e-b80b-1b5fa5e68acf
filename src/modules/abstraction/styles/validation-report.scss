.abstraction-validation-report-panel {
  padding: 0 !important;
  position: relative !important;
  min-height: calc(100vh - 120px);
  .validation-report-header {
    padding: 10px;
    color: #1e5180;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .response-section-header {
    color: rgba(0, 0, 0, 0.7);
    font-family: Open Sans, Arial, sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 2.5px 10px;
    background-color: #ccc !important;
    height: 20px;
  }
  .rrt-abstraction-response-item {
    i {
      display: none;
    }
    &:hover {
      background-color: #f4fcff;
      .prompt {
        color: #005f86;
        text-decoration: underline #005f86;
      }
      i {
        display: flex;
        align-items: center;
        color: #c4c4c4;
      }
    }
  }
  .abstraction-response {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    padding: 10px;
    list-style: none;
    border-bottom: 1px solid #c4c4c4;
    &:hover {
      cursor: pointer;
    }
    .prompt {
      font-family: Open Sans, Arial, sans-serif;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.7);
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .issue-counters-container {
      display: flex;
      align-items: center;
      gap: 20px;
      i {
        color: #005f86;
        font-family: Font Awesome 6 Pro;
        font-size: 14px;
        font-style: normal;
        font-weight: 900;
        line-height: normal;
      }
      .issue-counters {
        display: flex;
        flex-direction: column;
        .issue-counter {
          display: flex;
          font-size: 16px;
          font-weight: 600 !important;
          font-style: normal !important;
          line-height: normal;
          font-family: Open Sans, Arial, sans-serif;
          white-space: nowrap;
          color: #5c5c5c;
          &.error {
            color: #912018 !important;
          }
          &.warning {
            color: #c16800;
          }
        }
      }
    }
  }
  .validation-errors-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    gap: 10px;
    &:hover {
      cursor: pointer;
    }
    i {
      color: #005f86;
      font-family: Font Awesome 6 Pro;
      font-size: 14px;
      font-style: normal;
      font-weight: 900;
      line-height: normal;
    }
    .validation-error {
      display: flex;
      justify-content: space-between;
      font-family: Open Sans, Arial, sans-serif;
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      font-style: normal !important;
      color: #5c5c5c;
      &.error {
        color: #912018 !important;
      }
      &.warning {
        color: #c16800;
      }
    }
  }
  .validation-error-container {
    padding: 10px;
    .validation-error-description {
      font-size: 14px;
      font-weight: 400;
    }
  }
}

.panel-overflow {
  overflow: hidden !important;
}
