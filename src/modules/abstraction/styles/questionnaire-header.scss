$darkBlue: #005381;
$darkRedWine: #4f1d4a;
$grayGreen: #40925e;
$burntOrange: #f2562a;
$gray: #b0acad;
$lightYellow: #f4eca6;
$lightBeige: #fdfaf1;
$white: #ffffff;
main.abstraction .body .content-wrapper .questionnaire-header {
  align-items: center;
  display: flex;
  flex-grow: 0;
  justify-content: space-between;
  margin-bottom: 0.625rem;
  min-height: 3.625rem;
  width: 100%;
  h2 {
    color: #227b9c;
    font-size: 1.5625rem;
    font-weight: normal;
  }
  div {
    display: flex;
    height: 100%;
  }
  & > div {
    align-items: center;
    border: 1px solid #d8d7d7;
    padding-left: 1.5rem;
  }
  .questionnaire-header-left {
    flex-basis: 19.5%;
  }
  .questionnaire-header-right {
    flex-basis: 79.5%;
    span {
      color: #a4a4a4;
      font-weight: 800;
      font-size: 0.6875rem;
    }
    .links {
      margin-left: auto;
      padding-right: 1.5rem;
      gap: 20px;
      a {
        align-items: center;
        background: none;
        border: none;
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: center;
        i {
          color: #828282;
          font-size: 1.25rem;
          margin-bottom: 0.25rem;
        }
        & + button {
          margin-left: 1.25rem;
        }
      }
    }
    .status-container {
      border-left: 1px solid #d8d7d7;
      flex-direction: column;
      padding: 0.5rem 0.75rem;
      position: relative;
      width: 19.125rem;
      .status-menu {
        background: $white;
        border-radius: 0.625rem;
        border: 1px solid #d8d7d7;
        box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.15);
        flex-direction: column;
        height: fit-content;
        justify-content: space-between;
        right: 5%;
        min-height: 26rem;
        min-width: 19rem;
        padding: 1rem 0.5rem;
        position: absolute;
        top: 66%;
      }
      button {
        background: $white;
        border-radius: 0.9375rem;
        border: 1px solid #a4a4a4;
        color: #3f3e3e;
        font-size: 0.75rem;
        font-weight: 800;
        min-width: 8.75rem;
        padding: 0.375rem 0.875rem;
        text-transform: uppercase;
        &:hover,
        &.selected {
          color: $white;
          &.btn-0,
          &.btn-7 {
            background: $darkBlue;
          }
          &.btn-1,
          &.btn-3,
          &.btn-5,
          &.btn-10 {
            background: $burntOrange;
          }
          &.btn-2,
          &.btn-4,
          &.btn-6,
          &.btn-11 {
            background: $grayGreen;
          }
          &.btn-8 {
            background: $lightYellow;
            color: #3f3e3e;
          }
          &.btn-9 {
            background: $darkRedWine;
          }
        }
      }
    }
  }
}
