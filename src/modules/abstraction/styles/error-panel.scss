.error-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  height: fit-content;
  max-height: calc(100vh - 120px);
  overflow-y: scroll;
  background-color: #fff;
  z-index: 100;
  .error-panel-header-container {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 10px;
    i {
      color: #005f86;
      font-family: Font Awesome 6 Pro;
      font-size: 14px;
      font-style: normal;
      font-weight: 900;
      line-height: normal;
      &:hover {
        cursor: pointer;
      }
    }
    .error-panel-header {
      font-family: Open Sans, Arial, sans-serif;
      color: #1e5180;
      font-size: 20px;
      font-style: normal !important;
      font-weight: 600;
      line-height: normal;
      &.error {
        color: #912018 !important;
      }
      &.warning {
        color: #c16800;
      }
    }
  }
}
