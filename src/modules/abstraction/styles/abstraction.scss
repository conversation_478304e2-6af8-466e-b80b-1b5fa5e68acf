$success: #5faa40;

main.abstraction {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding-bottom: 0;
  min-height: auto;
  nav {
    .app-links {
      margin-top: 0;
    }
  }
  .body {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    height: calc(100vh - 120px);

    .side-navigation {
      flex-grow: 0;
      flex-basis: 40px;
      min-height: 100%;
      background: linear-gradient(180deg, #227b9c 0%, #7da1b4 100%);
    }

    .sidepanel {
      flex-basis: 25%;
      flex-grow: 0;
      height: 100%;
      border-right: 1px solid #dcd9d9;
      box-shadow: none;
      &.expanded-side {
        flex-basis: 50%;
        max-width: 50%;
      }
      .panel-content {
        background-color: #fff;
        min-height: calc(100vh - 120px);
        border-left: 1px solid #dcd9d9;
      }
      .panel-icons {
        background-color: #e1dfdf;
        button {
          font-size: 2rem;
          color: #a4a4a4;
          &.active {
            background-color: #fff;
            color: #227b9c;
          }
        }
      }
      .panels {
        height: 100%;
        .panel-icons {
          background-color: #e1dfdf;
          button {
            color: #a4a4a4;
            font-size: 2rem;
            &.active {
              background-color: #ffffff;
              color: #227b9c;
            }
          }
        }
        .panel-content {
          background-color: #ffffff;
          .case-details {
            border: 1px solid #d8d7d7;
            h1 {
              align-items: center;
              border-bottom: 1px solid #227b9c;
              color: #227b9c;
              display: flex;
              font-size: 1rem;
              justify-content: space-between;
              padding: 1rem 0.875rem;
              i {
                font-size: 1.25rem;
              }
            }
            ul {
              font-size: 0.75rem;
              padding: 0.5rem 0.875rem;
              li {
                display: flex;
                max-width: 14.0625rem;
                span {
                  max-width: 50%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
                .case-details-prompt {
                  font-weight: bold;
                  margin-right: 0.25rem;
                }
              }
            }
          }
          .patient-care-compliance-main {
            display: flex;
            flex-direction: column;
            margin: 4px;
            width: 380px;
            row-gap: 5px;
            font-size: 12px;
            font-family: "Open Sans", Arial, sans-serif;
            line-height: 15px;
            text-transform: capitalize;
            .column {
              min-width: 380px;
            }
            .pcc-title {
              display: flex;
              width: 380px;
              height: 35px;
              border-width: 1px 0px;
              border-style: solid;
              border-color: #1e5180;
              padding: 10px;
              font-weight: 400;
              color: #1e5180;
            }
            .pcc-update-info {
              width: 100%;
              height: fit-content;
              padding: 5px;
              color: black;
              text-transform: none;
            }
            .patient-care-compliance-grid {
              display: grid;
              grid-template-rows: 50px 100%;
              grid-template-areas:
                "header"
                "body";
              border-radius: 5px;
              border: 1px solid #005f86;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.7);
              .patient-care-compliance-header {
                display: grid;
                grid-template-columns: minmax(64px, 2fr) minmax(67px, 1.5fr) 5fr;
                grid-area: header;
                width: 100%;
                background: #d7d7d7;
                border-bottom: 1px solid #c4c4c4;
                .pcc-header-cell {
                  border-right: 1px solid #c4c4c4;
                  display: flex;
                  align-items: center;
                  text-align: center;
                  padding: 6px;
                  font-weight: 600;
                  &:last-child {
                    border: none;
                    padding-left: 10px;
                    text-align: left;
                  }
                }
              }
              .patient-care-compliance-body {
                height: fit-content;
                .pcc-body-row {
                  display: grid;
                  grid-template-columns: minmax(64px, 2fr) minmax(67px, 1.5fr) 5fr;
                  grid-template-rows: repeat(auto, 100%);
                  grid-area: body;
                  width: 100%;
                  background: #ffffff;
                  border-bottom: 1px solid #c4c4c4;
                  &:last-child {
                    border-bottom: none;
                  }
                  .pcc-body-cell {
                    border-right: 1px solid #c4c4c4;
                    display: flex;
                    align-items: center;
                    text-align: center;
                    padding: 8px;
                    font-weight: 400;
                    &:last-child {
                      border: none;
                      padding-left: 10px;
                      text-align: left;
                    }
                  }
                }
              }
            }
          }
          .client-cases-main {
            display: flex;
            width: 390px;
            flex-direction: column;
            width: 100%;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            font-family: "Open Sans", Arial, sans-serif;
            text-transform: capitalize;
            .column {
              min-width: 100%;
            }
            .client-cases-title {
              display: flex;
              width: 100%;
              height: 50px;
              padding: 10px;
              font-size: 20px;
              color: rgba(30, 81, 128, 1);
            }
            .client-cases-grid {
              display: grid;
              grid-template-rows: 40px 100%;
              grid-template-areas:
                "header"
                "body";
              overflow: hidden;
              color: rgba(0, 0, 0, 0.7);
              .client-cases-header {
                display: grid;
                grid-area: header;
                justify-content: space-between;
                column-gap: 10px;
                padding: 10px;
                grid-template-columns: minmax(225px, 2fr) minmax(85px, 1.5fr) 0.25fr;
                align-items: center;
                width: 100%;
                background: #ccc;
                .client-cases-header-cell {
                  display: flex;
                  text-align: center;
                }
              }
              .client-cases-body {
                height: fit-content;
                .client-cases-body-row {
                  display: grid;
                  column-gap: 10px;
                  padding: 10px;
                  justify-content: space-between;
                  grid-template-columns: minmax(225px, 2fr) minmax(85px, 1.5fr) 0.25fr;
                  grid-auto-rows: minmax(30px, auto);
                  grid-area: body;
                  width: 100%;
                  background: #ffffff;
                  border-bottom: 1px solid #c4c4c4;
                  align-items: center;
                  text-align: left;
                  &:hover {
                    cursor: pointer;
                  }
                  .client-cases-body-cell {
                    display: flex;
                    &.type {
                      text-decoration-line: underline;
                    }
                  }
                  i {
                    text-align: center;
                    color: #0075a5;
                  }
                }
              }
            }
          }
        }
      }

      .help-panel-coding {
        h1 {
          color: #227b9c;
          border-bottom: 2px solid #227b9c;
          margin: 2px;
          padding: 5px;
          font-size: 1rem;
        }
        button.accordion {
          display: none;
        }

        ul.link-list {
          li {
            border: none;
            border-bottom: 1px solid #d8d7d7;
            padding: 5px 20px;
            margin: 0px 3px;
            &:before {
              display: none;
            }
            a {
              font-size: 0.75rem;
              font-weight: normal;
              color: #000;
            }
          }
        }
      }
    }
    .content-wrapper {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      padding-bottom: 0;
      overflow: auto;
      .persistent-data {
        align-items: center;
        background-color: #f4f4f4;
        border-bottom: 1px solid #dcd9d9;
        display: flex;
        height: 100%;
        flex-grow: 1;
        margin-bottom: 10px;
        padding: 0.25rem 0.5rem 0.25rem 1.5rem;
        .persistent-data-element {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          margin-right: 0.25rem;
          width: 20%;
          hr {
            margin: 0.25rem 0;
          }
          .element-value {
            color: #3f3e3e;
            flex-basis: 1.1875rem;
            font-size: 0.75rem;
            font-weight: bold;
          }
          .element-label {
            color: #828282;
            font-size: 0.625rem;
            font-style: italic;
            font-weight: 600;
            line-height: 0.5625rem;
            text-transform: uppercase;
          }
        }
      }
      .questionnaire-body {
        flex-grow: 1;
        min-width: 100%;
        display: flex;
        flex-direction: row;
        height: calc(100% - 120px);
        overflow-y: hidden;
        position: relative;

        .questionnaire-container {
          display: flex;
          flex-direction: column;
          height: 100%;
          overflow-y: scroll;
          .questionnaire {
            flex-grow: 0;
            padding: 15px 20px;
          }
          .questionnaire-actions {
            background-color: #fcfcfc;
            flex-grow: 0;
            height: 130px;
            padding: 20px;
            .professional-societies-validations {
              color: #828282;
              font-size: 16px;
              small {
                font-style: italic;
                font-weight: 800;
              }
              fieldset {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.5rem;
                width: 40%;
                label {
                  align-items: center;
                  display: flex;
                  justify-content: space-between;
                  font-size: 14px;
                  input[type="checkbox"] {
                    margin-right: 0.25rem;
                    width: 16px;
                    height: 16px;
                  }
                }
                & + fieldset {
                  width: 100%;
                  .navigation-btn-group {
                    display: flex;
                    flex-direction: row;
                    gap: 10px;
                    justify-content: flex-end;
                    width: 100%;
                  }
                  .action-btn {
                    align-items: center;
                    border: 2px solid $success;
                    border-radius: 10px;
                    box-shadow: 4px 4px 4px rgba(0, 0, 0, 0.1);
                    display: flex;
                    font-size: 13px;
                    font-weight: 800;
                    justify-content: space-between;
                    padding: 0.825rem;
                    text-transform: uppercase;
                    min-width: 10.6875rem;
                    i {
                      font-size: 21px;
                    }
                  }
                  .previous-section {
                    i {
                      padding-right: 0.825rem;
                    }
                  }
                  button {
                    color: $success;
                    background: #ffffff;
                  }
                  a {
                    color: #ffffff;
                    background: #70c64d;
                  }
                }
              }
            }
            .previous-btn {
              background-color: red;
            }
          }
        }
      }
    }
  }
  footer {
    margin: 0;
    max-width: 100%;
  }
}
.modal-overlay {
  z-index: 10;
  .modal {
    overflow-y: hidden;
  }
  .modal-content {
    .modal-paragraph {
      font-size: 13px;
      word-wrap: normal;
    }
    .button-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .modal-buttons {
        i {
          padding-right: 10px;
        }
      }
    }
  }
}
