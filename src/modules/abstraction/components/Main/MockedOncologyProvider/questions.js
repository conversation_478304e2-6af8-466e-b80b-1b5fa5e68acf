export const textAreaQs = [
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3810",
    prompt: "Text--DX Proc--PE",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3811",
    prompt: "Text--DX Proc--true-ray/Scan",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3812",
    prompt: "Text--DX Proc--Scopes",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3813",
    prompt: "Text--DX Proc--Lab Tests",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3814",
    prompt: "Text--DX Proc--Op",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3815",
    prompt: "Text--DX Proc--Path",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3816",
    prompt: "Text--Primary Site Title",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3817",
    prompt: "Text--Histology Title",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3818",
    prompt: "Text--Staging",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3819",
    prompt: "RX Text--Surgery",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3820",
    prompt: "RX Text--Radiation (Beam)",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3821",
    prompt: "RX Text--Radiation Other",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3822",
    prompt: "RX Text--Chemo",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3823",
    prompt: "RX Text--Hormone",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3824",
    prompt: "RX Text--BRM",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3825",
    prompt: "RX Text--Other",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3826",
    prompt: "Text--Remarks",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3827",
    prompt: "Text--Place of Diagnosis",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  }
];

export const demographicsQs = [
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3756",
    prompt: "Name--Last",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3757",
    prompt: "Name--First",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3758",
    prompt: "Name--Middle",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3759",
    prompt: "Name--Maiden",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3760",
    prompt: "Name--Prefix",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3761",
    prompt: "Name--Suffix",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3762",
    prompt: "Name--Alias",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3763",
    prompt: "Name--Birth Surname",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3764",
    prompt: "Name--Spouse/Parent",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3765",
    prompt: "Patient ID Number",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3766",
    prompt: "Medical Record Number",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableOrOpenQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3767",
    prompt: "Social Security Number",
    type: "EnumerableOrOpen",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15955",
        description: "Unknown",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "EnumerableOrOpenQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3768",
    prompt: "Telephone",
    type: "EnumerableOrOpen",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15993",
        description: "Telephone number unavailable or unknown",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15992",
        description: "Patient does not have a telephone",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3769",
    prompt: "Sex",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15947",
        description: "Not stated/Unknown",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15946",
        description: "Transsexual, natal female",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15945",
        description: "Transsexual, natal male",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15944",
        description: "Transsexual, NOS",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15943",
        description:
          "Other (intersex, disorders of sexual development/DSD). The word hermaphrodite formerly classified under this code is an outdated term.",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15942",
        description: "Female",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15941",
        description: "Male",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3770",
    prompt: "Race 1",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15045",
        description: "Unknown",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15044",
        description: "Other",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15043",
        description: "Pacific Islander, NOS",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15042",
        description: "Other Asian, including Asian, NOS and Oriental, NOS",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15041",
        description: "New Guinean",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15040",
        description: "Fiji Islander",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15039",
        description: "Melanesian, NOS",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15038",
        description: "Tongan",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15037",
        description: "Samoan",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15036",
        description: "Tahitian",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15035",
        description: "Polynesian, NOS",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15034",
        description: "Guamanian, NOS",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15033",
        description: "Chamorro/Chamoru",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15032",
        description: "Micronesian, NOS",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15031",
        description: "Pakistani",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15030",
        description: "Asian Indian",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15029",
        description:
          "Asian Indian or Pakistani, NOS (code 09 prior to Version 12)",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15028",
        description: "Thai",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15027",
        description: "Kampuchean (Cambodian)",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15026",
        description: "Hmong",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15025",
        description: "Laotian",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15024",
        description: "Vietnamese",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15023",
        description: "Korean",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15022",
        description: "Hawaiian",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15021",
        description: "Filipino",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15020",
        description: "Japanese",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15019",
        description: "Chinese",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15018",
        description:
          "American Indian, Aleutian, or Eskimo (includes all indigenous populations of the Western hemisphere)",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15017",
        description: "Black",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15016",
        description: "White",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3771",
    prompt: "Race 2",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15076",
        description: "Unknown",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15075",
        description: "Other",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15074",
        description: "Pacific Islander, NOS",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15073",
        description: "Other Asian, including Asian, NOS and Oriental, NOS",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15072",
        description: "No further race documented",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15071",
        description: "New Guinean",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15070",
        description: "Fiji Islander",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15069",
        description: "Melanesian, NOS",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15068",
        description: "Tongan",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15067",
        description: "Samoan",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15066",
        description: "Tahitian",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15065",
        description: "Polynesian, NOS",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15064",
        description: "Guamanian, NOS",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15063",
        description: "Chamorro/Chamoru",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15062",
        description: "Micronesian, NOS",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15061",
        description: "Pakistani",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15060",
        description: "Asian Indian",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15059",
        description:
          "Asian Indian or Pakistani, NOS (code 09 prior to Version 12)",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15058",
        description: "Thai",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15057",
        description: "Kampuchean (Cambodian)",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15056",
        description: "Hmong",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15055",
        description: "Laotian",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15054",
        description: "Vietnamese",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15053",
        description: "Korean",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15052",
        description: "Hawaiian",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15051",
        description: "Filipino",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15050",
        description: "Japanese",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15049",
        description: "Chinese",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15048",
        description:
          "American Indian, Aleutian, or Eskimo (includes all indigenous populations of the Western hemisphere)",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15047",
        description: "Black",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15046",
        description: "White",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3772",
    prompt: "Race 3",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15107",
        description: "Unknown",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15106",
        description: "Other",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15105",
        description: "Pacific Islander, NOS",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15104",
        description: "Other Asian, including Asian, NOS and Oriental, NOS",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15103",
        description: "No further race documented",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15102",
        description: "New Guinean",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15101",
        description: "Fiji Islander",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15100",
        description: "Melanesian, NOS",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15099",
        description: "Tongan",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15098",
        description: "Samoan",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15097",
        description: "Tahitian",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15096",
        description: "Polynesian, NOS",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15095",
        description: "Guamanian, NOS",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15094",
        description: "Chamorro/Chamoru",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15093",
        description: "Micronesian, NOS",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15092",
        description: "Pakistani",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15091",
        description: "Asian Indian",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15090",
        description:
          "Asian Indian or Pakistani, NOS (code 09 prior to Version 12)",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15089",
        description: "Thai",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15088",
        description: "Kampuchean (Cambodian)",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15087",
        description: "Hmong",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15086",
        description: "Laotian",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15085",
        description: "Vietnamese",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15084",
        description: "Korean",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15083",
        description: "Hawaiian",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15082",
        description: "Filipino",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15081",
        description: "Japanese",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15080",
        description: "Chinese",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15079",
        description:
          "American Indian, Aleutian, or Eskimo (includes all indigenous populations of the Western hemisphere)",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15078",
        description: "Black",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15077",
        description: "White",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3773",
    prompt: "Race 4",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15138",
        description: "Unknown",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15137",
        description: "Other",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15136",
        description: "Pacific Islander, NOS",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15135",
        description: "Other Asian, including Asian, NOS and Oriental, NOS",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15134",
        description: "No further race documented",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15133",
        description: "New Guinean",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15132",
        description: "Fiji Islander",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15131",
        description: "Melanesian, NOS",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15130",
        description: "Tongan",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15129",
        description: "Samoan",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15128",
        description: "Tahitian",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15127",
        description: "Polynesian, NOS",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15126",
        description: "Guamanian, NOS",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15125",
        description: "Chamorro/Chamoru",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15124",
        description: "Micronesian, NOS",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15123",
        description: "Pakistani",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15122",
        description: "Asian Indian",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15121",
        description:
          "Asian Indian or Pakistani, NOS (code 09 prior to Version 12)",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15120",
        description: "Thai",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15119",
        description: "Kampuchean (Cambodian)",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15118",
        description: "Hmong",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15117",
        description: "Laotian",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15116",
        description: "Vietnamese",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15115",
        description: "Korean",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15114",
        description: "Hawaiian",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15113",
        description: "Filipino",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15112",
        description: "Japanese",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15111",
        description: "Chinese",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15110",
        description:
          "American Indian, Aleutian, or Eskimo (includes all indigenous populations of the Western hemisphere).",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15109",
        description: "Black",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15108",
        description: "White",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3774",
    prompt: "Race 5",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15169",
        description: "Unknown",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15168",
        description: "Other",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15167",
        description: "Pacific Islander, NOS",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15166",
        description: "Other Asian, including Asian, NOS and Oriental, NOS",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15165",
        description: "No further race documented",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15164",
        description: "New Guinean",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15163",
        description: "Fiji Islander",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15162",
        description: "Melanesian, NOS",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15161",
        description: "Tongan",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15160",
        description: "Samoan",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15159",
        description: "Tahitian",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15158",
        description: "Polynesian, NOS",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15157",
        description: "Guamanian, NOS",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15156",
        description: "Chamorro/Chamoru",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15155",
        description: "Micronesian, NOS",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15154",
        description: "Pakistani",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15153",
        description: "Asian Indian",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15152",
        description:
          "Asian Indian or Pakistani, NOS (code 09 prior to Version 12)",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15151",
        description: "Thai",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15150",
        description: "Kampuchean (Cambodian)",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15149",
        description: "Hmong",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15148",
        description: "Laotian",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15147",
        description: "Vietnamese",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15146",
        description: "Korean",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15145",
        description: "Hawaiian",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15144",
        description: "Filipino",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15143",
        description: "Japanese",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15142",
        description: "Chinese",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15141",
        description:
          "American Indian, Aleutian, or Eskimo (includes all indigenous populations of the Western hemisphere)",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15140",
        description: "Black",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15139",
        description: "White",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3775",
    prompt: "Spanish/Hispanic Origin",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "15965",
        description: "Unknown whether Spanish or not",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15964",
        description: "Dominican Republic",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15963",
        description:
          "Spanish surname only (Code 7 is ordinarily for central registry use only, hospital registrars may use code 7 if using a list of Hispanic surnames provided by their central registry; otherwise, code 9 'unknown whether Spanish or not' should be used.) The only evidence of the person's Hispanic origin is the surname or maiden name and there is no contrary evidence that the person is not Hispanic.",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15962",
        description:
          "Spanish, NOSHispanic, NOSLatino, NOSThere is evidence, other than surname or maiden name, that the person is Hispanic, but he/she cannot be assigned to any of the other categories 1-5.",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15961",
        description:
          "Other specified Spanish/Hispanic origin (includes European; excludes Dominican Republic)",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15960",
        description: "South or Central American (except Brazil)",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15959",
        description: "Cuban",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15958",
        description: "Puerto Rican",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15957",
        description: "Mexican (includes Chicano)",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "15956",
        description: "Non-Spanish; non-Hispanic",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableFavoritableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3776",
    prompt: "Birthplace",
    type: "EnumerableSearchableFavoritable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3777",
    prompt: "Birthplace--Country",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "13089",
        description: "Polynesian Islands",
        displayOrder: 35,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13088",
        description: "Micronesian Islands",
        displayOrder: 34,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13087",
        description: "Melanesian Islands",
        displayOrder: 33,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13086",
        description: "China, NOS",
        displayOrder: 32,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13085",
        description: "Malaysia, Singapore, Brunei",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13084",
        description: "Southeast Asia",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13083",
        description: "Other Asian Republics of former USSR",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13082",
        description: "Caucasian Republics of former USSR",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13081",
        description: "Israel and Palestine",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13080",
        description: "Arabian Peninsula",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13079",
        description: "Ethiopia and Eritrea",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13078",
        description: "African Islands",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13077",
        description: "East Africa",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13076",
        description: "South Africa",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13075",
        description: "West Africa",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13074",
        description: "Sudanese Countries",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13073",
        description: "North Africa",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13072",
        description: "Ukraine and Moldova",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13071",
        description: "Yugoslavia (former)",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13070",
        description: "Czechoslovakia (former)",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13069",
        description: "Slavic Countries",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13068",
        description: "Germanic Countries",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13067",
        description: "Scandinavia",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13066",
        description: "England, Channel Islands, Isle of Man",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13065",
        description: "Other Caribbean Islands",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13064",
        description: "North American Islands",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13063",
        description: "Unknown",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13062",
        description: "Non-US NOS",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13061",
        description: "Asia NOS",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13060",
        description: "Africa NOS",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13059",
        description: "Europe NOS",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13058",
        description: "Pacific NOS",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13057",
        description: "South America NOS",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13056",
        description: "Central American NOS",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13055",
        description: "North America NOS",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3778",
    prompt: "Birthplace--State",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "DateQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    questionEnumerables: null,
    columns: null,
    loadEnums: null,
    id: "3779",
    prompt: "Date of Birth",
    type: "Date",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "CalculatedQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3780",
    prompt: "Age at Diagnosis",
    type: "Calculated",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3781",
    prompt: "Date of Birth Flag",
    type: "Enumerable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "13310",
        description:
          "A proper value is applicable but not known (i.e., birth date is unknown)",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "EnumerableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3782",
    prompt: "Occupation Source",
    type: "Enumerable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "14385",
        description: "Unknown source",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14384",
        description:
          "Not applicable, patient less than 14 years of age at diagnosis",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14383",
        description: "Other source",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14382",
        description: "Interview",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14381",
        description: "Death certificate",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14380",
        description: "Reporting facility records",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14379",
        description: "Unknown occupation/no occupation available",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3783",
    prompt: "Industry Source",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "13909",
        description: "Unknown source",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13908",
        description:
          "Not applicable, patient less than 14 years of age at diagnosis",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13907",
        description: "Other source",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13906",
        description: "Interview",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13905",
        description: "Death certificate",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13904",
        description: "Reporting facility records",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13903",
        description: "Unknown industry/no industry available",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3784",
    prompt: "Text--Usual Occupation",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "TextAreaQuestion",
    maxCharacters: 1000,
    unit: "",
    questionEnumerables: [],
    loadEnums: false,
    columns: 0,
    primaryIds: 0,
    dependentEnumerableId: 0,
    id: "3785",
    prompt: "Text--Usual Industry",
    type: "TextArea",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "CalculatedQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3786",
    prompt: "Accession Number--Hosp",
    type: "Calculated",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "CalculatedQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3787",
    prompt: "Computed Ethnicity",
    type: "Calculated",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3788",
    prompt: "Computed Ethnicity Source",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "13279",
        description: "Unknown type of match",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13278",
        description: "Other type of match",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13277",
        description:
          "Combination of Census and GUESS, with or without other lists",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13276",
        description: "Combination of Census and other locally generated list",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13275",
        description: "Combination list including South Florida names",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13274",
        description: "GUESS Program",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13273",
        description: "1990 Census Bureau list of Spanish surnames",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13272",
        description: "1980 Census Bureau list of Spanish surnames",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13271",
        description: "Census Bureau list of Spanish surnames, NOS",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "13270",
        description: "No match was run, for 1994 and later tumors",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3789",
    prompt: "Addr Current--City",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3790",
    prompt: "Addr Current--Country",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "12921",
        description: "Polynesian Islands",
        displayOrder: 35,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12920",
        description: "Micronesian Islands",
        displayOrder: 34,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12919",
        description: "Melanesian Islands",
        displayOrder: 33,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12918",
        description: "China, NOS",
        displayOrder: 32,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12917",
        description: "Malaysia, Singapore, Brunei",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12916",
        description: "Southeast Asia",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12915",
        description: "Other Asian Republics of former USSR",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12914",
        description: "Caucasian Republics of former USSR",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12913",
        description: "Israel and Palestine",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12912",
        description: "Arabian Peninsula",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12911",
        description: "Ethiopia and Eritrea",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12910",
        description: "African Islands",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12909",
        description: "East Africa",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12908",
        description: "South Africa",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12907",
        description: "West Africa",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12906",
        description: "Sudanese Countries",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12905",
        description: "North Africa",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12904",
        description: "Ukraine and Moldova",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12903",
        description: "Yugoslavia (former)",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12902",
        description: "Czechoslovakia (former)",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12901",
        description: "Slavic Countries",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12900",
        description: "Germanic Countries",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12899",
        description: "Scandinavia",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12898",
        description: "England, Channel Island, Isle of Man",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12897",
        description: "Other Caribbean Islands",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12896",
        description: "North American Islands",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12895",
        description: "Unknown",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12894",
        description: "Non-US NOS",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12893",
        description: "Asia NOS",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12892",
        description: "Africa NOS",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12891",
        description: "Europe NOS",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12890",
        description: "Pacific NOS",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12889",
        description: "South America NOS",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12888",
        description: "Central America NOS",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12887",
        description: "North America NOS",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3791",
    prompt: "Addr Current--No & Street",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3792",
    prompt: "Addr Current--Postal Code",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3793",
    prompt: "Addr Current--State",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "12929",
        description: "Residence unknown",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12928",
        description:
          "Resident of country other than the United States (including its territories, commonwealths, or possessions) or Canada, and country is unknown",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12927",
        description:
          "Resident of country other than the United States (including its territories, commonwealths, or possessions) or Canada, and country is known",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12926",
        description:
          "Resident of United States, NOS (state/commonwealth/territory/possession unknown)",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12925",
        description: "Resident of Canada, NOS (province/territory unknown)",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3794",
    prompt: "Addr Current--Supplementl",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3795",
    prompt: "County--Current",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableOrOpenQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3796",
    prompt: "Addr at DX--City",
    type: "EnumerableOrOpen",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "12842",
        description: "City at diagnosis unknown",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    unit: null,
    primaryIds: null,
    id: "3797",
    prompt: "Addr at DX--Country",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "12877",
        description: "Polynesian Islands",
        displayOrder: 35,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12876",
        description: "Micronesian Islands",
        displayOrder: 34,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12875",
        description: "Melanesian Islands",
        displayOrder: 33,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12874",
        description: "China, NOS",
        displayOrder: 32,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12873",
        description: "Malaysia, Singapore, Brunei",
        displayOrder: 31,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12872",
        description: "Southeast Asia",
        displayOrder: 30,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12871",
        description: "Other Asian Republics of former USSR",
        displayOrder: 29,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12870",
        description: "Caucasian Republics of former USSR",
        displayOrder: 28,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12869",
        description: "Israel and Palestine",
        displayOrder: 27,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12868",
        description: "Arabian Peninsula",
        displayOrder: 26,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12867",
        description: "Ethiopia and Eritrea",
        displayOrder: 25,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12866",
        description: "African Islands",
        displayOrder: 24,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12865",
        description: "East Africa",
        displayOrder: 23,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12864",
        description: "South Africa",
        displayOrder: 22,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12863",
        description: "West Africa",
        displayOrder: 21,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12862",
        description: "Sudanese Countries",
        displayOrder: 20,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12861",
        description: "North Africa",
        displayOrder: 19,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12860",
        description: "Ukraine and Moldova",
        displayOrder: 18,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12859",
        description: "Yugoslavia",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12858",
        description: "Czechoslovakia (former)",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12857",
        description: "Slavic Countries",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12856",
        description: "Germanic Countries",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12855",
        description: "Scandinavia",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12854",
        description: "England, Channel islands, Isle of Man",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12853",
        description: "Other Caribbean Islands",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12852",
        description: "North American Islands",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12851",
        description: "Unknown",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12850",
        description: "Non-US NOS",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12849",
        description: "Asia NOS",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12848",
        description: "Africa NOS",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12847",
        description: "Europe NOS",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12846",
        description: "Pacific NOS",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12845",
        description: "South America NOS",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12844",
        description: "Central America NOS",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12843",
        description: "North America NOS",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "EnumerableOrOpenQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3798",
    prompt: "Addr at DX--No & Street",
    type: "EnumerableOrOpen",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "12878",
        description: "Patient's address is unknown",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3799",
    prompt: "Addr at DX--Postal Code",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3800",
    prompt: "Addr at DX--State",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "12886",
        description: "Residence unknown",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12885",
        description:
          "Resident of country other than the United States (including its territories, commonwealths, or possessions) or Canada, and country is unknown",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12884",
        description:
          "Resident of country other than the United States (including its territories, commonwealths, or possessions) or Canada, and country is known",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12883",
        description:
          "Resident of United States, NOS (state/commonwealth/territory/possession unknown)",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "12882",
        description: "Resident of Canada, NOS (province/territory unknown)",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3801",
    prompt: "Addr at DX--Supplementl",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3802",
    prompt: "County at DX Reported",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3803",
    prompt: "County at DX Analysis",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableSearchableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    unit: null,
    id: "3804",
    prompt: "Primary Payer at DX",
    type: "EnumerableSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "14960",
        description: "Insurance status unknown",
        displayOrder: 17,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14959",
        description: "Indian/Public Health Service",
        displayOrder: 16,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14958",
        description: "Veterans Affairs",
        displayOrder: 15,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14957",
        description: "Military",
        displayOrder: 14,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14956",
        description: "TRICARE",
        displayOrder: 13,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14955",
        description: "Medicare with Medicaid eligibility",
        displayOrder: 12,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14954",
        description: "Medicare with private supplement",
        displayOrder: 11,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14953",
        description: "Medicare - Administered through a Managed Care plan",
        displayOrder: 10,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14952",
        description: "Medicare with supplement, NOS",
        displayOrder: 9,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14951",
        description: "Medicare/Medicare, NOS",
        displayOrder: 8,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14950",
        description: "Medicaid - Administered through a Managed Care plan",
        displayOrder: 7,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14949",
        description: "Medicaid",
        displayOrder: 6,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14948",
        description: "Private Insurance: Fee-for-Service",
        displayOrder: 5,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14947",
        description: "Private Insurance: Managed care, HMO, or PPO",
        displayOrder: 4,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14946",
        description: "Insurance, NOS",
        displayOrder: 3,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14945",
        description: "Not insured, self-pay",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "14944",
        description: "Not insured",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ],
    columns: ["code", "description"],
    loadEnums: true
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3805",
    prompt: "Place of Death",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3806",
    prompt: "Vital Status",
    type: "Enumerable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "16145",
        description: "Alive",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "16144",
        description: "Dead",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  },
  {
    __typename: "NumberSearchableQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3807",
    prompt: "Marital Status at DX",
    type: "NumberSearchable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "OpenQuestion",
    maxCharacters: null,
    questionEnumerables: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3808",
    prompt: "Medicare Beneficiary Identifier",
    type: "Open",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null
  },
  {
    __typename: "EnumerableQuestion",
    maxCharacters: null,
    dependentEnumerableId: null,
    primaryIds: null,
    columns: null,
    loadEnums: null,
    unit: null,
    id: "3809",
    prompt: "Vital Status Recode",
    type: "Enumerable",
    emphasis: false,
    edgeTarget: false,
    calculationTarget: false,
    hasHelpInformation: false,
    effectiveOn: null,
    effectiveOnComparand: null,
    expirationOn: null,
    questionEnumerables: [
      {
        __typename: "QuestionEnumerable",
        id: "16147",
        description: "Alive as of study cutoff date",
        displayOrder: 2,
        effectiveOn: null,
        expirationOn: null
      },
      {
        __typename: "QuestionEnumerable",
        id: "16146",
        description: "Dead as of study cutoff date",
        displayOrder: 1,
        effectiveOn: null,
        expirationOn: null
      }
    ]
  }
];
