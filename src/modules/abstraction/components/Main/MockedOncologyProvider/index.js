import { InMemoryCache } from "@apollo/client";
import { MockedProvider } from "@apollo/client/testing";
import possibleTypes from "base/possibleTypes.json";
import mocks from "./mocks";
// Creates Cache to hold objects
const cache = new InMemoryCache({
  possibleTypes
});

export const MockedOncologyProvider = ({ children }) => (
  <MockedProvider mocks={mocks} cache={cache} removeTypename>
    {children}
  </MockedProvider>
);

export default MockedOncologyProvider;
