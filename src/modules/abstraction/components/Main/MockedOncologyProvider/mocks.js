import queryResponse from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import queryQuestionnaire from "modules/questionnaire/components/ApolloMain/QuestionnaireQuery/query";
import { textAreaQs, demographicsQs } from "./questions";

export const questionnaireWithId8Mock = {
  request: {
    query: queryQuestionnaire,
    variables: {
      id: 8
    }
  },
  result: {
    data: {
      questionnaire: {
        arrivalDateQuestionId: null,
        dischargeDateQuestionId: null,
        displayLogics: [
          {
            children: [
              {
                id: "178",
                __typename: "QuestionListDisplayLogic"
              }
            ],
            buttonText: null,
            valueToSet: null,
            jumpTarget: null,
            inline: true,
            collapsible: false,
            hideable: false,
            rowData: null,
            headerData: null,
            indentLevels: null,
            questionOrder: null,
            helpInformation: null,
            displayName: null,
            questionIds: null,
            groupId: "1119",
            id: "167",
            instanceTitleId: [],
            isTopLevel: true,
            name: "Demographic",
            repeatable: false,
            rightSidePane: true,
            sectionQuestions: [],
            sortOrder: 4,
            type: "Section",
            __typename: "SectionDisplayLogic"
          },
          {
            children: [],
            collapsible: false,
            groupId: "1119",
            helpInformation: [],
            hideable: false,
            id: "178",
            name: null,
            displayName: null,
            questionIds: null,
            buttonText: null,
            valueToSet: null,
            jumpTarget: null,
            rowData: null,
            headerData: null,
            isTopLevel: false,
            sortOrder: null,
            repeatable: false,
            rightSidePane: false,
            persistentQuestions: false,
            instanceTitleId: null,
            sectionQuestions: null,
            indentLevels: [0, 0, 0, 0, 0],
            inline: false,
            questionOrder: [3756, 3757, 3758, 3767, 3765],
            type: "QuestionList",
            __typename: "QuestionListDisplayLogic"
          },
          {
            children: [
              {
                id: "179",
                __typename: "QuestionListDisplayLogic",
                indentLevels: null,
                questionOrder: null,
                helpInformation: null,
                displayName: null,
                questionIds: null
              }
            ],
            buttonText: null,
            valueToSet: null,
            jumpTarget: null,
            inline: true,
            collapsible: false,
            hideable: false,
            rowData: null,
            headerData: null,
            indentLevels: null,
            questionOrder: null,
            helpInformation: null,
            displayName: null,
            questionIds: null,
            groupId: "1120",
            id: "168",
            instanceTitleId: [],
            isTopLevel: true,
            name: "Oncology Details",
            repeatable: false,
            rightSidePane: false,
            sectionQuestions: [],
            sortOrder: 5,
            type: "Section",
            __typename: "SectionDisplayLogic"
          },
          {
            children: [],
            collapsible: false,
            groupId: "1120",
            helpInformation: [],
            hideable: false,
            id: "179",
            name: null,
            displayName: null,
            questionIds: null,
            buttonText: null,
            valueToSet: null,
            jumpTarget: null,
            rowData: null,
            headerData: null,
            isTopLevel: false,
            sortOrder: null,
            repeatable: false,
            rightSidePane: false,
            persistentQuestions: true,
            instanceTitleId: null,
            sectionQuestions: null,
            indentLevels: [
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
            ],
            inline: false,
            questionOrder: [
              3810, 3811, 3812, 3813, 3814, 3815, 3816, 3817, 3818, 3819, 3820,
              3821, 3822, 3823, 3824, 3825, 3826, 3827
            ],
            type: "QuestionList",
            __typename: "QuestionListDisplayLogic"
          }
        ],
        groups: [
          {
            id: "1119",
            questions: [
              { id: "3756", __typename: "OpenQuestion" },
              { id: "3757", __typename: "OpenQuestion" },
              { id: "3758", __typename: "OpenQuestion" },
              { id: "3759", __typename: "OpenQuestion" },
              { id: "3760", __typename: "OpenQuestion" },
              { id: "3761", __typename: "OpenQuestion" },
              { id: "3762", __typename: "OpenQuestion" },
              { id: "3763", __typename: "OpenQuestion" },
              { id: "3764", __typename: "OpenQuestion" },
              { id: "3765", __typename: "OpenQuestion" },
              { id: "3766", __typename: "NumberSearchableQuestion" },
              { id: "3767", __typename: "EnumerableOrOpenQuestion" },
              { id: "3768", __typename: "EnumerableOrOpenQuestion" },
              { id: "3769", __typename: "EnumerableSearchableQuestion" },
              { id: "3770", __typename: "EnumerableSearchableQuestion" },
              { id: "3771", __typename: "EnumerableSearchableQuestion" },
              { id: "3772", __typename: "EnumerableSearchableQuestion" },
              { id: "3773", __typename: "EnumerableSearchableQuestion" },
              { id: "3774", __typename: "EnumerableSearchableQuestion" },
              { id: "3775", __typename: "EnumerableSearchableQuestion" },
              {
                id: "3776",
                __typename: "EnumerableSearchableFavoritableQuestion"
              },
              { id: "3777", __typename: "EnumerableSearchableQuestion" },
              { id: "3778", __typename: "EnumerableSearchableQuestion" },
              { id: "3779", __typename: "DateQuestion" },
              { id: "3780", __typename: "CalculatedQuestion" },
              { id: "3781", __typename: "EnumerableQuestion" },
              { id: "3782", __typename: "EnumerableQuestion" },
              { id: "3783", __typename: "EnumerableSearchableQuestion" },
              { id: "3784", __typename: "TextAreaQuestion" },
              { id: "3785", __typename: "TextAreaQuestion" },
              { id: "3786", __typename: "CalculatedQuestion" },
              { id: "3787", __typename: "CalculatedQuestion" },
              { id: "3788", __typename: "EnumerableSearchableQuestion" },
              { id: "3789", __typename: "OpenQuestion" },
              { id: "3790", __typename: "EnumerableSearchableQuestion" },
              { id: "3791", __typename: "OpenQuestion" },
              { id: "3792", __typename: "NumberSearchableQuestion" },
              { id: "3793", __typename: "EnumerableSearchableQuestion" },
              { id: "3794", __typename: "OpenQuestion" },
              { id: "3795", __typename: "NumberSearchableQuestion" },
              { id: "3796", __typename: "EnumerableOrOpenQuestion" },
              { id: "3797", __typename: "EnumerableSearchableQuestion" },
              { id: "3798", __typename: "EnumerableOrOpenQuestion" },
              { id: "3799", __typename: "NumberSearchableQuestion" },
              { id: "3800", __typename: "EnumerableSearchableQuestion" },
              { id: "3801", __typename: "OpenQuestion" },
              { id: "3802", __typename: "NumberSearchableQuestion" },
              { id: "3803", __typename: "NumberSearchableQuestion" },
              { id: "3804", __typename: "EnumerableSearchableQuestion" },
              { id: "3805", __typename: "NumberSearchableQuestion" },
              { id: "3806", __typename: "EnumerableQuestion" },
              { id: "3807", __typename: "NumberSearchableQuestion" },
              { id: "3808", __typename: "OpenQuestion" },
              { id: "3809", __typename: "EnumerableQuestion" }
            ],
            __typename: "Group"
          },
          {
            id: "1120",
            questions: [
              { id: "3810", __typename: "TextAreaQuestion" },
              { id: "3811", __typename: "TextAreaQuestion" },
              { id: "3812", __typename: "TextAreaQuestion" },
              { id: "3813", __typename: "TextAreaQuestion" },
              { id: "3814", __typename: "TextAreaQuestion" },
              { id: "3815", __typename: "TextAreaQuestion" },
              { id: "3816", __typename: "TextAreaQuestion" },
              { id: "3817", __typename: "TextAreaQuestion" },
              { id: "3818", __typename: "TextAreaQuestion" },
              { id: "3819", __typename: "TextAreaQuestion" },
              { id: "3820", __typename: "TextAreaQuestion" },
              { id: "3821", __typename: "TextAreaQuestion" },
              { id: "3822", __typename: "TextAreaQuestion" },
              { id: "3823", __typename: "TextAreaQuestion" },
              { id: "3824", __typename: "TextAreaQuestion" },
              { id: "3825", __typename: "TextAreaQuestion" },
              { id: "3826", __typename: "TextAreaQuestion" },
              { id: "3827", __typename: "TextAreaQuestion" }
            ],
            __typename: "Group"
          }
        ],
        id: "8",
        name: "Oncology v21",
        nodes: [
          { id: "1052", groupId: 1119, edges: [], __typename: "Node" },
          { id: "1053", groupId: 1120, edges: [], __typename: "Node" }
        ],
        questions: [...demographicsQs, ...textAreaQs],
        __typename: "Questionnaire"
      }
    }
  }
};
export const questionnaireResponseWithId11Mock = {
  request: {
    query: queryResponse,
    variables: {
      id: 11
    }
  },
  result: {
    data: {
      questionnaireResponse: {
        __typename: "QuestionnaireResponse",
        answerGroups: [
          {
            __typename: "AnswerGroup",
            groupId: "1119",
            id: "1984",
            parentId: null
          },
          {
            __typename: "AnswerGroup",
            groupId: "1120",
            id: "1983",
            parentId: null
          }
        ],
        answers: [],
        id: 11,
        patient: {
          __typename: "Patient",
          firstName: "Testy",
          id: "36",
          lastName: "Tester",
          mrn: "**********"
        },
        questionnaireId: 8,
        responseCase: null,
        validationReport: null,
        visibleQuestions: [
          { id: "3756|1984", __typename: "VisibleQuestion" },
          { id: "3757|1984", __typename: "VisibleQuestion" },
          { id: "3758|1984", __typename: "VisibleQuestion" },
          { id: "3759|1984", __typename: "VisibleQuestion" },
          { id: "3760|1984", __typename: "VisibleQuestion" },
          { id: "3761|1984", __typename: "VisibleQuestion" },
          { id: "3762|1984", __typename: "VisibleQuestion" },
          { id: "3763|1984", __typename: "VisibleQuestion" },
          { id: "3764|1984", __typename: "VisibleQuestion" },
          { id: "3765|1984", __typename: "VisibleQuestion" },
          { id: "3766|1984", __typename: "VisibleQuestion" },
          { id: "3767|1984", __typename: "VisibleQuestion" },
          { id: "3768|1984", __typename: "VisibleQuestion" },
          { id: "3769|1984", __typename: "VisibleQuestion" },
          { id: "3770|1984", __typename: "VisibleQuestion" },
          { id: "3771|1984", __typename: "VisibleQuestion" },
          { id: "3772|1984", __typename: "VisibleQuestion" },
          { id: "3773|1984", __typename: "VisibleQuestion" },
          { id: "3774|1984", __typename: "VisibleQuestion" },
          { id: "3775|1984", __typename: "VisibleQuestion" },
          { id: "3776|1984", __typename: "VisibleQuestion" },
          { id: "3777|1984", __typename: "VisibleQuestion" },
          { id: "3778|1984", __typename: "VisibleQuestion" },
          { id: "3779|1984", __typename: "VisibleQuestion" },
          { id: "3780|1984", __typename: "VisibleQuestion" },
          { id: "3781|1984", __typename: "VisibleQuestion" },
          { id: "3782|1984", __typename: "VisibleQuestion" },
          { id: "3783|1984", __typename: "VisibleQuestion" },
          { id: "3784|1984", __typename: "VisibleQuestion" },
          { id: "3785|1984", __typename: "VisibleQuestion" },
          { id: "3786|1984", __typename: "VisibleQuestion" },
          { id: "3787|1984", __typename: "VisibleQuestion" },
          { id: "3788|1984", __typename: "VisibleQuestion" },
          { id: "3789|1984", __typename: "VisibleQuestion" },
          { id: "3790|1984", __typename: "VisibleQuestion" },
          { id: "3791|1984", __typename: "VisibleQuestion" },
          { id: "3792|1984", __typename: "VisibleQuestion" },
          { id: "3793|1984", __typename: "VisibleQuestion" },
          { id: "3794|1984", __typename: "VisibleQuestion" },
          { id: "3795|1984", __typename: "VisibleQuestion" },
          { id: "3796|1984", __typename: "VisibleQuestion" },
          { id: "3797|1984", __typename: "VisibleQuestion" },
          { id: "3798|1984", __typename: "VisibleQuestion" },
          { id: "3799|1984", __typename: "VisibleQuestion" },
          { id: "3800|1984", __typename: "VisibleQuestion" },
          { id: "3801|1984", __typename: "VisibleQuestion" },
          { id: "3802|1984", __typename: "VisibleQuestion" },
          { id: "3803|1984", __typename: "VisibleQuestion" },
          { id: "3804|1984", __typename: "VisibleQuestion" },
          { id: "3805|1984", __typename: "VisibleQuestion" },
          { id: "3806|1984", __typename: "VisibleQuestion" },
          { id: "3807|1984", __typename: "VisibleQuestion" },
          { id: "3808|1984", __typename: "VisibleQuestion" },
          { id: "3809|1984", __typename: "VisibleQuestion" },
          { id: "3810|1983", __typename: "VisibleQuestion" },
          { id: "3811|1983", __typename: "VisibleQuestion" },
          { id: "3812|1983", __typename: "VisibleQuestion" },
          { id: "3813|1983", __typename: "VisibleQuestion" },
          { id: "3814|1983", __typename: "VisibleQuestion" },
          { id: "3815|1983", __typename: "VisibleQuestion" },
          { id: "3816|1983", __typename: "VisibleQuestion" },
          { id: "3817|1983", __typename: "VisibleQuestion" },
          { id: "3818|1983", __typename: "VisibleQuestion" },
          { id: "3819|1983", __typename: "VisibleQuestion" },
          { id: "3820|1983", __typename: "VisibleQuestion" },
          { id: "3821|1983", __typename: "VisibleQuestion" },
          { id: "3822|1983", __typename: "VisibleQuestion" },
          { id: "3823|1983", __typename: "VisibleQuestion" },
          { id: "3824|1983", __typename: "VisibleQuestion" },
          { id: "3825|1983", __typename: "VisibleQuestion" },
          { id: "3826|1983", __typename: "VisibleQuestion" },
          { id: "3827|1983", __typename: "VisibleQuestion" }
        ],
        visit: {
          __typename: "Visit",
          admittedAt: 1587340800.0,
          arrivedAt: 1587340800.0,
          dischargedAt: 1587427200.0,
          id: "36",
          number: "92616361"
        },
        writeLocked: false
      }
    }
  }
};

export default [questionnaireWithId8Mock, questionnaireResponseWithId11Mock];
