import { or } from "ramda";
import { Route, Switch, Redirect } from "react-router-dom";
import { Spin<PERSON> } from "@q-centrix/q-components-react";
import Abstraction from "../../Abstraction";
import <PERSON>rro<PERSON><PERSON><PERSON><PERSON> from "shared/components/ErrorHandler";
import {
  useComponentLogic,
  useQuestionnaire,
  useQuestionnaireResponse
} from "./hooks";

// eslint-disable-next-line complexity
export const AbstractionRouter = ({ id }) => {
  const {
    errorQuestionnaireResponse,
    loadingQuestionnaireResponse,
    questionnaireResponse,
    answerGroups
  } = useQuestionnaireResponse(id);
  const { errorQuestionnaire, loadingQuestionnaire } = useQuestionnaire(
    questionnaireResponse,
    errorQuestionnaireResponse,
    loadingQuestionnaireResponse,
    answerGroups
  );
  const { firstSectionId, clinicalQrId } = useComponentLogic({ id });

  if (or(errorQuestionnaire, errorQuestionnaireResponse)) {
    return (
      <ErrorHandler error={errorQuestionnaireResponse || errorQuestionnaire} />
    );
  } else if (
    or(loadingQuestionnaire, or(loadingQuestionnaireResponse, !firstSectionId))
  ) {
    return <Spinner />;
  }

  return (
    <Switch>
      <Route
        path="/abstraction/:id/section/:answerGroupId/:fieldName?"
        render={({
          match: {
            params: { answerGroupId, fieldName }
          }
        }) => (
          <Abstraction
            currentAnswerGroup={answerGroupId}
            questionnaireResponseId={id}
            focusFieldName={fieldName}
          />
        )}
      />

      <Route
        render={({ match: { url } }) => (
          <Redirect
            to={`${url}/section/${firstSectionId}${
              clinicalQrId ? `?clinicalQrId=${clinicalQrId}` : ""
            }`}
          />
        )}
      />
    </Switch>
  );
};

export default AbstractionRouter;
