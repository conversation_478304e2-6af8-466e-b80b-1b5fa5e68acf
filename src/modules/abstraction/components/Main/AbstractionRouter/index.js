import { Spinner } from "@q-centrix/q-components-react";
import { or } from "ramda";
import { useEffect } from "react";
import { Route, Switch, useHistory, useLocation } from "react-router-dom";
import Error<PERSON>andler from "shared/components/ErrorHandler";
import Abstraction from "../../Abstraction";
import {
  useComponentLogic,
  useQuestionnaire,
  useQuestionnaireResponse
} from "./hooks";

// eslint-disable-next-line complexity
export const AbstractionRouter = ({ id }) => {
  const history = useHistory();
  const location = useLocation();
  const {
    errorQuestionnaireResponse,
    loadingQuestionnaireResponse,
    questionnaireResponse,
    answerGroups
  } = useQuestionnaireResponse(id);
  const { errorQuestionnaire, loadingQuestionnaire } = useQuestionnaire(
    questionnaireResponse,
    errorQuestionnaireResponse,
    loadingQuestionnaireResponse,
    answerGroups
  );
  const { firstSectionId, clinicalQrId } = useComponentLogic({ id });

  // Programmatic navigation using useHistory instead of Redirect
  useEffect(() => {
    if (firstSectionId && !location.pathname.includes("/section/")) {
      const redirectUrl = `${location.pathname}/section/${firstSectionId}${
        clinicalQrId ? `?clinicalQrId=${clinicalQrId}` : ""
      }`;

      history.replace(redirectUrl);
    }
  }, [firstSectionId, clinicalQrId, history, location.pathname]);

  if (or(errorQuestionnaire, errorQuestionnaireResponse)) {
    return (
      <ErrorHandler error={errorQuestionnaireResponse || errorQuestionnaire} />
    );
  } else if (
    or(loadingQuestionnaire, or(loadingQuestionnaireResponse, !firstSectionId))
  ) {
    return <Spinner />;
  }

  return (
    <Switch>
      <Route
        path="/abstraction/:id/section/:answerGroupId/:fieldName?"
        render={({
          match: {
            params: { answerGroupId, fieldName }
          }
        }) => (
          <Abstraction
            currentAnswerGroup={answerGroupId}
            questionnaireResponseId={id}
            focusFieldName={fieldName}
          />
        )}
      />

      {/* Redirect is now handled by useEffect above */}
    </Switch>
  );
};

export default AbstractionRouter;
