// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AbstracionRouter renders the loading 1`] = `<Spinner />`;

exports[`AbstracionRouter renders with error message due Questionnaire 1`] = `
<ErrorHandler
  error={[ApolloError: This is a test error]}
/>
`;

exports[`AbstracionRouter renders with error message due Response 1`] = `
<ErrorHandler
  error={[ApolloError: This is a test error]}
/>
`;

exports[`AbstracionRouter renders without error 1`] = `null`;
