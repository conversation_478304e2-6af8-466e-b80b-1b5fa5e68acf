import TestRenderer from "react-test-renderer";
import { createStore } from "redux";
import { MockedProvider } from "@apollo/client/testing";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import queryResponse from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import queryQuestionnaire from "modules/questionnaire/components/ApolloMain/QuestionnaireQuery/query";
import { initialState } from "modules/questionnaire/reducers";
import wait from "waait";
import { assoc, pipe } from "ramda";
import AbstractionRouter from "..";
import localeData from "base/locales/data";
import { IntlProvider } from "react-intl";

jest.mock("@q-centrix/q-components-react", () => ({ Spinner: "Spinner" }));
jest.mock("../../../Main", () => "Main");
jest.mock("shared/components/ErrorHandler", () => "ErrorHandler");

const answerGroups = [{ id: "1", parentId: null, groupId: "1" }];

const unsuccessfulResponseRequest = {
  request: {
    query: queryResponse,
    variables: { id: 2 }
  },
  error: new Error("This is a test error")
};

const successfulResponseRequest = {
  request: {
    query: queryResponse,
    variables: {
      id: 1
    }
  },
  result: {
    data: {
      questionnaireResponse: {
        id: "1",
        questionnaireId: "1",
        visit: {
          id: 1,
          number: 1,
          admittedAt: 123123,
          arrivedAt: 123,
          dischargedAt: 1231,
          __typename: "Visit"
        },
        visibleQuestions: [],
        patient: null,
        responseCase: null,
        questionnaireStatus: null,
        answers: [],
        answerGroups: [
          { id: "1", parentId: null, groupId: "1", __typename: "AnswerGroup" }
        ],
        validationReport: null,
        writeLocked: false,
        __typename: "QuestionnaireResponse"
      }
    }
  }
};

const successfulResponseRequestInvalidQuestionnaire = {
  request: {
    query: queryResponse,
    variables: {
      id: 3
    }
  },
  result: {
    data: {
      questionnaireResponse: {
        id: "3",
        questionnaireId: "2",
        visit: {
          id: 1,
          number: 1,
          admittedAt: 123123,
          arrivedAt: 123,
          dischargedAt: 1231,
          __typename: "Visit"
        },
        visibleQuestions: [],
        patient: null,
        responseCase: null,
        questionnaireStatus: null,
        answers: [],
        answerGroups: [],
        validationReport: null,
        writeLocked: false,
        __typename: "QuestionnaireResponse"
      }
    }
  }
};

const successfulQuestionnaireRequest = {
  request: {
    query: queryQuestionnaire,
    variables: {
      id: 1,
      visitId: 1
    }
  },
  result: {
    data: {
      questionnaire: {
        id: "1",
        name: "Test Questionnaire",
        nodes: [],
        displayLogics: [],
        groups: [],
        questions: [],
        arrivalDateQuestionId: "7",
        dischargeDateQuestionId: "8",
        validationOptions: ["State"],
        questionnaireStatuses: [{ code: 0, text: "Not Submitted" }],
        helpInformation: null,
        caseDetails: {},
        // eslint-disable-next-line camelcase
        regulatory_clinical: false,
        // eslint-disable-next-line camelcase
        regulatory_administrative: false,
        persistentData: null,
        __typename: "Questionnaire"
      }
    }
  }
};

const unsuccessfulQuestionnaireRequest = {
  request: {
    query: queryQuestionnaire,
    variables: { id: 2, visitId: 1 }
  },
  error: new Error("This is a test error")
};

const mocks = [
  successfulResponseRequest,
  unsuccessfulResponseRequest,
  successfulQuestionnaireRequest,
  successfulResponseRequestInvalidQuestionnaire,
  unsuccessfulQuestionnaireRequest
];
const state = pipe(
  assoc("answerGroups", answerGroups),
  assoc("sections", [{ answerGroups }])
)(initialState);
const store = createStore(() => ({
  app: {
    questionnaire: state
  }
}));

const mockedQueryComponent = ({ id }) => {
  const messages = localeData.en;

  return TestRenderer.create(
    <Provider store={store}>
      <IntlProvider locale="en" messages={messages}>
        <MockedProvider mocks={mocks}>
          <MemoryRouter>
            <AbstractionRouter id={id} />
          </MemoryRouter>
        </MockedProvider>
      </IntlProvider>
    </Provider>
  );
};
const { act } = TestRenderer;

describe("AbstracionRouter", () => {
  test("renders without error", async () => {
    const component = mockedQueryComponent({ id: 1, visitId: 1 });

    await act(() => wait(500)); // wait for response

    expect(component).toMatchSnapshot();
  });

  test("renders the loading", () => {
    const component = mockedQueryComponent({ id: 1 });

    expect(component).toMatchSnapshot();
  });

  test("renders with error message due Response", async () => {
    const component = mockedQueryComponent({ id: 2 });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("renders with error message due Questionnaire", async () => {
    const component = mockedQueryComponent({ id: 3 });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
