import { useEffect, useMemo } from "react";
import { useMutation, useQuery } from "@apollo/client";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  __,
  always,
  defaultTo,
  head,
  ifElse,
  includes,
  pipe,
  propOr,
  path,
  equals,
  identity
} from "ramda";
import questionnaireResponseQuery from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import questionnaireQuery from "modules/questionnaire/components/ApolloMain/QuestionnaireQuery/query";
import {
  setVisibleQuestions,
  setValidationReport as setQuestionValidationReport,
  setWriteLocked,
  setResponseQuery,
  setResponseObjectName
} from "modules/question";
import {
  setPersistentQuestions,
  setSections,
  setAnswersWithQuestions,
  setAnswerGroups,
  setQuestionnaire,
  setQuestionnaireDisplayLogics,
  setPatient,
  setVisit,
  setRegulatoryAdminQuestionnaireResponseId,
  setCase,
  setValidationReport,
  setQuestionnaireStatus,
  setQuestionnaireQuestions
} from "modules/questionnaire/actions";
import { setCurrentQuestionnaireResponseIndex } from "modules/validationReport/actions";
import { pushFlashMessage } from "modules/app/actions";
import { fillAnswerGroupTree } from "modules/questionnaire/services/answerGroups";
import Questionnaire from "modules/questionnaire/selectors";
import Abstraction from "modules/abstraction/redux/selectors";
import { getSectionAGId } from "modules/questionnaire/services/sections";
import { isNullOrEmpty } from "utils/fp";
import QuestionMutation from "modules/question/components/Question/mutations";
import {
  setAbstractionAnswerGroups,
  setAbstractionQuestionnaire,
  setErrors
} from "modules/abstraction/redux/actions";

const getCustomExtensionForWrongFacility = ifElse(
  pipe(
    path(["networkError", "result", "errors", 0, "message"]),
    equals("The questionnaire response is not present under this facility")
  ),
  always({
    extensions: "errors.404.facility"
  }),
  identity
);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({ id }) => {
  const sections = useSelector(state => Questionnaire.getSections(state));
  const firstSectionId = ifElse(
    isNullOrEmpty,
    always(null),
    pipe(head, getSectionAGId(__, 0))
  )(sections);
  const [updateUserLastModifiedMutate] = useMutation(
    QuestionMutation.updateUserLastModified
  );
  const updateUserLastModifiedTimer = useSelector(state =>
    Abstraction.getUpdateUserLastModifiedTimer(state)
  );
  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );

  const location = useLocation();
  const querySearchParams = new URLSearchParams(location.search);
  const clinicalQrId = querySearchParams.get("clinicalQrId");

  const isOncology = pipe(
    defaultTo({}),
    propOr("", "name"),
    includes("Oncology")
  )(questionnaire);

  useEffect(() => {
    const handleUnload = () => {
      // Perform actions before the component unloads
      if (isOncology && updateUserLastModifiedTimer) {
        updateUserLastModifiedMutate({
          variables: {
            questionnaireResponseId: Number(id)
          }
        });
      }
    };

    window.addEventListener("beforeunload", handleUnload);

    return () => {
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, [
    id,
    isOncology,
    updateUserLastModifiedTimer,
    updateUserLastModifiedMutate
  ]);

  return {
    firstSectionId,
    clinicalQrId
  };
};

// eslint-disable-next-line max-statements
export const useQuestionnaireResponse = id => {
  const {
    data = { questionnaireResponse: {} },
    error,
    loading
  } = useQuery(questionnaireResponseQuery, {
    variables: { id: Number(id) }
  });

  const errorMessage = error ? getCustomExtensionForWrongFacility(error) : null;

  const dispatch = useDispatch();
  const {
    answerGroups = [],
    patient = {},
    questionniareResponseId = id,
    questionnaireStatus = {},
    responseCase = {},
    visibleQuestions = [],
    visit = {},
    writeLocked = false,
    regulatoryAdminQuestionnaireResponseId
  } = data.questionnaireResponse;

  const updatedAnswerGroups = useMemo(
    () => (!loading && answerGroups ? fillAnswerGroupTree(answerGroups) : []),
    [loading, answerGroups]
  );

  useEffect(() => {
    dispatch(setResponseQuery(questionnaireResponseQuery));
    dispatch(setResponseObjectName("questionnaireResponse"));
  }, []);
  useEffect(() => {
    if (!loading) {
      dispatch(setAnswerGroups(fillAnswerGroupTree(answerGroups)));
      dispatch(setAbstractionAnswerGroups(fillAnswerGroupTree(answerGroups)));
    }
  }, [answerGroups, loading, dispatch]);
  useEffect(() => {
    if (!loading) dispatch(setPatient(patient));
  }, [patient, loading, dispatch]);
  useEffect(() => {
    if (!loading) dispatch(setVisit(visit));
  }, [visit, loading, dispatch]);
  useEffect(() => {
    if (!loading)
      dispatch(
        setRegulatoryAdminQuestionnaireResponseId(
          regulatoryAdminQuestionnaireResponseId
        )
      );
  }, [regulatoryAdminQuestionnaireResponseId, loading, dispatch]);

  useEffect(() => {
    if (!loading) dispatch(setCase(responseCase));
  }, [responseCase, loading, dispatch]);
  useEffect(() => {
    dispatch(setWriteLocked(writeLocked));
    if (writeLocked && !loading)
      dispatch(pushFlashMessage("Questionnaire Response is Locked"));
  }, [writeLocked, loading, dispatch]);
  useEffect(() => {
    if (!loading) dispatch(setVisibleQuestions(visibleQuestions));
  }, [visibleQuestions, loading, dispatch]);
  useEffect(() => {
    if (!loading) {
      dispatch(setQuestionnaireStatus(questionnaireStatus));
    }
  }, [questionnaireStatus, loading, dispatch]);
  useEffect(() => {
    if (!loading)
      dispatch(setCurrentQuestionnaireResponseIndex(questionniareResponseId));
  }, [questionniareResponseId, loading, dispatch]);

  return {
    loadingQuestionnaireResponse: loading,
    errorQuestionnaireResponse: errorMessage,
    questionnaireResponse: data.questionnaireResponse,
    answerGroups: updatedAnswerGroups
  };
};

// eslint-disable-next-line max-statements
export const useQuestionnaire = (
  questionnaireResponse,
  errorQuestionnaireResponse,
  loadingQuestionnaireResponse,
  answerGroups
  // eslint-disable-next-line max-params
) => {
  const questionnaireId = propOr(0, "questionnaireId", questionnaireResponse);
  const dispatch = useDispatch();
  const {
    answers = [],
    visit = {},
    validationReport = {}
  } = questionnaireResponse;
  const visitId = visit.id;
  const {
    data = { questionnaire: {} },
    error,
    loading
  } = useQuery(questionnaireQuery, {
    variables: { id: Number(questionnaireId), visitId: Number(visitId) },
    skip: loadingQuestionnaireResponse || errorQuestionnaireResponse
  });

  const updateQuestionnaireAndAnswerGroups = useMemo(
    () => !isNullOrEmpty(answerGroups) && !isNullOrEmpty(data.questionnaire),
    [answerGroups, data.questionnaire]
  );

  useEffect(() => {
    if (!isNullOrEmpty(data.questionnaire)) {
      dispatch(setAbstractionQuestionnaire(data.questionnaire, visit));
      dispatch(setQuestionnaire(data.questionnaire));
      dispatch(setQuestionnaireQuestions(visit));
    }
  }, [data.questionnaire, visit]);

  useEffect(() => {
    if (updateQuestionnaireAndAnswerGroups) {
      dispatch(setQuestionnaireDisplayLogics(answerGroups));
      dispatch(setAbstractionAnswerGroups(answerGroups));
      dispatch(setAnswerGroups(answerGroups));
      dispatch(setSections());
      dispatch(setPersistentQuestions());
    }
  }, [
    data.questionnaire,
    visit,
    answerGroups,
    updateQuestionnaireAndAnswerGroups
  ]);

  useEffect(() => {
    if (updateQuestionnaireAndAnswerGroups) {
      dispatch(setAnswersWithQuestions(answerGroups, answers));
    }
  }, [
    data.questionnaire,
    visit,
    answerGroups,
    answers,
    updateQuestionnaireAndAnswerGroups
  ]);

  useEffect(() => {
    if (!loading && !isNullOrEmpty(data.questionnaire)) {
      dispatch(setErrors(validationReport));
      dispatch(setValidationReport(validationReport));
      dispatch(setQuestionValidationReport(validationReport));
    }
  }, [validationReport, data.questionnaire, loading]);

  return {
    loadingQuestionnaire: loading,
    errorQuestionnaire: error,
    questionnaire: data.questionnaire
  };
};
