import { ApolloProvider } from "@apollo/client";
import { apolloClient } from "../../../../base";
import AbstractionRouter from "./AbstractionRouter";
// import MockedOncologyProvider from "./MockedOncologyProvider";

export function Main(props) {
  const {
    match: {
      params: { id }
    }
  } = props;

  return (
    // <MockedOncologyProvider>
    <ApolloProvider client={apolloClient("/api/registries/graphql")}>
      <AbstractionRouter id={id} />
    </ApolloProvider>
    // </MockedOncologyProvider>
  );
}

export default Main;
