// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Main renders Main component 1`] = `
<ApolloProvider
  client={
    ApolloClient {
      "cache": InMemoryCache {
        "addTypename": true,
        "addTypenameTransform": DocumentTransform {
          "cached": true,
          "performWork": [Function],
          "resultCache": WeakSet {},
          "transform": [Function],
        },
        "assumeImmutableResults": true,
        "config": Object {
          "addTypename": true,
          "canonizeResults": false,
          "dataIdFromObject": [Function],
          "possibleTypes": Object {
            "BaseAnswer": Array [
              "Answer",
              "AnswerWithWarnings",
              "AnswerWithErrors",
            ],
            "DisplayLogic": Array [
              "BooleanSequenceDisplayLogic",
              "CounterTableDisplayLogic",
              "EnumerableTableDisplayLogic",
              "OtherDisplayLogic",
              "QuestionListDisplayLogic",
              "QuestionFillButtonDisplayLogic",
              "SectionDisplayLogic",
              "SubheaderDisplayLogic",
              "TableDisplayLogic",
              "ThreeColumnDisplayLogic",
              "TwoColumnDisplayLogic",
              "QuestionCopyButtonDisplayLogic",
            ],
            "Question": Array [
              "CalculatedQuestion",
              "CounterQuestion",
              "DateOrEnumerableQuestion",
              "DependentEnumerableQuestion",
              "DependentExternalEnumerableSearchableQuestion",
              "DependentFilteredEnumerableSearchableQuestion",
              "DateQuestion",
              "DateTimeOrEnumerableQuestion",
              "EnumerableQuestion",
              "EnumerableCollectionQuestion",
              "EnumerableCollectionLongListQuestion",
              "EnumerableCollectionSearchableQuestion",
              "EnumerableOrEnumerableCollectionQuestion",
              "EnumerableOrOpenQuestion",
              "EnumerableSearchableQuestion",
              "EnumerableCollectionSearchableFavoritableQuestion",
              "EnumerableSearchableFavoritableQuestion",
              "GeneratedQuestion",
              "DateQuestion",
              "DateOrEnumerableQuestion",
              "DateTimeOrEnumerableQuestion",
              "DateTimeQuestion",
              "NumberQuestion",
              "NumberOrEnumerableQuestion",
              "TimeStringOrEnumerableQuestion",
              "NumberSearchableQuestion",
              "NumberWithEnumerableQuestion",
              "NumberWithUnitQuestion",
              "OpenQuestion",
              "OtherQuestion",
              "RepeatableEnumerableQuestion",
              "TextAreaQuestion",
              "TimeStringQuestion",
            ],
            "RegistryConfigurationTableData": Array [
              "MaintenanceData",
              "StateData",
              "CoCData",
              "ExtractData",
              "ConversionData",
              "ImportData",
            ],
            "Rule": Array [
              "EnumerableEqualityRule",
              "OtherRule",
              "UnconditionalRule",
              "EnumerableInclusionRule",
            ],
          },
          "resultCaching": true,
        },
        "data": Root {
          "canRead": [Function],
          "data": Object {},
          "getFieldValue": [Function],
          "group": CacheGroup {
            "caching": true,
            "d": [Function],
            "keyMaker": Trie {
              "makeData": [Function],
              "weakness": true,
            },
            "parent": null,
          },
          "policies": Policies {
            "cache": [Circular],
            "config": Object {
              "cache": [Circular],
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "BaseAnswer": Array [
                  "Answer",
                  "AnswerWithWarnings",
                  "AnswerWithErrors",
                ],
                "DisplayLogic": Array [
                  "BooleanSequenceDisplayLogic",
                  "CounterTableDisplayLogic",
                  "EnumerableTableDisplayLogic",
                  "OtherDisplayLogic",
                  "QuestionListDisplayLogic",
                  "QuestionFillButtonDisplayLogic",
                  "SectionDisplayLogic",
                  "SubheaderDisplayLogic",
                  "TableDisplayLogic",
                  "ThreeColumnDisplayLogic",
                  "TwoColumnDisplayLogic",
                  "QuestionCopyButtonDisplayLogic",
                ],
                "Question": Array [
                  "CalculatedQuestion",
                  "CounterQuestion",
                  "DateOrEnumerableQuestion",
                  "DependentEnumerableQuestion",
                  "DependentExternalEnumerableSearchableQuestion",
                  "DependentFilteredEnumerableSearchableQuestion",
                  "DateQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "EnumerableQuestion",
                  "EnumerableCollectionQuestion",
                  "EnumerableCollectionLongListQuestion",
                  "EnumerableCollectionSearchableQuestion",
                  "EnumerableOrEnumerableCollectionQuestion",
                  "EnumerableOrOpenQuestion",
                  "EnumerableSearchableQuestion",
                  "EnumerableCollectionSearchableFavoritableQuestion",
                  "EnumerableSearchableFavoritableQuestion",
                  "GeneratedQuestion",
                  "DateQuestion",
                  "DateOrEnumerableQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "DateTimeQuestion",
                  "NumberQuestion",
                  "NumberOrEnumerableQuestion",
                  "TimeStringOrEnumerableQuestion",
                  "NumberSearchableQuestion",
                  "NumberWithEnumerableQuestion",
                  "NumberWithUnitQuestion",
                  "OpenQuestion",
                  "OtherQuestion",
                  "RepeatableEnumerableQuestion",
                  "TextAreaQuestion",
                  "TimeStringQuestion",
                ],
                "RegistryConfigurationTableData": Array [
                  "MaintenanceData",
                  "StateData",
                  "CoCData",
                  "ExtractData",
                  "ConversionData",
                  "ImportData",
                ],
                "Rule": Array [
                  "EnumerableEqualityRule",
                  "OtherRule",
                  "UnconditionalRule",
                  "EnumerableInclusionRule",
                ],
              },
              "typePolicies": undefined,
            },
            "fuzzySubtypes": Map {},
            "rootIdsByTypename": Object {
              "Mutation": "ROOT_MUTATION",
              "Query": "ROOT_QUERY",
              "Subscription": "ROOT_SUBSCRIPTION",
            },
            "rootTypenamesById": Object {
              "ROOT_MUTATION": "Mutation",
              "ROOT_QUERY": "Query",
              "ROOT_SUBSCRIPTION": "Subscription",
            },
            "supertypeMap": Map {
              "DisplayLogic" => Set {},
              "BooleanSequenceDisplayLogic" => Set {
                "DisplayLogic",
              },
              "CounterTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "EnumerableTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "OtherDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionListDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionFillButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SectionDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SubheaderDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "ThreeColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TwoColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionCopyButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "Question" => Set {},
              "CalculatedQuestion" => Set {
                "Question",
              },
              "CounterQuestion" => Set {
                "Question",
              },
              "DateOrEnumerableQuestion" => Set {
                "Question",
              },
              "DependentEnumerableQuestion" => Set {
                "Question",
              },
              "DependentExternalEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DependentFilteredEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DateQuestion" => Set {
                "Question",
              },
              "DateTimeOrEnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionLongListQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableOrEnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableOrOpenQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "GeneratedQuestion" => Set {
                "Question",
              },
              "DateTimeQuestion" => Set {
                "Question",
              },
              "NumberQuestion" => Set {
                "Question",
              },
              "NumberOrEnumerableQuestion" => Set {
                "Question",
              },
              "TimeStringOrEnumerableQuestion" => Set {
                "Question",
              },
              "NumberSearchableQuestion" => Set {
                "Question",
              },
              "NumberWithEnumerableQuestion" => Set {
                "Question",
              },
              "NumberWithUnitQuestion" => Set {
                "Question",
              },
              "OpenQuestion" => Set {
                "Question",
              },
              "OtherQuestion" => Set {
                "Question",
              },
              "RepeatableEnumerableQuestion" => Set {
                "Question",
              },
              "TextAreaQuestion" => Set {
                "Question",
              },
              "TimeStringQuestion" => Set {
                "Question",
              },
              "Rule" => Set {},
              "EnumerableEqualityRule" => Set {
                "Rule",
              },
              "OtherRule" => Set {
                "Rule",
              },
              "UnconditionalRule" => Set {
                "Rule",
              },
              "EnumerableInclusionRule" => Set {
                "Rule",
              },
              "BaseAnswer" => Set {},
              "Answer" => Set {
                "BaseAnswer",
              },
              "AnswerWithWarnings" => Set {
                "BaseAnswer",
              },
              "AnswerWithErrors" => Set {
                "BaseAnswer",
              },
              "RegistryConfigurationTableData" => Set {},
              "MaintenanceData" => Set {
                "RegistryConfigurationTableData",
              },
              "StateData" => Set {
                "RegistryConfigurationTableData",
              },
              "CoCData" => Set {
                "RegistryConfigurationTableData",
              },
              "ExtractData" => Set {
                "RegistryConfigurationTableData",
              },
              "ConversionData" => Set {
                "RegistryConfigurationTableData",
              },
              "ImportData" => Set {
                "RegistryConfigurationTableData",
              },
            },
            "toBeAdded": Object {},
            "typePolicies": Object {},
            "usingPossibleTypes": true,
          },
          "refs": Object {},
          "rootIds": Object {},
          "storageTrie": Trie {
            "makeData": [Function],
            "weakness": true,
          },
          "stump": Stump {
            "canRead": [Function],
            "data": Object {},
            "getFieldValue": [Function],
            "group": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
            },
            "id": "EntityStore.Stump",
            "parent": [Circular],
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "refs": Object {},
            "replay": [Function],
            "rootIds": Object {},
            "toReference": [Function],
          },
          "toReference": [Function],
        },
        "getFragmentDoc": [Function],
        "makeVar": [Function],
        "maybeBroadcastWatch": [Function],
        "optimisticData": Stump {
          "canRead": [Function],
          "data": Object {},
          "getFieldValue": [Function],
          "group": CacheGroup {
            "caching": true,
            "d": [Function],
            "keyMaker": Trie {
              "makeData": [Function],
              "weakness": true,
            },
            "parent": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": null,
            },
          },
          "id": "EntityStore.Stump",
          "parent": Root {
            "canRead": [Function],
            "data": Object {},
            "getFieldValue": [Function],
            "group": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": null,
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "refs": Object {},
            "rootIds": Object {},
            "storageTrie": Trie {
              "makeData": [Function],
              "weakness": true,
            },
            "stump": [Circular],
            "toReference": [Function],
          },
          "policies": Policies {
            "cache": [Circular],
            "config": Object {
              "cache": [Circular],
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "BaseAnswer": Array [
                  "Answer",
                  "AnswerWithWarnings",
                  "AnswerWithErrors",
                ],
                "DisplayLogic": Array [
                  "BooleanSequenceDisplayLogic",
                  "CounterTableDisplayLogic",
                  "EnumerableTableDisplayLogic",
                  "OtherDisplayLogic",
                  "QuestionListDisplayLogic",
                  "QuestionFillButtonDisplayLogic",
                  "SectionDisplayLogic",
                  "SubheaderDisplayLogic",
                  "TableDisplayLogic",
                  "ThreeColumnDisplayLogic",
                  "TwoColumnDisplayLogic",
                  "QuestionCopyButtonDisplayLogic",
                ],
                "Question": Array [
                  "CalculatedQuestion",
                  "CounterQuestion",
                  "DateOrEnumerableQuestion",
                  "DependentEnumerableQuestion",
                  "DependentExternalEnumerableSearchableQuestion",
                  "DependentFilteredEnumerableSearchableQuestion",
                  "DateQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "EnumerableQuestion",
                  "EnumerableCollectionQuestion",
                  "EnumerableCollectionLongListQuestion",
                  "EnumerableCollectionSearchableQuestion",
                  "EnumerableOrEnumerableCollectionQuestion",
                  "EnumerableOrOpenQuestion",
                  "EnumerableSearchableQuestion",
                  "EnumerableCollectionSearchableFavoritableQuestion",
                  "EnumerableSearchableFavoritableQuestion",
                  "GeneratedQuestion",
                  "DateQuestion",
                  "DateOrEnumerableQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "DateTimeQuestion",
                  "NumberQuestion",
                  "NumberOrEnumerableQuestion",
                  "TimeStringOrEnumerableQuestion",
                  "NumberSearchableQuestion",
                  "NumberWithEnumerableQuestion",
                  "NumberWithUnitQuestion",
                  "OpenQuestion",
                  "OtherQuestion",
                  "RepeatableEnumerableQuestion",
                  "TextAreaQuestion",
                  "TimeStringQuestion",
                ],
                "RegistryConfigurationTableData": Array [
                  "MaintenanceData",
                  "StateData",
                  "CoCData",
                  "ExtractData",
                  "ConversionData",
                  "ImportData",
                ],
                "Rule": Array [
                  "EnumerableEqualityRule",
                  "OtherRule",
                  "UnconditionalRule",
                  "EnumerableInclusionRule",
                ],
              },
              "typePolicies": undefined,
            },
            "fuzzySubtypes": Map {},
            "rootIdsByTypename": Object {
              "Mutation": "ROOT_MUTATION",
              "Query": "ROOT_QUERY",
              "Subscription": "ROOT_SUBSCRIPTION",
            },
            "rootTypenamesById": Object {
              "ROOT_MUTATION": "Mutation",
              "ROOT_QUERY": "Query",
              "ROOT_SUBSCRIPTION": "Subscription",
            },
            "supertypeMap": Map {
              "DisplayLogic" => Set {},
              "BooleanSequenceDisplayLogic" => Set {
                "DisplayLogic",
              },
              "CounterTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "EnumerableTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "OtherDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionListDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionFillButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SectionDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SubheaderDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "ThreeColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TwoColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionCopyButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "Question" => Set {},
              "CalculatedQuestion" => Set {
                "Question",
              },
              "CounterQuestion" => Set {
                "Question",
              },
              "DateOrEnumerableQuestion" => Set {
                "Question",
              },
              "DependentEnumerableQuestion" => Set {
                "Question",
              },
              "DependentExternalEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DependentFilteredEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DateQuestion" => Set {
                "Question",
              },
              "DateTimeOrEnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionLongListQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableOrEnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableOrOpenQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "GeneratedQuestion" => Set {
                "Question",
              },
              "DateTimeQuestion" => Set {
                "Question",
              },
              "NumberQuestion" => Set {
                "Question",
              },
              "NumberOrEnumerableQuestion" => Set {
                "Question",
              },
              "TimeStringOrEnumerableQuestion" => Set {
                "Question",
              },
              "NumberSearchableQuestion" => Set {
                "Question",
              },
              "NumberWithEnumerableQuestion" => Set {
                "Question",
              },
              "NumberWithUnitQuestion" => Set {
                "Question",
              },
              "OpenQuestion" => Set {
                "Question",
              },
              "OtherQuestion" => Set {
                "Question",
              },
              "RepeatableEnumerableQuestion" => Set {
                "Question",
              },
              "TextAreaQuestion" => Set {
                "Question",
              },
              "TimeStringQuestion" => Set {
                "Question",
              },
              "Rule" => Set {},
              "EnumerableEqualityRule" => Set {
                "Rule",
              },
              "OtherRule" => Set {
                "Rule",
              },
              "UnconditionalRule" => Set {
                "Rule",
              },
              "EnumerableInclusionRule" => Set {
                "Rule",
              },
              "BaseAnswer" => Set {},
              "Answer" => Set {
                "BaseAnswer",
              },
              "AnswerWithWarnings" => Set {
                "BaseAnswer",
              },
              "AnswerWithErrors" => Set {
                "BaseAnswer",
              },
              "RegistryConfigurationTableData" => Set {},
              "MaintenanceData" => Set {
                "RegistryConfigurationTableData",
              },
              "StateData" => Set {
                "RegistryConfigurationTableData",
              },
              "CoCData" => Set {
                "RegistryConfigurationTableData",
              },
              "ExtractData" => Set {
                "RegistryConfigurationTableData",
              },
              "ConversionData" => Set {
                "RegistryConfigurationTableData",
              },
              "ImportData" => Set {
                "RegistryConfigurationTableData",
              },
            },
            "toBeAdded": Object {},
            "typePolicies": Object {},
            "usingPossibleTypes": true,
          },
          "refs": Object {},
          "replay": [Function],
          "rootIds": Object {},
          "toReference": [Function],
        },
        "policies": Policies {
          "cache": [Circular],
          "config": Object {
            "cache": [Circular],
            "dataIdFromObject": [Function],
            "possibleTypes": Object {
              "BaseAnswer": Array [
                "Answer",
                "AnswerWithWarnings",
                "AnswerWithErrors",
              ],
              "DisplayLogic": Array [
                "BooleanSequenceDisplayLogic",
                "CounterTableDisplayLogic",
                "EnumerableTableDisplayLogic",
                "OtherDisplayLogic",
                "QuestionListDisplayLogic",
                "QuestionFillButtonDisplayLogic",
                "SectionDisplayLogic",
                "SubheaderDisplayLogic",
                "TableDisplayLogic",
                "ThreeColumnDisplayLogic",
                "TwoColumnDisplayLogic",
                "QuestionCopyButtonDisplayLogic",
              ],
              "Question": Array [
                "CalculatedQuestion",
                "CounterQuestion",
                "DateOrEnumerableQuestion",
                "DependentEnumerableQuestion",
                "DependentExternalEnumerableSearchableQuestion",
                "DependentFilteredEnumerableSearchableQuestion",
                "DateQuestion",
                "DateTimeOrEnumerableQuestion",
                "EnumerableQuestion",
                "EnumerableCollectionQuestion",
                "EnumerableCollectionLongListQuestion",
                "EnumerableCollectionSearchableQuestion",
                "EnumerableOrEnumerableCollectionQuestion",
                "EnumerableOrOpenQuestion",
                "EnumerableSearchableQuestion",
                "EnumerableCollectionSearchableFavoritableQuestion",
                "EnumerableSearchableFavoritableQuestion",
                "GeneratedQuestion",
                "DateQuestion",
                "DateOrEnumerableQuestion",
                "DateTimeOrEnumerableQuestion",
                "DateTimeQuestion",
                "NumberQuestion",
                "NumberOrEnumerableQuestion",
                "TimeStringOrEnumerableQuestion",
                "NumberSearchableQuestion",
                "NumberWithEnumerableQuestion",
                "NumberWithUnitQuestion",
                "OpenQuestion",
                "OtherQuestion",
                "RepeatableEnumerableQuestion",
                "TextAreaQuestion",
                "TimeStringQuestion",
              ],
              "RegistryConfigurationTableData": Array [
                "MaintenanceData",
                "StateData",
                "CoCData",
                "ExtractData",
                "ConversionData",
                "ImportData",
              ],
              "Rule": Array [
                "EnumerableEqualityRule",
                "OtherRule",
                "UnconditionalRule",
                "EnumerableInclusionRule",
              ],
            },
            "typePolicies": undefined,
          },
          "fuzzySubtypes": Map {},
          "rootIdsByTypename": Object {
            "Mutation": "ROOT_MUTATION",
            "Query": "ROOT_QUERY",
            "Subscription": "ROOT_SUBSCRIPTION",
          },
          "rootTypenamesById": Object {
            "ROOT_MUTATION": "Mutation",
            "ROOT_QUERY": "Query",
            "ROOT_SUBSCRIPTION": "Subscription",
          },
          "supertypeMap": Map {
            "DisplayLogic" => Set {},
            "BooleanSequenceDisplayLogic" => Set {
              "DisplayLogic",
            },
            "CounterTableDisplayLogic" => Set {
              "DisplayLogic",
            },
            "EnumerableTableDisplayLogic" => Set {
              "DisplayLogic",
            },
            "OtherDisplayLogic" => Set {
              "DisplayLogic",
            },
            "QuestionListDisplayLogic" => Set {
              "DisplayLogic",
            },
            "QuestionFillButtonDisplayLogic" => Set {
              "DisplayLogic",
            },
            "SectionDisplayLogic" => Set {
              "DisplayLogic",
            },
            "SubheaderDisplayLogic" => Set {
              "DisplayLogic",
            },
            "TableDisplayLogic" => Set {
              "DisplayLogic",
            },
            "ThreeColumnDisplayLogic" => Set {
              "DisplayLogic",
            },
            "TwoColumnDisplayLogic" => Set {
              "DisplayLogic",
            },
            "QuestionCopyButtonDisplayLogic" => Set {
              "DisplayLogic",
            },
            "Question" => Set {},
            "CalculatedQuestion" => Set {
              "Question",
            },
            "CounterQuestion" => Set {
              "Question",
            },
            "DateOrEnumerableQuestion" => Set {
              "Question",
            },
            "DependentEnumerableQuestion" => Set {
              "Question",
            },
            "DependentExternalEnumerableSearchableQuestion" => Set {
              "Question",
            },
            "DependentFilteredEnumerableSearchableQuestion" => Set {
              "Question",
            },
            "DateQuestion" => Set {
              "Question",
            },
            "DateTimeOrEnumerableQuestion" => Set {
              "Question",
            },
            "EnumerableQuestion" => Set {
              "Question",
            },
            "EnumerableCollectionQuestion" => Set {
              "Question",
            },
            "EnumerableCollectionLongListQuestion" => Set {
              "Question",
            },
            "EnumerableCollectionSearchableQuestion" => Set {
              "Question",
            },
            "EnumerableOrEnumerableCollectionQuestion" => Set {
              "Question",
            },
            "EnumerableOrOpenQuestion" => Set {
              "Question",
            },
            "EnumerableSearchableQuestion" => Set {
              "Question",
            },
            "EnumerableCollectionSearchableFavoritableQuestion" => Set {
              "Question",
            },
            "EnumerableSearchableFavoritableQuestion" => Set {
              "Question",
            },
            "GeneratedQuestion" => Set {
              "Question",
            },
            "DateTimeQuestion" => Set {
              "Question",
            },
            "NumberQuestion" => Set {
              "Question",
            },
            "NumberOrEnumerableQuestion" => Set {
              "Question",
            },
            "TimeStringOrEnumerableQuestion" => Set {
              "Question",
            },
            "NumberSearchableQuestion" => Set {
              "Question",
            },
            "NumberWithEnumerableQuestion" => Set {
              "Question",
            },
            "NumberWithUnitQuestion" => Set {
              "Question",
            },
            "OpenQuestion" => Set {
              "Question",
            },
            "OtherQuestion" => Set {
              "Question",
            },
            "RepeatableEnumerableQuestion" => Set {
              "Question",
            },
            "TextAreaQuestion" => Set {
              "Question",
            },
            "TimeStringQuestion" => Set {
              "Question",
            },
            "Rule" => Set {},
            "EnumerableEqualityRule" => Set {
              "Rule",
            },
            "OtherRule" => Set {
              "Rule",
            },
            "UnconditionalRule" => Set {
              "Rule",
            },
            "EnumerableInclusionRule" => Set {
              "Rule",
            },
            "BaseAnswer" => Set {},
            "Answer" => Set {
              "BaseAnswer",
            },
            "AnswerWithWarnings" => Set {
              "BaseAnswer",
            },
            "AnswerWithErrors" => Set {
              "BaseAnswer",
            },
            "RegistryConfigurationTableData" => Set {},
            "MaintenanceData" => Set {
              "RegistryConfigurationTableData",
            },
            "StateData" => Set {
              "RegistryConfigurationTableData",
            },
            "CoCData" => Set {
              "RegistryConfigurationTableData",
            },
            "ExtractData" => Set {
              "RegistryConfigurationTableData",
            },
            "ConversionData" => Set {
              "RegistryConfigurationTableData",
            },
            "ImportData" => Set {
              "RegistryConfigurationTableData",
            },
          },
          "toBeAdded": Object {},
          "typePolicies": Object {},
          "usingPossibleTypes": true,
        },
        "storeReader": StoreReader {
          "canon": ObjectCanon {
            "empty": Object {},
            "keysByJSON": Map {
              "[]" => Object {
                "json": "[]",
                "sorted": Array [],
              },
            },
            "known": WeakSet {},
            "passes": WeakMap {},
            "pool": Trie {
              "data": Object {
                "keys": Object {
                  "json": "[]",
                  "sorted": Array [],
                },
              },
              "makeData": [Function],
              "weak": WeakMap {},
              "weakness": true,
            },
          },
          "config": Object {
            "addTypename": true,
            "cache": [Circular],
            "canonizeResults": false,
          },
          "executeSelectionSet": [Function],
          "executeSubSelectedArray": [Function],
          "knownResults": WeakMap {},
        },
        "storeWriter": StoreWriter {
          "cache": [Circular],
          "fragments": undefined,
          "reader": StoreReader {
            "canon": ObjectCanon {
              "empty": Object {},
              "keysByJSON": Map {
                "[]" => Object {
                  "json": "[]",
                  "sorted": Array [],
                },
              },
              "known": WeakSet {},
              "passes": WeakMap {},
              "pool": Trie {
                "data": Object {
                  "keys": Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "makeData": [Function],
                "weak": WeakMap {},
                "weakness": true,
              },
            },
            "config": Object {
              "addTypename": true,
              "cache": [Circular],
              "canonizeResults": false,
            },
            "executeSelectionSet": [Function],
            "executeSubSelectedArray": [Function],
            "knownResults": WeakMap {},
          },
        },
        "txCount": 0,
        "watches": Set {},
      },
      "clearStoreCallbacks": Array [],
      "defaultOptions": Object {},
      "disableNetworkFetches": false,
      "link": ApolloLink {
        "left": ApolloLink {
          "request": [Function],
        },
        "request": [Function],
        "right": ApolloLink {
          "left": ApolloLink {
            "request": [Function],
          },
          "request": [Function],
          "right": ApolloLink {
            "request": [Function],
          },
        },
      },
      "localState": LocalState {
        "cache": InMemoryCache {
          "addTypename": true,
          "addTypenameTransform": DocumentTransform {
            "cached": true,
            "performWork": [Function],
            "resultCache": WeakSet {},
            "transform": [Function],
          },
          "assumeImmutableResults": true,
          "config": Object {
            "addTypename": true,
            "canonizeResults": false,
            "dataIdFromObject": [Function],
            "possibleTypes": Object {
              "BaseAnswer": Array [
                "Answer",
                "AnswerWithWarnings",
                "AnswerWithErrors",
              ],
              "DisplayLogic": Array [
                "BooleanSequenceDisplayLogic",
                "CounterTableDisplayLogic",
                "EnumerableTableDisplayLogic",
                "OtherDisplayLogic",
                "QuestionListDisplayLogic",
                "QuestionFillButtonDisplayLogic",
                "SectionDisplayLogic",
                "SubheaderDisplayLogic",
                "TableDisplayLogic",
                "ThreeColumnDisplayLogic",
                "TwoColumnDisplayLogic",
                "QuestionCopyButtonDisplayLogic",
              ],
              "Question": Array [
                "CalculatedQuestion",
                "CounterQuestion",
                "DateOrEnumerableQuestion",
                "DependentEnumerableQuestion",
                "DependentExternalEnumerableSearchableQuestion",
                "DependentFilteredEnumerableSearchableQuestion",
                "DateQuestion",
                "DateTimeOrEnumerableQuestion",
                "EnumerableQuestion",
                "EnumerableCollectionQuestion",
                "EnumerableCollectionLongListQuestion",
                "EnumerableCollectionSearchableQuestion",
                "EnumerableOrEnumerableCollectionQuestion",
                "EnumerableOrOpenQuestion",
                "EnumerableSearchableQuestion",
                "EnumerableCollectionSearchableFavoritableQuestion",
                "EnumerableSearchableFavoritableQuestion",
                "GeneratedQuestion",
                "DateQuestion",
                "DateOrEnumerableQuestion",
                "DateTimeOrEnumerableQuestion",
                "DateTimeQuestion",
                "NumberQuestion",
                "NumberOrEnumerableQuestion",
                "TimeStringOrEnumerableQuestion",
                "NumberSearchableQuestion",
                "NumberWithEnumerableQuestion",
                "NumberWithUnitQuestion",
                "OpenQuestion",
                "OtherQuestion",
                "RepeatableEnumerableQuestion",
                "TextAreaQuestion",
                "TimeStringQuestion",
              ],
              "RegistryConfigurationTableData": Array [
                "MaintenanceData",
                "StateData",
                "CoCData",
                "ExtractData",
                "ConversionData",
                "ImportData",
              ],
              "Rule": Array [
                "EnumerableEqualityRule",
                "OtherRule",
                "UnconditionalRule",
                "EnumerableInclusionRule",
              ],
            },
            "resultCaching": true,
          },
          "data": Root {
            "canRead": [Function],
            "data": Object {},
            "getFieldValue": [Function],
            "group": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": null,
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "refs": Object {},
            "rootIds": Object {},
            "storageTrie": Trie {
              "makeData": [Function],
              "weakness": true,
            },
            "stump": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": [Circular],
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "BaseAnswer": Array [
                      "Answer",
                      "AnswerWithWarnings",
                      "AnswerWithErrors",
                    ],
                    "DisplayLogic": Array [
                      "BooleanSequenceDisplayLogic",
                      "CounterTableDisplayLogic",
                      "EnumerableTableDisplayLogic",
                      "OtherDisplayLogic",
                      "QuestionListDisplayLogic",
                      "QuestionFillButtonDisplayLogic",
                      "SectionDisplayLogic",
                      "SubheaderDisplayLogic",
                      "TableDisplayLogic",
                      "ThreeColumnDisplayLogic",
                      "TwoColumnDisplayLogic",
                      "QuestionCopyButtonDisplayLogic",
                    ],
                    "Question": Array [
                      "CalculatedQuestion",
                      "CounterQuestion",
                      "DateOrEnumerableQuestion",
                      "DependentEnumerableQuestion",
                      "DependentExternalEnumerableSearchableQuestion",
                      "DependentFilteredEnumerableSearchableQuestion",
                      "DateQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "EnumerableQuestion",
                      "EnumerableCollectionQuestion",
                      "EnumerableCollectionLongListQuestion",
                      "EnumerableCollectionSearchableQuestion",
                      "EnumerableOrEnumerableCollectionQuestion",
                      "EnumerableOrOpenQuestion",
                      "EnumerableSearchableQuestion",
                      "EnumerableCollectionSearchableFavoritableQuestion",
                      "EnumerableSearchableFavoritableQuestion",
                      "GeneratedQuestion",
                      "DateQuestion",
                      "DateOrEnumerableQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "DateTimeQuestion",
                      "NumberQuestion",
                      "NumberOrEnumerableQuestion",
                      "TimeStringOrEnumerableQuestion",
                      "NumberSearchableQuestion",
                      "NumberWithEnumerableQuestion",
                      "NumberWithUnitQuestion",
                      "OpenQuestion",
                      "OtherQuestion",
                      "RepeatableEnumerableQuestion",
                      "TextAreaQuestion",
                      "TimeStringQuestion",
                    ],
                    "RegistryConfigurationTableData": Array [
                      "MaintenanceData",
                      "StateData",
                      "CoCData",
                      "ExtractData",
                      "ConversionData",
                      "ImportData",
                    ],
                    "Rule": Array [
                      "EnumerableEqualityRule",
                      "OtherRule",
                      "UnconditionalRule",
                      "EnumerableInclusionRule",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "DisplayLogic" => Set {},
                  "BooleanSequenceDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "CounterTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "EnumerableTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "OtherDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionListDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionFillButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SectionDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SubheaderDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "ThreeColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TwoColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionCopyButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "Question" => Set {},
                  "CalculatedQuestion" => Set {
                    "Question",
                  },
                  "CounterQuestion" => Set {
                    "Question",
                  },
                  "DateOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentExternalEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DependentFilteredEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DateQuestion" => Set {
                    "Question",
                  },
                  "DateTimeOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionLongListQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrEnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrOpenQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "GeneratedQuestion" => Set {
                    "Question",
                  },
                  "DateTimeQuestion" => Set {
                    "Question",
                  },
                  "NumberQuestion" => Set {
                    "Question",
                  },
                  "NumberOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TimeStringOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberSearchableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithUnitQuestion" => Set {
                    "Question",
                  },
                  "OpenQuestion" => Set {
                    "Question",
                  },
                  "OtherQuestion" => Set {
                    "Question",
                  },
                  "RepeatableEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TextAreaQuestion" => Set {
                    "Question",
                  },
                  "TimeStringQuestion" => Set {
                    "Question",
                  },
                  "Rule" => Set {},
                  "EnumerableEqualityRule" => Set {
                    "Rule",
                  },
                  "OtherRule" => Set {
                    "Rule",
                  },
                  "UnconditionalRule" => Set {
                    "Rule",
                  },
                  "EnumerableInclusionRule" => Set {
                    "Rule",
                  },
                  "BaseAnswer" => Set {},
                  "Answer" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithWarnings" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithErrors" => Set {
                    "BaseAnswer",
                  },
                  "RegistryConfigurationTableData" => Set {},
                  "MaintenanceData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "StateData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "CoCData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ExtractData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ConversionData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ImportData" => Set {
                    "RegistryConfigurationTableData",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "toReference": [Function],
          },
          "getFragmentDoc": [Function],
          "makeVar": [Function],
          "maybeBroadcastWatch": [Function],
          "optimisticData": Stump {
            "canRead": [Function],
            "data": Object {},
            "getFieldValue": [Function],
            "group": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
            },
            "id": "EntityStore.Stump",
            "parent": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "BaseAnswer": Array [
                      "Answer",
                      "AnswerWithWarnings",
                      "AnswerWithErrors",
                    ],
                    "DisplayLogic": Array [
                      "BooleanSequenceDisplayLogic",
                      "CounterTableDisplayLogic",
                      "EnumerableTableDisplayLogic",
                      "OtherDisplayLogic",
                      "QuestionListDisplayLogic",
                      "QuestionFillButtonDisplayLogic",
                      "SectionDisplayLogic",
                      "SubheaderDisplayLogic",
                      "TableDisplayLogic",
                      "ThreeColumnDisplayLogic",
                      "TwoColumnDisplayLogic",
                      "QuestionCopyButtonDisplayLogic",
                    ],
                    "Question": Array [
                      "CalculatedQuestion",
                      "CounterQuestion",
                      "DateOrEnumerableQuestion",
                      "DependentEnumerableQuestion",
                      "DependentExternalEnumerableSearchableQuestion",
                      "DependentFilteredEnumerableSearchableQuestion",
                      "DateQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "EnumerableQuestion",
                      "EnumerableCollectionQuestion",
                      "EnumerableCollectionLongListQuestion",
                      "EnumerableCollectionSearchableQuestion",
                      "EnumerableOrEnumerableCollectionQuestion",
                      "EnumerableOrOpenQuestion",
                      "EnumerableSearchableQuestion",
                      "EnumerableCollectionSearchableFavoritableQuestion",
                      "EnumerableSearchableFavoritableQuestion",
                      "GeneratedQuestion",
                      "DateQuestion",
                      "DateOrEnumerableQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "DateTimeQuestion",
                      "NumberQuestion",
                      "NumberOrEnumerableQuestion",
                      "TimeStringOrEnumerableQuestion",
                      "NumberSearchableQuestion",
                      "NumberWithEnumerableQuestion",
                      "NumberWithUnitQuestion",
                      "OpenQuestion",
                      "OtherQuestion",
                      "RepeatableEnumerableQuestion",
                      "TextAreaQuestion",
                      "TimeStringQuestion",
                    ],
                    "RegistryConfigurationTableData": Array [
                      "MaintenanceData",
                      "StateData",
                      "CoCData",
                      "ExtractData",
                      "ConversionData",
                      "ImportData",
                    ],
                    "Rule": Array [
                      "EnumerableEqualityRule",
                      "OtherRule",
                      "UnconditionalRule",
                      "EnumerableInclusionRule",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "DisplayLogic" => Set {},
                  "BooleanSequenceDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "CounterTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "EnumerableTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "OtherDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionListDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionFillButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SectionDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SubheaderDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "ThreeColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TwoColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionCopyButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "Question" => Set {},
                  "CalculatedQuestion" => Set {
                    "Question",
                  },
                  "CounterQuestion" => Set {
                    "Question",
                  },
                  "DateOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentExternalEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DependentFilteredEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DateQuestion" => Set {
                    "Question",
                  },
                  "DateTimeOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionLongListQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrEnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrOpenQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "GeneratedQuestion" => Set {
                    "Question",
                  },
                  "DateTimeQuestion" => Set {
                    "Question",
                  },
                  "NumberQuestion" => Set {
                    "Question",
                  },
                  "NumberOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TimeStringOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberSearchableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithUnitQuestion" => Set {
                    "Question",
                  },
                  "OpenQuestion" => Set {
                    "Question",
                  },
                  "OtherQuestion" => Set {
                    "Question",
                  },
                  "RepeatableEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TextAreaQuestion" => Set {
                    "Question",
                  },
                  "TimeStringQuestion" => Set {
                    "Question",
                  },
                  "Rule" => Set {},
                  "EnumerableEqualityRule" => Set {
                    "Rule",
                  },
                  "OtherRule" => Set {
                    "Rule",
                  },
                  "UnconditionalRule" => Set {
                    "Rule",
                  },
                  "EnumerableInclusionRule" => Set {
                    "Rule",
                  },
                  "BaseAnswer" => Set {},
                  "Answer" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithWarnings" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithErrors" => Set {
                    "BaseAnswer",
                  },
                  "RegistryConfigurationTableData" => Set {},
                  "MaintenanceData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "StateData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "CoCData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ExtractData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ConversionData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ImportData" => Set {
                    "RegistryConfigurationTableData",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": [Circular],
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "refs": Object {},
            "replay": [Function],
            "rootIds": Object {},
            "toReference": [Function],
          },
          "policies": Policies {
            "cache": [Circular],
            "config": Object {
              "cache": [Circular],
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "BaseAnswer": Array [
                  "Answer",
                  "AnswerWithWarnings",
                  "AnswerWithErrors",
                ],
                "DisplayLogic": Array [
                  "BooleanSequenceDisplayLogic",
                  "CounterTableDisplayLogic",
                  "EnumerableTableDisplayLogic",
                  "OtherDisplayLogic",
                  "QuestionListDisplayLogic",
                  "QuestionFillButtonDisplayLogic",
                  "SectionDisplayLogic",
                  "SubheaderDisplayLogic",
                  "TableDisplayLogic",
                  "ThreeColumnDisplayLogic",
                  "TwoColumnDisplayLogic",
                  "QuestionCopyButtonDisplayLogic",
                ],
                "Question": Array [
                  "CalculatedQuestion",
                  "CounterQuestion",
                  "DateOrEnumerableQuestion",
                  "DependentEnumerableQuestion",
                  "DependentExternalEnumerableSearchableQuestion",
                  "DependentFilteredEnumerableSearchableQuestion",
                  "DateQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "EnumerableQuestion",
                  "EnumerableCollectionQuestion",
                  "EnumerableCollectionLongListQuestion",
                  "EnumerableCollectionSearchableQuestion",
                  "EnumerableOrEnumerableCollectionQuestion",
                  "EnumerableOrOpenQuestion",
                  "EnumerableSearchableQuestion",
                  "EnumerableCollectionSearchableFavoritableQuestion",
                  "EnumerableSearchableFavoritableQuestion",
                  "GeneratedQuestion",
                  "DateQuestion",
                  "DateOrEnumerableQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "DateTimeQuestion",
                  "NumberQuestion",
                  "NumberOrEnumerableQuestion",
                  "TimeStringOrEnumerableQuestion",
                  "NumberSearchableQuestion",
                  "NumberWithEnumerableQuestion",
                  "NumberWithUnitQuestion",
                  "OpenQuestion",
                  "OtherQuestion",
                  "RepeatableEnumerableQuestion",
                  "TextAreaQuestion",
                  "TimeStringQuestion",
                ],
                "RegistryConfigurationTableData": Array [
                  "MaintenanceData",
                  "StateData",
                  "CoCData",
                  "ExtractData",
                  "ConversionData",
                  "ImportData",
                ],
                "Rule": Array [
                  "EnumerableEqualityRule",
                  "OtherRule",
                  "UnconditionalRule",
                  "EnumerableInclusionRule",
                ],
              },
              "typePolicies": undefined,
            },
            "fuzzySubtypes": Map {},
            "rootIdsByTypename": Object {
              "Mutation": "ROOT_MUTATION",
              "Query": "ROOT_QUERY",
              "Subscription": "ROOT_SUBSCRIPTION",
            },
            "rootTypenamesById": Object {
              "ROOT_MUTATION": "Mutation",
              "ROOT_QUERY": "Query",
              "ROOT_SUBSCRIPTION": "Subscription",
            },
            "supertypeMap": Map {
              "DisplayLogic" => Set {},
              "BooleanSequenceDisplayLogic" => Set {
                "DisplayLogic",
              },
              "CounterTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "EnumerableTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "OtherDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionListDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionFillButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SectionDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SubheaderDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "ThreeColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TwoColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionCopyButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "Question" => Set {},
              "CalculatedQuestion" => Set {
                "Question",
              },
              "CounterQuestion" => Set {
                "Question",
              },
              "DateOrEnumerableQuestion" => Set {
                "Question",
              },
              "DependentEnumerableQuestion" => Set {
                "Question",
              },
              "DependentExternalEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DependentFilteredEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DateQuestion" => Set {
                "Question",
              },
              "DateTimeOrEnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionLongListQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableOrEnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableOrOpenQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "GeneratedQuestion" => Set {
                "Question",
              },
              "DateTimeQuestion" => Set {
                "Question",
              },
              "NumberQuestion" => Set {
                "Question",
              },
              "NumberOrEnumerableQuestion" => Set {
                "Question",
              },
              "TimeStringOrEnumerableQuestion" => Set {
                "Question",
              },
              "NumberSearchableQuestion" => Set {
                "Question",
              },
              "NumberWithEnumerableQuestion" => Set {
                "Question",
              },
              "NumberWithUnitQuestion" => Set {
                "Question",
              },
              "OpenQuestion" => Set {
                "Question",
              },
              "OtherQuestion" => Set {
                "Question",
              },
              "RepeatableEnumerableQuestion" => Set {
                "Question",
              },
              "TextAreaQuestion" => Set {
                "Question",
              },
              "TimeStringQuestion" => Set {
                "Question",
              },
              "Rule" => Set {},
              "EnumerableEqualityRule" => Set {
                "Rule",
              },
              "OtherRule" => Set {
                "Rule",
              },
              "UnconditionalRule" => Set {
                "Rule",
              },
              "EnumerableInclusionRule" => Set {
                "Rule",
              },
              "BaseAnswer" => Set {},
              "Answer" => Set {
                "BaseAnswer",
              },
              "AnswerWithWarnings" => Set {
                "BaseAnswer",
              },
              "AnswerWithErrors" => Set {
                "BaseAnswer",
              },
              "RegistryConfigurationTableData" => Set {},
              "MaintenanceData" => Set {
                "RegistryConfigurationTableData",
              },
              "StateData" => Set {
                "RegistryConfigurationTableData",
              },
              "CoCData" => Set {
                "RegistryConfigurationTableData",
              },
              "ExtractData" => Set {
                "RegistryConfigurationTableData",
              },
              "ConversionData" => Set {
                "RegistryConfigurationTableData",
              },
              "ImportData" => Set {
                "RegistryConfigurationTableData",
              },
            },
            "toBeAdded": Object {},
            "typePolicies": Object {},
            "usingPossibleTypes": true,
          },
          "storeReader": StoreReader {
            "canon": ObjectCanon {
              "empty": Object {},
              "keysByJSON": Map {
                "[]" => Object {
                  "json": "[]",
                  "sorted": Array [],
                },
              },
              "known": WeakSet {},
              "passes": WeakMap {},
              "pool": Trie {
                "data": Object {
                  "keys": Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "makeData": [Function],
                "weak": WeakMap {},
                "weakness": true,
              },
            },
            "config": Object {
              "addTypename": true,
              "cache": [Circular],
              "canonizeResults": false,
            },
            "executeSelectionSet": [Function],
            "executeSubSelectedArray": [Function],
            "knownResults": WeakMap {},
          },
          "storeWriter": StoreWriter {
            "cache": [Circular],
            "fragments": undefined,
            "reader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
          },
          "txCount": 0,
          "watches": Set {},
        },
        "client": [Circular],
        "selectionsToResolveCache": WeakMap {},
      },
      "mutate": [Function],
      "query": [Function],
      "queryDeduplication": true,
      "queryManager": QueryManager {
        "assumeImmutableResults": true,
        "cache": InMemoryCache {
          "addTypename": true,
          "addTypenameTransform": DocumentTransform {
            "cached": true,
            "performWork": [Function],
            "resultCache": WeakSet {},
            "transform": [Function],
          },
          "assumeImmutableResults": true,
          "config": Object {
            "addTypename": true,
            "canonizeResults": false,
            "dataIdFromObject": [Function],
            "possibleTypes": Object {
              "BaseAnswer": Array [
                "Answer",
                "AnswerWithWarnings",
                "AnswerWithErrors",
              ],
              "DisplayLogic": Array [
                "BooleanSequenceDisplayLogic",
                "CounterTableDisplayLogic",
                "EnumerableTableDisplayLogic",
                "OtherDisplayLogic",
                "QuestionListDisplayLogic",
                "QuestionFillButtonDisplayLogic",
                "SectionDisplayLogic",
                "SubheaderDisplayLogic",
                "TableDisplayLogic",
                "ThreeColumnDisplayLogic",
                "TwoColumnDisplayLogic",
                "QuestionCopyButtonDisplayLogic",
              ],
              "Question": Array [
                "CalculatedQuestion",
                "CounterQuestion",
                "DateOrEnumerableQuestion",
                "DependentEnumerableQuestion",
                "DependentExternalEnumerableSearchableQuestion",
                "DependentFilteredEnumerableSearchableQuestion",
                "DateQuestion",
                "DateTimeOrEnumerableQuestion",
                "EnumerableQuestion",
                "EnumerableCollectionQuestion",
                "EnumerableCollectionLongListQuestion",
                "EnumerableCollectionSearchableQuestion",
                "EnumerableOrEnumerableCollectionQuestion",
                "EnumerableOrOpenQuestion",
                "EnumerableSearchableQuestion",
                "EnumerableCollectionSearchableFavoritableQuestion",
                "EnumerableSearchableFavoritableQuestion",
                "GeneratedQuestion",
                "DateQuestion",
                "DateOrEnumerableQuestion",
                "DateTimeOrEnumerableQuestion",
                "DateTimeQuestion",
                "NumberQuestion",
                "NumberOrEnumerableQuestion",
                "TimeStringOrEnumerableQuestion",
                "NumberSearchableQuestion",
                "NumberWithEnumerableQuestion",
                "NumberWithUnitQuestion",
                "OpenQuestion",
                "OtherQuestion",
                "RepeatableEnumerableQuestion",
                "TextAreaQuestion",
                "TimeStringQuestion",
              ],
              "RegistryConfigurationTableData": Array [
                "MaintenanceData",
                "StateData",
                "CoCData",
                "ExtractData",
                "ConversionData",
                "ImportData",
              ],
              "Rule": Array [
                "EnumerableEqualityRule",
                "OtherRule",
                "UnconditionalRule",
                "EnumerableInclusionRule",
              ],
            },
            "resultCaching": true,
          },
          "data": Root {
            "canRead": [Function],
            "data": Object {},
            "getFieldValue": [Function],
            "group": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": null,
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "refs": Object {},
            "rootIds": Object {},
            "storageTrie": Trie {
              "makeData": [Function],
              "weakness": true,
            },
            "stump": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": [Circular],
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "BaseAnswer": Array [
                      "Answer",
                      "AnswerWithWarnings",
                      "AnswerWithErrors",
                    ],
                    "DisplayLogic": Array [
                      "BooleanSequenceDisplayLogic",
                      "CounterTableDisplayLogic",
                      "EnumerableTableDisplayLogic",
                      "OtherDisplayLogic",
                      "QuestionListDisplayLogic",
                      "QuestionFillButtonDisplayLogic",
                      "SectionDisplayLogic",
                      "SubheaderDisplayLogic",
                      "TableDisplayLogic",
                      "ThreeColumnDisplayLogic",
                      "TwoColumnDisplayLogic",
                      "QuestionCopyButtonDisplayLogic",
                    ],
                    "Question": Array [
                      "CalculatedQuestion",
                      "CounterQuestion",
                      "DateOrEnumerableQuestion",
                      "DependentEnumerableQuestion",
                      "DependentExternalEnumerableSearchableQuestion",
                      "DependentFilteredEnumerableSearchableQuestion",
                      "DateQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "EnumerableQuestion",
                      "EnumerableCollectionQuestion",
                      "EnumerableCollectionLongListQuestion",
                      "EnumerableCollectionSearchableQuestion",
                      "EnumerableOrEnumerableCollectionQuestion",
                      "EnumerableOrOpenQuestion",
                      "EnumerableSearchableQuestion",
                      "EnumerableCollectionSearchableFavoritableQuestion",
                      "EnumerableSearchableFavoritableQuestion",
                      "GeneratedQuestion",
                      "DateQuestion",
                      "DateOrEnumerableQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "DateTimeQuestion",
                      "NumberQuestion",
                      "NumberOrEnumerableQuestion",
                      "TimeStringOrEnumerableQuestion",
                      "NumberSearchableQuestion",
                      "NumberWithEnumerableQuestion",
                      "NumberWithUnitQuestion",
                      "OpenQuestion",
                      "OtherQuestion",
                      "RepeatableEnumerableQuestion",
                      "TextAreaQuestion",
                      "TimeStringQuestion",
                    ],
                    "RegistryConfigurationTableData": Array [
                      "MaintenanceData",
                      "StateData",
                      "CoCData",
                      "ExtractData",
                      "ConversionData",
                      "ImportData",
                    ],
                    "Rule": Array [
                      "EnumerableEqualityRule",
                      "OtherRule",
                      "UnconditionalRule",
                      "EnumerableInclusionRule",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "DisplayLogic" => Set {},
                  "BooleanSequenceDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "CounterTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "EnumerableTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "OtherDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionListDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionFillButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SectionDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SubheaderDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "ThreeColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TwoColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionCopyButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "Question" => Set {},
                  "CalculatedQuestion" => Set {
                    "Question",
                  },
                  "CounterQuestion" => Set {
                    "Question",
                  },
                  "DateOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentExternalEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DependentFilteredEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DateQuestion" => Set {
                    "Question",
                  },
                  "DateTimeOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionLongListQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrEnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrOpenQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "GeneratedQuestion" => Set {
                    "Question",
                  },
                  "DateTimeQuestion" => Set {
                    "Question",
                  },
                  "NumberQuestion" => Set {
                    "Question",
                  },
                  "NumberOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TimeStringOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberSearchableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithUnitQuestion" => Set {
                    "Question",
                  },
                  "OpenQuestion" => Set {
                    "Question",
                  },
                  "OtherQuestion" => Set {
                    "Question",
                  },
                  "RepeatableEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TextAreaQuestion" => Set {
                    "Question",
                  },
                  "TimeStringQuestion" => Set {
                    "Question",
                  },
                  "Rule" => Set {},
                  "EnumerableEqualityRule" => Set {
                    "Rule",
                  },
                  "OtherRule" => Set {
                    "Rule",
                  },
                  "UnconditionalRule" => Set {
                    "Rule",
                  },
                  "EnumerableInclusionRule" => Set {
                    "Rule",
                  },
                  "BaseAnswer" => Set {},
                  "Answer" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithWarnings" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithErrors" => Set {
                    "BaseAnswer",
                  },
                  "RegistryConfigurationTableData" => Set {},
                  "MaintenanceData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "StateData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "CoCData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ExtractData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ConversionData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ImportData" => Set {
                    "RegistryConfigurationTableData",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "toReference": [Function],
          },
          "getFragmentDoc": [Function],
          "makeVar": [Function],
          "maybeBroadcastWatch": [Function],
          "optimisticData": Stump {
            "canRead": [Function],
            "data": Object {},
            "getFieldValue": [Function],
            "group": CacheGroup {
              "caching": true,
              "d": [Function],
              "keyMaker": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "parent": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
            },
            "id": "EntityStore.Stump",
            "parent": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "BaseAnswer": Array [
                      "Answer",
                      "AnswerWithWarnings",
                      "AnswerWithErrors",
                    ],
                    "DisplayLogic": Array [
                      "BooleanSequenceDisplayLogic",
                      "CounterTableDisplayLogic",
                      "EnumerableTableDisplayLogic",
                      "OtherDisplayLogic",
                      "QuestionListDisplayLogic",
                      "QuestionFillButtonDisplayLogic",
                      "SectionDisplayLogic",
                      "SubheaderDisplayLogic",
                      "TableDisplayLogic",
                      "ThreeColumnDisplayLogic",
                      "TwoColumnDisplayLogic",
                      "QuestionCopyButtonDisplayLogic",
                    ],
                    "Question": Array [
                      "CalculatedQuestion",
                      "CounterQuestion",
                      "DateOrEnumerableQuestion",
                      "DependentEnumerableQuestion",
                      "DependentExternalEnumerableSearchableQuestion",
                      "DependentFilteredEnumerableSearchableQuestion",
                      "DateQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "EnumerableQuestion",
                      "EnumerableCollectionQuestion",
                      "EnumerableCollectionLongListQuestion",
                      "EnumerableCollectionSearchableQuestion",
                      "EnumerableOrEnumerableCollectionQuestion",
                      "EnumerableOrOpenQuestion",
                      "EnumerableSearchableQuestion",
                      "EnumerableCollectionSearchableFavoritableQuestion",
                      "EnumerableSearchableFavoritableQuestion",
                      "GeneratedQuestion",
                      "DateQuestion",
                      "DateOrEnumerableQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "DateTimeQuestion",
                      "NumberQuestion",
                      "NumberOrEnumerableQuestion",
                      "TimeStringOrEnumerableQuestion",
                      "NumberSearchableQuestion",
                      "NumberWithEnumerableQuestion",
                      "NumberWithUnitQuestion",
                      "OpenQuestion",
                      "OtherQuestion",
                      "RepeatableEnumerableQuestion",
                      "TextAreaQuestion",
                      "TimeStringQuestion",
                    ],
                    "RegistryConfigurationTableData": Array [
                      "MaintenanceData",
                      "StateData",
                      "CoCData",
                      "ExtractData",
                      "ConversionData",
                      "ImportData",
                    ],
                    "Rule": Array [
                      "EnumerableEqualityRule",
                      "OtherRule",
                      "UnconditionalRule",
                      "EnumerableInclusionRule",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "DisplayLogic" => Set {},
                  "BooleanSequenceDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "CounterTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "EnumerableTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "OtherDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionListDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionFillButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SectionDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SubheaderDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "ThreeColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TwoColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionCopyButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "Question" => Set {},
                  "CalculatedQuestion" => Set {
                    "Question",
                  },
                  "CounterQuestion" => Set {
                    "Question",
                  },
                  "DateOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentExternalEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DependentFilteredEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DateQuestion" => Set {
                    "Question",
                  },
                  "DateTimeOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionLongListQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrEnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrOpenQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "GeneratedQuestion" => Set {
                    "Question",
                  },
                  "DateTimeQuestion" => Set {
                    "Question",
                  },
                  "NumberQuestion" => Set {
                    "Question",
                  },
                  "NumberOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TimeStringOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberSearchableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithUnitQuestion" => Set {
                    "Question",
                  },
                  "OpenQuestion" => Set {
                    "Question",
                  },
                  "OtherQuestion" => Set {
                    "Question",
                  },
                  "RepeatableEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TextAreaQuestion" => Set {
                    "Question",
                  },
                  "TimeStringQuestion" => Set {
                    "Question",
                  },
                  "Rule" => Set {},
                  "EnumerableEqualityRule" => Set {
                    "Rule",
                  },
                  "OtherRule" => Set {
                    "Rule",
                  },
                  "UnconditionalRule" => Set {
                    "Rule",
                  },
                  "EnumerableInclusionRule" => Set {
                    "Rule",
                  },
                  "BaseAnswer" => Set {},
                  "Answer" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithWarnings" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithErrors" => Set {
                    "BaseAnswer",
                  },
                  "RegistryConfigurationTableData" => Set {},
                  "MaintenanceData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "StateData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "CoCData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ExtractData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ConversionData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ImportData" => Set {
                    "RegistryConfigurationTableData",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": [Circular],
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "refs": Object {},
            "replay": [Function],
            "rootIds": Object {},
            "toReference": [Function],
          },
          "policies": Policies {
            "cache": [Circular],
            "config": Object {
              "cache": [Circular],
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "BaseAnswer": Array [
                  "Answer",
                  "AnswerWithWarnings",
                  "AnswerWithErrors",
                ],
                "DisplayLogic": Array [
                  "BooleanSequenceDisplayLogic",
                  "CounterTableDisplayLogic",
                  "EnumerableTableDisplayLogic",
                  "OtherDisplayLogic",
                  "QuestionListDisplayLogic",
                  "QuestionFillButtonDisplayLogic",
                  "SectionDisplayLogic",
                  "SubheaderDisplayLogic",
                  "TableDisplayLogic",
                  "ThreeColumnDisplayLogic",
                  "TwoColumnDisplayLogic",
                  "QuestionCopyButtonDisplayLogic",
                ],
                "Question": Array [
                  "CalculatedQuestion",
                  "CounterQuestion",
                  "DateOrEnumerableQuestion",
                  "DependentEnumerableQuestion",
                  "DependentExternalEnumerableSearchableQuestion",
                  "DependentFilteredEnumerableSearchableQuestion",
                  "DateQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "EnumerableQuestion",
                  "EnumerableCollectionQuestion",
                  "EnumerableCollectionLongListQuestion",
                  "EnumerableCollectionSearchableQuestion",
                  "EnumerableOrEnumerableCollectionQuestion",
                  "EnumerableOrOpenQuestion",
                  "EnumerableSearchableQuestion",
                  "EnumerableCollectionSearchableFavoritableQuestion",
                  "EnumerableSearchableFavoritableQuestion",
                  "GeneratedQuestion",
                  "DateQuestion",
                  "DateOrEnumerableQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "DateTimeQuestion",
                  "NumberQuestion",
                  "NumberOrEnumerableQuestion",
                  "TimeStringOrEnumerableQuestion",
                  "NumberSearchableQuestion",
                  "NumberWithEnumerableQuestion",
                  "NumberWithUnitQuestion",
                  "OpenQuestion",
                  "OtherQuestion",
                  "RepeatableEnumerableQuestion",
                  "TextAreaQuestion",
                  "TimeStringQuestion",
                ],
                "RegistryConfigurationTableData": Array [
                  "MaintenanceData",
                  "StateData",
                  "CoCData",
                  "ExtractData",
                  "ConversionData",
                  "ImportData",
                ],
                "Rule": Array [
                  "EnumerableEqualityRule",
                  "OtherRule",
                  "UnconditionalRule",
                  "EnumerableInclusionRule",
                ],
              },
              "typePolicies": undefined,
            },
            "fuzzySubtypes": Map {},
            "rootIdsByTypename": Object {
              "Mutation": "ROOT_MUTATION",
              "Query": "ROOT_QUERY",
              "Subscription": "ROOT_SUBSCRIPTION",
            },
            "rootTypenamesById": Object {
              "ROOT_MUTATION": "Mutation",
              "ROOT_QUERY": "Query",
              "ROOT_SUBSCRIPTION": "Subscription",
            },
            "supertypeMap": Map {
              "DisplayLogic" => Set {},
              "BooleanSequenceDisplayLogic" => Set {
                "DisplayLogic",
              },
              "CounterTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "EnumerableTableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "OtherDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionListDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionFillButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SectionDisplayLogic" => Set {
                "DisplayLogic",
              },
              "SubheaderDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TableDisplayLogic" => Set {
                "DisplayLogic",
              },
              "ThreeColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "TwoColumnDisplayLogic" => Set {
                "DisplayLogic",
              },
              "QuestionCopyButtonDisplayLogic" => Set {
                "DisplayLogic",
              },
              "Question" => Set {},
              "CalculatedQuestion" => Set {
                "Question",
              },
              "CounterQuestion" => Set {
                "Question",
              },
              "DateOrEnumerableQuestion" => Set {
                "Question",
              },
              "DependentEnumerableQuestion" => Set {
                "Question",
              },
              "DependentExternalEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DependentFilteredEnumerableSearchableQuestion" => Set {
                "Question",
              },
              "DateQuestion" => Set {
                "Question",
              },
              "DateTimeOrEnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionLongListQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableOrEnumerableCollectionQuestion" => Set {
                "Question",
              },
              "EnumerableOrOpenQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableQuestion" => Set {
                "Question",
              },
              "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "EnumerableSearchableFavoritableQuestion" => Set {
                "Question",
              },
              "GeneratedQuestion" => Set {
                "Question",
              },
              "DateTimeQuestion" => Set {
                "Question",
              },
              "NumberQuestion" => Set {
                "Question",
              },
              "NumberOrEnumerableQuestion" => Set {
                "Question",
              },
              "TimeStringOrEnumerableQuestion" => Set {
                "Question",
              },
              "NumberSearchableQuestion" => Set {
                "Question",
              },
              "NumberWithEnumerableQuestion" => Set {
                "Question",
              },
              "NumberWithUnitQuestion" => Set {
                "Question",
              },
              "OpenQuestion" => Set {
                "Question",
              },
              "OtherQuestion" => Set {
                "Question",
              },
              "RepeatableEnumerableQuestion" => Set {
                "Question",
              },
              "TextAreaQuestion" => Set {
                "Question",
              },
              "TimeStringQuestion" => Set {
                "Question",
              },
              "Rule" => Set {},
              "EnumerableEqualityRule" => Set {
                "Rule",
              },
              "OtherRule" => Set {
                "Rule",
              },
              "UnconditionalRule" => Set {
                "Rule",
              },
              "EnumerableInclusionRule" => Set {
                "Rule",
              },
              "BaseAnswer" => Set {},
              "Answer" => Set {
                "BaseAnswer",
              },
              "AnswerWithWarnings" => Set {
                "BaseAnswer",
              },
              "AnswerWithErrors" => Set {
                "BaseAnswer",
              },
              "RegistryConfigurationTableData" => Set {},
              "MaintenanceData" => Set {
                "RegistryConfigurationTableData",
              },
              "StateData" => Set {
                "RegistryConfigurationTableData",
              },
              "CoCData" => Set {
                "RegistryConfigurationTableData",
              },
              "ExtractData" => Set {
                "RegistryConfigurationTableData",
              },
              "ConversionData" => Set {
                "RegistryConfigurationTableData",
              },
              "ImportData" => Set {
                "RegistryConfigurationTableData",
              },
            },
            "toBeAdded": Object {},
            "typePolicies": Object {},
            "usingPossibleTypes": true,
          },
          "storeReader": StoreReader {
            "canon": ObjectCanon {
              "empty": Object {},
              "keysByJSON": Map {
                "[]" => Object {
                  "json": "[]",
                  "sorted": Array [],
                },
              },
              "known": WeakSet {},
              "passes": WeakMap {},
              "pool": Trie {
                "data": Object {
                  "keys": Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "makeData": [Function],
                "weak": WeakMap {},
                "weakness": true,
              },
            },
            "config": Object {
              "addTypename": true,
              "cache": [Circular],
              "canonizeResults": false,
            },
            "executeSelectionSet": [Function],
            "executeSubSelectedArray": [Function],
            "knownResults": WeakMap {},
          },
          "storeWriter": StoreWriter {
            "cache": [Circular],
            "fragments": undefined,
            "reader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
          },
          "txCount": 0,
          "watches": Set {},
        },
        "clientAwareness": Object {
          "name": undefined,
          "version": undefined,
        },
        "defaultContext": Object {},
        "defaultOptions": Object {},
        "documentTransform": DocumentTransform {
          "cached": false,
          "resultCache": WeakSet {},
          "transform": [Function],
        },
        "fetchCancelFns": Map {},
        "inFlightLinkObservables": Trie {
          "makeData": [Function],
          "weakness": false,
        },
        "link": ApolloLink {
          "left": ApolloLink {
            "request": [Function],
          },
          "request": [Function],
          "right": ApolloLink {
            "left": ApolloLink {
              "request": [Function],
            },
            "request": [Function],
            "right": ApolloLink {
              "request": [Function],
            },
          },
        },
        "localState": LocalState {
          "cache": InMemoryCache {
            "addTypename": true,
            "addTypenameTransform": DocumentTransform {
              "cached": true,
              "performWork": [Function],
              "resultCache": WeakSet {},
              "transform": [Function],
            },
            "assumeImmutableResults": true,
            "config": Object {
              "addTypename": true,
              "canonizeResults": false,
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "BaseAnswer": Array [
                  "Answer",
                  "AnswerWithWarnings",
                  "AnswerWithErrors",
                ],
                "DisplayLogic": Array [
                  "BooleanSequenceDisplayLogic",
                  "CounterTableDisplayLogic",
                  "EnumerableTableDisplayLogic",
                  "OtherDisplayLogic",
                  "QuestionListDisplayLogic",
                  "QuestionFillButtonDisplayLogic",
                  "SectionDisplayLogic",
                  "SubheaderDisplayLogic",
                  "TableDisplayLogic",
                  "ThreeColumnDisplayLogic",
                  "TwoColumnDisplayLogic",
                  "QuestionCopyButtonDisplayLogic",
                ],
                "Question": Array [
                  "CalculatedQuestion",
                  "CounterQuestion",
                  "DateOrEnumerableQuestion",
                  "DependentEnumerableQuestion",
                  "DependentExternalEnumerableSearchableQuestion",
                  "DependentFilteredEnumerableSearchableQuestion",
                  "DateQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "EnumerableQuestion",
                  "EnumerableCollectionQuestion",
                  "EnumerableCollectionLongListQuestion",
                  "EnumerableCollectionSearchableQuestion",
                  "EnumerableOrEnumerableCollectionQuestion",
                  "EnumerableOrOpenQuestion",
                  "EnumerableSearchableQuestion",
                  "EnumerableCollectionSearchableFavoritableQuestion",
                  "EnumerableSearchableFavoritableQuestion",
                  "GeneratedQuestion",
                  "DateQuestion",
                  "DateOrEnumerableQuestion",
                  "DateTimeOrEnumerableQuestion",
                  "DateTimeQuestion",
                  "NumberQuestion",
                  "NumberOrEnumerableQuestion",
                  "TimeStringOrEnumerableQuestion",
                  "NumberSearchableQuestion",
                  "NumberWithEnumerableQuestion",
                  "NumberWithUnitQuestion",
                  "OpenQuestion",
                  "OtherQuestion",
                  "RepeatableEnumerableQuestion",
                  "TextAreaQuestion",
                  "TimeStringQuestion",
                ],
                "RegistryConfigurationTableData": Array [
                  "MaintenanceData",
                  "StateData",
                  "CoCData",
                  "ExtractData",
                  "ConversionData",
                  "ImportData",
                ],
                "Rule": Array [
                  "EnumerableEqualityRule",
                  "OtherRule",
                  "UnconditionalRule",
                  "EnumerableInclusionRule",
                ],
              },
              "resultCaching": true,
            },
            "data": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "BaseAnswer": Array [
                      "Answer",
                      "AnswerWithWarnings",
                      "AnswerWithErrors",
                    ],
                    "DisplayLogic": Array [
                      "BooleanSequenceDisplayLogic",
                      "CounterTableDisplayLogic",
                      "EnumerableTableDisplayLogic",
                      "OtherDisplayLogic",
                      "QuestionListDisplayLogic",
                      "QuestionFillButtonDisplayLogic",
                      "SectionDisplayLogic",
                      "SubheaderDisplayLogic",
                      "TableDisplayLogic",
                      "ThreeColumnDisplayLogic",
                      "TwoColumnDisplayLogic",
                      "QuestionCopyButtonDisplayLogic",
                    ],
                    "Question": Array [
                      "CalculatedQuestion",
                      "CounterQuestion",
                      "DateOrEnumerableQuestion",
                      "DependentEnumerableQuestion",
                      "DependentExternalEnumerableSearchableQuestion",
                      "DependentFilteredEnumerableSearchableQuestion",
                      "DateQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "EnumerableQuestion",
                      "EnumerableCollectionQuestion",
                      "EnumerableCollectionLongListQuestion",
                      "EnumerableCollectionSearchableQuestion",
                      "EnumerableOrEnumerableCollectionQuestion",
                      "EnumerableOrOpenQuestion",
                      "EnumerableSearchableQuestion",
                      "EnumerableCollectionSearchableFavoritableQuestion",
                      "EnumerableSearchableFavoritableQuestion",
                      "GeneratedQuestion",
                      "DateQuestion",
                      "DateOrEnumerableQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "DateTimeQuestion",
                      "NumberQuestion",
                      "NumberOrEnumerableQuestion",
                      "TimeStringOrEnumerableQuestion",
                      "NumberSearchableQuestion",
                      "NumberWithEnumerableQuestion",
                      "NumberWithUnitQuestion",
                      "OpenQuestion",
                      "OtherQuestion",
                      "RepeatableEnumerableQuestion",
                      "TextAreaQuestion",
                      "TimeStringQuestion",
                    ],
                    "RegistryConfigurationTableData": Array [
                      "MaintenanceData",
                      "StateData",
                      "CoCData",
                      "ExtractData",
                      "ConversionData",
                      "ImportData",
                    ],
                    "Rule": Array [
                      "EnumerableEqualityRule",
                      "OtherRule",
                      "UnconditionalRule",
                      "EnumerableInclusionRule",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "DisplayLogic" => Set {},
                  "BooleanSequenceDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "CounterTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "EnumerableTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "OtherDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionListDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionFillButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SectionDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SubheaderDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "ThreeColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TwoColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionCopyButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "Question" => Set {},
                  "CalculatedQuestion" => Set {
                    "Question",
                  },
                  "CounterQuestion" => Set {
                    "Question",
                  },
                  "DateOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentExternalEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DependentFilteredEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DateQuestion" => Set {
                    "Question",
                  },
                  "DateTimeOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionLongListQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrEnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrOpenQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "GeneratedQuestion" => Set {
                    "Question",
                  },
                  "DateTimeQuestion" => Set {
                    "Question",
                  },
                  "NumberQuestion" => Set {
                    "Question",
                  },
                  "NumberOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TimeStringOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberSearchableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithUnitQuestion" => Set {
                    "Question",
                  },
                  "OpenQuestion" => Set {
                    "Question",
                  },
                  "OtherQuestion" => Set {
                    "Question",
                  },
                  "RepeatableEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TextAreaQuestion" => Set {
                    "Question",
                  },
                  "TimeStringQuestion" => Set {
                    "Question",
                  },
                  "Rule" => Set {},
                  "EnumerableEqualityRule" => Set {
                    "Rule",
                  },
                  "OtherRule" => Set {
                    "Rule",
                  },
                  "UnconditionalRule" => Set {
                    "Rule",
                  },
                  "EnumerableInclusionRule" => Set {
                    "Rule",
                  },
                  "BaseAnswer" => Set {},
                  "Answer" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithWarnings" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithErrors" => Set {
                    "BaseAnswer",
                  },
                  "RegistryConfigurationTableData" => Set {},
                  "MaintenanceData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "StateData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "CoCData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ExtractData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ConversionData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ImportData" => Set {
                    "RegistryConfigurationTableData",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": [Circular],
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "BaseAnswer": Array [
                        "Answer",
                        "AnswerWithWarnings",
                        "AnswerWithErrors",
                      ],
                      "DisplayLogic": Array [
                        "BooleanSequenceDisplayLogic",
                        "CounterTableDisplayLogic",
                        "EnumerableTableDisplayLogic",
                        "OtherDisplayLogic",
                        "QuestionListDisplayLogic",
                        "QuestionFillButtonDisplayLogic",
                        "SectionDisplayLogic",
                        "SubheaderDisplayLogic",
                        "TableDisplayLogic",
                        "ThreeColumnDisplayLogic",
                        "TwoColumnDisplayLogic",
                        "QuestionCopyButtonDisplayLogic",
                      ],
                      "Question": Array [
                        "CalculatedQuestion",
                        "CounterQuestion",
                        "DateOrEnumerableQuestion",
                        "DependentEnumerableQuestion",
                        "DependentExternalEnumerableSearchableQuestion",
                        "DependentFilteredEnumerableSearchableQuestion",
                        "DateQuestion",
                        "DateTimeOrEnumerableQuestion",
                        "EnumerableQuestion",
                        "EnumerableCollectionQuestion",
                        "EnumerableCollectionLongListQuestion",
                        "EnumerableCollectionSearchableQuestion",
                        "EnumerableOrEnumerableCollectionQuestion",
                        "EnumerableOrOpenQuestion",
                        "EnumerableSearchableQuestion",
                        "EnumerableCollectionSearchableFavoritableQuestion",
                        "EnumerableSearchableFavoritableQuestion",
                        "GeneratedQuestion",
                        "DateQuestion",
                        "DateOrEnumerableQuestion",
                        "DateTimeOrEnumerableQuestion",
                        "DateTimeQuestion",
                        "NumberQuestion",
                        "NumberOrEnumerableQuestion",
                        "TimeStringOrEnumerableQuestion",
                        "NumberSearchableQuestion",
                        "NumberWithEnumerableQuestion",
                        "NumberWithUnitQuestion",
                        "OpenQuestion",
                        "OtherQuestion",
                        "RepeatableEnumerableQuestion",
                        "TextAreaQuestion",
                        "TimeStringQuestion",
                      ],
                      "RegistryConfigurationTableData": Array [
                        "MaintenanceData",
                        "StateData",
                        "CoCData",
                        "ExtractData",
                        "ConversionData",
                        "ImportData",
                      ],
                      "Rule": Array [
                        "EnumerableEqualityRule",
                        "OtherRule",
                        "UnconditionalRule",
                        "EnumerableInclusionRule",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "DisplayLogic" => Set {},
                    "BooleanSequenceDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "CounterTableDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "EnumerableTableDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "OtherDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "QuestionListDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "QuestionFillButtonDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "SectionDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "SubheaderDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "TableDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "ThreeColumnDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "TwoColumnDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "QuestionCopyButtonDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "Question" => Set {},
                    "CalculatedQuestion" => Set {
                      "Question",
                    },
                    "CounterQuestion" => Set {
                      "Question",
                    },
                    "DateOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "DependentEnumerableQuestion" => Set {
                      "Question",
                    },
                    "DependentExternalEnumerableSearchableQuestion" => Set {
                      "Question",
                    },
                    "DependentFilteredEnumerableSearchableQuestion" => Set {
                      "Question",
                    },
                    "DateQuestion" => Set {
                      "Question",
                    },
                    "DateTimeOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionLongListQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionSearchableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableOrEnumerableCollectionQuestion" => Set {
                      "Question",
                    },
                    "EnumerableOrOpenQuestion" => Set {
                      "Question",
                    },
                    "EnumerableSearchableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableSearchableFavoritableQuestion" => Set {
                      "Question",
                    },
                    "GeneratedQuestion" => Set {
                      "Question",
                    },
                    "DateTimeQuestion" => Set {
                      "Question",
                    },
                    "NumberQuestion" => Set {
                      "Question",
                    },
                    "NumberOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "TimeStringOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "NumberSearchableQuestion" => Set {
                      "Question",
                    },
                    "NumberWithEnumerableQuestion" => Set {
                      "Question",
                    },
                    "NumberWithUnitQuestion" => Set {
                      "Question",
                    },
                    "OpenQuestion" => Set {
                      "Question",
                    },
                    "OtherQuestion" => Set {
                      "Question",
                    },
                    "RepeatableEnumerableQuestion" => Set {
                      "Question",
                    },
                    "TextAreaQuestion" => Set {
                      "Question",
                    },
                    "TimeStringQuestion" => Set {
                      "Question",
                    },
                    "Rule" => Set {},
                    "EnumerableEqualityRule" => Set {
                      "Rule",
                    },
                    "OtherRule" => Set {
                      "Rule",
                    },
                    "UnconditionalRule" => Set {
                      "Rule",
                    },
                    "EnumerableInclusionRule" => Set {
                      "Rule",
                    },
                    "BaseAnswer" => Set {},
                    "Answer" => Set {
                      "BaseAnswer",
                    },
                    "AnswerWithWarnings" => Set {
                      "BaseAnswer",
                    },
                    "AnswerWithErrors" => Set {
                      "BaseAnswer",
                    },
                    "RegistryConfigurationTableData" => Set {},
                    "MaintenanceData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "StateData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "CoCData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "ExtractData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "ConversionData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "ImportData" => Set {
                      "RegistryConfigurationTableData",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "toReference": [Function],
            },
            "getFragmentDoc": [Function],
            "makeVar": [Function],
            "maybeBroadcastWatch": [Function],
            "optimisticData": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "BaseAnswer": Array [
                        "Answer",
                        "AnswerWithWarnings",
                        "AnswerWithErrors",
                      ],
                      "DisplayLogic": Array [
                        "BooleanSequenceDisplayLogic",
                        "CounterTableDisplayLogic",
                        "EnumerableTableDisplayLogic",
                        "OtherDisplayLogic",
                        "QuestionListDisplayLogic",
                        "QuestionFillButtonDisplayLogic",
                        "SectionDisplayLogic",
                        "SubheaderDisplayLogic",
                        "TableDisplayLogic",
                        "ThreeColumnDisplayLogic",
                        "TwoColumnDisplayLogic",
                        "QuestionCopyButtonDisplayLogic",
                      ],
                      "Question": Array [
                        "CalculatedQuestion",
                        "CounterQuestion",
                        "DateOrEnumerableQuestion",
                        "DependentEnumerableQuestion",
                        "DependentExternalEnumerableSearchableQuestion",
                        "DependentFilteredEnumerableSearchableQuestion",
                        "DateQuestion",
                        "DateTimeOrEnumerableQuestion",
                        "EnumerableQuestion",
                        "EnumerableCollectionQuestion",
                        "EnumerableCollectionLongListQuestion",
                        "EnumerableCollectionSearchableQuestion",
                        "EnumerableOrEnumerableCollectionQuestion",
                        "EnumerableOrOpenQuestion",
                        "EnumerableSearchableQuestion",
                        "EnumerableCollectionSearchableFavoritableQuestion",
                        "EnumerableSearchableFavoritableQuestion",
                        "GeneratedQuestion",
                        "DateQuestion",
                        "DateOrEnumerableQuestion",
                        "DateTimeOrEnumerableQuestion",
                        "DateTimeQuestion",
                        "NumberQuestion",
                        "NumberOrEnumerableQuestion",
                        "TimeStringOrEnumerableQuestion",
                        "NumberSearchableQuestion",
                        "NumberWithEnumerableQuestion",
                        "NumberWithUnitQuestion",
                        "OpenQuestion",
                        "OtherQuestion",
                        "RepeatableEnumerableQuestion",
                        "TextAreaQuestion",
                        "TimeStringQuestion",
                      ],
                      "RegistryConfigurationTableData": Array [
                        "MaintenanceData",
                        "StateData",
                        "CoCData",
                        "ExtractData",
                        "ConversionData",
                        "ImportData",
                      ],
                      "Rule": Array [
                        "EnumerableEqualityRule",
                        "OtherRule",
                        "UnconditionalRule",
                        "EnumerableInclusionRule",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "DisplayLogic" => Set {},
                    "BooleanSequenceDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "CounterTableDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "EnumerableTableDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "OtherDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "QuestionListDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "QuestionFillButtonDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "SectionDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "SubheaderDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "TableDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "ThreeColumnDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "TwoColumnDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "QuestionCopyButtonDisplayLogic" => Set {
                      "DisplayLogic",
                    },
                    "Question" => Set {},
                    "CalculatedQuestion" => Set {
                      "Question",
                    },
                    "CounterQuestion" => Set {
                      "Question",
                    },
                    "DateOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "DependentEnumerableQuestion" => Set {
                      "Question",
                    },
                    "DependentExternalEnumerableSearchableQuestion" => Set {
                      "Question",
                    },
                    "DependentFilteredEnumerableSearchableQuestion" => Set {
                      "Question",
                    },
                    "DateQuestion" => Set {
                      "Question",
                    },
                    "DateTimeOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionLongListQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionSearchableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableOrEnumerableCollectionQuestion" => Set {
                      "Question",
                    },
                    "EnumerableOrOpenQuestion" => Set {
                      "Question",
                    },
                    "EnumerableSearchableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                      "Question",
                    },
                    "EnumerableSearchableFavoritableQuestion" => Set {
                      "Question",
                    },
                    "GeneratedQuestion" => Set {
                      "Question",
                    },
                    "DateTimeQuestion" => Set {
                      "Question",
                    },
                    "NumberQuestion" => Set {
                      "Question",
                    },
                    "NumberOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "TimeStringOrEnumerableQuestion" => Set {
                      "Question",
                    },
                    "NumberSearchableQuestion" => Set {
                      "Question",
                    },
                    "NumberWithEnumerableQuestion" => Set {
                      "Question",
                    },
                    "NumberWithUnitQuestion" => Set {
                      "Question",
                    },
                    "OpenQuestion" => Set {
                      "Question",
                    },
                    "OtherQuestion" => Set {
                      "Question",
                    },
                    "RepeatableEnumerableQuestion" => Set {
                      "Question",
                    },
                    "TextAreaQuestion" => Set {
                      "Question",
                    },
                    "TimeStringQuestion" => Set {
                      "Question",
                    },
                    "Rule" => Set {},
                    "EnumerableEqualityRule" => Set {
                      "Rule",
                    },
                    "OtherRule" => Set {
                      "Rule",
                    },
                    "UnconditionalRule" => Set {
                      "Rule",
                    },
                    "EnumerableInclusionRule" => Set {
                      "Rule",
                    },
                    "BaseAnswer" => Set {},
                    "Answer" => Set {
                      "BaseAnswer",
                    },
                    "AnswerWithWarnings" => Set {
                      "BaseAnswer",
                    },
                    "AnswerWithErrors" => Set {
                      "BaseAnswer",
                    },
                    "RegistryConfigurationTableData" => Set {},
                    "MaintenanceData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "StateData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "CoCData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "ExtractData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "ConversionData" => Set {
                      "RegistryConfigurationTableData",
                    },
                    "ImportData" => Set {
                      "RegistryConfigurationTableData",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": [Circular],
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "BaseAnswer": Array [
                      "Answer",
                      "AnswerWithWarnings",
                      "AnswerWithErrors",
                    ],
                    "DisplayLogic": Array [
                      "BooleanSequenceDisplayLogic",
                      "CounterTableDisplayLogic",
                      "EnumerableTableDisplayLogic",
                      "OtherDisplayLogic",
                      "QuestionListDisplayLogic",
                      "QuestionFillButtonDisplayLogic",
                      "SectionDisplayLogic",
                      "SubheaderDisplayLogic",
                      "TableDisplayLogic",
                      "ThreeColumnDisplayLogic",
                      "TwoColumnDisplayLogic",
                      "QuestionCopyButtonDisplayLogic",
                    ],
                    "Question": Array [
                      "CalculatedQuestion",
                      "CounterQuestion",
                      "DateOrEnumerableQuestion",
                      "DependentEnumerableQuestion",
                      "DependentExternalEnumerableSearchableQuestion",
                      "DependentFilteredEnumerableSearchableQuestion",
                      "DateQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "EnumerableQuestion",
                      "EnumerableCollectionQuestion",
                      "EnumerableCollectionLongListQuestion",
                      "EnumerableCollectionSearchableQuestion",
                      "EnumerableOrEnumerableCollectionQuestion",
                      "EnumerableOrOpenQuestion",
                      "EnumerableSearchableQuestion",
                      "EnumerableCollectionSearchableFavoritableQuestion",
                      "EnumerableSearchableFavoritableQuestion",
                      "GeneratedQuestion",
                      "DateQuestion",
                      "DateOrEnumerableQuestion",
                      "DateTimeOrEnumerableQuestion",
                      "DateTimeQuestion",
                      "NumberQuestion",
                      "NumberOrEnumerableQuestion",
                      "TimeStringOrEnumerableQuestion",
                      "NumberSearchableQuestion",
                      "NumberWithEnumerableQuestion",
                      "NumberWithUnitQuestion",
                      "OpenQuestion",
                      "OtherQuestion",
                      "RepeatableEnumerableQuestion",
                      "TextAreaQuestion",
                      "TimeStringQuestion",
                    ],
                    "RegistryConfigurationTableData": Array [
                      "MaintenanceData",
                      "StateData",
                      "CoCData",
                      "ExtractData",
                      "ConversionData",
                      "ImportData",
                    ],
                    "Rule": Array [
                      "EnumerableEqualityRule",
                      "OtherRule",
                      "UnconditionalRule",
                      "EnumerableInclusionRule",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "DisplayLogic" => Set {},
                  "BooleanSequenceDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "CounterTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "EnumerableTableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "OtherDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionListDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionFillButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SectionDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "SubheaderDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TableDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "ThreeColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "TwoColumnDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "QuestionCopyButtonDisplayLogic" => Set {
                    "DisplayLogic",
                  },
                  "Question" => Set {},
                  "CalculatedQuestion" => Set {
                    "Question",
                  },
                  "CounterQuestion" => Set {
                    "Question",
                  },
                  "DateOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentEnumerableQuestion" => Set {
                    "Question",
                  },
                  "DependentExternalEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DependentFilteredEnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "DateQuestion" => Set {
                    "Question",
                  },
                  "DateTimeOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionLongListQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrEnumerableCollectionQuestion" => Set {
                    "Question",
                  },
                  "EnumerableOrOpenQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "EnumerableSearchableFavoritableQuestion" => Set {
                    "Question",
                  },
                  "GeneratedQuestion" => Set {
                    "Question",
                  },
                  "DateTimeQuestion" => Set {
                    "Question",
                  },
                  "NumberQuestion" => Set {
                    "Question",
                  },
                  "NumberOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TimeStringOrEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberSearchableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithEnumerableQuestion" => Set {
                    "Question",
                  },
                  "NumberWithUnitQuestion" => Set {
                    "Question",
                  },
                  "OpenQuestion" => Set {
                    "Question",
                  },
                  "OtherQuestion" => Set {
                    "Question",
                  },
                  "RepeatableEnumerableQuestion" => Set {
                    "Question",
                  },
                  "TextAreaQuestion" => Set {
                    "Question",
                  },
                  "TimeStringQuestion" => Set {
                    "Question",
                  },
                  "Rule" => Set {},
                  "EnumerableEqualityRule" => Set {
                    "Rule",
                  },
                  "OtherRule" => Set {
                    "Rule",
                  },
                  "UnconditionalRule" => Set {
                    "Rule",
                  },
                  "EnumerableInclusionRule" => Set {
                    "Rule",
                  },
                  "BaseAnswer" => Set {},
                  "Answer" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithWarnings" => Set {
                    "BaseAnswer",
                  },
                  "AnswerWithErrors" => Set {
                    "BaseAnswer",
                  },
                  "RegistryConfigurationTableData" => Set {},
                  "MaintenanceData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "StateData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "CoCData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ExtractData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ConversionData" => Set {
                    "RegistryConfigurationTableData",
                  },
                  "ImportData" => Set {
                    "RegistryConfigurationTableData",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "BaseAnswer": Array [
                    "Answer",
                    "AnswerWithWarnings",
                    "AnswerWithErrors",
                  ],
                  "DisplayLogic": Array [
                    "BooleanSequenceDisplayLogic",
                    "CounterTableDisplayLogic",
                    "EnumerableTableDisplayLogic",
                    "OtherDisplayLogic",
                    "QuestionListDisplayLogic",
                    "QuestionFillButtonDisplayLogic",
                    "SectionDisplayLogic",
                    "SubheaderDisplayLogic",
                    "TableDisplayLogic",
                    "ThreeColumnDisplayLogic",
                    "TwoColumnDisplayLogic",
                    "QuestionCopyButtonDisplayLogic",
                  ],
                  "Question": Array [
                    "CalculatedQuestion",
                    "CounterQuestion",
                    "DateOrEnumerableQuestion",
                    "DependentEnumerableQuestion",
                    "DependentExternalEnumerableSearchableQuestion",
                    "DependentFilteredEnumerableSearchableQuestion",
                    "DateQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "EnumerableQuestion",
                    "EnumerableCollectionQuestion",
                    "EnumerableCollectionLongListQuestion",
                    "EnumerableCollectionSearchableQuestion",
                    "EnumerableOrEnumerableCollectionQuestion",
                    "EnumerableOrOpenQuestion",
                    "EnumerableSearchableQuestion",
                    "EnumerableCollectionSearchableFavoritableQuestion",
                    "EnumerableSearchableFavoritableQuestion",
                    "GeneratedQuestion",
                    "DateQuestion",
                    "DateOrEnumerableQuestion",
                    "DateTimeOrEnumerableQuestion",
                    "DateTimeQuestion",
                    "NumberQuestion",
                    "NumberOrEnumerableQuestion",
                    "TimeStringOrEnumerableQuestion",
                    "NumberSearchableQuestion",
                    "NumberWithEnumerableQuestion",
                    "NumberWithUnitQuestion",
                    "OpenQuestion",
                    "OtherQuestion",
                    "RepeatableEnumerableQuestion",
                    "TextAreaQuestion",
                    "TimeStringQuestion",
                  ],
                  "RegistryConfigurationTableData": Array [
                    "MaintenanceData",
                    "StateData",
                    "CoCData",
                    "ExtractData",
                    "ConversionData",
                    "ImportData",
                  ],
                  "Rule": Array [
                    "EnumerableEqualityRule",
                    "OtherRule",
                    "UnconditionalRule",
                    "EnumerableInclusionRule",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "DisplayLogic" => Set {},
                "BooleanSequenceDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "CounterTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "EnumerableTableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "OtherDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionListDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionFillButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SectionDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "SubheaderDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TableDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "ThreeColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "TwoColumnDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "QuestionCopyButtonDisplayLogic" => Set {
                  "DisplayLogic",
                },
                "Question" => Set {},
                "CalculatedQuestion" => Set {
                  "Question",
                },
                "CounterQuestion" => Set {
                  "Question",
                },
                "DateOrEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentEnumerableQuestion" => Set {
                  "Question",
                },
                "DependentExternalEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DependentFilteredEnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "DateQuestion" => Set {
                  "Question",
                },
                "DateTimeOrEnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionLongListQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableOrEnumerableCollectionQuestion" => Set {
                  "Question",
                },
                "EnumerableOrOpenQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableQuestion" => Set {
                  "Question",
                },
                "EnumerableCollectionSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "EnumerableSearchableFavoritableQuestion" => Set {
                  "Question",
                },
                "GeneratedQuestion" => Set {
                  "Question",
                },
                "DateTimeQuestion" => Set {
                  "Question",
                },
                "NumberQuestion" => Set {
                  "Question",
                },
                "NumberOrEnumerableQuestion" => Set {
                  "Question",
                },
                "TimeStringOrEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberSearchableQuestion" => Set {
                  "Question",
                },
                "NumberWithEnumerableQuestion" => Set {
                  "Question",
                },
                "NumberWithUnitQuestion" => Set {
                  "Question",
                },
                "OpenQuestion" => Set {
                  "Question",
                },
                "OtherQuestion" => Set {
                  "Question",
                },
                "RepeatableEnumerableQuestion" => Set {
                  "Question",
                },
                "TextAreaQuestion" => Set {
                  "Question",
                },
                "TimeStringQuestion" => Set {
                  "Question",
                },
                "Rule" => Set {},
                "EnumerableEqualityRule" => Set {
                  "Rule",
                },
                "OtherRule" => Set {
                  "Rule",
                },
                "UnconditionalRule" => Set {
                  "Rule",
                },
                "EnumerableInclusionRule" => Set {
                  "Rule",
                },
                "BaseAnswer" => Set {},
                "Answer" => Set {
                  "BaseAnswer",
                },
                "AnswerWithWarnings" => Set {
                  "BaseAnswer",
                },
                "AnswerWithErrors" => Set {
                  "BaseAnswer",
                },
                "RegistryConfigurationTableData" => Set {},
                "MaintenanceData" => Set {
                  "RegistryConfigurationTableData",
                },
                "StateData" => Set {
                  "RegistryConfigurationTableData",
                },
                "CoCData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ExtractData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ConversionData" => Set {
                  "RegistryConfigurationTableData",
                },
                "ImportData" => Set {
                  "RegistryConfigurationTableData",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "storeReader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
            "storeWriter": StoreWriter {
              "cache": [Circular],
              "fragments": undefined,
              "reader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
            },
            "txCount": 0,
            "watches": Set {},
          },
          "client": [Circular],
          "selectionsToResolveCache": WeakMap {},
        },
        "mutationIdCounter": 1,
        "onBroadcast": undefined,
        "queries": Map {},
        "queryDeduplication": true,
        "queryIdCounter": 1,
        "requestIdCounter": 1,
        "ssrMode": false,
        "transformCache": WeakCache {
          "dispose": [Function],
          "finalizationScheduled": false,
          "finalize": [Function],
          "map": WeakMap {},
          "max": 2000,
          "newest": null,
          "oldest": null,
          "registry": FinalizationRegistry {},
          "set": [Function],
          "size": 0,
          "unfinalizedNodes": Set {},
        },
      },
      "reFetchObservableQueries": [Function],
      "resetStore": [Function],
      "resetStoreCallbacks": Array [],
      "typeDefs": undefined,
      "version": "3.10.5",
      "watchFragment": [Function],
      "watchQuery": [Function],
    }
  }
>
  <AbstractionRouter
    id={1}
  />
</ApolloProvider>
`;
