import ValidationErrorsPanel from "..";
import { create } from "react-test-renderer";
import { decoratedWithDispatch } from "utils/test/decorated";
import { MemoryRouter } from "react-router-dom";

describe("ValidationErrorsPanel", () => {
  const render = cmp => create(<MemoryRouter>{cmp}</MemoryRouter>);

  test("renders ValidationErrorsPanel", () => {
    const { component } = decoratedWithDispatch(
      ValidationErrorsPanel,
      {},
      {
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [{ id: 0, failureMessage: "Cannot be blank!" }]
          },
          error: {},
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    const renderedComponent = render(component);

    expect(renderedComponent).toMatchSnapshot();
  });
});
