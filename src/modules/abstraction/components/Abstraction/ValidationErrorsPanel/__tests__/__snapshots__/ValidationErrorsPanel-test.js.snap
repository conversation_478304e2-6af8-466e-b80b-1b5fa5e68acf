// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ValidationErrorsPanel renders ValidationErrorsPanel 1`] = `
Array [
  <div
    className="error-panel-header-container"
  >
    <i
      className="fa-solid fa-arrow-left"
      onClick={[Function]}
    />
    <h3
      className="error-panel-header"
    >
      Date of Birth
    </h3>
  </div>,
  <div>
    <div
      className="validation-errors-container"
      onClick={[Function]}
    >
      <p
        className="validation-error"
      >
        Cannot be blank!
      </p>
      <i
        className="fa-solid fa-angle-right"
      />
    </div>
  </div>,
]
`;
