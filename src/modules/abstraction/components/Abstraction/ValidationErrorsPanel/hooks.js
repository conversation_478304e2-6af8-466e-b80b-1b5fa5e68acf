import { useDispatch, useSelector } from "react-redux";
import ValidationReportState from "modules/abstraction/redux/selectors";
import { prop } from "ramda";
import {
  setErrorSidePanel2,
  setError
} from "modules/abstraction/redux/actions";

export const useComponentLogic = _props => {
  const errorPrompt = useSelector(state =>
    ValidationReportState.getErrorPrompt(state)
  );
  const errors = useSelector(state => ValidationReportState.getErrors(state));

  const errorsByPrompt = prop(errorPrompt)(errors);

  const dispatch = useDispatch();

  const handleClickLink = issue => {
    dispatch(setErrorSidePanel2(true));
    dispatch(setError(issue));
  };

  return { errorPrompt, errorsByPrompt, handleClickLink };
};
