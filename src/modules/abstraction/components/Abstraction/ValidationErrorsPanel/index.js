import { setErrorSidePanel } from "modules/abstraction/redux/actions";
import { and, map } from "ramda";
import { useComponentLogic } from "./hooks";
import classnames from "classnames";
import ErrorPanelHeader from "../ErrorPanel/ErrorPanelHeader";

const ValidationErrorsPanel = () => {
  const { errorPrompt, errorsByPrompt, handleClickLink } = useComponentLogic();

  return (
    <>
      <ErrorPanelHeader
        setter={setErrorSidePanel(false)}
        header={errorPrompt}
      />
      <div>
        {and(errorPrompt, errorsByPrompt) &&
          map(issue => {
            const {
              error = false,
              warning = false,
              id,
              failureMessage
            } = issue;
            const errorClass = classnames("validation-error", {
              error,
              warning
            });

            return (
              <div
                key={id}
                className="validation-errors-container"
                onClick={() => handleClickLink(issue)}
              >
                <p className={errorClass}>{failureMessage}</p>
                <i className="fa-solid fa-angle-right" />
              </div>
            );
          })(errorsByPrompt)}
      </div>
    </>
  );
};

export default ValidationErrorsPanel;
