import shallowRender from "utils/shallowRender";
import Field from "..";

describe("Field", () => {
  const answerGroup = { id: "1", groupId: "1" };
  const question1 = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open"
  };
  const question2 = {
    id: "2",
    groupId: "1",
    prompt: "test question 2",
    type: "Unimplemented"
  };

  const getComponent = question =>
    shallowRender(
      <Field
        {...{
          question,
          answerGroup
        }}
      />
    );

  test("renders component with implemented type (Open)", () => {
    const result = getComponent(question1);

    expect(result).toMatchSnapshot();
  });

  test("renders component with unimplemented type", () => {
    const result = getComponent(question2);

    expect(result).toMatchSnapshot();
  });
});
