// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Field renders component with implemented type (Open) 1`] = `
<div
  className=""
>
  <Open
    answerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    question={
      Object {
        "groupId": "1",
        "id": "1",
        "prompt": "test question 1",
        "type": "Open",
      }
    }
  />
</div>
`;

exports[`Field renders component with unimplemented type 1`] = `
<div
  className=""
>
  <Unimplemented
    answerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    question={
      Object {
        "groupId": "1",
        "id": "2",
        "prompt": "test question 2",
        "type": "Unimplemented",
      }
    }
  />
</div>
`;
