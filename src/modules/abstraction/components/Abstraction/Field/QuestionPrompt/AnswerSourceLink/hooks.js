import { useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import {
  toggleAnswerSource,
  setCurrentActivePanel
} from "modules/questionnaire/actions";
import { both, identity, prop } from "ramda";

export const useComponentLogic = props => {
  const { name } = props;
  const dispatch = useDispatch();
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const hasAnswerSource = useMemo(
    () => both(identity, prop("hasAnswerSource"))(answer),
    [answer]
  );
  const openAnswerSource = useCallback(
    event => {
      event.preventDefault();
      dispatch(toggleAnswerSource(answer));
      dispatch(setCurrentActivePanel(panelTypes.prepopPanel));
    },
    [answer, dispatch]
  );

  return { hasAnswerSource, openAnswerSource };
};
