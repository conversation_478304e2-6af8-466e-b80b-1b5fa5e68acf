import { useComponentLogic } from "./hooks";

export const AnswerSourceLink = props => {
  const { hasAnswerSource, openAnswerSource } = useComponentLogic(props);

  if (!hasAnswerSource) return null;

  return (
    <div className="question-help" onClick={openAnswerSource}>
      <i id="help-icon" className="fal fa-file-alt">
        <span className="text-content">Prepopulated</span>
      </i>
    </div>
  );
};

export default AnswerSourceLink;
