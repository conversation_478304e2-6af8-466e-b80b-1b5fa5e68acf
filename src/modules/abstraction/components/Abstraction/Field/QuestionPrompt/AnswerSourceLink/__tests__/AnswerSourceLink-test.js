import { act, create } from "react-test-renderer";
import { decoratedWithDispatch, decorated } from "utils/test/decorated";
import AnswerSourceLink from "..";
import panelTypes from "modules/questionnaire/constants/panelTypes";

import {
  toggleAnswerSource,
  setCurrentActivePanel
} from "modules/questionnaire/actions";

describe("AnswerSourceLink", () => {
  function render(props = {}) {
    return create(
      decorated(
        AnswerSourceLink,
        props,
        {},
        {
          questionnaire: { answers: { "100|1": { hasAnswerSource: true } } }
        }
      )
    );
  }

  test("renders AnswerSourceLink", () => {
    const answerSourceLink = render({ name: "100|1" });

    expect(answerSourceLink).toMatchSnapshot();
  });

  test("renders AnswerSourceLink when no answer in props", () => {
    const answerSourceLink = render({});

    expect(answerSourceLink).toMatchSnapshot();
  });

  test("testing dispatch", () => {
    const preventDefault = jest.fn();
    const { component, dispatch } = decoratedWithDispatch(
      AnswerSourceLink,
      { name: "100|1" },
      {
        questionnaire: { answers: { "100|1": { hasAnswerSource: true } } }
      }
    );
    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const button = instance.findByProps({ className: "question-help" });

    act(() => button.props.onClick({ preventDefault }));

    expect(dispatch).toHaveBeenCalledWith(
      toggleAnswerSource({ hasAnswerSource: true })
    );
    expect(dispatch).toHaveBeenCalledWith(
      setCurrentActivePanel(panelTypes.prepopPanel)
    );
    expect(preventDefault).toHaveBeenCalled();
  });
});
