import TestRenderer from "react-test-renderer";
import { decorated } from "utils/test/decorated";
import QuestionPrompt from "..";

describe("QuestionPrompt", () => {
  const questionSimple = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open"
  };

  const questionwithEmphasis = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open",
    emphasis: true
  };

  const getComponent = (question = questionSimple, longPrompt) =>
    decorated(
      QuestionPrompt,
      {
        question,
        helpInfo: { test: "help text" },
        name: "1|1",
        longPrompt
      },
      null,
      {}
    );

  test("renders component correctly", () => {
    const result = TestRenderer.create(getComponent());

    expect(result).toMatchSnapshot();
  });

  test("correct class is applied when emphasis is true", () => {
    const result = TestRenderer.create(getComponent(questionwithEmphasis));

    expect(result).toMatchSnapshot();
  });

  test("renders component with longPrompt true", () => {
    const result = TestRenderer.create(getComponent(questionSimple, true));

    expect(result).toMatchSnapshot();
  });
});
