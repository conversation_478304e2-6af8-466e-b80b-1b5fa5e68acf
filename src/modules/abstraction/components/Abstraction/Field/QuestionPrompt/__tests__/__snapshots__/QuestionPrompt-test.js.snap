// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QuestionPrompt correct class is applied when emphasis is true 1`] = `
<div
  className="question-prompt core-question"
>
  <div
    className="menu-container"
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
  >
    <div
      className="menu-button"
      onClick={[Function]}
    >
      <i
        className="far fa-ellipsis-v"
      />
    </div>
  </div>
  <div
    className=""
  >
    <div
      className="prompt"
    >
      test question 1
    </div>
    <div
      className="pulse"
    />
  </div>
</div>
`;

exports[`QuestionPrompt renders component correctly 1`] = `
<div
  className="question-prompt"
>
  <div
    className="menu-container"
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
  >
    <div
      className="menu-button"
      onClick={[Function]}
    >
      <i
        className="far fa-ellipsis-v"
      />
    </div>
  </div>
  <div
    className=""
  >
    <div
      className="prompt"
    >
      test question 1
    </div>
  </div>
</div>
`;

exports[`QuestionPrompt renders component with longPrompt true 1`] = `
<div
  className="question-prompt long-prompt"
>
  <div
    className="menu-container"
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
  >
    <div
      className="menu-button"
      onClick={[Function]}
    >
      <i
        className="far fa-ellipsis-v"
      />
    </div>
  </div>
  <div
    className=""
  >
    <div
      className="prompt"
    >
      test question 1
    </div>
  </div>
</div>
`;
