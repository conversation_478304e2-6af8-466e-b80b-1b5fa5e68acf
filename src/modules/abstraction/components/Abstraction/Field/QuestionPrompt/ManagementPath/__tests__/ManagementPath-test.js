import { create } from "react-test-renderer";

import ManagementPath from "..";

describe("ManagementPath", () => {
  const questionWithoutManagementPath = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open"
  };

  const questionWithManagementPath = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open",
    managementPath: "https://localhost:3000"
  };

  function render() {
    return create(
      <ManagementPath question={questionWithoutManagementPath} name="1|1" />
    );
  }

  test("renders component correctly", () => {
    const managementPath = render();

    expect(managementPath).toMatchSnapshot();
  });

  function renderQuestionWithManagementPath() {
    return create(
      <ManagementPath question={questionWithManagementPath} name="1|1" />
    );
  }

  test("renders component correctly with Management Path", () => {
    const managementPath = renderQuestionWithManagementPath();

    expect(managementPath).toMatchSnapshot();
  });
});
