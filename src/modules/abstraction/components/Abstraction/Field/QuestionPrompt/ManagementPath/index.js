export const ManagementPath = props => {
  const { question = {}, name } = props;
  const { managementPath } = question;

  return (
    <>
      {managementPath && (
        <a
          href={`${managementPath}?returnUrl=${window.location}/fieldName=${name}`}
          className="management-button"
        >
          <i className="far fa-portrait">
            <span className="text-content">Configure</span>
          </i>
        </a>
      )}
    </>
  );
};

export default ManagementPath;
