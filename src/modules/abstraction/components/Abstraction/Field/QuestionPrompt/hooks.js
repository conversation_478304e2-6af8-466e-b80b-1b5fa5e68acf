import { useCallback } from "react";
import { useDispatch } from "react-redux";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import {
  openHelpInfo,
  setCurrentActivePanel
} from "modules/questionnaire/actions";
import { or } from "ramda";

export const useComponentLogic = props => {
  const { question, helpInfo } = props;
  const dispatch = useDispatch();
  const handleHelpClick = useCallback(
    event => {
      event.preventDefault();
      if (or(helpInfo, question.hasHelpInformation)) {
        dispatch(openHelpInfo({ helpInfo, question }));
      } else {
        dispatch(openHelpInfo());
      }
      dispatch(setCurrentActivePanel(panelTypes.helpPanel));
    },
    [question, helpInfo, dispatch]
  );

  return { handleHelpClick };
};
