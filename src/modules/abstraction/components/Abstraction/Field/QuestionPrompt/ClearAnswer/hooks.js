import { useSelector } from "react-redux";
import { useClearAnswer } from "modules/fields/components/hooks/useValue";
import Questions from "modules/question/selectors";

export const useComponentLogic = props => {
  const { name } = props;
  const { handleClear } = useClearAnswer(props);
  const disabled = useSelector(
    state => !Questions.hasVisibleQuestion(name, state)
  );

  return { disabled, handleClear };
};
