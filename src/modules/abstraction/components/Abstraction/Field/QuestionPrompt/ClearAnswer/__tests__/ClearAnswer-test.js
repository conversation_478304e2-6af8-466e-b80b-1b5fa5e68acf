import ClearAnswer from "..";
import {
  decorated<PERSON><PERSON>lo,
  decoratedApolloWithDispatch
} from "utils/test/decorated";
import { act, create } from "react-test-renderer";
import { setFieldValue } from "modules/question/actions";

jest.mock("modules/question/components/Question/processAnswer", () => ({
  useProcessAnswer: () => jest.fn()
}));

describe("ClearAnswer", () => {
  function render(name = "1|1") {
    return create(
      decoratedApollo({
        component: ClearAnswer,
        props: { name },
        initialValues: {},
        initialAppValues: {
          questionnaire: { answers: { "100|1": { hasAnswerSource: true } } },
          questions: {
            visibleQuestions: {
              "1|1": { id: "1|1" },
              "3|1": { id: "3|1" },
              "1|2": { id: "1|2" }
            }
          }
        },
        apolloMocks: []
      })
    );
  }

  test("renders component correctly", () => {
    const clearAnswer = render();

    expect(clearAnswer).toMatchSnapshot();
  });

  test("renders component correctly when disabled", () => {
    const clearAnswer = render("1|0");

    expect(clearAnswer).toMatchSnapshot();
  });

  test("dispatches the correct action when the button is clicked", () => {
    const preventDefault = jest.fn();
    const { component, dispatch } = decoratedApolloWithDispatch({
      component: ClearAnswer,
      props: { question: { id: 1, type: "open" }, name: "1|1" },
      initialAppValues: {
        questionnaire: { answers: { "100|1": { hasAnswerSource: true } } },
        questions: {
          visibleQuestions: {
            "1|1": { id: "1|1" },
            "3|1": { id: "3|1" },
            "1|2": { id: "1|2" }
          }
        }
      },
      apolloMocks: []
    });

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const button = instance.findByType("i");

    act(() => button.props.onClick({ preventDefault }));
    expect(dispatch).toHaveBeenCalledWith(setFieldValue("1|1", ""));
  });
});
