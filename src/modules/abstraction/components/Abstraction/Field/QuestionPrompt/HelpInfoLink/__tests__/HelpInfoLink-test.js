import { act, create } from "react-test-renderer";
import { decorated, decoratedWithDispatch } from "utils/test/decorated";
import {
  openHelpInfo,
  setCurrentActivePanel
} from "modules/questionnaire/actions";

import panelTypes from "modules/questionnaire/constants/panelTypes";

import HelpInfoLink from "..";

describe("HelpInfoLink", () => {
  const questionSimple = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open"
  };

  const questionWithHelpInfo = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open",
    helpInfo: { test: "help text" },
    hasHelpInformation: true
  };

  function render(question = questionWithHelpInfo) {
    return create(decorated(HelpInfoLink, { question, name: "1|1" }, null, {}));
  }

  test("renders component correctly", () => {
    const helpInfoLink = render();

    expect(helpInfoLink).toMatchSnapshot();
  });

  test("calls correct actions when help icon clicked, with a help info", () => {
    const preventDefault = jest.fn();
    const helpInfo = { test: "help test" };

    const { component, dispatch } = decoratedWithDispatch(
      HelpInfoLink,
      { question: questionSimple, helpInfo },
      {}
    );

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const button = instance.findByType("i");

    act(() => button.props.onClick({ preventDefault }));
    expect(dispatch).toHaveBeenCalledWith(
      openHelpInfo({ helpInfo, question: questionSimple })
    );
    expect(dispatch).toHaveBeenCalledWith(
      setCurrentActivePanel(panelTypes.helpPanel)
    );
  });

  test("calls correct actions when help icon clicked, question with help info", () => {
    const preventDefault = jest.fn();

    const { component, dispatch } = decoratedWithDispatch(
      HelpInfoLink,
      { question: questionWithHelpInfo },
      {}
    );

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const button = instance.findByType("i");

    act(() => button.props.onClick({ preventDefault }));
    expect(dispatch).toHaveBeenCalledWith(
      openHelpInfo({ question: questionWithHelpInfo })
    );
    expect(dispatch).toHaveBeenCalledWith(
      setCurrentActivePanel(panelTypes.helpPanel)
    );
  });

  test("calls correct actions when help icon clicked, no help info", () => {
    const preventDefault = jest.fn();

    const { component, dispatch } = decoratedWithDispatch(
      HelpInfoLink,
      { question: questionSimple },
      {}
    );

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const button = instance.findByType("i");

    act(() => button.props.onClick({ preventDefault }));
    expect(dispatch).toHaveBeenCalledWith(openHelpInfo());
    expect(dispatch).toHaveBeenCalledWith(
      setCurrentActivePanel(panelTypes.helpPanel)
    );
  });
});
