import classnames from "classnames";
import ContextMenu from "./ContextMenu";

export const QuestionPrompt = props => {
  const { question = {}, longPrompt, disabled } = props;
  const { prompt } = question;
  const promptClasses = classnames("question-prompt", {
    "core-question": question.emphasis,
    "long-prompt": longPrompt
  });
  const promptTextDisabledClass = classnames({ disabled });

  return (
    <>
      <div className={promptClasses}>
        <ContextMenu {...props} />
        <div className={promptTextDisabledClass}>
          <div className="prompt">{prompt}</div>
          {question.emphasis && <div className="pulse" />}
        </div>
      </div>
    </>
  );
};

export default QuestionPrompt;
