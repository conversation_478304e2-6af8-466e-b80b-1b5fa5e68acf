import { useMemo, useState, useCallback } from "react";
import AnswerSourceLink from "../AnswerSourceLink";
import HelpInfoLink from "../HelpInfoLink";
import ManagementPath from "../ManagementPath";
import ClearAnswer from "../ClearAnswer";

export const useComponentLogic = props => {
  const {
    name,
    question,
    questionAnswerGroup,
    helpInfo,
    questionnaireResponseId
  } = props;
  const [isVisible, setIsVisible] = useState(false);

  const handleOpenMenu = useCallback(() => {
    setIsVisible(true);
  }, [setIsVisible]);

  const handleCloseMenu = useCallback(() => {
    setIsVisible(false);
  }, [setIsVisible]);

  const menuItems = useMemo(
    () => [
      {
        id: "1",
        item: <HelpInfoLink helpInfo={helpInfo} question={question} />
      },
      {
        id: "2",
        item: (
          <ClearAnswer
            question={question}
            name={name}
            questionAnswerGroup={questionAnswerGroup}
            answerGroup={questionAnswerGroup}
            questionnaireResponseId={questionnaireResponseId}
          />
        )
      },
      { id: "3", item: <ManagementPath name={name} question={question} /> },
      { id: "4", item: <AnswerSourceLink name={name} /> }
    ],
    [name, question, helpInfo]
  );

  return { menuItems, isVisible, handleOpenMenu, handleCloseMenu };
};
