import { Menu } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

export const ContextMenu = props => {
  const { menuItems, isVisible, handleOpenMenu, handleCloseMenu } =
    useComponentLogic(props);

  return (
    <>
      <div
        className="menu-container"
        onMouseEnter={handleOpenMenu}
        onMouseLeave={handleCloseMenu}
      >
        <div className="menu-button" onClick={handleOpenMenu}>
          <i className="far fa-ellipsis-v" />
        </div>
        <Menu
          className="context-menu"
          direction="down"
          open={isVisible}
          menuItems={menuItems}
          close={handleCloseMenu}
        />
      </div>
    </>
  );
};

export default ContextMenu;
