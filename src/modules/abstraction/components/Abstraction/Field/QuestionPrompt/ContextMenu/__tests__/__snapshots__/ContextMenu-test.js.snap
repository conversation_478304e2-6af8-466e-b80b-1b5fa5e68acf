// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ContextMenu renders component correctly 1`] = `
<div
  className="menu-container"
  onMouseEnter={[Function]}
  onMouseLeave={[Function]}
>
  <div
    className="menu-button"
    onClick={[Function]}
  >
    <i
      className="far fa-ellipsis-v"
    />
  </div>
  <Menu
    className="context-menu"
    close={[Function]}
    direction="down"
    menuItems={
      Array [
        Object {
          "id": "1",
          "item": <HelpInfoLink
            question={
              Object {
                "id": "42",
              }
            }
          />,
        },
        Object {
          "id": "2",
          "item": <ClearAnswer
            name="42|22"
            question={
              Object {
                "id": "42",
              }
            }
            questionnaireResponseId={5}
          />,
        },
        Object {
          "id": "3",
          "item": <ManagementPath
            name="42|22"
            question={
              Object {
                "id": "42",
              }
            }
          />,
        },
        Object {
          "id": "4",
          "item": <AnswerSourceLink
            name="42|22"
          />,
        },
      ]
    }
    open={false}
  />
</div>
`;

exports[`ContextMenu renders component on close after open 1`] = `
<div
  className="menu-container"
  onMouseEnter={[Function]}
  onMouseLeave={[Function]}
>
  <div
    className="menu-button"
    onClick={[Function]}
  >
    <i
      className="far fa-ellipsis-v"
    />
  </div>
  <Menu
    className="context-menu"
    close={[Function]}
    direction="down"
    menuItems={
      Array [
        Object {
          "id": "1",
          "item": <HelpInfoLink
            question={
              Object {
                "id": "42",
              }
            }
          />,
        },
        Object {
          "id": "2",
          "item": <ClearAnswer
            name="42|22"
            question={
              Object {
                "id": "42",
              }
            }
            questionnaireResponseId={5}
          />,
        },
        Object {
          "id": "3",
          "item": <ManagementPath
            name="42|22"
            question={
              Object {
                "id": "42",
              }
            }
          />,
        },
        Object {
          "id": "4",
          "item": <AnswerSourceLink
            name="42|22"
          />,
        },
      ]
    }
    open={false}
  />
</div>
`;

exports[`ContextMenu renders menu if the button is clicked 1`] = `
<div
  className="menu-container"
  onMouseEnter={[Function]}
  onMouseLeave={[Function]}
>
  <div
    className="menu-button"
    onClick={[Function]}
  >
    <i
      className="far fa-ellipsis-v"
    />
  </div>
  <Menu
    className="context-menu"
    close={[Function]}
    direction="down"
    menuItems={
      Array [
        Object {
          "id": "1",
          "item": <HelpInfoLink
            question={
              Object {
                "id": "42",
              }
            }
          />,
        },
        Object {
          "id": "2",
          "item": <ClearAnswer
            name="42|22"
            question={
              Object {
                "id": "42",
              }
            }
            questionnaireResponseId={5}
          />,
        },
        Object {
          "id": "3",
          "item": <ManagementPath
            name="42|22"
            question={
              Object {
                "id": "42",
              }
            }
          />,
        },
        Object {
          "id": "4",
          "item": <AnswerSourceLink
            name="42|22"
          />,
        },
      ]
    }
    open={true}
  />
</div>
`;
