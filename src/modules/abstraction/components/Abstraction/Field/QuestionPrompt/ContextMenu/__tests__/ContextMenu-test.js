import { act, create } from "react-test-renderer";
import wait from "waait";

import { decorated } from "utils/test/decorated";

import ContextMenu from "..";

import { Menu } from "@q-centrix/q-components-react";

jest.mock("@q-centrix/q-components-react", () => ({
  Menu: "Menu"
}));

describe("ContextMenu", () => {
  function render() {
    return create(
      decorated(
        ContextMenu,
        {
          questionnaireResponseId: 5,
          answerGroup: { id: 1, groupId: 1 }
        },
        {},
        {
          questionnaire: { answers: { "100|1": { hasAnswerSource: true } } },
          questions: {
            visibleQuestions: {
              "1|1": { id: "1|1" },
              "3|1": { id: "3|1" },
              "1|2": { id: "1|2" }
            }
          }
        }
      )
    );
  }

  test("renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("renders menu if the button is clicked", async () => {
    const component = render();
    const instance = component.root;
    const button = instance.findByProps({ className: "menu-button" });

    act(() => button.props.onClick({ preventDefault: jest.fn() }));
    await act(() => wait(3));
    expect(component).toMatchSnapshot();
  });

  test("renders component on close after open", async () => {
    const component = render();
    const instance = component.root;
    const menu = instance.findByType(Menu);
    const button = instance.findByProps({ className: "menu-button" });

    act(() => button.props.onClick({ preventDefault: jest.fn() }));
    await act(() => wait(3));
    act(() => menu.props.close());
    expect(component).toMatchSnapshot();
  });
});
