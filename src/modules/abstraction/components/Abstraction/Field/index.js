import { __, compose, defaultTo, prop } from "ramda";
import classnames from "classnames";
// import { HotKeys } from "react-hotkeys";
import Calculated from "modules/fields/components/Calculated";
import Counter from "modules/fields/components/Counter";
import Date from "modules/fields/components/Date";
import DateOrEnumerable from "modules/fields/components/DateOrEnumerable";

// import DateOrEnumerable from "./DateOrEnumerable";
// import DateTime from "./DateTime";
// import DateTimeOrEnumerable from "./DateTimeOrEnumerable";
// import DependentEnumerable from "./DependentEnumerable";
import DependentExternalEnumerableSearchable from "modules/fields/components/DependentExternalEnumerableSearchable";
import DependentFilteredEnumerableSearchable from "modules/fields/components/DependentFilteredEnumerableSearchable";
import Enumerable from "modules/fields/components/Enumerable";
import EnumerableCollection from "modules/fields/components/EnumerableCollection";
// import EnumerableCollectionLongList from "./EnumerableCollectionLongList";
// import EnumerableCollectionSearchable from "./EnumerableCollectionSearchable";
// import EnumerableOrEnumerableCollection from "./EnumerableOrEnumerableCollection";
import EnumerableOrOpen from "modules/fields/components/EnumerableOrOpen";
import EnumerableSearchable from "modules/fields/components/EnumerableSearchable";
import EnumerableSearchableFavoritable from "modules/fields/components/EnumerableSearchableFavoritable";
import Generated from "modules/fields/components/Generated";
import Open from "modules/fields/components/Open";
import Number from "modules/fields/components/Number";
import NumberOrEnumerable from "modules/fields/components/NumberOrEnumerable";
import NumberSearchable from "modules/fields/components/NumberSearchable";
import NumberSearchableFavoritable from "modules/fields/components/NumberSearchableFavoritable";
// import NumberWithEnumerable from "./NumberWithEnumerable";
import NumberWithUnit from "modules/fields/components/NumberWithUnit";
// import RepeatableEnumerable from "./RepeatableEnumerable";
import TextArea from "modules/fields/components/TextArea";
import TimeString from "modules/fields/components/TimeString";
import TimeStringOrEnumerable from "modules/fields/components/TimeStringOrEnumerable";

const type = compose(
  defaultTo(Unimplemented),
  prop(__, {
    Calculated,
    Counter,
    Date,
    DateOrEnumerable,
    // DateTime,
    // DateTimeOrEnumerable,
    // DependentEnumerable,
    DependentExternalEnumerableSearchable,
    DependentFilteredEnumerableSearchable,
    Enumerable,
    EnumerableCollection,
    // EnumerableCollectionLongList,
    // EnumerableCollectionSearchable,
    // EnumerableOrEnumerableCollection,
    EnumerableOrOpen,
    EnumerableSearchable,
    EnumerableSearchableFavoritable,
    Generated,
    Number,
    NumberOrEnumerable,
    TimeStringOrEnumerable,
    NumberSearchable,
    NumberSearchableFavoritable,
    // NumberWithEnumerable,
    NumberWithUnit,
    Open,
    // RepeatableEnumerable,
    TextArea,
    TimeString
  })
);

export const Field = props => {
  const { question, disabled } = props;
  const FieldType = type(question.type);
  const disabledClass = classnames({ disabled });

  return (
    <div className={disabledClass}>
      <FieldType {...props} />
    </div>
  );
};

function Unimplemented({ question }) {
  return <div style={{ color: "red" }}>Question: {question.type}</div>;
}

export default Field;
