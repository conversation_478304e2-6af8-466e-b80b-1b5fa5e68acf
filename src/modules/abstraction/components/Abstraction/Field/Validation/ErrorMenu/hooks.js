import ValidationReportState from "modules/abstraction/redux/selectors";
import Questionnaire from "modules/questionnaire/selectors";
import { useDispatch, useSelector } from "react-redux";
import { find, pipe, prop, propEq, either } from "ramda";
import {
  setErrorSidePanel,
  setErrorSidePanel2,
  setError,
  setErrorPrompt
} from "modules/abstraction/redux/actions";
import { isNullOrEmpty } from "utils/fp";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import { setCurrentActivePanel } from "modules/questionnaire/actions";

export const useComponentLogic = ({ prompt }) => {
  const errors = useSelector(state => ValidationReportState.getErrors(state));

  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );

  const isRrtQuestionnaire = either(
    prop("regulatory_administrative"),
    prop("regulatory_clinical")
  )(questionnaire);

  const dispatch = useDispatch();

  const handleClick = error => {
    if (!isNullOrEmpty(errors)) {
      dispatch(setCurrentActivePanel(panelTypes.validationReportPanel));
      dispatch(setErrorPrompt(prompt));
      dispatch(
        setError(
          pipe(prop(prompt), find(propEq("failureMessage", error)))(errors)
        )
      );
      dispatch(setErrorSidePanel(true));
      dispatch(setErrorSidePanel2(true));
    }
  };

  return {
    handleClick,
    isRrtQuestionnaire
  };
};
