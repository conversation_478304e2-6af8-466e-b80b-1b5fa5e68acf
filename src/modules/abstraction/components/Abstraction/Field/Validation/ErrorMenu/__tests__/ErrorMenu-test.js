import { create } from "react-test-renderer";
import { decoratedWithDispatch } from "utils/test/decorated";
import ErrorMenu from "..";

describe("Validation", () => {
  const render = cmp => create(cmp);

  /* eslint-disable camelcase */
  test("renders ErrorMenu", () => {
    const { component } = decoratedWithDispatch(
      ErrorMenu,
      {
        animate: false,
        updateAnimateHandler: jest.fn(),
        newValidationText: [
          "Please enter correct format",
          "Please enter correct date"
        ]
      },
      {
        questionnaire: {
          questionnaire: {
            regulatory_clinical: false,
            regulatory_administrative: false
          }
        },
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [
              { id: 0, failureMessage: "Cannot be blank!", error: true }
            ]
          },
          error: { id: 0, failureMessage: "Cannot be blank!", error: true },
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    const renderedComponent = render(component);

    expect(renderedComponent).toMatchSnapshot();
  });

  test("renders ErrorMenu in RRT Questionnaire", () => {
    const { component } = decoratedWithDispatch(
      ErrorMenu,
      {
        animate: false,
        updateAnimateHandler: jest.fn(),
        newValidationText: [
          "Please enter correct format",
          "Please enter correct date"
        ]
      },
      {
        questionnaire: {
          questionnaire: {
            regulatory_clinical: false,
            regulatory_administrative: true
          }
        },
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [
              { id: 0, failureMessage: "Cannot be blank!", error: true }
            ]
          },
          error: { id: 0, failureMessage: "Cannot be blank!", error: true },
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    /* eslint-enable camelcase */

    const renderedComponent = render(component);

    expect(renderedComponent).toMatchSnapshot();
  });
});
