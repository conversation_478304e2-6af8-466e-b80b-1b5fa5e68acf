import { motion } from "framer-motion";
import { useComponentLogic } from "./hooks";

const errorContainerVariants = {
  open: {
    opacity: 1,
    height: "auto",
    width: "350px",
    transition: {
      type: "spring",
      bounce: 0.1,
      duration: 0.2
    }
  },
  closed: {
    opacity: 0,
    height: "0px",
    width: "0px"
  }
};

const ErrorMenu = ({
  animate,
  updateAnimateHandler,
  errorMenuRef,
  newValidationText,
  prompt
}) => {
  const { handleClick, isRrtQuestionnaire } = useComponentLogic({ prompt });

  return (
    <motion.div
      variants={errorContainerVariants}
      initial="closed"
      animate={animate ? "open" : "closed"}
      onAnimationComplete={def => def === "closed" && updateAnimateHandler}
      className="error-menu"
      ref={errorMenuRef}
    >
      <ul>
        {newValidationText.map(error => (
          <li key={error} className="error-menu-item">
            {error}
            {!isRrtQuestionnaire && (
              <p className="error-menu-link" onClick={() => handleClick(error)}>
                (view description)
              </p>
            )}
          </li>
        ))}
      </ul>
    </motion.div>
  );
};

export default ErrorMenu;
