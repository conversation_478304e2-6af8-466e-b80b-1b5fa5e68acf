// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Validation renders ErrorMenu 1`] = `
<div
  className="error-menu"
  style={
    Object {
      "height": "0px",
      "opacity": 0,
      "width": "0px",
    }
  }
>
  <ul>
    <li
      className="error-menu-item"
    >
      Please enter correct format
      <p
        className="error-menu-link"
        onClick={[Function]}
      >
        (view description)
      </p>
    </li>
    <li
      className="error-menu-item"
    >
      Please enter correct date
      <p
        className="error-menu-link"
        onClick={[Function]}
      >
        (view description)
      </p>
    </li>
  </ul>
</div>
`;

exports[`Validation renders ErrorMenu in RRT Questionnaire 1`] = `
<div
  className="error-menu"
  style={
    Object {
      "height": "0px",
      "opacity": 0,
      "width": "0px",
    }
  }
>
  <ul>
    <li
      className="error-menu-item"
    >
      Please enter correct format
    </li>
    <li
      className="error-menu-item"
    >
      Please enter correct date
    </li>
  </ul>
</div>
`;
