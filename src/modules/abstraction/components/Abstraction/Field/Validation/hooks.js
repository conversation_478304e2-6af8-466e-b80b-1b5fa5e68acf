import { useCallback, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { all, identity, ifElse, includes, not, split } from "ramda";
import Questionnaire from "modules/questionnaire/selectors";
import { useIsSaving } from "modules/fields/components/hooks/useSaveAnswer";
import { useIsFocused } from "modules/fields/components/hooks/useFocus";
import { useValidation } from "modules/fields/components/hooks/useValidations";
import useOnClickOutside from "utils/hooks/useOnClickOutside";

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { name, hideIfNoValidations } = props;
  const [showErrorMenu, setShowErrorMenu] = useState(false);
  const [animate, setAnimate] = useState(false);
  const errorMenuRef = useRef();
  const errorIconRef = useRef();
  const updateErrorMenuHandler = useCallback(() => setShowErrorMenu(not), []);
  const updateAnimateHandler = useCallback(() => setAnimate(not), []);
  const answer = useSelector(state => Questionnaire.getAnswer(name, state));
  const hasValidationReport = useSelector(state =>
    Questionnaire.hasValidationReport(state)
  );
  const isSaving = useIsSaving(name);
  const isFocused = useIsFocused(name);
  const {
    hasValidationErrors,
    validationErrorsCount,
    validationWarningsCount,
    hasValidationWarnings,
    validationText
  } = useValidation(name);
  const showFocused = all(identity, [!isSaving, isFocused]);
  const showGood = all(identity, [
    answer,
    hasValidationReport,
    !isSaving,
    !hasValidationErrors,
    !hasValidationWarnings,
    !isFocused
  ]);
  const showError = all(identity, [!isSaving, hasValidationErrors]);
  const showWarning = all(identity, [
    !isSaving,
    !hasValidationErrors,
    validationWarningsCount,
    hasValidationWarnings
  ]);
  const hide = all(identity, [
    hideIfNoValidations,
    !hasValidationErrors,
    !hasValidationWarnings
  ]);

  const newValidationText = ifElse(includes("<br />"), split("<br />"), () => [
    validationText
  ])(validationText);

  const animateDelayCloseHandler = () => {
    if (showErrorMenu) {
      updateAnimateHandler();
      setTimeout(() => {
        updateErrorMenuHandler();
      }, 200);
    }
  };

  useOnClickOutside(errorMenuRef, animateDelayCloseHandler, e =>
    errorIconRef.current.contains(e.target)
  );

  const iconErrorMenuHandler = () => {
    if (animate) {
      animateDelayCloseHandler();
    } else {
      updateErrorMenuHandler();
      updateAnimateHandler();
    }
  };

  return {
    hide,
    isSaving,
    showGood,
    showFocused,
    showError,
    validationErrorsCount,
    validationWarningsCount,
    showWarning,
    showErrorMenu,
    newValidationText,
    errorMenuRef,
    errorIconRef,
    animate,
    updateAnimateHandler,
    iconErrorMenuHandler
  };
};
