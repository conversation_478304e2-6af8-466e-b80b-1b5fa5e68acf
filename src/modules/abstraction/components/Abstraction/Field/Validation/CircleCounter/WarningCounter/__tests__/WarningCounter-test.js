import { create } from "react-test-renderer";
import WarningCounter from "..";

describe("Validation", () => {
  function render(shouldShow = true) {
    return create(<WarningCounter shouldShow={shouldShow} count={1} />);
  }

  test("renders CircleCounter if shouldShow true", () => {
    const circleCounterMockComponent = render();

    expect(circleCounterMockComponent).toMatchSnapshot();
  });
  test("does not render CircleCounter if shouldShow is false", () => {
    const circleCounterMockComponent = render(false);

    expect(circleCounterMockComponent).toMatchSnapshot();
  });
});
