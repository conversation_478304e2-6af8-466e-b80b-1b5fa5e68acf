import { create } from "react-test-renderer";
import ErrorCounter from "..";

describe("Validation", () => {
  function render(showError = true) {
    return create(
      <ErrorCounter
        showError={showError}
        validationErrorsCount={1}
        validationWarningsCount={1}
      />
    );
  }

  test("renders CircleCounter if shouldShow true", () => {
    const circleCounterMockComponent = render();

    expect(circleCounterMockComponent).toMatchSnapshot();
  });
  test("does not render CircleCounter if shouldShow is false", () => {
    const circleCounterMockComponent = render(false);

    expect(circleCounterMockComponent).toMatchSnapshot();
  });
});
