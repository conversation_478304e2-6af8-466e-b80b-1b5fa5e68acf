// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Validation error menu is closed when x icon is clicked a second time 1`] = `
<div
  className="field-validation"
>
  <p
    className="number-circle"
  >
    2
  </p>
  <i
    className="far fa-times-circle validation-error"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders edit icon when field is focused 1`] = `
<div
  className="field-validation"
>
  <i
    className="far fa-edit focused"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders error menu when x icon is clicked 1`] = `
<div
  className="field-validation"
>
  <div
    animate="open"
    className="mock-motion-div error-menu"
    initial="closed"
    onAnimationComplete={[Function]}
    variants={
      Object {
        "closed": Object {
          "height": "0px",
          "opacity": 0,
          "width": "0px",
        },
        "open": Object {
          "height": "auto",
          "opacity": 1,
          "transition": Object {
            "bounce": 0.1,
            "duration": 0.2,
            "type": "spring",
          },
          "width": "350px",
        },
      }
    }
  >
    <ul>
      <li
        className="error-menu-item"
      >
        must be present
        <p
          className="error-menu-link"
          onClick={[Function]}
        >
          (view description)
        </p>
      </li>
      <li
        className="error-menu-item"
      >
        should be present
        <p
          className="error-menu-link"
          onClick={[Function]}
        >
          (view description)
        </p>
      </li>
    </ul>
  </div>
  <p
    className="number-circle"
  >
    2
  </p>
  <i
    className="far fa-times-circle validation-error"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders null when field no validations and no answer and hideIfNoValidations = true 1`] = `null`;

exports[`Validation renders spinner icon when field is saving 1`] = `
<div
  className="field-validation"
>
  <i
    className="far fa-spinner fa-spin"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders warning icon when field is not focused, has warnings but no errors  1`] = `
<div
  className="field-validation"
>
  <p
    className="number-circle"
  >
    1
  </p>
  <i
    className="far fa-exclamation-triangle validation-warning"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders without a defined icon when field is not focused, no validations and no answer 1`] = `
<div
  className="field-validation"
>
  <i
    className="far"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders without a defined icon when field is not focused, no validations and no validation report 1`] = `
<div
  className="field-validation"
>
  <i
    className="far"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders x icon when field is focused and has error 1`] = `
<div
  className="field-validation"
>
  <p
    className="number-circle"
  >
    1
  </p>
  <i
    className="far fa-times-circle validation-error"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders x icon when field is focused and has warning 1`] = `
<div
  className="field-validation"
>
  <p
    className="number-circle"
  >
    1
  </p>
  <i
    className="far fa-exclamation-triangle validation-warning"
    onClick={[Function]}
  />
</div>
`;

exports[`Validation renders x icon when field is not focused and has errors 1`] = `
<div
  className="field-validation"
>
  <p
    className="number-circle"
  >
    2
  </p>
  <i
    className="far fa-times-circle validation-error"
    onClick={[Function]}
  />
</div>
`;
