import { always, evolve, assocPath } from "ramda";
import TestRenderer, { act } from "react-test-renderer";
import { decoratedWithDispatch } from "utils/test/decorated";
import wait from "waait";
import Validation from "..";

describe("Validation", () => {
  const appState = {
    abstractionValidationReport: {
      errorSidePanelActive: false,
      errorSidePanelActive2: false,
      errors: {
        "Date of Birth": [
          { id: 0, failureMessage: "Cannot be blank!", error: true }
        ]
      },
      error: { id: 0, failureMessage: "Cannot be blank!", error: true },
      errorPrompt: "Date of Birth",
      errorUrl: null
    },
    questionnaire: {
      validationReport: { id: "1" },
      answers: { "2|1": { id: "1" } }
    },
    questions: {
      validationFailures: {
        "1|1": [
          { error: false, failureMessage: "" },
          { warning: false, failureMessage: "" }
        ],
        "3|1": [{ warning: true, failureMessage: "be present" }],
        "4|1": [
          { error: true, failureMessage: "be present" },
          { warning: true, failureMessage: "be present" }
        ],
        "5|1": [
          { error: true, failureMessage: "be present" },
          { warning: true, failureMessage: "be present" }
        ]
      },
      focusedFieldName: "1|1"
    },
    status: { mutationLoading: { "5|1": true } }
  };
  const getComponent = (
    { name, hideIfNoValidations = false },
    state = appState
  ) =>
    decoratedWithDispatch(
      Validation,
      {
        name,
        hideIfNoValidations
      },
      state
    );

  test("renders edit icon when field is focused", () => {
    const { component } = getComponent({ name: "1|1" });
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test("renders warning icon when field is not focused, has warnings but no errors ", () => {
    const { component } = getComponent({ name: "3|1" });
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test("renders x icon when field is not focused and has errors", () => {
    const { component } = getComponent({ name: "4|1" });
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test("renders x icon when field is focused and has error", () => {
    const errorState = [{ error: true, failureMessage: "be present" }];
    const focusedWithErrorState = assocPath(
      ["questions", "validationFailures", "1|1"],
      errorState,
      appState
    );
    const { component } = getComponent({ name: "1|1" }, focusedWithErrorState);
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });
  test("renders x icon when field is focused and has warning", () => {
    const warningState = [{ warning: true, failureMessage: "be present" }];
    const focusedWithErrorState = assocPath(
      ["questions", "validationFailures", "1|1"],
      warningState,
      appState
    );

    const { component } = getComponent({ name: "1|1" }, focusedWithErrorState);
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });
  test("renders spinner icon when field is saving", () => {
    const { component } = getComponent({ name: "5|1" });
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test("renders without a defined icon when field is not focused, no validations and no answer", () => {
    // remove the validation report from original state
    const state = evolve({ questionnaire: always({}) }, appState);
    const { component } = getComponent({ name: "6|1" }, state);
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test("renders without a defined icon when field is not focused, no validations and no validation report", () => {
    // remove the validation report from original state
    const state = evolve({ questionnaire: always({}) }, appState);
    const { component } = getComponent({ name: "2|1" }, state);
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test("renders null when field no validations and no answer and hideIfNoValidations = true", () => {
    // remove the validation report from original state
    const state = evolve({ questionnaire: always({}) }, appState);
    const { component } = getComponent(
      { name: "6|1", hideIfNoValidations: true },
      state
    );
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });

  test.skip("renders error menu when x icon is clicked", () => {
    const preventDefault = jest.fn();
    const { component } = getComponent({ name: "4|1" });
    const result = TestRenderer.create(component);

    const icon = result.root.findByType("i");

    act(() => icon.props.onClick({ preventDefault }));

    expect(result).toMatchSnapshot();
  });

  test.skip("error menu is closed when x icon is clicked a second time", async () => {
    const preventDefault = jest.fn();
    const { component } = getComponent({ name: "4|1" });
    const result = TestRenderer.create(component);

    const icon = result.root.findByType("i");

    act(() => icon.props.onClick({ preventDefault }));
    await act(() => wait(100));
    act(() => icon.props.onClick({ preventDefault }));
    await act(() => wait(200));

    expect(result).toMatchSnapshot();
  });
});
