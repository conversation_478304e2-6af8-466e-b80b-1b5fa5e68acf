import classnames from "classnames";
import { useComponentLogic } from "./hooks";
import ErrorMenu from "./ErrorMenu";
import ErrorCounter from "./CircleCounter/ErrorCounter";
import WarningCounter from "./CircleCounter/WarningCounter";

// eslint-disable-next-line complexity
export const Validation = props => {
  const { prompt } = props;
  const {
    hide,
    isSaving,
    showFocused,
    showWarning,
    validationWarningsCount,
    validationErrorsCount,
    showError,
    showErrorMenu,
    newValidationText,
    errorMenuRef,
    errorIconRef,
    animate,
    updateAnimateHandler,
    iconErrorMenuHandler
  } = useComponentLogic(props);

  const indicatorClassIcon = classnames("far", {
    "fa-spinner": isSaving,
    "fa-spin": isSaving,
    "fa-times-circle": showError,
    "validation-error": showError,
    "fa-exclamation-triangle": showWarning,
    "validation-warning": showWarning,
    "fa-edit": showFocused && !showError && !showWarning,
    focused: showFocused && !showError && !showWarning
  });

  if (hide) return null;

  return (
    <div className="field-validation">
      {showErrorMenu && (
        <ErrorMenu
          errorMenuRef={errorMenuRef}
          newValidationText={newValidationText}
          animate={animate}
          updateAnimateHandler={updateAnimateHandler}
          prompt={prompt}
        />
      )}
      <ErrorCounter
        showError={showError}
        validationErrorsCount={validationErrorsCount}
        validationWarningsCount={validationWarningsCount}
      />
      <WarningCounter
        shouldShow={showWarning}
        count={validationWarningsCount}
      />
      <i
        ref={errorIconRef}
        className={indicatorClassIcon}
        onClick={iconErrorMenuHandler}
      />
    </div>
  );
};

export default Validation;
