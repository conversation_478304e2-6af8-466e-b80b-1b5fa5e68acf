import classnames from "classnames";
// import { HotKeys } from "react-hotkeys";
import { Navbar, Footer, Sidenav } from "@q-centrix/q-components-react";
import SidePane from "./SidePane";
// import keyMap from "modules/questionnaire/constants/keyMap";
import PersistentData from "./PersistentData";
import QuestionnaireBody from "./QuestionnaireBody";
import QuestionnaireHeader from "./QuestionnaireHeader";
import { useComponentLogic } from "./hooks";

import "../../styles/abstraction.scss";
import ValidateSection from "./ValidateSection";

// eslint-disable-next-line complexity
export function Abstraction({
  currentAnswerGroup,
  questionnaireResponseId,
  focusFieldName
}) {
  const { isSidePanelExpanded } = useComponentLogic();
  const mainClasses = classnames("abstraction", {
    expanded: isSidePanelExpanded
  });
  const itemClass = classnames("content-wrapper", {
    "expanded-side": isSidePanelExpanded
  });

  return (
    <main className={mainClasses}>
      <Navbar />
      <div className="body">
        <Sidenav />
        <SidePane
          currentAnswerGroupId={currentAnswerGroup}
          questionnaireResponseId={questionnaireResponseId}
          isSidePanelExpanded={isSidePanelExpanded}
        />
        <div className={itemClass}>
          <div className="tw-flex tw-flex-grow tw-grow-0 tw-basis-[61px]">
            <PersistentData />
            <ValidateSection
              questionnaireResponseId={questionnaireResponseId}
            />
          </div>
          <QuestionnaireHeader
            currentAnswerGroup={currentAnswerGroup}
            questionnaireResponseId={questionnaireResponseId}
          />
          <QuestionnaireBody
            currentAnswerGroup={currentAnswerGroup}
            questionnaireResponseId={questionnaireResponseId}
            focusFieldName={focusFieldName}
          />
        </div>
      </div>
      <Footer />
    </main>
  );
}

export default Abstraction;
