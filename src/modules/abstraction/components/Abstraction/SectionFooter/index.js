import NextSection from "./NextSection";
import PreviousSection from "./PreviousSection";

export const SectionFooter = ({
  currentAnswerGroupId,
  questionnaireResponseId
}) => (
  <div className="questionnaire-actions">
    <div className="professional-societies-validations">
      <fieldset>&nbsp;</fieldset>
      <fieldset className="actions">
        <div className="navigation-btn-group">
          <PreviousSection
            currentAnswerGroupId={currentAnswerGroupId}
            questionnaireResponseId={questionnaireResponseId}
          />
          <NextSection
            currentAnswerGroupId={currentAnswerGroupId}
            questionnaireResponseId={questionnaireResponseId}
          />
        </div>
      </fieldset>
    </div>
  </div>
);

export default SectionFooter;
