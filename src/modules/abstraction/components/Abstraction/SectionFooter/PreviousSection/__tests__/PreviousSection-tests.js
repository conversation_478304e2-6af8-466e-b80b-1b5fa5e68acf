import TestRenderer from "react-test-renderer";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import { PreviousSection } from "..";

jest.mock("modules/question/selectors", () => ({
  getVisibleQuestions() {
    return [];
  }
}));

jest.mock("modules/questionnaire/selectors", () => ({
  getPreviousSection() {
    return {
      descendants: [],
      groupId: "155",
      id: "725",
      parentId: null
    };
  }
}));
describe("PreviousSection", () => {
  function render() {
    return TestRenderer.create(
      <Provider
        store={getStore(
          {},
          {
            questionnaire: {
              ...initialState,
              visibleQuestions: [],
              sections: []
            }
          }
        )}
      >
        <MemoryRouter>
          <PreviousSection questionnaireResponseId={1} />
        </MemoryRouter>
      </Provider>
    );
  }
  test("it renders component correctly", () => {
    const result = render();

    expect(result).toMatchSnapshot();
  });
});
