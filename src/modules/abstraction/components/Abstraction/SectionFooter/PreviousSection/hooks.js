import { useMemo, useCallback } from "react";
import { useHistory } from "react-router-dom";
import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import Questions from "modules/question/selectors";
import { curry, identity, ifElse, isNil, pipe, prop } from "ramda";
import { useLinkButtonKeyboard } from "modules/abstraction/hooks";

export const getUrl = curry(
  (questionnaireResponseId, id) =>
    `/abstraction/${questionnaireResponseId}/section/${id}`
);

export const useComponentLogic = ({
  currentAnswerGroupId,
  questionnaireResponseId
}) => {
  const previousSection = useSelector(state =>
    Questionnaire.getPreviousSection(
      currentAnswerGroupId,
      Questions.getVisibleQuestions(state),
      state
    )
  );

  const { history } = useHistory();

  const url = useMemo(
    () =>
      ifElse(
        isNil,
        identity,
        pipe(prop("id"), getUrl(questionnaireResponseId))
      )(previousSection),
    [previousSection, questionnaireResponseId]
  );

  const onKeyboardAction = useCallback(() => {
    history.push(url);
  }, [history, url]);
  const { handleKeyBoardAction } = useLinkButtonKeyboard({
    onKeyboardAction,
    actionDisabled: !url
  });

  return { handleKeyBoardAction, url };
};
