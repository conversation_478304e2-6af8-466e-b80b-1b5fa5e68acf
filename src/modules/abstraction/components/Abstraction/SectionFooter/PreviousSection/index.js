import { Link } from "react-router-dom";
import { FormattedMessage } from "react-intl";
import { isNullOrEmpty } from "utils/fp";
import { useComponentLogic } from "./hooks";

export const PreviousSection = props => {
  const { url, handleKeyBoardAction } = useComponentLogic(props);

  if (isNullOrEmpty(url)) return null;

  return (
    <Link className="previous-section action-btn" to={url}>
      <i className="fal fa-arrow-circle-left" />
      <FormattedMessage
        id="navigation.previous"
        defaultMessage="Previous Section"
        onKeyDown={handleKeyBoardAction}
      />
    </Link>
  );
};

export default PreviousSection;
