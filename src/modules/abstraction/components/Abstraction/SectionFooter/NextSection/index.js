import { Link } from "react-router-dom";
import { FormattedMessage } from "react-intl";
import { isNullOrEmpty } from "utils/fp";
import { useComponentLogic } from "./hooks";

export const NextSection = props => {
  const { handleKeyBoardAction, url } = useComponentLogic(props);

  if (isNullOrEmpty(url)) return null;

  return (
    <Link className="next-section action-btn" to={url}>
      <FormattedMessage
        id="navigation.next"
        defaultMessage="Next Section"
        onKeyDown={handleKeyBoardAction}
      />
      <i className="fal fa-arrow-circle-right" />
    </Link>
  );
};

export default NextSection;
