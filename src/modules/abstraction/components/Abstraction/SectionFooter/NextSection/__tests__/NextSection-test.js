import TestRenderer from "react-test-renderer";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import { NextSection } from "..";

jest.mock("modules/question/selectors", () => ({
  getVisibleQuestions() {
    return [];
  }
}));
jest.mock("modules/questionnaire/selectors", () => ({
  getNextSection() {
    return { descendants: [], groupId: "155", id: "725", parentId: null };
  }
}));
describe("NextSection", () => {
  function render() {
    return TestRenderer.create(
      <Provider
        store={getStore(
          {},
          {
            questionnaire: {
              ...initialState,
              visibleQuestions: []
            }
          }
        )}
      >
        <MemoryRouter>
          <NextSection questionnaireResponseId={1} />
        </MemoryRouter>
      </Provider>
    );
  }
  test("renders component correctly", () => {
    const result = render();

    expect(result).toMatchSnapshot();
  });
});
