import { useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { curry, identity, ifElse, isNil, pipe, prop } from "ramda";
import { useLinkButtonKeyboard } from "modules/abstraction/hooks";
import Questionnaire from "modules/questionnaire/selectors";
import Questions from "modules/question/selectors";

export const getNextUrl = curry(
  (questionnaireResponseId, id) =>
    `/abstraction/${questionnaireResponseId}/section/${id}`
);

export const useComponentLogic = ({
  currentAnswerGroupId,
  questionnaireResponseId
}) => {
  const { history } = useHistory();
  const nextSection = useSelector(state =>
    Questionnaire.getNextSection(
      currentAnswerGroupId,
      Questions.getVisibleQuestions(state),
      state
    )
  );
  const url = useMemo(
    () =>
      ifElse(
        isNil,
        identity,
        pipe(prop("id"), getNextUrl(questionnaireResponseId))
      )(nextSection),
    [nextSection, questionnaireResponseId]
  );
  const onKeyboardAction = useCallback(() => {
    history.push(url);
  }, [history, url]);
  const { handleKeyBoardAction } = useLinkButtonKeyboard({
    onKeyboardAction,
    actionDisabled: !url
  });

  return { handleKeyBoardAction, url };
};
