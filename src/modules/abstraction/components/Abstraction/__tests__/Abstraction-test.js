import { Provider } from "react-redux";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import { Abstraction } from "..";
import { create } from "react-test-renderer";

jest.mock("@q-centrix/q-components-react", () => ({
  Footer: "Footer",
  Navbar: "Navbar",
  Sidenav: "Sidenav"
}));
jest.mock("../PersistentData", () => "PersistentData");
jest.mock("../ValidateSection", () => "ValidateSection");
jest.mock("../QuestionnaireBody", () => "QuestionnaireBody");
jest.mock("../QuestionnaireHeader", () => "QuestionnaireHeader");
jest.mock("../SidePane", () => "SidePane");

describe("Abstraction", () => {
  function render() {
    return create(
      <Provider store={getStore({}, { questionnaire: initialState })}>
        <Abstraction />
      </Provider>
    );
  }

  test("it renders correctly", () => {
    const output = render();

    expect(output).toMatchSnapshot();
  });
});
