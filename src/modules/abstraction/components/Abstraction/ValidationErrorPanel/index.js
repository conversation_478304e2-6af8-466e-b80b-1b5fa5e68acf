import { setErrorSidePanel2 } from "modules/abstraction/redux/actions";
import ErrorPanelHeader from "../ErrorPanel/ErrorPanelHeader";
import { useComponentLogic } from "./hooks";

const ValidationErrorPanel = () => {
  const { errorMessage, errorDescription, issueColorClass } =
    useComponentLogic();

  return (
    <>
      <ErrorPanelHeader
        setter={setErrorSidePanel2(false)}
        header={errorMessage}
        headerIssue={issueColorClass}
      />
      <div className="validation-error-container">
        <p className="validation-error-description">{errorDescription}</p>
      </div>
    </>
  );
};

export default ValidationErrorPanel;
