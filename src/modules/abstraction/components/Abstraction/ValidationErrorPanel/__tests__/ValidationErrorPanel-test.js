import ValidationErrorPanel from "..";
import { create } from "react-test-renderer";
import { decoratedWithDispatch } from "utils/test/decorated";

jest.mock("../../ErrorPanel/ErrorPanelHeader", () => "ErrorPanelHeader");

describe("ValidationErrorPanel", () => {
  const render = cmp => create(cmp);

  test("renders ValidationErrorPanel", () => {
    const { component } = decoratedWithDispatch(
      ValidationErrorPanel,
      {},
      {
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [
              { id: 0, failureMessage: "Cannot be blank!", error: true }
            ]
          },
          error: { id: 0, failureMessage: "Cannot be blank!", error: true },
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    const renderedComponent = render(component);

    expect(renderedComponent).toMatchSnapshot();
  });
});
