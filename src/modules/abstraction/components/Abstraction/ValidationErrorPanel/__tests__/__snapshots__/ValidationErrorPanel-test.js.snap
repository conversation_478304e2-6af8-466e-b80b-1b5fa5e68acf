// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ValidationErrorPanel renders ValidationErrorPanel 1`] = `
Array [
  <ErrorPanelHeader
    header="Cannot be blank!"
    headerIssue="error"
    setter={
      Object {
        "payload": Object {
          "errorState": false,
        },
        "type": "~abstractionValidationReport/SET_ERROR_SIDE_PANEL2",
      }
    }
  />,
  <div
    className="validation-error-container"
  >
    <p
      className="validation-error-description"
    >
      No description present.
    </p>
  </div>,
]
`;
