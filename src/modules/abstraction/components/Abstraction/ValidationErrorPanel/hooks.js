import { prop, cond, T, always, propOr } from "ramda";
import ValidationReportState from "modules/abstraction/redux/selectors";
import { useSelector } from "react-redux";

export const useComponentLogic = _props => {
  const error = useSelector(state => ValidationReportState.getError(state));

  const errorMessage = prop("failureMessage", error);
  const errorDescription = propOr(
    "No description present.",
    "description",
    error
  );

  const issueColorClass = cond([
    [prop("error"), always("error")],
    [prop("warning"), always("warning")],
    [T, always(null)]
  ])(error);

  return {
    errorMessage,
    errorDescription,
    issueColorClass
  };
};
