import { server<PERSON><PERSON>, webURI } from "base/constants";
import { clearSections } from "modules/questionnaire/actions";
import Questionnaire from "modules/questionnaire/selectors";
import { and, either, not, prop } from "ramda";
import { useCallback, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { cleanURL, isNotNullOrEmpty } from "utils/fp";

// eslint-disable-next-line max-statements
export const useComponentLogic = ({ currentAnswerGroup }) => {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const currentSection = useSelector(state =>
    Questionnaire.getCurrentSection(currentAnswerGroup, state)
  );
  const questionnaireStatus = useSelector(state =>
    Questionnaire.getQuestionnaireStatus(state)
  );
  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );

  const toggleMenu = useCallback(() => setMenuIsOpen(not), [setMenuIsOpen]);
  const patient = useSelector(state => Questionnaire.getPatient(state));
  const rCase = useSelector(state => Questionnaire.getCase(state));
  const backToPatientURL = useMemo(
    () => cleanURL(`${serverURI}/admin/patients/${patient.id}`),
    [patient.id]
  );
  const backToCaseURL = useMemo(
    () => (rCase ? cleanURL(`${serverURI}/qapps/cases/${rCase.caseId}`) : null),
    [rCase]
  );

  const regulatoryAdminQuestionnaireResponseId = useSelector(state =>
    Questionnaire.getRegulatoryAdminQuestionnaireResponseId(state)
  );

  const backToCaseListingURL = cleanURL(`${webURI}/web/cases`);

  const isRrtQuestionnaire = either(
    prop("regulatory_administrative"),
    prop("regulatory_clinical")
  )(questionnaire);

  const isRrtClinicalQuestionnaire = prop("regulatory_clinical")(questionnaire);
  const isRrtAdminQuestionnaire = prop("regulatory_administrative")(
    questionnaire
  );

  const location = useLocation();
  const querySearchParams = new URLSearchParams(location.search);
  const clinicalQrId = querySearchParams.get("clinicalQrId");

  const { id: qrId } = useParams();

  const dispatch = useDispatch();

  const adminRrtQuestionnaireUrl = `/abstraction/${regulatoryAdminQuestionnaireResponseId}?clinicalQrId=${qrId}`;

  const handleNavigateToQR = useCallback(() => {
    dispatch(clearSections());
  }, []);

  const clinicalRrtQuestionnaireUrl = `/abstraction/${clinicalQrId}`;

  const shouldShowClinicalBtnNav = and(
    isNotNullOrEmpty(clinicalQrId),
    isRrtAdminQuestionnaire
  );

  return {
    currentStatus: questionnaireStatus,
    menuIsOpen,
    name: questionnaire.name,
    sectionName: currentSection?.name,
    toggleMenu,
    backToPatientURL,
    backToCaseURL,
    backToCaseListingURL,
    isRrtQuestionnaire,
    isRrtClinicalQuestionnaire,
    adminRrtQuestionnaireUrl,
    handleNavigateToQR,
    shouldShowClinicalBtnNav,
    clinicalRrtQuestionnaireUrl
  };
};
