import { useCallback } from "react";
import { useMutation } from "@apollo/client";
import { lensPath, set } from "ramda";
import query from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import mutation from "./mutation";

const questionnaireStatusLens = lensPath([
  "questionnaireResponse",
  "questionnaireStatus"
]);

export const useUpdateQuestionnaireStatusMutation = ({
  code,
  questionnaireResponseId
}) => {
  const [
    updateQuestionnaireStatus,
    { loading: mutationLoading, error: mutationError }
  ] = useMutation(mutation, {
    variables: {
      code,
      questionnaireResponseId: Number(questionnaireResponseId)
    },
    update: (store, { data: serverData }) => {
      const data = store.readQuery({
        query,
        variables: { id: Number(questionnaireResponseId) }
      });

      // Save changes in the Apollo store
      store.writeQuery({
        query,
        variables: { id: Number(questionnaireResponseId) },
        data: set(
          questionnaireStatusLens,
          serverData.updateQuestionnaireStatus,
          data
        )
      });
    }
  });

  return { updateQuestionnaireStatus, mutationLoading, mutationError };
};

export const useComponentLogic = props => {
  const { selected, toggleMenu } = props;
  const { updateQuestionnaireStatus } =
    useUpdateQuestionnaireStatusMutation(props);
  const handleClick = useCallback(() => {
    !selected && updateQuestionnaireStatus();
    toggleMenu();
  }, [selected, toggleMenu, updateQuestionnaireStatus]);

  return { handleClick };
};
