import classnames from "classnames";
import { useComponentLogic } from "./hooks";

export const StatusButton = props => {
  const { code, selected, text } = props;
  const buttonClassName = classnames({ [`btn-${code}`]: true, selected });
  const { handleClick } = useComponentLogic(props);

  return (
    <button className={buttonClassName} onClick={handleClick} type="button">
      {code} - {text}
    </button>
  );
};

StatusButton.defaultProps = {
  code: "0",
  selected: false,
  text: "Status Button",
  // eslint-disable-next-line no-empty-function
  toggleMenu: () => {}
};

export default StatusButton;
