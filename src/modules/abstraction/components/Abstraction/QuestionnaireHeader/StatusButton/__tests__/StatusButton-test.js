import { useQuery } from "@apollo/client";
import { act, create } from "react-test-renderer";
import wait from "waait";
import query from "modules/questionnaire/components/ApolloMain/QuestionnaireResponseQuery/query";
import { initialState } from "modules/questionnaire/reducers";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import { cache } from "base/apolloClient";
import UPDATE_STATUS from "../mutation";
import { StatusButton } from "..";

const initialStatus = {
  code: 1,
  text: "Not Submitted",
  __typename: "QuestionnaireResponseStatus"
};

const successfulResponseRequest = {
  request: {
    query,
    variables: {
      id: 1
    }
  },
  result: {
    data: {
      questionnaireResponse: {
        id: "1",
        questionnaireId: "1",
        visit: null,
        visibleQuestions: [],
        patient: null,
        responseCase: null,
        answers: [],
        answerGroups: [
          { id: "1", parentId: null, groupId: "1", __typename: "AnswerGroup" }
        ],
        questionnaireStatus: initialStatus,
        validationReport: null,
        writeLocked: false,
        __typename: "QuestionnaireResponse"
      }
    }
  }
};

const successfulMutationMock = {
  request: {
    query: UPDATE_STATUS,
    variables: { code: 1, questionnaireResponseId: 1 }
  },
  result: {
    data: {
      updateQuestionnaireStatus: {
        code: 1,
        text: "Updated Status",
        errors: null,
        __typename: "QuestionnaireResponseStatus"
      }
    }
  }
};

const apolloMocks = [
  successfulResponseRequest,
  successfulMutationMock,
  {
    request: {
      query: UPDATE_STATUS,
      variables: { code: "-01", questionnaireResponseId: 1 }
    },
    error: new Error("An error occurred")
  }
];

// using Wrapper to test the mutation's update option
const Wrapper = props => {
  useQuery(query, { variables: { id: 1 } });
  return (
    <div>
      <StatusButton {...props} />
    </div>
  );
};

describe("StatusButton", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: Wrapper,
        props,
        initialAppValues: initialState,
        apolloMocks
      })
    );
  }

  test("it renders correctly", () => {
    const output = render();

    expect(output).toMatchSnapshot();
  });

  test("tests toggleMenu is called when clicked", () => {
    const component = render({
      code: 1,
      questionnaireResponseId: "1",
      toggleMenu: jest.fn()
    });
    const instance = component.root;
    const sButton = instance.findByType(StatusButton);
    const button = instance.findByType("button");

    const spy = jest.spyOn(sButton.props, "toggleMenu");

    act(() => button.props.onClick());

    expect(spy).toHaveBeenCalled();
  });

  // Add this test back in with ONC-224 -> it should pass and cover the update on the mutation
  test.skip("tests mutation when clicked", async () => {
    const component = render({
      code: 1,
      questionnaireResponseId: "1"
    });
    const instance = component.root;
    const button = instance.findByType("button");

    await act(() => wait(3));
    const earlyCache = cache.extract();

    expect(earlyCache["$QuestionnaireResponse1.questionnaireStatus"].code).toBe(
      0
    );

    act(() => button.props.onClick());
    await act(() => wait(3));
    const updatedCache = cache.extract();

    expect(
      updatedCache["$QuestionnaireResponse1.questionnaireStatus"].code
    ).toBe(1);
  });
});

// Need to remove options and abstract to it's own query
