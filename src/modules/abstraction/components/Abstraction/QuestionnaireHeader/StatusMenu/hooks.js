import { useRef } from "react";
import useOnClickOutside from "utils/hooks/useOnClickOutside";
import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";

export const useComponentLogic = close => {
  const ref = useRef(null);
  const statuses = useSelector(state => Questionnaire.getStatusOptions(state));

  useOnClickOutside(ref, close);
  return { ref, statuses };
};
