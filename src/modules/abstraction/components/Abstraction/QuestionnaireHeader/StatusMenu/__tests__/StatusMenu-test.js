import { act, create } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import query from "../query";
import { StatusMenu } from "..";

jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));

jest.mock("../../StatusButton", () => "StatusButton");

const statusOptions = [
  { code: 0, text: "Not Submitted" },
  { code: 1, text: "Submitted" }
];

const successMock = {
  request: {
    query
  },
  result: {
    questionnaireStatusOptions: statusOptions
  }
};

const apolloMocks = [successMock];
const appState = {
  questionnaire: {
    questionnaireStatuses: statusOptions
  }
};

describe("StatusMenu", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: StatusMenu,
        props,
        initialAppValues: appState,
        apolloMocks
      })
    );
  }

  test("it renders correctly when open=true", async () => {
    const output = render({ open: true });

    await act(() => wait(100));
    expect(output).toMatchSnapshot();
  });

  test("it renders correctly when open=false", async () => {
    const output = render({ open: false });

    await act(() => wait(100));
    expect(output).toMatchSnapshot();
  });

  test("it renders statuses correctly when open", async () => {
    const output = render({
      open: true,
      statuses: statusOptions
    });

    await act(() => wait(100));
    expect(output).toMatchSnapshot();
  });
});
