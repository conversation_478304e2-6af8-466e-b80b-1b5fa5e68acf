// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StatusMenu it renders correctly when open=false 1`] = `<AnimatePresence />`;

exports[`StatusMenu it renders correctly when open=true 1`] = `
<AnimatePresence>
  <motion.div
    animate="animate"
    exit="exit"
    initial="enter"
    style={
      Object {
        "zIndex": 10,
      }
    }
    variants={
      Object {
        "animate": Object {
          "opacity": 1,
          "x": 0,
          "y": 0,
        },
        "enter": Object {
          "opacity": 0,
          "x": 0,
          "y": 200,
        },
        "exit": Object {
          "opacity": 0,
          "x": 0,
          "y": -200,
        },
      }
    }
  >
    <div
      className="status-menu"
    >
      <StatusButton
        code={0}
        text="Not Submitted"
        toggleMenu={[Function]}
      />
      <StatusButton
        code={1}
        text="Submitted"
        toggleMenu={[Function]}
      />
    </div>
  </motion.div>
</AnimatePresence>
`;

exports[`StatusMenu it renders statuses correctly when open 1`] = `
<AnimatePresence>
  <motion.div
    animate="animate"
    exit="exit"
    initial="enter"
    style={
      Object {
        "zIndex": 10,
      }
    }
    variants={
      Object {
        "animate": Object {
          "opacity": 1,
          "x": 0,
          "y": 0,
        },
        "enter": Object {
          "opacity": 0,
          "x": 0,
          "y": 200,
        },
        "exit": Object {
          "opacity": 0,
          "x": 0,
          "y": -200,
        },
      }
    }
  >
    <div
      className="status-menu"
    >
      <StatusButton
        code={0}
        text="Not Submitted"
        toggleMenu={[Function]}
      />
      <StatusButton
        code={1}
        text="Submitted"
        toggleMenu={[Function]}
      />
    </div>
  </motion.div>
</AnimatePresence>
`;
