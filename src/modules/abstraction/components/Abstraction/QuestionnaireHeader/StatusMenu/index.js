import { AnimatePresence, motion } from "framer-motion";
import StatusButton from "../StatusButton";
import { useComponentLogic } from "./hooks";

export const variants = {
  enter: {
    opacity: 0,
    x: 0,
    y: 200
  },
  animate: { opacity: 1, x: 0, y: 0 },
  exit: {
    opacity: 0,
    x: 0,
    y: -200
  }
};
export const StatusMenu = ({ open, questionnaireResponseId, toggleMenu }) => {
  const { ref, statuses } = useComponentLogic(toggleMenu);

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          animate="animate"
          exit="exit"
          initial="enter"
          style={{ zIndex: 10 }}
          variants={variants}
        >
          <div className="status-menu" ref={ref}>
            {statuses.map(status => (
              <StatusButton
                key={status.code}
                questionnaireResponseId={questionnaireResponseId}
                toggleMenu={toggleMenu}
                {...status}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

StatusMenu.defaultProps = {
  open: false,
  statuses: [],
  // eslint-disable-next-line no-empty-function
  toggleMenu: () => {}
};

export default StatusMenu;
