// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QuestionnaireHeader it renders correctly 1`] = `
<div
  className="questionnaire-header"
>
  <div
    className="questionnaire-header-left"
  >
    <h2>
      Test Questionnaire Name
    </h2>
  </div>
  <div
    className="questionnaire-header-right"
  >
    <h2>
      Test Section Name
    </h2>
    <div
      className="links"
    >
      <a
        href="http://localhost:3000/admin/patients/13"
      >
        <i
          className="far fa-users-medical"
        />
        <span>
          Patient
        </span>
      </a>
      <a
        href="http://localhost:3000/qapps/cases/50"
      >
        <i
          className="far fa-briefcase-medical"
        />
        <span>
          Case
        </span>
      </a>
    </div>
    <div
      className="status-container"
    >
      <StatusButton
        code={0}
        selected={true}
        text="Not Submitted"
        toggleMenu={[Function]}
      />
      <span>
        Status
      </span>
      <StatusMenu
        open={false}
        toggleMenu={[Function]}
      />
    </div>
  </div>
</div>
`;

exports[`QuestionnaireHeader it renders with Patient button when Regulatory Clinical and Regulatory Administrative are false 1`] = `
<div
  className="questionnaire-header"
>
  <div
    className="questionnaire-header-left"
  >
    <h2>
      Test Questionnaire Name
    </h2>
  </div>
  <div
    className="questionnaire-header-right"
  >
    <h2>
      Test Section Name
    </h2>
    <div
      className="links"
    >
      <a
        href="http://localhost:3000/admin/patients/13"
      >
        <i
          className="far fa-users-medical"
        />
        <span>
          Patient
        </span>
      </a>
      <a
        href="http://localhost:3000/qapps/cases/50"
      >
        <i
          className="far fa-briefcase-medical"
        />
        <span>
          Case
        </span>
      </a>
    </div>
    <div
      className="status-container"
    >
      <StatusButton
        code={0}
        selected={true}
        text="Not Submitted"
        toggleMenu={[Function]}
      />
      <span>
        Status
      </span>
      <StatusMenu
        open={false}
        toggleMenu={[Function]}
      />
    </div>
  </div>
</div>
`;

exports[`QuestionnaireHeader it renders without Patient button and Status button when Regulatory Clinical is false and Regulatory Administrative is true 1`] = `
<div
  className="questionnaire-header"
>
  <div
    className="questionnaire-header-left"
  >
    <h2>
      Test Questionnaire Name
    </h2>
  </div>
  <div
    className="questionnaire-header-right"
  >
    <h2>
      Test Section Name
    </h2>
    <div
      className="links"
    >
      <a
        href="http://localhost:3000/qapps/cases/50"
      >
        <i
          className="far fa-briefcase-medical"
        />
        <span>
          Case
        </span>
      </a>
      <a
        href="http://localhost:4003/web/cases"
      >
        <i
          className="fa-solid fa-table-list"
        />
        <span>
          Case List
        </span>
      </a>
    </div>
  </div>
</div>
`;

exports[`QuestionnaireHeader it renders without Patient button and Status button when Regulatory Clinical is true and Regulatory Administrative is false 1`] = `
<div
  className="questionnaire-header"
>
  <div
    className="questionnaire-header-left"
  >
    <h2>
      Test Questionnaire Name
    </h2>
  </div>
  <div
    className="questionnaire-header-right"
  >
    <h2>
      Test Section Name
    </h2>
    <div
      className="links"
    >
      <a
        href="/abstraction/1?clinicalQrId=123"
        onClick={[Function]}
      >
        <i
          className="fa-regular fa-memo"
        />
        <span>
          Admin QR
        </span>
      </a>
      <a
        href="http://localhost:3000/qapps/cases/50"
      >
        <i
          className="far fa-briefcase-medical"
        />
        <span>
          Case
        </span>
      </a>
      <a
        href="http://localhost:4003/web/cases"
      >
        <i
          className="fa-solid fa-table-list"
        />
        <span>
          Case List
        </span>
      </a>
    </div>
  </div>
</div>
`;
