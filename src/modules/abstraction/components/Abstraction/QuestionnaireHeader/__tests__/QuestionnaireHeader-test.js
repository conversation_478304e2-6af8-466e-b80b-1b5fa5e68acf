/* eslint-disable camelcase */

import TestRenderer from "react-test-renderer";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import { QuestionnaireHeader } from "..";

jest.mock("../StatusButton", () => "StatusButton");
jest.mock("../StatusMenu", () => "StatusMenu");

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    id: "123",
    clinicalQrId: "456"
  })
}));

describe("QuestionnaireHeader", () => {
  function render(
    regulatory_clinical = false,
    regulatory_administrative = false
  ) {
    return TestRenderer.create(
      <Provider
        store={getStore(
          {},
          {
            questionnaire: {
              ...initialState,
              questionnaire: {
                name: "Test Questionnaire Name",
                regulatory_clinical,
                regulatory_administrative
              },
              regulatoryAdminQuestionnaireResponseId: 1,
              questionnaireStatus: { code: 0, text: "Not Submitted" },
              answerGroups: [{ id: "1", groupId: "2" }],
              sections: [
                {
                  answerGroups: [{ id: "1", groupId: "2" }],
                  groupId: "2",
                  id: "168",
                  name: "Test Section Name"
                }
              ],
              patient: { id: 13 },
              responseCase: { caseId: 50 }
            }
          }
        )}
      >
        <MemoryRouter>
          <QuestionnaireHeader currentAnswerGroup="1" />
        </MemoryRouter>
      </Provider>
    );
  }

  test("it renders correctly", () => {
    const output = render();

    expect(output).toMatchSnapshot();
  });

  test("it renders without Patient button and Status button when Regulatory Clinical is true and Regulatory Administrative is false", () => {
    const output = render(true, false);

    expect(output).toMatchSnapshot();
  });

  test("it renders without Patient button and Status button when Regulatory Clinical is false and Regulatory Administrative is true", () => {
    const output = render(false, true);

    expect(output).toMatchSnapshot();
  });

  test("it renders with Patient button when Regulatory Clinical and Regulatory Administrative are false", () => {
    const output = render(false, false);

    expect(output).toMatchSnapshot();
  });
});
