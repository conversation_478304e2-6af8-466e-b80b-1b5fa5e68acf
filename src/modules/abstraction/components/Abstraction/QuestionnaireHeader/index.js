import { <PERSON> } from "react-router-dom";
import StatusButton from "./StatusButton";
import StatusMenu from "./StatusMenu";
import { useComponentLogic } from "./hooks";
import "modules/abstraction/styles/questionnaire-header.scss";

// eslint-disable-next-line complexity
export const QuestionnaireHeader = props => {
  const { questionnaireResponseId } = props;
  const {
    currentStatus,
    menuIsOpen,
    name,
    sectionName,
    toggleMenu,
    backToPatientURL,
    backToCaseURL,
    backToCaseListingURL,
    isRrtQuestionnaire,
    isRrtClinicalQuestionnaire,
    adminRrtQuestionnaireUrl,
    handleNavigateToQR,
    shouldShowClinicalBtnNav,
    clinicalRrtQuestionnaireUrl
  } = useComponentLogic(props);

  return (
    <div className="questionnaire-header">
      <div className="questionnaire-header-left">
        <h2>{name}</h2>
      </div>
      <div className="questionnaire-header-right">
        <h2>{sectionName}</h2>
        <div className="links">
          {!isRrtQuestionnaire && (
            <a href={backToPatientURL}>
              <i className="far fa-users-medical" />
              <span>Patient</span>
            </a>
          )}
          {shouldShowClinicalBtnNav && (
            <Link to={clinicalRrtQuestionnaireUrl} onClick={handleNavigateToQR}>
              <i className="fa-solid fa-newspaper" />
              <span>Clinical QR</span>
            </Link>
          )}
          {isRrtClinicalQuestionnaire && (
            <Link to={adminRrtQuestionnaireUrl} onClick={handleNavigateToQR}>
              <i className="fa-regular fa-memo" />
              <span>Admin QR</span>
            </Link>
          )}
          {backToCaseURL && (
            <a href={backToCaseURL}>
              <i className="far fa-briefcase-medical" />
              <span>Case</span>
            </a>
          )}
          {isRrtQuestionnaire && (
            <a href={backToCaseListingURL}>
              <i className="fa-solid fa-table-list" />
              <span>Case List</span>
            </a>
          )}
        </div>
        {!isRrtQuestionnaire && (
          <div className="status-container">
            {currentStatus && (
              <StatusButton
                questionnaireResponseId={questionnaireResponseId}
                selected
                toggleMenu={toggleMenu}
                {...currentStatus}
              />
            )}
            <span>Status</span>
            <StatusMenu
              toggleMenu={toggleMenu}
              open={menuIsOpen}
              questionnaireResponseId={questionnaireResponseId}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default QuestionnaireHeader;
