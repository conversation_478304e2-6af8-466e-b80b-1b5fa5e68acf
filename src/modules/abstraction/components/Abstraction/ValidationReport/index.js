import QuestionnaireNavigation from "modules/questionnaire/components/Questionnaire/SidePane/PanelContent/ValidationReport/QuestionnaireNavigation";
import { useComponentLogic } from "./hooks";
import DefaultValidations from "./DefaultValidations";
import ErrorPanel from "../ErrorPanel";
import ValidationErrorsPanel from "../ValidationErrorsPanel";
import ValidationErrorPanel from "../ValidationErrorPanel";
import classnames from "classnames";
import "../../../styles/validation-report.scss";
import { or, propOr, __ } from "ramda";
import RrtValidations from "./RrtValidations";

const type = propOr(DefaultValidations, __, {
  [true]: RrtValidations
});

export const ValidationReport = props => {
  const { questionnaireResponseId, baseRoute } = props;

  const {
    isValidationReportPanelInactive,
    isRrtQuestionnaire,
    validationItems,
    errorPanelState,
    errorPanelState2
  } = useComponentLogic(props);

  const abstractionValidationPanelClasses = classnames(
    "abstraction-validation-report-panel",
    {
      "panel-overflow": or(errorPanelState, errorPanelState2)
    }
  );

  if (isValidationReportPanelInactive) {
    return null;
  }

  const ValidationType = type(isRrtQuestionnaire);

  return (
    <div className={abstractionValidationPanelClasses}>
      <h3 className="validation-report-header">Validation Report</h3>
      <div className="validation-responses">
        {validationItems.map(section => (
          <ValidationType
            key={section.sectionId}
            section={section}
            questionnaireResponseId={questionnaireResponseId}
            baseRoute={baseRoute}
          />
        ))}
      </div>
      {!isRrtQuestionnaire && (
        <>
          <QuestionnaireNavigation baseRoute={baseRoute} />
          <ErrorPanel errorState={errorPanelState}>
            <ValidationErrorsPanel />
          </ErrorPanel>
          <ErrorPanel errorState={errorPanelState2}>
            <ValidationErrorPanel />
          </ErrorPanel>
        </>
      )}
    </div>
  );
};

export default ValidationReport;
