import { create } from "react-test-renderer";
import RrtValidations from "..";
import { decoratedWithDispatch } from "utils/test/decorated";
import { failure } from "../../DefaultValidations/Failure/__tests__/Failure-test";
import { MemoryRouter } from "react-router-dom";

export const section = {
  displayLogic: {
    groupId: "1419",
    id: "2889",
    isTopLevel: true,
    listMarker: "",
    longPrompt: false,
    name: "Demographics"
  },
  failures: { error: [failure] },
  sectionId: 1,
  sectionName: "Demographics"
};

describe("RrtValidations", () => {
  function render(cmp) {
    return create(<MemoryRouter>{cmp}</MemoryRouter>);
  }

  /* eslint-disable camelcase */

  test("renders RrtValidations", () => {
    const { component } = decoratedWithDispatch(
      RrtValidations,
      { section, questionnaireResponseId: 1, baseRoute: "/abstraction" },
      {
        questionnaire: {
          questionnaire: {
            regulatory_clinical: false,
            regulatory_administrative: true
          }
        },
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [{ id: 0, failureMessage: "Cannot be blank!" }]
          },
          error: {},
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    /* eslint-enable camelcase */

    const answerSourceLink = render(component);

    expect(answerSourceLink).toMatchSnapshot();
  });
});
