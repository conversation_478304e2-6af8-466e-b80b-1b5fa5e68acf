import { useComponentLogic } from "./hooks";
import ValidationItem from "./ValidationItem";
import { keys, map, pipe } from "ramda";
import { isNullOrEmpty } from "utils/fp";

const RrtValidations = props => {
  const { section, questionnaireResponseId, baseRoute } = props;
  const { allFailures } = useComponentLogic(props);

  if (isNullOrEmpty(section.failures)) {
    return null;
  }

  return (
    <div className="validation-responses">
      <h4 className="response-section-header">{section.sectionName}</h4>
      {!isNullOrEmpty(allFailures) &&
        pipe(
          keys,
          map(item => (
            <ValidationItem
              key={allFailures[item][0].id}
              prompt={item}
              failure={allFailures[item][0]}
              questionnaireResponseId={questionnaireResponseId}
              sectionId={section.sectionId}
              baseRoute={baseRoute}
            />
          ))
        )(allFailures)}
    </div>
  );
};

export default RrtValidations;
