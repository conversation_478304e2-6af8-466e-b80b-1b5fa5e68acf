import { Link } from "react-router-dom";
import { useComponentLogic } from "./hooks";

const ValidationItem = props => {
  const { prompt } = props;

  const { url } = useComponentLogic(props);

  return (
    <Link to={url}>
      <li className="abstraction-response rrt-abstraction-response-item">
        <div className="prompt">{prompt}</div>
        <i className="fa-solid fa-eye" />
      </li>
    </Link>
  );
};

export default ValidationItem;
