import { create } from "react-test-renderer";
import { decoratedWithDispatch } from "utils/test/decorated";
import ValidationItem from "..";
import { MemoryRouter } from "react-router-dom";

export const failure = {
  __typename: "ResponseValidationFailure",
  id: "25",
  answerGroupId: "1",
  questions: [
    {
      __typename: "DateQuestion",
      id: "3334",
      prompt: "Date of Birth",
      type: "Date"
    }
  ],
  error: true,
  warning: false,
  failureMessage: "Cannot be blank",
  failureCategory: null,
  answerGroup: {
    __typename: "AnswerGroup",
    id: "1",
    parentId: null,
    groupId: "1419",
    descendants: [
      {
        __typename: "AnswerGroup",
        id: "81",
        parentId: "1",
        groupId: "1420",
        descendants: []
      },
      {
        __typename: "AnswerGroup",
        id: "82",
        parentId: "1",
        groupId: "1421",
        descendants: []
      },
      {
        __typename: "AnswerGroup",
        id: "83",
        parentId: "1",
        groupId: "1422",
        descendants: []
      },
      {
        __typename: "AnswerGroup",
        id: "84",
        parentId: "1",
        groupId: "1423",
        descendants: []
      },
      {
        __typename: "AnswerGroup",
        id: "85",
        parentId: "1",
        groupId: "1424",
        descendants: []
      }
    ]
  },
  failureIndex: 4000
};

describe("ValidationItem", () => {
  function render(cmp) {
    return create(<MemoryRouter>{cmp}</MemoryRouter>);
  }

  test("renders ValidationItem", () => {
    const { component } = decoratedWithDispatch(
      ValidationItem,
      {
        failure,
        prompt: "Date of Birth",
        baseRoute: "/abstraction",
        questionnaireResponseId: 1,
        sectionId: 1,
        errorCount: 1,
        warningCount: 2,
        presenceCount: 0
      },
      {}
    );

    const answerSourceLink = render(component);

    expect(answerSourceLink).toMatchSnapshot();
  });
});
