import { isNullOrEmpty } from "utils/fp";
import {
  always,
  ifElse,
  map,
  pipe,
  prop,
  groupBy,
  path,
  concat,
  reduce,
  mergeWith,
  append,
  __,
  join
} from "ramda";

const getPrompt = failure =>
  pipe(
    ifElse(
      prop("questions"),
      prop("questions"),
      pipe(prop("question"), append(__, []))
    ),
    map(prop("prompt")),
    join(" / ")
  )(failure);

export const useComponentLogic = props => {
  const { section } = props;

  const groupByFn = failure =>
    pipe(
      path(["failures", failure]),
      ifElse(isNullOrEmpty, always([]), groupBy(getPrompt))
    )(section);

  const allFailures = pipe(
    map(groupByFn),
    reduce((acc, val) => mergeWith(concat, val, acc), [])
  )(["error", "warning", "presence"]);

  return {
    allFailures
  };
};
