import { complement, propEq, values, prop, either } from "ramda";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import Questionnaire from "modules/questionnaire/selectors";
import ValidationReportState from "modules/abstraction/redux/selectors";
import { useSelector } from "react-redux";

export const useComponentLogic = props => {
  const { currentActivePanel } = props;

  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );

  const isValidationReportPanelInactive = complement(
    propEq("validationReportPanel", currentActivePanel)
  )(panelTypes);

  const isRrtQuestionnaire = either(
    prop("regulatory_administrative"),
    prop("regulatory_clinical")
  )(questionnaire);

  const validationItems = useSelector(state =>
    values(Questionnaire.getOrderedValidationReport(state))
  );

  const errorPanelState = useSelector(state =>
    ValidationReportState.getErrorPanelState(state)
  );
  const errorPanelState2 = useSelector(state =>
    ValidationReportState.getErrorPanelState2(state)
  );

  return {
    isValidationReportPanelInactive,
    isRrtQuestionnaire,
    validationItems,
    errorPanelState,
    errorPanelState2
  };
};
