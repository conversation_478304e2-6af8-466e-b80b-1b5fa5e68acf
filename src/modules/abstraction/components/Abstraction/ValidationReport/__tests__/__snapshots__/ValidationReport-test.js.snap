// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`DefaultValidations renders DefaultValidations 1`] = `
<DefaultValidations
  baseRoute="/abstraction"
  dispatch={[MockFunction]}
  questionnaireResponseId={1}
  section={
    Object {
      "displayLogic": Object {
        "groupId": "1419",
        "id": "2889",
        "isTopLevel": true,
        "listMarker": "",
        "longPrompt": false,
        "name": "Demographics",
      },
      "failures": Object {
        "error": Array [
          Object {
            "__typename": "ResponseValidationFailure",
            "answerGroup": Object {
              "__typename": "AnswerGroup",
              "descendants": Array [
                Object {
                  "__typename": "AnswerGroup",
                  "descendants": Array [],
                  "groupId": "1420",
                  "id": "81",
                  "parentId": "1",
                },
                Object {
                  "__typename": "AnswerGroup",
                  "descendants": Array [],
                  "groupId": "1421",
                  "id": "82",
                  "parentId": "1",
                },
                Object {
                  "__typename": "AnswerGroup",
                  "descendants": Array [],
                  "groupId": "1422",
                  "id": "83",
                  "parentId": "1",
                },
                Object {
                  "__typename": "AnswerGroup",
                  "descendants": Array [],
                  "groupId": "1423",
                  "id": "84",
                  "parentId": "1",
                },
                Object {
                  "__typename": "AnswerGroup",
                  "descendants": Array [],
                  "groupId": "1424",
                  "id": "85",
                  "parentId": "1",
                },
              ],
              "groupId": "1419",
              "id": "1",
              "parentId": null,
            },
            "answerGroupId": "1",
            "error": true,
            "failureCategory": null,
            "failureIndex": 4000,
            "failureMessage": "Cannot be blank",
            "id": "25",
            "questions": Array [
              Object {
                "__typename": "DateQuestion",
                "id": "3334",
                "prompt": "Date of Birth",
                "type": "Date",
              },
            ],
            "warning": false,
          },
        ],
      },
      "sectionId": 1,
      "sectionName": "Demographics",
    }
  }
/>
`;

exports[`Failure renders Failure 1`] = `
<a
  href="/abstraction/1/section/1/3334|1"
  onClick={[Function]}
>
  <li
    className="abstraction-response"
    onClick={[Function]}
  >
    <div
      className="prompt"
    >
      Date of Birth
    </div>
    <div
      className="issue-counters-container"
    >
      <div
        className="issue-counters"
      >
        <p
          className="issue-counter error"
        >
          1
           
          Error
        </p>
        <p
          className="issue-counter warning"
        >
          2
           
          Warnings
        </p>
      </div>
      <i
        className="fa-solid fa-angle-right"
      />
    </div>
  </li>
</a>
`;

exports[`ValidationReport renders ValidationReport 1`] = `
<Context.Provider
  value={
    Object {
      "store": Object {
        "dispatch": [Function],
        "getState": [Function],
        "replaceReducer": [Function],
        "subscribe": [Function],
        Symbol(observable): [Function],
      },
      "subscription": Object {
        "addNestedSub": [Function],
        "getListeners": [Function],
        "handleChangeWrapper": [Function],
        "isSubscribed": [Function],
        "notifyNestedSubs": [Function],
        "trySubscribe": [Function],
        "tryUnsubscribe": [Function],
      },
    }
  }
>
  <ValidationReport
    currentActivePanel="Validation Report"
    questionnaireResponseId="1"
    validationItems={
      Array [
        Object {
          "displayLogic": Object {
            "groupId": "1419",
            "id": "2889",
            "isTopLevel": true,
            "listMarker": "",
            "longPrompt": false,
            "name": "Demographics",
          },
          "failures": Object {
            "error": Array [
              Object {
                "__typename": "ResponseValidationFailure",
                "answerGroup": Object {
                  "__typename": "AnswerGroup",
                  "descendants": Array [
                    Object {
                      "__typename": "AnswerGroup",
                      "descendants": Array [],
                      "groupId": "1420",
                      "id": "81",
                      "parentId": "1",
                    },
                    Object {
                      "__typename": "AnswerGroup",
                      "descendants": Array [],
                      "groupId": "1421",
                      "id": "82",
                      "parentId": "1",
                    },
                    Object {
                      "__typename": "AnswerGroup",
                      "descendants": Array [],
                      "groupId": "1422",
                      "id": "83",
                      "parentId": "1",
                    },
                    Object {
                      "__typename": "AnswerGroup",
                      "descendants": Array [],
                      "groupId": "1423",
                      "id": "84",
                      "parentId": "1",
                    },
                    Object {
                      "__typename": "AnswerGroup",
                      "descendants": Array [],
                      "groupId": "1424",
                      "id": "85",
                      "parentId": "1",
                    },
                  ],
                  "groupId": "1419",
                  "id": "1",
                  "parentId": null,
                },
                "answerGroupId": "1",
                "error": true,
                "failureCategory": null,
                "failureIndex": 4000,
                "failureMessage": "Cannot be blank",
                "id": "25",
                "questions": Array [
                  Object {
                    "__typename": "DateQuestion",
                    "id": "3334",
                    "prompt": "Date of Birth",
                    "type": "Date",
                  },
                ],
                "warning": false,
              },
            ],
          },
          "sectionId": 1,
          "sectionName": "Demographics",
        },
      ]
    }
  />
</Context.Provider>
`;
