import { ValidationReport } from "..";
import { Provider } from "react-redux";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import { getStore } from "utils/test/decorated";
// eslint-disable-next-line import/extensions
import { section } from "../DefaultValidations/__tests__/DefaultValidations-test.js";
import shallowRender from "utils/shallowRender";

jest.mock("../DefaultValidations", () => "DefaultValidations");

describe("ValidationReport", () => {
  const validationItems = [section];
  const questionnaireResponseId = "1";

  const render = currentActivePanel =>
    shallowRender(
      <Provider store={getStore({}, { oroderedValidationReport: [section] })}>
        <ValidationReport
          validationItems={validationItems}
          questionnaireResponseId={questionnaireResponseId}
          currentActivePanel={currentActivePanel}
        />
      </Provider>
    );

  test("renders ValidationReport", () => {
    expect(render(panelTypes.validationReportPanel)).toMatchSnapshot();
  });
});
