import { __, always, ifElse, gt, concat } from "ramda";
import {
  setErrorPrompt,
  setErrorSidePanel,
  setErrorUrl
} from "modules/abstraction/redux/actions";
import { useDispatch } from "react-redux";
import { getFailureFieldName } from "modules/questionnaire/services/specialFields";

export const useComponentLogic = props => {
  const { prompt, failure, baseRoute, questionnaireResponseId, sectionId } =
    props;

  const dispatch = useDispatch();

  const url = `${baseRoute}/${questionnaireResponseId}/section/${sectionId}/${getFailureFieldName(
    failure
  )}`;

  const displayFailures = count => gt(count, 0);
  const displayFailuresText = (count, text) =>
    ifElse(gt(__, 1), always(concat(text, "s")), always(text))(count);

  const handleClickError = () => {
    dispatch(setErrorSidePanel(true));
    dispatch(setErrorPrompt(prompt));
    dispatch(setErrorUrl(url));
  };

  return {
    url,
    displayFailures,
    displayFailuresText,
    handleClickError
  };
};
