import { useComponentLogic } from "./hooks";
import { Link } from "react-router-dom";

// eslint-disable-next-line complexity
const Failure = props => {
  const { prompt, errorCount, warningCount, presenceCount } = props;

  const { url, handleClickError, displayFailures, displayFailuresText } =
    useComponentLogic(props);

  return (
    <Link to={url}>
      <li onClick={handleClickError} className="abstraction-response">
        <div className="prompt">{prompt}</div>
        <div className="issue-counters-container">
          <div className="issue-counters">
            {displayFailures(errorCount) && (
              <p className="issue-counter error">
                {errorCount} {displayFailuresText(errorCount, "Error")}
              </p>
            )}
            {displayFailures(warningCount) && (
              <p className="issue-counter warning">
                {warningCount} {displayFailuresText(warningCount, "Warning")}
              </p>
            )}
            {displayFailures(presenceCount) && (
              <p className="issue-counter">
                {presenceCount} {displayFailuresText(presenceCount, "Blank")}
              </p>
            )}
          </div>
          <i className="fa-solid fa-angle-right" />
        </div>
      </li>
    </Link>
  );
};

export default Failure;
