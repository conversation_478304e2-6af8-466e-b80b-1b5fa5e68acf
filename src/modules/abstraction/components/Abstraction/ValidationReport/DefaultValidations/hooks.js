import { isNullOrEmpty } from "utils/fp";
import {
  always,
  ifElse,
  map,
  pipe,
  prop,
  groupBy,
  path,
  concat,
  reduce,
  mergeWith,
  append,
  __,
  join
} from "ramda";

const getPrompt = failure =>
  pipe(
    ifElse(
      prop("questions"),
      prop("questions"),
      pipe(prop("question"), append(__, []))
    ),
    map(prop("prompt")),
    join(" / ")
  )(failure);

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { section } = props;

  const groupByFn = failure =>
    pipe(
      path(["failures", failure]),
      ifElse(isNullOrEmpty, always([]), groupBy(getPrompt))
    )(section);

  const groupedPromptErrors = groupByFn("error");
  const groupedPromptWarnings = groupByFn("warning");
  const groupedPromptPresences = groupByFn("presence");

  const groupedIssues = reduce(
    (acc, val) => mergeWith(concat, val, acc),
    []
  )([groupedPromptErrors, groupedPromptWarnings, groupedPromptPresences]);

  return {
    groupedIssues,
    groupedPromptErrors,
    groupedPromptWarnings,
    groupedPromptPresences
  };
};
