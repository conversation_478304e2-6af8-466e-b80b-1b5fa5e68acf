import Failure from "./Failure";

import { keys, length } from "ramda";
import { isNullOrEmpty } from "utils/fp";
import { useComponentLogic } from "./hooks";

const DefaultValidations = props => {
  const { section, questionnaireResponseId, baseRoute } = props;

  const {
    groupedIssues,
    groupedPromptErrors,
    groupedPromptWarnings,
    groupedPromptPresences
  } = useComponentLogic(props);

  if (isNullOrEmpty(section.failures)) {
    return null;
  }

  const renderFailures = failures =>
    failures &&
    failures.map(failure => (
      <Failure
        key={groupedIssues[failure][0].id}
        prompt={failure}
        failure={groupedIssues[failure][0]}
        questionnaireResponseId={questionnaireResponseId}
        sectionId={section.sectionId}
        baseRoute={baseRoute}
        errorCount={length(groupedPromptErrors[failure])}
        warningCount={length(groupedPromptWarnings[failure])}
        presenceCount={length(groupedPromptPresences[failure])}
      />
    ));

  return (
    <div>
      <h4 className="response-section-header">{section.sectionName}</h4>
      {renderFailures(keys(groupedIssues))}
    </div>
  );
};

export default DefaultValidations;
