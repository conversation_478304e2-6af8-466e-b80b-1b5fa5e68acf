import { create } from "react-test-renderer";
import DefaultValidations from "..";
import { decoratedWithDispatch } from "utils/test/decorated";
import { failure } from "../Failure/__tests__/Failure-test";
import { MemoryRouter } from "react-router-dom";

export const section = {
  displayLogic: {
    groupId: "1419",
    id: "2889",
    isTopLevel: true,
    listMarker: "",
    longPrompt: false,
    name: "Demographics"
  },
  failures: { error: [failure] },
  sectionId: 1,
  sectionName: "Demographics"
};

describe("DefaultValidations", () => {
  function render(cmp) {
    return create(<MemoryRouter>{cmp}</MemoryRouter>);
  }

  test("renders DefaultValidations", () => {
    const { component } = decoratedWithDispatch(
      DefaultValidations,
      { section, questionnaireResponseId: 1, baseRoute: "/abstraction" },
      {
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [{ id: 0, failureMessage: "Cannot be blank!" }]
          },
          error: {},
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    const answerSourceLink = render(component);

    expect(answerSourceLink).toMatchSnapshot();
  });
});
