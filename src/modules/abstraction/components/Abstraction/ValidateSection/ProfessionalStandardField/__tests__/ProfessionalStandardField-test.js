import TestRenderer from "react-test-renderer";
import { ProfessionalStandardField } from "..";

describe("ProfessionalStandardField", () => {
  function render() {
    return TestRenderer.create(
      <ProfessionalStandardField
        field={{ name: "test", text: "Test", id: 1, selected: false }}
      />
    );
  }
  test("renders component correctly", () => {
    const result = render();

    expect(result).toMatchSnapshot();
  });
});
