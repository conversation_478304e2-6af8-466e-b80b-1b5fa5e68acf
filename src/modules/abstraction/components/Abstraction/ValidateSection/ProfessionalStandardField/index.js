export const ProfessionalStandardField = ({ field, handleChange }) => (
  <label
    htmlFor={field.name}
    className="tw-flex tw-items-center tw-justify-between tw-text-xs"
  >
    <input
      className="tw-mr-1 tw-h-4 tw-w-4"
      type="checkbox"
      name={field.name}
      onChange={handleChange}
      checked={field.selected}
      id={field.name}
    />
    <span>{field.text}</span>
  </label>
);

export default ProfessionalStandardField;
