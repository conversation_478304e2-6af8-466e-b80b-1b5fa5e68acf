// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ValidateSection renders component correctly 1`] = `
<div
  className="tw-grow-0 tw-bg-[#fcfcfc] tw-px-5 tw-py-2 tw-text-sm tw-text-[#828282]"
>
  <fieldset
    className="actions"
  >
    <ValidationButton
      questionnaireResponseId={1}
    />
  </fieldset>
</div>
`;

exports[`ValidateSection renders component correctly with standards 1`] = `
<div
  className="tw-grow-0 tw-bg-[#fcfcfc] tw-px-5 tw-py-2 tw-text-sm tw-text-[#828282]"
>
  <small
    className="tw-font-extrabold tw-italic"
  >
    Validate for:
  </small>
  <fieldset
    className="tw-mb-2 tw-flex tw-justify-between tw-gap-x-2"
  >
    <label
      className="tw-flex tw-items-center tw-justify-between tw-text-xs"
      htmlFor="state"
    >
      <input
        checked={false}
        className="tw-mr-1 tw-h-4 tw-w-4"
        id="state"
        name="state"
        onChange={[Function]}
        type="checkbox"
      />
      <span>
        State
      </span>
    </label>
    <label
      className="tw-flex tw-items-center tw-justify-between tw-text-xs"
      htmlFor="rcrs"
    >
      <input
        checked={true}
        className="tw-mr-1 tw-h-4 tw-w-4"
        id="rcrs"
        name="rcrs"
        onChange={[Function]}
        type="checkbox"
      />
      <span>
        RCRS
      </span>
    </label>
  </fieldset>
  <fieldset
    className="actions"
  >
    <ValidationButton
      questionnaireResponseId={1}
    />
  </fieldset>
</div>
`;
