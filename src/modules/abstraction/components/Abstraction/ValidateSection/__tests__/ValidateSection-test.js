import { create } from "react-test-renderer";
import { initialState } from "modules/questionnaire/reducers";
import decorated from "utils/test/decorated";
import { ValidateSection } from "..";

jest.mock("../ValidationButton", () => "ValidationButton");

describe("ValidateSection", () => {
  function render(standards = []) {
    return create(
      decorated(ValidateSection, { questionnaireResponseId: 1 }, null, {
        questionnaire: { ...initialState, standards }
      })
    );
  }
  test("renders component correctly", () => {
    const result = render();

    expect(result).toMatchSnapshot();
  });

  test("renders component correctly with standards", () => {
    const result = render([
      { name: "state", text: "State", selected: false },
      { name: "rcrs", text: "RCRS", selected: true }
    ]);

    expect(result).toMatchSnapshot();
  });
});
