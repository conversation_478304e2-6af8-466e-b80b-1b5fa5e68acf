import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setStandards } from "modules/questionnaire/actions";
import Questionnaire from "modules/questionnaire/selectors";
import { __, assoc, findIndex, lensIndex, over, pipe, propEq } from "ramda";

export const useComponentLogic = () => {
  const dispatch = useDispatch();
  const standards = useSelector(state => Questionnaire.getStandards(state));

  const handleChange = useCallback(
    e => {
      const newStandards = pipe(
        findIndex(propEq("name", e.target.name)),
        lensIndex,
        over(__, assoc("selected", e.target.checked), standards)
      )(standards);

      dispatch(setStandards(newStandards));
    },
    [dispatch, standards]
  );

  return { handleChange, standards };
};
