import { useCallback, useMemo, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  any,
  values,
  compose,
  fromPairs,
  map,
  props,
  none,
  propEq,
  prop,
  ifElse,
  not
} from "ramda";
import {
  useLinkButtonKeyboard,
  useValidationReportMutation
} from "modules/abstraction/hooks";
import panelTypes from "modules/questionnaire/constants/panelTypes";
import { pushFlashMessage } from "modules/app/actions";
import { setCurrentActivePanel } from "modules/questionnaire/actions";
import Questionnaire from "modules/questionnaire/selectors";
import { isNullOrEmpty } from "utils/fp";
/* eslint-disable-next-line max-statements */
export const useComponentLogic = ({ questionnaireResponseId }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const parsedId = useMemo(
    () => Number(questionnaireResponseId),
    [questionnaireResponseId]
  );
  const standards = useSelector(state => Questionnaire.getStandards(state));
  const { validateReport, isValidating } = useValidationReportMutation({
    questionnaireResponseId: parsedId
  });
  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(not);
  });

  const isHop29Qr = propEq("name", "Colonoscopy v17.0A")(questionnaire);

  const isRrtClinicalQuestionnaire = prop("regulatory_clinical")(questionnaire);

  const dispatch = useDispatch();
  const allFieldsSavingStatus = useSelector(
    state => state.app.status.mutationLoading
  );
  const isAnyFieldSaving = any(Boolean, values(allFieldsSavingStatus));
  const [shouldValidate, setShouldValidate] = useState(false);

  const handleValidateReport = useCallback(
    // eslint-disable-next-line complexity, max-statements
    event => {
      const nonExistentStandards = isNullOrEmpty(standards);
      const noStandardsSelected = none(propEq("selected", true))(standards);

      if (nonExistentStandards) {
        if (isHop29Qr) {
          setIsModalOpen(true);
        } else {
          validateReport(event);
        }

        if (isRrtClinicalQuestionnaire) {
          dispatch(
            setCurrentActivePanel(panelTypes.patientCareCompliancePanel)
          );
        }
        return;
      }

      if (noStandardsSelected) {
        dispatch(
          pushFlashMessage(
            "Please select State, RCRS, and/or NCDB to validate!"
          )
        );
        return;
      }

      const options = JSON.stringify({
        standardSetters: compose(
          fromPairs,
          map(props(["name", "selected"]))
        )(standards)
      });

      validateReport(event, options);
    },
    [validateReport, standards]
  );
  const handleValidateReportSubmit = ifElse(
    () => isAnyFieldSaving,
    () => setShouldValidate(true),
    () => handleValidateReport()
  );

  const { handleKeyBoardAction } = useLinkButtonKeyboard({
    actionDisabled: isValidating,
    handleValidateReportSubmit
  });

  useEffect(() => {
    if (!isAnyFieldSaving && shouldValidate) {
      handleValidateReport();
      setShouldValidate(false);
    }
  }, [isAnyFieldSaving, shouldValidate, handleValidateReport]);

  return {
    handleKeyBoardAction,
    isValidating,
    handleValidateReportSubmit,
    isModalOpen,
    setIsModalOpen,
    handleCloseModal
  };
};
