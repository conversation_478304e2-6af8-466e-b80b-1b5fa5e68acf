// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ValidationModal it renders component correctly 1`] = `
<ConfirmationModal
  isDanger={true}
  title="Run Validation Report"
>
  <div
    className="modal-content"
  >
    <p
      className="modal-paragraph"
    >
      If the Measure Category for this case is a 'B', it will be excluded and removed from the population and sample, and replaced with another eligible case. Please contact support if the measure category=B was validated in error.
      <br />
      <a
        href="mailto: <EMAIL>"
      >
        <EMAIL>
      </a>
    </p>
  </div>
  <div
    className="button-container"
  >
    <Button
      bg="success"
      customStyle="modal-buttons"
      onClick={[Function]}
    >
      <i
        className="fa-regular fa-check"
      />
      Confirm
    </Button>
    <Button
      bg="neutral"
      customStyle="modal-buttons"
      outline={true}
    >
      <i
        className="fa fa-xmark"
      />
      Cancel
    </Button>
  </div>
</ConfirmationModal>
`;
