import TestRenderer from "react-test-renderer";
import { Provider } from "react-redux";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import ValidationModal from "..";
import { useValidationReportMutation } from "modules/abstraction/hooks/useValidationReportMutation";

jest.mock("modules/abstraction/hooks/useValidationReportMutation");
jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal"
}));

describe("ValidationModal", () => {
  function render(loading) {
    return TestRenderer.create(
      <Provider
        store={getStore(
          {},
          {
            questionnaire: {
              ...initialState,
              questionnaire: {}
            },
            status: {
              mutationLoading: {
                validation: loading
              }
            }
          }
        )}
      >
        <ValidationModal />
      </Provider>
    );
  }

  test("it renders component correctly", () => {
    useValidationReportMutation.mockReturnValue({
      validationReport: jest.fn(),
      isValidating: false
    });

    const component = render(false);

    expect(component).toMatchSnapshot();
  });
});
