import { Button, ConfirmationModal } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const ValidationModal = props => {
  const { isModalOpen, handleCloseModal } = props;
  const { handleValidateReport } = useComponentLogic(props);

  return (
    <ConfirmationModal
      isDanger
      title="Run Validation Report"
      isOpen={isModalOpen}
    >
      <div className="modal-content">
        <p className="modal-paragraph">
          If the Measure Category for this case is a &apos;B&apos;, it will be
          excluded and removed from the population and sample, and replaced with
          another eligible case. Please contact support if the measure
          category=B was validated in error.
          <br />
          <a href="mailto: <EMAIL>"><EMAIL></a>
        </p>
      </div>
      <div className="button-container">
        <Button
          onClick={handleValidateReport}
          customStyle="modal-buttons"
          bg="success"
        >
          <i className="fa-regular fa-check" />
          Confirm
        </Button>
        <Button
          onClick={handleCloseModal}
          customStyle="modal-buttons"
          outline
          bg="neutral"
        >
          <i className="fa fa-xmark" />
          Cancel
        </Button>
      </div>
    </ConfirmationModal>
  );
};

export default ValidationModal;
