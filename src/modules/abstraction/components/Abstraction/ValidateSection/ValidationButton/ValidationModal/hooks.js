import { useCallback } from "react";
import { useValidationReportMutation } from "modules/abstraction/hooks";

export const useComponentLogic = props => {
  const { handleCloseModal, questionnaireResponseId } = props;

  const { validateReport } = useValidationReportMutation({
    questionnaireResponseId: Number(questionnaireResponseId)
  });

  const handleValidateReport = useCallback(
    event => {
      validateReport(event);
      handleCloseModal();
    },
    [validateReport]
  );

  return { handleValidateReport };
};
