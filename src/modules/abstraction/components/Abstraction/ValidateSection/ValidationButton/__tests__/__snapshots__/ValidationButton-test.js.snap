// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ValidationButton renders component correctly 1`] = `
<button
  className="validation-btn shadow-[4px_4px_4px_rgba(0,0,0,0.1)] tw-flex tw-min-w-min tw-items-center tw-justify-between tw-gap-x-1 tw-rounded-lg tw-border-2 tw-border-[#5faa40] tw-px-3.5 tw-py-2 tw-text-center tw-text-xs tw-font-extrabold tw-uppercase"
  disabled={false}
  onClick={[Function]}
  onKeyPress={[Function]}
  type="submit"
>
  Validate
  <i
    className="fal fa-arrow-circle-right tw-text-base"
  />
</button>
`;

exports[`ValidationButton renders component with isValidating = true 1`] = `
<button
  className="validation-btn shadow-[4px_4px_4px_rgba(0,0,0,0.1)] tw-flex tw-min-w-min tw-items-center tw-justify-between tw-gap-x-1 tw-rounded-lg tw-border-2 tw-border-[#5faa40] tw-px-3.5 tw-py-2 tw-text-center tw-text-xs tw-font-extrabold tw-uppercase"
  disabled={true}
  onClick={[Function]}
  onKeyPress={[Function]}
  type="submit"
>
  <span>
    please wait ...
  </span>
  <i
    className="fal fa-arrow-circle-right tw-text-base"
  />
</button>
`;
