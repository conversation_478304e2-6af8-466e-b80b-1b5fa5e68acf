import { act, create } from "react-test-renderer";
import wait from "waait";
import { Provider } from "react-redux";
import { initialState } from "modules/questionnaire/reducers";
import { getStore, decoratedApolloWithDispatch } from "utils/test/decorated";
import { ValidationButton } from "..";
import { useValidationReportMutation } from "modules/abstraction/hooks/useValidationReportMutation";
import { pushFlashMessage } from "modules/app/actions";

jest.mock("modules/abstraction/hooks/useValidationReportMutation");

describe("ValidationButton", () => {
  function render(loading) {
    return create(
      <Provider
        store={getStore(
          {},
          {
            questionnaire: {
              ...initialState,
              questionnaire: {},
              standards: ["State"]
            },
            status: {
              mutationLoading: {
                validation: loading
              }
            }
          }
        )}
      >
        <ValidationButton />
      </Provider>
    );
  }

  test("renders component correctly", () => {
    useValidationReportMutation.mockReturnValue({
      validationReport: jest.fn(),
      isValidating: false
    });

    const result = render(false);

    expect(result).toMatchSnapshot();
  });
  test("renders component with isValidating = true", () => {
    useValidationReportMutation.mockReturnValue({
      validationReport: jest.fn(),
      isValidating: true
    });

    const result = render(true);

    expect(result).toMatchSnapshot();
  });
  test("Flash message is sent with the correct message if the button is clicked without selecting State or CoC", async () => {
    useValidationReportMutation.mockReturnValue({
      validationReport: jest.fn(),
      isValidating: false
    });

    const { component, dispatch } = decoratedApolloWithDispatch({
      component: ValidationButton,
      props: {},
      initialAppValues: {
        questionnaire: {
          ...initialState,
          questionnaire: {},
          standards: [
            { name: "state", text: "State", id: 0, selected: false },
            { name: "rcrs", text: "RCRS", id: 1, selected: false },
            { name: "ncdb", text: "NCDB", id: 2, selected: false }
          ]
        },
        status: {
          mutationLoading: {
            validation: false
          }
        }
      }
    });
    const output = create(component);
    const instance = output.root;
    const [button] = instance.findAllByType("button");

    act(() => {
      button.props.onClick();
    });

    await act(() => wait(5));

    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage("Please select State, RCRS, and/or NCDB to validate!")
    );
  });
});
