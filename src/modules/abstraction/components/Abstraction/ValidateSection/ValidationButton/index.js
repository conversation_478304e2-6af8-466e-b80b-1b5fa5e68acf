import { FormattedMessage } from "react-intl";
import ValidationModal from "./ValidationModal";
import { useComponentLogic } from "./hooks";

export const ValidationButton = props => {
  const { questionnaireResponseId } = props;
  const {
    handleKeyBoardAction,
    isValidating,
    handleValidateReportSubmit,
    isModalOpen,
    handleCloseModal
  } = useComponentLogic(props);

  return (
    <>
      <ValidationModal
        isModalOpen={isModalOpen}
        questionnaireResponseId={questionnaireResponseId}
        handleCloseModal={handleCloseModal}
      />
      <button
        type="submit"
        className="validation-btn shadow-[4px_4px_4px_rgba(0,0,0,0.1)] tw-flex tw-min-w-min tw-items-center tw-justify-between tw-gap-x-1 tw-rounded-lg tw-border-2 tw-border-[#5faa40] tw-px-3.5 tw-py-2 tw-text-center tw-text-xs tw-font-extrabold tw-uppercase"
        onClick={handleValidateReportSubmit}
        onKeyPress={handleKeyBoardAction}
        disabled={isValidating}
      >
        {isValidating ? (
          <FormattedMessage
            id="components.wait"
            defaultMessage="please wait ..."
          />
        ) : (
          "Validate"
        )}
        <i className="fal fa-arrow-circle-right tw-text-base" />
      </button>
    </>
  );
};

export default ValidationButton;
