import ValidationButton from "./ValidationButton";
import ProfessionalStandardField from "./ProfessionalStandardField";
import { useComponentLogic } from "./hooks";

export const ValidateSection = ({ questionnaireResponseId }) => {
  const { handleChange, standards } = useComponentLogic();

  return (
    <div className="tw-grow-0 tw-bg-[#fcfcfc] tw-px-5 tw-py-2 tw-text-sm tw-text-[#828282]">
      {standards.length > 0 && (
        <>
          <small className="tw-font-extrabold tw-italic">Validate for:</small>
          <fieldset className="tw-mb-2 tw-flex tw-justify-between tw-gap-x-2">
            {standards.map(field => (
              <ProfessionalStandardField
                key={field.id}
                field={field}
                handleChange={handleChange}
              />
            ))}
          </fieldset>
        </>
      )}
      <fieldset className="actions">
        <ValidationButton questionnaireResponseId={questionnaireResponseId} />
      </fieldset>
    </div>
  );
};

export default ValidateSection;
