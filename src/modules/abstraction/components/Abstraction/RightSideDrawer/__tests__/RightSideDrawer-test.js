import TestRenderer from "react-test-renderer";
import RightSideDrawer from "..";

jest.mock("../Drawer", () => "Drawer");
jest.mock("../SideBar", () => "SideBar");

describe("RightSideDrawer", () => {
  const getComponent = props => <RightSideDrawer {...props} />;

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent());

    expect(result).toMatchSnapshot();
  });
  test("renders disabled", () => {
    const result = TestRenderer.create(getComponent({ disabled: true }));

    expect(result).toMatchSnapshot();
  });
});
