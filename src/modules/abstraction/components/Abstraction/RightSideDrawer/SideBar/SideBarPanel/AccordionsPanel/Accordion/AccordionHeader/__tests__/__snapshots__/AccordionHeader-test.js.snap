// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AccordionHeader renders correctly 1`] = `
<div
  className="accordion-header open"
>
  <h6>
    test
  </h6>
  <div
    className="accordion-header-right-side"
  >
    <span
      className="character-count"
    >
      0
       / 
      1
    </span>
    <i
      className="fa fas fa-caret-right"
      style={Object {}}
    />
  </div>
</div>
`;

exports[`AccordionHeader renders with open = false 1`] = `
<div
  className="accordion-header"
>
  <h6>
    test
  </h6>
  <div
    className="accordion-header-right-side"
  >
    <span
      className="character-count"
    >
      0
       / 
      1
    </span>
    <i
      className="fa fas fa-caret-right"
      style={Object {}}
    />
  </div>
</div>
`;

exports[`AccordionHeader renders without maxCharacters 1`] = `
<div
  className="accordion-header open"
>
  <h6>
    test
  </h6>
  <div
    className="accordion-header-right-side"
  >
    0
    <i
      className="fa fas fa-caret-right"
      style={Object {}}
    />
  </div>
</div>
`;
