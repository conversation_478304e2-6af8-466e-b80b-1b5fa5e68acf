import classnames from "classnames";
import { motion } from "framer-motion";
import { useComponentLogic } from "./hooks";

const iconClassName = classnames("fa fas fa-caret-right");
const rotateVariants = {
  close: { rotate: 0 },
  open: { rotate: 90 }
};

export const AccordionHeader = ({
  currentCharacters,
  open,
  question,
  toggleAccordion
}) => {
  const { maxCharacters, prompt } = question;
  const { animate, isCurrentGreaterThanMax } = useComponentLogic({
    currentCharacters,
    maxCharacters,
    open
  });
  const accordionHeaderClass = classnames("accordion-header", {
    open,
    red: isCurrentGreaterThanMax
  });

  return (
    <div className={accordionHeaderClass} onClick={toggleAccordion}>
      <h6>{prompt}</h6>
      <div className="accordion-header-right-side">
        {maxCharacters && (
          <span className="character-count">
            {currentCharacters} / {maxCharacters}
          </span>
        )}
        <motion.i
          animate={animate}
          className={iconClassName}
          variants={rotateVariants}
        />
      </div>
    </div>
  );
};

export default AccordionHeader;
