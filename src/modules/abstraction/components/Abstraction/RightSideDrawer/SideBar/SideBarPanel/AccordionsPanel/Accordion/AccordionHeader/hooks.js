import { useMemo } from "react";
import { gt } from "ramda";

export const useComponentLogic = ({
  currentCharacters,
  maxCharacters,
  open
}) => {
  const isCurrentGreaterThanMax = useMemo(
    () => gt(currentCharacters, maxCharacters),
    [currentCharacters, maxCharacters]
  );
  const animate = useMemo(() => (open ? "open" : "closed"), [open]);

  return { animate, isCurrentGreaterThanMax };
};
