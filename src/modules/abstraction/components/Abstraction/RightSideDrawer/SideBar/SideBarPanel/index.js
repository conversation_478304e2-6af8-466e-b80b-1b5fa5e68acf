import SideBarPanelHeader from "./SideBarPanelHeader";
import AccordionsPanel from "./AccordionsPanel";
import { useComponentLogic } from "./hooks";

// Unsure if DisplayLogics will get icon
const icon = "fas fa-hand-holding-medical";

export const SideBarPanel = ({ panel, questionnaireResponseId }) => {
  const { sectionAnswerGroup } = useComponentLogic(panel);

  return (
    <div className="abstraction-sidebar-panel">
      <SideBarPanelHeader icon={icon} text={panel.name} />
      <div className="abstraction-sidebar-panel-content">
        {panel.children.map(child => (
          <AccordionsPanel
            key={child.id}
            panel={child}
            questionnaireResponseId={questionnaireResponseId}
            sectionAnswerGroup={sectionAnswerGroup}
          />
        ))}
      </div>
      <div className="resize-handle" />
    </div>
  );
};

export default SideBarPanel;

// need answerGroupName/Id to pass into useSingleValue, answerType: "text-area"
