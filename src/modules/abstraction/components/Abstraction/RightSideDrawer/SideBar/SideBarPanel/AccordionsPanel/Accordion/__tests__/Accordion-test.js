import TestRenderer from "react-test-renderer";
import { Provider } from "react-redux";
import MockedOncologyProvider from "modules/abstraction/components/Main/MockedOncologyProvider";
import { initialState as questionnaire } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import Accordion from "..";

const props = {
  question: { groupId: 1, prompt: "Test", maxCharacters: 100 }
};

describe("Accordion", () => {
  const getComponent = () => (
    <Provider
      store={getStore(
        {},
        {
          questionnaire,
          questions: { visibleQuestions: [] }
        }
      )}
    >
      <MockedOncologyProvider>
        <Accordion {...props} />
      </MockedOncologyProvider>
    </Provider>
  );

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent());

    expect(result).toMatchSnapshot();
  });
});
