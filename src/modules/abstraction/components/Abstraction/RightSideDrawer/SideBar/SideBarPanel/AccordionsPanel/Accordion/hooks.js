import { useCallback, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { defaultTo, length, not } from "ramda";
import { useQuestionAnswerGroupAndName } from "modules/abstraction/hooks";
import { useSingleValue } from "modules/fields/components/hooks/useValue";
import Questions from "modules/question/selectors";

export const useComponentLogic = props => {
  const { answerGroup } = props;
  const { fieldAnswerGroup, name } = useQuestionAnswerGroupAndName(props);
  const isEnabled = useSelector(state =>
    Questions.hasVisibleQuestion(name, state)
  );
  const valueProps = useMemo(
    () => ({
      questionAnswerGroup: fieldAnswerGroup,
      name,
      parentAnswerGroup: answerGroup,
      ...props
    }),
    [fieldAnswerGroup, answerGroup, name]
  );
  const { value, handleSave, handleChange } = useSingleValue({
    ...valueProps,
    answerType: "text-area"
  });

  const [open, setOpen] = useState(false);

  const toggleAccordion = useCallback(() => setOpen(not), [setOpen]);
  const realValue = defaultTo("", value);
  const currentCharacters = length(realValue);

  return {
    currentCharacters,
    disabled: !isEnabled,
    fieldAnswerGroup,
    handleChange,
    handleSave,
    name,
    open,
    toggleAccordion,
    value: realValue
  };
};
