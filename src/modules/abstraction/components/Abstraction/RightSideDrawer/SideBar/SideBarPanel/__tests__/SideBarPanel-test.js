import { Provider } from "react-redux";
import Test<PERSON>enderer from "react-test-renderer";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import SideBarPanel from "..";

jest.mock("../AccordionsPanel", () => "AccordionsPanel");

const props = {
  panel: {
    answerGroups: [{ id: 1 }],
    children: [{ id: 1 }]
  }
};

describe("SideBarPanel", () => {
  const getComponent = () => (
    <Provider store={getStore({}, { questionnaire: initialState })}>
      <SideBarPanel {...props} />
    </Provider>
  );

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent());

    expect(result).toMatchSnapshot();
  });
});
