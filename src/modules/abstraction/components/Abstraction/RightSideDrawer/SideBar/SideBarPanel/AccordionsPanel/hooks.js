import { useMemo } from "react";
import { defaultTo, head, pipe } from "ramda";
import { getInstances } from "modules/questionnaire/components/Questionnaire/Section/enhancers";

export const useComponentLogic = ({ sectionAnswerGroup, panel }) => {
  const answerGroups = useMemo(
    () => getInstances(sectionAnswerGroup, panel),
    [sectionAnswerGroup, panel]
  );
  const answerGroup = pipe(defaultTo([]), head)(answerGroups);

  return { answerGroup, answerGroups };
};
