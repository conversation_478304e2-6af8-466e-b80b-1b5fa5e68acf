// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AccordionsPanel renders correctly 1`] = `
<div
  className="accordions-panel"
>
  <Accordion
    answerGroup={
      Object {
        "descendants": Array [
          Object {
            "descendants": Array [],
            "groupId": "152",
            "id": "1798",
            "parentId": "726",
          },
        ],
        "groupId": "151",
        "id": "726",
        "parentId": null,
      }
    }
    answerGroups={
      Array [
        Object {
          "descendants": Array [
            Object {
              "descendants": Array [],
              "groupId": "152",
              "id": "1798",
              "parentId": "726",
            },
          ],
          "groupId": "151",
          "id": "726",
          "parentId": null,
        },
      ]
    }
    collapsible={false}
    hideable={false}
    isBooleanSequence={false}
    isInline={false}
    question={
      Object {
        "id": 1,
      }
    }
    sectionAnswerGroup={
      Object {
        "descendants": Array [
          Object {
            "descendants": Array [],
            "groupId": "152",
            "id": "1798",
            "parentId": "726",
          },
        ],
        "groupId": "151",
        "id": "726",
        "parentId": null,
      }
    }
  />
</div>
`;
