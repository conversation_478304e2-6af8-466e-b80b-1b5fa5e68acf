import classnames from "classnames";
import AccordionContent from "./AccordionContent";
import AccordionHeader from "./AccordionHeader";
import { useComponentLogic } from "./hooks";

export const Accordion = props => {
  const { question } = props;
  const {
    currentCharacters,
    disabled,
    handleChange,
    handleSave,
    open,
    toggleAccordion,
    value
  } = useComponentLogic(props);

  const accordionClass = classnames("accordion", { open });

  return (
    <div className={accordionClass}>
      <AccordionHeader
        currentCharacters={currentCharacters}
        open={open}
        question={question}
        toggleAccordion={toggleAccordion}
      />
      <AccordionContent
        disabled={disabled}
        handleChange={handleChange}
        handleSave={handleSave}
        open={open}
        value={value}
      />
    </div>
  );
};

export default Accordion;
