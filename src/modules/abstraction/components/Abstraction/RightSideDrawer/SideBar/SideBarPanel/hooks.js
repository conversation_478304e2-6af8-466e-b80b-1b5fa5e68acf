import { useMemo } from "react";
import { useCurrentSectionAndSectionAnswerGroup } from "modules/abstraction/hooks";
import { head, pipe, prop } from "ramda";

export const useComponentLogic = panel => {
  const id = useMemo(
    () => pipe(prop("answerGroups"), head, prop("id"))(panel),
    [panel]
  );
  const { sectionAnswerGroup } = useCurrentSectionAndSectionAnswerGroup(id);

  return { sectionAnswerGroup };
};
