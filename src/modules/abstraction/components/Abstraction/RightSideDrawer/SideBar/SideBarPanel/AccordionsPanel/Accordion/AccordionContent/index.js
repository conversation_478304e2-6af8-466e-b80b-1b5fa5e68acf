import { AnimatePresence, motion } from "framer-motion";

const transition = { duration: 0.2 };
const variants = {
  open: {
    opacity: 1,
    height: "auto"
  },
  close: {
    opacity: 0,
    height: 0
  }
};

export const AccordionContent = ({
  disabled,
  handleChange,
  handleSave,
  open,
  value
}) => (
  <AnimatePresence>
    {open && (
      <motion.div
        animate="open"
        className="accordion-content"
        exit="close"
        initial="close"
        transition={transition}
        variants={variants}
      >
        <textarea
          disabled={disabled}
          onBlur={handleSave}
          onChange={handleChange}
          type="text"
          value={value}
        />
      </motion.div>
    )}
  </AnimatePresence>
);

export default AccordionContent;
