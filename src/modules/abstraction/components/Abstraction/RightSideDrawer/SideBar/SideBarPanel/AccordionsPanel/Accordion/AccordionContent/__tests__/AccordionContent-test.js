import TestRenderer from "react-test-renderer";
import AccordionContent from "..";

jest.mock("framer-motion", () => ({
  AnimatePresence: () => "AnimatePresence",
  motion: {
    div: () => "motion.div"
  }
}));

describe("AccordionContent", () => {
  const getComponent = open => <AccordionContent open={open} />;

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent(true));

    expect(result).toMatchSnapshot();
  });

  test("renders with open = false", () => {
    const result = TestRenderer.create(getComponent(false));

    expect(result).toMatchSnapshot();
  });
});
