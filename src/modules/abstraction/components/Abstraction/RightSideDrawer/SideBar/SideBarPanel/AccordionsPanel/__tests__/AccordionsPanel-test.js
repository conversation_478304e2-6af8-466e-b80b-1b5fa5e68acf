import TestRenderer from "react-test-renderer";
import AccordionsPanel from "..";

jest.mock("../Accordion", () => "Accordion");

const sectionAnswerGroup = {
  descendants: [
    {
      descendants: [],
      groupId: "152",
      id: "1798",
      parentId: "726"
    }
  ],
  groupId: "151",
  id: "726",
  parentId: null
};

const props = {
  sectionAnswerGroup,
  panel: {
    answerGroups: [sectionAnswerGroup],
    helpInformation: [],
    questions: [{ id: 1 }]
  }
};

describe("AccordionsPanel", () => {
  const getComponent = () => <AccordionsPanel {...props} />;

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent());

    expect(result).toMatchSnapshot();
  });
});
