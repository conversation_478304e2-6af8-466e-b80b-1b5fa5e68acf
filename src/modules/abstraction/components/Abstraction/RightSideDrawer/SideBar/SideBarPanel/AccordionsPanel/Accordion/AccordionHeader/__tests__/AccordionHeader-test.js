import { create } from "react-test-renderer";
import AccordionHeader from "..";

const standardProps = {
  currentCharacters: 0
};

const mockedComponent = props => <AccordionHeader {...props} />;

describe("AccordionHeader", () => {
  test("renders correctly", () => {
    const props = {
      ...standardProps,
      open: true,
      question: { maxCharacters: 1, prompt: "test" }
    };
    const result = create(mockedComponent(props));

    expect(result).toMatchSnapshot();
  });

  test("renders with open = false", () => {
    const props = {
      ...standardProps,
      open: false,
      question: { maxCharacters: 1, prompt: "test" }
    };
    const result = create(mockedComponent(props));

    expect(result).toMatchSnapshot();
  });

  test("renders without maxCharacters", () => {
    const props = {
      ...standardProps,
      open: true,
      question: { maxCharacters: 0, prompt: "test" }
    };
    const result = create(mockedComponent(props));

    expect(result).toMatchSnapshot();
  });
});
