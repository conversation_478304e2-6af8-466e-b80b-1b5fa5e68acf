import { isNullOrEmpty } from "utils/fp";
import Accordion from "./Accordion";
import { useComponentLogic } from "./hooks";

export const AccordionsPanel = props => {
  const { panel, ...otherProps } = props;
  const { answerGroup, answerGroups } = useComponentLogic(props);

  return (
    <div className="accordions-panel">
      {!isNullOrEmpty(panel.questions) &&
        panel.questions.map((question, index) => (
          <Accordion
            key={question.id}
            question={question}
            helpInfo={panel.helpInformation[index]}
            isBooleanSequence={false}
            isInline={false}
            collapsible={false}
            hideable={false}
            answerGroups={answerGroups}
            answerGroup={answerGroup}
            {...otherProps}
          />
        ))}
    </div>
  );
};

export default AccordionsPanel;
