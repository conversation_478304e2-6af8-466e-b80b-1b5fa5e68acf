import isNullOrEmpty from "utils/fp/isNullOrEmpty";
import SideBarPanel from "./SideBarPanel";
import { useComponentLogic } from "./hooks";

export const SideBar = ({ questionnaireResponseId }) => {
  const { persistentQuestions } = useComponentLogic();

  if (isNullOrEmpty(persistentQuestions)) return null;

  return (
    <div className="abstraction-sidebar">
      {persistentQuestions.map(section => (
        <SideBarPanel
          key={section.id}
          panel={section}
          questionnaireResponseId={questionnaireResponseId}
        />
      ))}
    </div>
  );
};

export default SideBar;
