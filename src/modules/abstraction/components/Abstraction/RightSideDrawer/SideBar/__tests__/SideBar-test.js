import { Provider } from "react-redux";
import TestRenderer from "react-test-renderer";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import SideBar from "..";

jest.mock("../SideBarPanel", () => "SideBarPanel");

describe("SideBar", () => {
  const getComponent = persistentQuestions => (
    <Provider
      store={getStore(
        {},
        { questionnaire: { ...initialState, persistentQuestions } }
      )}
    >
      <SideBar />
    </Provider>
  );

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent([{ id: 1 }]));

    expect(result).toMatchSnapshot();
  });
  test("renders empty", () => {
    const result = TestRenderer.create(getComponent([]));

    expect(result).toMatchSnapshot();
  });
});
