import TestRenderer from "react-test-renderer";
import Drawer from "..";

jest.mock("framer-motion", () => ({
  motion: {
    div: () => "motion.div"
  }
}));

describe("Drawer", () => {
  const getComponent = isActive => <Drawer isActive={isActive} />;

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent(true));

    expect(result).toMatchSnapshot();
  });
  test("renders with isActive = false", () => {
    const result = TestRenderer.create(getComponent(false));

    expect(result).toMatchSnapshot();
  });
});
