import classnames from "classnames";
import { motion } from "framer-motion";

const Drawer = ({ children, dragElastic, isActive, onDragEnd }) => {
  const wrapperClass = classnames("drawer-wrapper", { active: isActive });
  const containerClass = classnames("drawer-container");

  return (
    <motion.div
      drag="x"
      dragConstraints={{ left: 0, right: 0 }}
      dragElastic={dragElastic}
      className={wrapperClass}
      onDragEnd={onDragEnd}
    >
      <motion.div
        animate={{ opacity: 1, x: isActive ? 0 : "96%" }}
        className={containerClass}
        initial={{ opacity: 1, x: 0 }}
        transition={{ damping: 20, type: "spring" }}
      >
        <div className="drawer">{children}</div>
      </motion.div>
    </motion.div>
  );
};

export default Drawer;
