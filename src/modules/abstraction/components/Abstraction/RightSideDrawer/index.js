import "modules/abstraction/styles/right-side-drawer.scss";
import Drawer from "./Drawer";
import SideBar from "./SideBar";

export const RightSideDrawer = ({
  disabled,
  dragElastic,
  isActive,
  onDragEnd,
  ...otherProps
}) => {
  if (disabled) return null;
  return (
    <Drawer dragElastic={dragElastic} isActive={isActive} onDragEnd={onDragEnd}>
      <div className="right-side-drawer-container">
        <div className="drawer-handle" />
        <div className="right-side-drawer">
          <SideBar {...otherProps} />
        </div>
      </div>
    </Drawer>
  );
};

export default RightSideDrawer;
