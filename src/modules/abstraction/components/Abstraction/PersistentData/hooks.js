import { useMemo } from "react";
import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import { join, defaultTo, values, path, either, prop } from "ramda";
import moment from "moment";

export const usePersistentDataSelectors = () => {
  const patient = useSelector(state => Questionnaire.getPatient(state));
  const persistentData = useSelector(state =>
    Questionnaire.getPersistentData(state)
  );
  const visit = useSelector(state => Questionnaire.getVisit(state));

  const questionnaireData = useSelector(state =>
    path(["app", "questionnaire", "questionnaire"])(state)
  );

  return {
    patient,
    persistentData,
    visit,
    questionnaireData
  };
};

export const useComponentLogic = () => {
  const {
    patient,
    persistentData,
    visit: { number, inpatient, dischargedAt, admittedAt } = {},
    questionnaireData
  } = usePersistentDataSelectors();
  const persistentDataIds = values(persistentData);
  const name = useMemo(
    () =>
      join(" ", [
        defaultTo("", patient.firstName),
        defaultTo("", patient.lastName)
      ]),
    [patient.firstName, patient.lastName]
  );
  const visitMRN = useMemo(
    () => join(" / ", [defaultTo("", number), defaultTo("", patient.mrn)]),
    [patient.mrn, number]
  );

  const dateLabel = inpatient ? "Date of Discharge" : "Date of Encounter";

  const dateToDisplay = inpatient
    ? moment.unix(dischargedAt).format("MM/DD/YYYY")
    : moment.unix(admittedAt).format("MM/DD/YYYY");

  const isRRP = either(
    prop("regulatory_administrative"),
    prop("regulatory_clinical")
  )(questionnaireData);

  return {
    persistentDataIds,
    name,
    visitMRN,
    dateToDisplay,
    dateLabel,
    isRRP
  };
};
