import { create } from "react-test-renderer";
import query from "modules/question/components/Question/EnumerableSearchable/query";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import PersistentDataElement from "..";

const apolloMocks = [
  {
    request: { query, options: { variables: { id: 1 } } },
    result: { questionEnumerables: [{}] }
  }
];

const mockedComponent = (props = { id: 1 }) =>
  create(
    decoratedApollo({
      component: PersistentDataElement,
      props,
      initialValues: {},
      initialAppValues: {
        questionnaire: {
          answers: { "1|1": { answerData: "Test Answer" } },
          questionnaire: {
            questions: { 1: { prompt: "Test Prompt", type: "Open" } }
          }
        }
      },
      apolloMocks
    })
  );

describe("PersistentDataElement", () => {
  test("renders component", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
});
