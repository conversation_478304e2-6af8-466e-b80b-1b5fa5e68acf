import { useQuestionAndAnswer } from "modules/abstraction/hooks";

export const PersistentDataElement = props => {
  const { answer, prompt } = useQuestionAndAnswer(props);

  return (
    <div className="persistent-data-element">
      <div className="element-value">{answer}</div>
      <hr />
      <div className="element-label">{prompt}</div>
    </div>
  );
};

export default PersistentDataElement;
