import { Provider } from "react-redux";
import { act, create } from "react-test-renderer";
import wait from "waait";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import PersistentData from "..";

const persistentData = JSON.stringify({
  arrivalDateQuestionId: "1",
  dateOfBirthQuestionId: "3",
  dischargeDateQuestionId: "2"
});

jest.mock("../PersistentDataElement", () => "PersistentDataElement");

describe("PersistentData", () => {
  const getComponent = ({ inpatient = false, regulatoryClinical = false }) => (
    <Provider
      store={getStore(
        {},
        {
          questionnaire: {
            ...initialState,
            patient: {
              firstName: "John",
              lastName: "Doe",
              mrn: "222"
            },
            questionnaire: {
              persistentData,
              regulatoryClinical
            },
            visit: {
              number: "111",
              arrivedAt: **********,
              dischargedAt: **********,
              inpatient
            }
          }
        }
      )}
    >
      <PersistentData />
    </Provider>
  );

  test("renders correctly", async () => {
    const result = create(getComponent({ inpatient: false }));

    await act(() => wait(100));

    expect(result).toMatchSnapshot();
  });

  test("renders correctly with inpatient flag", async () => {
    const result = create(getComponent({ inpatient: true }));

    await act(() => wait(100));

    expect(result).toMatchSnapshot();
  });

  test("renders correctly with regulatoryClinical flag", async () => {
    const result = create(getComponent({ regulatoryClinical: true }));

    await act(() => wait(100));

    expect(result).toMatchSnapshot();
  });
});
