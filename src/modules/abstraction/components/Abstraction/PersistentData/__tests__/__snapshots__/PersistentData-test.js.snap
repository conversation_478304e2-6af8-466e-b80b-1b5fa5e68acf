// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PersistentData renders correctly 1`] = `
<div
  className="persistent-data"
>
  <div
    className="persistent-data-element"
  >
    <div
      className="element-value"
    >
      <PERSON>
    </div>
    <hr />
    <div
      className="element-label"
    >
      Patient
    </div>
  </div>
  <div
    className="persistent-data-element"
  >
    <div
      className="element-value"
    >
      111 / 222
    </div>
    <hr />
    <div
      className="element-label"
    >
      Visit / MRN
    </div>
  </div>
  <PersistentDataElement
    id="1"
  />
  <PersistentDataElement
    id="3"
  />
  <PersistentDataElement
    id="2"
  />
</div>
`;

exports[`PersistentData renders correctly with inpatient flag 1`] = `
<div
  className="persistent-data"
>
  <div
    className="persistent-data-element"
  >
    <div
      className="element-value"
    >
      <PERSON>
    </div>
    <hr />
    <div
      className="element-label"
    >
      Patient
    </div>
  </div>
  <div
    className="persistent-data-element"
  >
    <div
      className="element-value"
    >
      111 / 222
    </div>
    <hr />
    <div
      className="element-label"
    >
      Visit / MRN
    </div>
  </div>
  <PersistentDataElement
    id="1"
  />
  <PersistentDataElement
    id="3"
  />
  <PersistentDataElement
    id="2"
  />
</div>
`;

exports[`PersistentData renders correctly with regulatoryClinical flag 1`] = `
<div
  className="persistent-data"
>
  <div
    className="persistent-data-element"
  >
    <div
      className="element-value"
    >
      John Doe
    </div>
    <hr />
    <div
      className="element-label"
    >
      Patient
    </div>
  </div>
  <div
    className="persistent-data-element"
  >
    <div
      className="element-value"
    >
      111 / 222
    </div>
    <hr />
    <div
      className="element-label"
    >
      Visit / MRN
    </div>
  </div>
  <PersistentDataElement
    id="1"
  />
  <PersistentDataElement
    id="3"
  />
  <PersistentDataElement
    id="2"
  />
</div>
`;
