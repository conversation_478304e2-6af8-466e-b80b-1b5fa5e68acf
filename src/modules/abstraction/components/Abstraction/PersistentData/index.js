import { useComponentLogic } from "./hooks";
import PersistentDataElement from "./PersistentDataElement";

export const PersistentData = () => {
  const { persistentDataIds, name, visitMRN, dateToDisplay, dateLabel, isRRP } =
    useComponentLogic();

  return (
    <div className="persistent-data">
      <div className="persistent-data-element">
        <div className="element-value">{name}</div>
        <hr />
        <div className="element-label">Patient</div>
      </div>
      <div className="persistent-data-element">
        <div className="element-value">{visitMRN}</div>
        <hr />
        <div className="element-label">Visit / MRN</div>
      </div>
      {isRRP && (
        <div className="persistent-data-element">
          <div className="element-value">{dateToDisplay}</div>
          <hr />
          <div className="element-label">{dateLabel}</div>
        </div>
      )}
      {persistentDataIds.map(id => (
        <PersistentDataElement id={id} key={id} />
      ))}
    </div>
  );
};

export default PersistentData;
