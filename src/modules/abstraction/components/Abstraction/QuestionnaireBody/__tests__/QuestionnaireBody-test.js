import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import TestRenderer from "react-test-renderer";
import { initialState } from "modules/questionnaire/reducers";
import { getStore } from "utils/test/decorated";
import QuestionnaireBody from "..";

jest.mock("modules/questionnaire/selectors", () => ({
  getCurrentSection: value => ({ rightSidePane: Boolean(value) })
}));
jest.mock("framer-motion", () => ({
  motion: {
    div: () => "motion.div"
  }
}));

describe("QuestionnaireBody", () => {
  const getComponent = currentAnswerGroup => (
    <Provider store={getStore({}, { questionnaire: { ...initialState } })}>
      <MemoryRouter>
        <QuestionnaireBody currentAnswerGroup={currentAnswerGroup} />
      </MemoryRouter>
    </Provider>
  );

  test("renders correctly", () => {
    const result = TestRenderer.create(getComponent(true));

    expect(result).toMatchSnapshot();
  });
  test("renders with rightSidePane = false", () => {
    const result = TestRenderer.create(getComponent(false));

    expect(result).toMatchSnapshot();
  });
});
