import { motion } from "framer-motion";
import RightSideDrawer from "../RightSideDrawer";
import Section from "../Section";
import { useComponentLogic } from "./hooks";

export const QuestionnaireBody = ({
  questionnaireResponseId,
  currentAnswerGroup,
  focusFieldName
}) => {
  const { containerRef, disabled, dragElastic, isActive, onDragEnd } =
    useComponentLogic(currentAnswerGroup);

  return (
    <div className="questionnaire-body">
      <motion.div
        animate={{
          flexGrow: isActive ? 0 : 1,
          width: isActive ? "56vw" : "75vw"
        }}
        className="questionnaire-container"
        ref={containerRef}
        transition={{ damping: 20, type: "spring" }}
      >
        <Section
          questionnaireResponseId={questionnaireResponseId}
          id={currentAnswerGroup}
          focusFieldName={focusFieldName}
        />
      </motion.div>

      <RightSideDrawer
        disabled={disabled}
        dragElastic={dragElastic}
        isActive={isActive}
        onDragEnd={onDragEnd}
        questionnaireResponseId={questionnaireResponseId}
      />
    </div>
  );
};

export default QuestionnaireBody;
