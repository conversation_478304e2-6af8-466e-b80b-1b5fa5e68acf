import { useCallback, useEffect, useMemo, useState } from "react";
import { always, identity, ifElse, propOr } from "ramda";
import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import { useScrollToTop } from "utils/hooks/useScrollToTop";

const defaultToFalse = propOr(false, "rightSidePane");

export const useComponentLogic = currentAnswerGroup => {
  const currentSection = useSelector(state =>
    Questionnaire.getCurrentSection(currentAnswerGroup, state)
  );
  const { containerRef } = useScrollToTop();

  const [isActive, setIsActive] = useState(defaultToFalse(currentSection));
  const isRightSidePaneEnabled = useMemo(
    () => defaultToFalse(currentSection),
    [currentSection]
  );
  const enabledDragEnd = useCallback(
    (_, { offset }) => {
      if (offset.x > 150) setIsActive(false);
      if (offset.x < -150) setIsActive(true);
    },
    [setIsActive]
  );
  const onDragEnd = useMemo(
    () =>
      ifElse(
        identity,
        always(enabledDragEnd),
        always(() => null)
      )(isRightSidePaneEnabled),
    [enabledDragEnd, isRightSidePaneEnabled]
  );

  const dragElastic = useMemo(
    () => ifElse(identity, always(0.5), always(0))(isRightSidePaneEnabled),
    [isRightSidePaneEnabled]
  );

  useEffect(() => {
    setIsActive(defaultToFalse(currentSection));
  }, [currentSection, setIsActive]);

  return {
    containerRef,
    disabled: !isRightSidePaneEnabled,
    dragElastic,
    isActive,
    onDragEnd
  };
};
