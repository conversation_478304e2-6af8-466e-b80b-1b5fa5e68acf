import classnames from "classnames";
import Panels from "modules/questionnaire/components/Questionnaire/SidePane/Panels";

export function SidePane(props) {
  const { isSidePanelExpanded } = props;
  const asideClass = classnames("sidepanel", {
    "expanded-side": isSidePanelExpanded
  });

  return (
    <aside className={asideClass}>
      <div className="sidepanel-layout">
        <Panels {...props} baseRoute="/abstraction" />
      </div>
    </aside>
  );
}
export default SidePane;
