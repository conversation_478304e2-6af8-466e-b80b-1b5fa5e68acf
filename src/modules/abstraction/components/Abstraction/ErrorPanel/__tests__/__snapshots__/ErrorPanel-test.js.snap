// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ErrorPanel renders ErrorPanel 1`] = `
<div
  className="error-panel"
  style={
    Object {
      "transform": "translateX(-100%) translateZ(0)",
    }
  }
/>
`;

exports[`ErrorPanel renders ErrorPanel with children 1`] = `
<div
  className="error-panel"
  style={
    Object {
      "transform": "translateX(-100%) translateZ(0)",
    }
  }
>
  <p>
    Lorem ipsum dolor sit amet consectetur.
  </p>
</div>
`;

exports[`ErrorPanel renders ErrorPanel with children and errorState true 1`] = `
<div
  className="error-panel"
  style={
    Object {
      "transform": "translateX(-100%) translateZ(0)",
    }
  }
>
  <p>
    Lorem ipsum dolor sit amet consectetur.
  </p>
</div>
`;
