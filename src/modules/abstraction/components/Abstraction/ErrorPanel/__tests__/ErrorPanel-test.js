import ErrorPanel from "..";
import { create } from "react-test-renderer";

describe("ErrorPanel", () => {
  const render = props => create(<ErrorPanel {...props} />);

  test("renders ErrorPanel", () => {
    const component = render({
      errorState: false,
      children: null
    });

    expect(component).toMatchSnapshot();
  });

  test("renders ErrorPanel with children", () => {
    const component = render({
      errorState: false,
      children: <p>Lorem ipsum dolor sit amet consectetur.</p>
    });

    expect(component).toMatchSnapshot();
  });

  test("renders ErrorPanel with children and errorState true", () => {
    const component = render({
      errorState: true,
      children: <p>Lorem ipsum dolor sit amet consectetur.</p>
    });

    expect(component).toMatchSnapshot();
  });
});
