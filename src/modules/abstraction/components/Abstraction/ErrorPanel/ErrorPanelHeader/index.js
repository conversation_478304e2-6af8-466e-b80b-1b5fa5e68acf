import { useDispatch } from "react-redux";
import classnames from "classnames";

const ErrorPanelHeader = ({ setter, header, headerIssue = null }) => {
  const dispatch = useDispatch();

  const errorHeaderClasses = classnames("error-panel-header", headerIssue);

  return (
    <div className="error-panel-header-container">
      <i onClick={() => dispatch(setter)} className="fa-solid fa-arrow-left" />
      <h3 className={errorHeaderClasses}>{header}</h3>
    </div>
  );
};

export default ErrorPanelHeader;
