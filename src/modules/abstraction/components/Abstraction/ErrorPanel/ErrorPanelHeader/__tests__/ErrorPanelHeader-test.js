import ErrorPanelHead from "..";
import { create } from "react-test-renderer";
import { decoratedWithDispatch } from "utils/test/decorated";

describe("ErrorPanelHeader", () => {
  const render = cmp => create(cmp);

  test("renders ErrorPanelHeader", () => {
    const { component } = decoratedWithDispatch(
      ErrorPanelHead,
      { setter: jest.fn(), header: "Date of Birth" },
      {
        abstractionValidationReport: {
          errorSidePanelActive: false,
          errorSidePanelActive2: false,
          errors: {
            "Date of Birth": [
              { id: 0, failureMessage: "Cannot be blank!", error: true }
            ]
          },
          error: { id: 0, failureMessage: "Cannot be blank!", error: true },
          errorPrompt: "Date of Birth",
          errorUrl: null
        }
      }
    );

    const renderedComponent = render(component);

    expect(renderedComponent).toMatchSnapshot();
  });
});
