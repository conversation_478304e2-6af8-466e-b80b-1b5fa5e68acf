import { motion } from "framer-motion";
import "../../../styles/error-panel.scss";

const variants = {
  open: {
    x: "0",
    transition: {
      bounce: 0,
      duration: 0.2
    }
  },
  close: {
    x: "-100%",
    transition: {
      bounce: 0,
      duration: 0.2
    }
  }
};

const ErrorPanel = props => {
  const { errorState, children } = props;

  return (
    <motion.div
      className="error-panel"
      initial="close"
      animate={errorState ? "open" : "close"}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

export default ErrorPanel;
