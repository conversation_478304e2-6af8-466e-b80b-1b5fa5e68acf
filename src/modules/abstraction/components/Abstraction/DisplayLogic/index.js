import { __, compose, defaultTo, prop } from "ramda";
import Question<PERSON><PERSON>Button from "./QuestionCopyButton";
import QuestionFillButton from "./QuestionFillButton";
import QuestionList from "./QuestionList";
import RepeatableLoop from "./RepeatableLoop";
import Separator from "modules/questionnaire/components/Questionnaire/DisplayLogic/Separator";
import Subheader from "modules/questionnaire/components/Questionnaire/DisplayLogic/Subheader";

import { useComponentLogic } from "./hooks";

export const type = compose(
  defaultTo(QuestionList),
  prop(__, {
    QuestionList,
    Separator,
    Subheader,
    QuestionFillButton,
    QuestionCopyButton
  })
);

export const DisplayLogic = props => {
  const { displayLogic, ...otherProps } = props;
  const { answerGroup, answerGroups, isRepeatable } = useComponentLogic(props);
  const Type = type(displayLogic.type);

  if (!isRepeatable) {
    return (
      <Type
        displayLogic={displayLogic}
        answerGroup={answerGroup}
        {...otherProps}
      />
    );
  }

  return (
    <RepeatableLoop
      displayLogic={displayLogic}
      answerGroups={answerGroups}
      {...otherProps}
    />
  );
};

export function Unimplemented({ displayLogic }) {
  return <div style={{ color: "red" }}>DisplayLogic: {displayLogic.type}</div>;
}

export default DisplayLogic;
