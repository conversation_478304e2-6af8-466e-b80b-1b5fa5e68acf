import { act, create } from "react-test-renderer";
import { decoratedApolloWithDispatch } from "utils/test/decorated";
import Engine from "modules/engine";
import QuestionFillButton from "..";
import { fillAnswers } from "../hooks";

jest.mock("modules/engine", () => ({
  __esModule: true,
  default: {
    saveAnswers: jest.fn()
  }
}));

const fillButton = {
  id: "121",
  children: [],
  questions: [],
  questionIds: [57, 58, 59, 60, 61, 62, 63, 64, 66, 68, 69, 70, 71, 72, 20],
  valueToSet: "No",
  tartgetTo: null,
  buttonText: "No for remaining medications",
  groupId: "2"
};

const questionAnswergroup = {
  id: "675",
  groupId: "3",
  descendants: []
};

const answerGroup = {
  id: "674",
  groupId: "2",
  descendants: [questionAnswergroup]
};

const currentSection = {
  id: "51",
  groupId: "1",
  children: [
    {
      children: [],
      questions: [
        {
          id: "232",
          type: "NumberWithUnit",
          groupId: "3"
        },
        {
          id: "233",
          type: "NumberWithUnit",
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: []
    },
    fillButton,
    {
      children: [],
      questions: [
        {
          id: "57",
          type: "Open",
          groupId: "3"
        },
        {
          id: "58",
          type: "Enumerable",
          questionEnumerables: [
            { id: "98", description: "Yes" },
            { id: "99", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "59",
          type: "Enumerable",
          questionEnumerables: [
            { id: "100", description: "Yes" },
            { id: "101", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "60",
          type: "Enumerable",
          questionEnumerables: [
            { id: "102", description: "Yes" },
            { id: "103", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "61",
          type: "Enumerable",
          questionEnumerables: [
            { id: "104", description: "Yes" },
            { id: "105", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "62",
          type: "Enumerable",
          questionEnumerables: [
            { id: "106", description: "Yes" },
            { id: "107", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "63",
          type: "Enumerable",
          questionEnumerables: [
            { id: "108", description: "Yes" },
            { id: "109", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "64",
          type: "Enumerable",
          questionEnumerables: [
            { id: "154", description: "Yes" },
            { id: "155", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "20",
          type: "NumberOrEnumerable",
          questionEnumerables: [
            { id: "40", description: "Yes" },
            { id: "42", description: "No" }
          ],
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: [{ id: "65", type: "Date" }]
    },
    {
      children: [],
      questions: [
        {
          id: "66",
          questionEnumerables: [
            { id: "156", description: "Yes" },
            { id: "157", description: "No" }
          ],
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: [{ id: "67", type: "Date" }]
    },
    {
      children: [],
      questions: [
        {
          id: "68",
          questionEnumerables: [
            { id: "130", description: "Yes" },
            { id: "131", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "69",
          questionEnumerables: [
            { id: "110", description: "Yes" },
            { id: "111", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "70",
          questionEnumerables: [
            { id: "112", description: "Yes" },
            { id: "113", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "71",
          questionEnumerables: [
            { id: "114", description: "Yes" },
            { id: "115", description: "No" }
          ],
          groupId: "3"
        },
        {
          id: "72",
          questionEnumerables: [
            { id: "116", description: "Yes" },
            { id: "117", description: "No" }
          ],
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: [{ id: "73", groupId: "3" }]
    }
  ],
  questions: [],
  answerGroups: [answerGroup]
};

const answers = {
  "59|674": { answerData: "100" },
  "60|675": { answerData: "102" },
  "61|674": { answerData: "104" },
  "62|675": { answerData: "106" },
  "63|675": { answerData: "108" },
  "64|675": { answerData: "154" },
  "66|675": { answerData: "156" },
  "68|675": { answerData: "130" },
  "69|675": { answerData: "110" },
  "70|675": { answerData: "112" },
  "71|675": { answerData: "114" },
  "72|675": { answerData: "116" },
  "2956|600": { answerData: "10521" }
};

const fillButtonTable = {
  id: "3",
  type: "QuestionFillButton",
  children: [],
  groupId: "5",
  questionIds: [2956, 2957, 2958, 2959],
  buttonText: "No for remaining medication questions",
  valueToSet: "Not Prescribed - No Reason",
  jumpTarget: null
};

const q1 = {
  id: "2956",
  prompt: "ACE (Any)",
  type: "Enumerable",
  emphasis: false,
  edgeTarget: false,
  calculationTarget: false,
  hasHelpInformation: false,
  groupId: "5",
  questionEnumerables: [
    {
      id: "10521",
      description: "Not Prescribed - Patient Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10520",
      description: "Not Prescribed - Medical Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10519",
      description: "Not Prescribed - No Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10518",
      description: "Yes - Prescribed",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    }
  ]
};
const q2 = {
  id: "2957",
  prompt: "ARB (Any)",
  type: "Enumerable",
  emphasis: false,
  edgeTarget: false,
  calculationTarget: false,
  hasHelpInformation: false,
  groupId: "5",
  questionEnumerables: [
    {
      id: "10525",
      description: "Not Prescribed - Patient Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10524",
      description: "Not Prescribed - Medical Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10523",
      description: "Not Prescribed - No Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10522",
      description: "Yes - Prescribed",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    }
  ]
};
const q3 = {
  id: "2958",
  prompt: "ABA (Any)",
  type: "Enumerable",
  emphasis: false,
  edgeTarget: false,
  calculationTarget: false,
  hasHelpInformation: false,
  groupId: "5",
  questionEnumerables: [
    {
      id: "10529",
      description: "Not Prescribed - Patient Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10528",
      description: "Not Prescribed - Medical Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10527",
      description: "Not Prescribed - No Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10526",
      description: "Yes - Prescribed",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    }
  ]
};
const q4 = {
  id: "2959",
  prompt: "Warfarin",
  type: "Enumerable",
  emphasis: false,
  edgeTarget: false,
  calculationTarget: false,
  hasHelpInformation: false,
  groupId: "5",
  questionEnumerables: [
    {
      id: "10533",
      description: "Not Prescribed - Patient Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10532",
      description: "Not Prescribed - Medical Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10531",
      description: "Not Prescribed - No Reason",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    },
    {
      id: "10530",
      description: "Yes - Prescribed",
      displayOrder: null,
      effectiveOn: null,
      expirationOn: null
    }
  ]
};

const answerGroup2 = {
  id: "600",
  groupId: "5",
  descendants: []
};

const table = {
  id: "6",
  type: "Table",
  children: [],
  groupId: "5",
  rowData: [
    {
      id: "3393-0",
      row: [
        {
          id: "3393-0-0",
          text: "ACE (Any)",
          questionId: null
        },
        {
          id: "3393-0-1",
          text: null,
          questionId: "2956",
          question: q1
        }
      ]
    },
    {
      id: "3393-1",
      row: [
        {
          id: "3393-1-0",
          text: "ARB (Any)",
          questionId: null
        },
        {
          id: "3393-1-1",
          text: null,
          questionId: "2957",
          question: q2
        }
      ]
    },
    {
      id: "3393-2",
      row: [
        {
          id: "3393-2-0",
          text: "ABA (Any)",
          questionId: null
        },
        {
          id: "3393-2-1",
          text: null,
          questionId: "2958",
          question: q3
        }
      ]
    },
    {
      id: "3393-3",
      row: [
        {
          id: "3393-3-0",
          text: "Warfarin",
          questionId: null
        },
        {
          id: "3393-3-1",
          text: null,
          questionId: "2959",
          question: q4
        }
      ]
    }
  ],
  headerData: [
    {
      id: "3393-0",
      text: "Medication Code",
      emphasis: null
    },
    {
      id: "3393-1",
      text: "Prescribed at Discharge",
      emphasis: null
    }
  ],
  helpInformation: []
};

const currentSection2 = {
  id: "7",
  type: "Section",
  children: [fillButtonTable, table],
  groupId: "5",
  isTopLevel: true,
  sortOrder: 136,
  name: "Discharge",
  repeatable: false,
  sectionQuestions: [],
  instanceTitleId: [],
  answerGroups: [answerGroup2]
};

describe("QuestionFillButton", () => {
  const render = (displayLogic, writeLocked) => {
    const { component, dispatch } = decoratedApolloWithDispatch({
      component: QuestionFillButton,
      props: { answerGroup, displayLogic, currentSection, writeLocked },
      initialAppValues: {
        questions: { writeLocked },
        questionnaire: { answers },
        status: { mutationLoading: false }
      },
      apolloMocks: []
    });

    return { component: create(component), dispatch };
  };

  test("renders correctly", () => {
    const { component } = render(fillButton);

    expect(component).toMatchSnapshot();
  });

  test("is disabled when writeLocked is true", () => {
    const { component } = render(fillButton, true);

    expect(component).toMatchSnapshot();
  });

  test("enabledQuestionIds: returns questionIds filtered by enabled status", () => {
    const setValue = jest.fn();

    const props = {
      displayLogic: fillButton,
      answerGroup,
      currentSection,
      allAnswers: answers,
      setValue
    };

    fillAnswers(props);

    expect(Engine.saveAnswers).toHaveBeenCalledWith(props, [
      {
        answerData: "99",
        answerGroup: questionAnswergroup,
        answerGroupId: "675",
        answerType: "enumerable",
        question: {
          groupId: "3",
          id: "58",
          questionEnumerables: [
            { description: "Yes", id: "98" },
            { description: "No", id: "99" }
          ],
          type: "Enumerable"
        },
        questionId: "58"
      },
      {
        answerData: "101",
        answerGroup: questionAnswergroup,
        answerGroupId: "675",
        answerType: "enumerable",
        question: {
          groupId: "3",
          id: "59",
          questionEnumerables: [
            { description: "Yes", id: "100" },
            { description: "No", id: "101" }
          ],
          type: "Enumerable"
        },
        questionId: "59"
      },
      {
        answerData: "105",
        answerGroup: questionAnswergroup,
        answerGroupId: "675",
        answerType: "enumerable",
        question: {
          groupId: "3",
          id: "61",
          questionEnumerables: [
            { description: "Yes", id: "104" },
            { description: "No", id: "105" }
          ],
          type: "Enumerable"
        },
        questionId: "61"
      },
      {
        answerData: "42",
        answerGroup: questionAnswergroup,
        answerGroupId: "675",
        answerType: "enumerable",
        question: {
          groupId: "3",
          id: "20",
          questionEnumerables: [
            { description: "Yes", id: "40" },
            { description: "No", id: "42" }
          ],
          type: "NumberOrEnumerable"
        },
        questionId: "20"
      }
    ]);
  });

  test("enabledQuestionIds: returns questionIds filtered by enabled status for table", () => {
    const setValue = jest.fn();

    const props = {
      displayLogic: fillButtonTable,
      answerGroup: answerGroup2,
      currentSection: currentSection2,
      answers,
      setValue
    };

    fillAnswers(props);

    expect(Engine.saveAnswers).toHaveBeenCalledWith(props, [
      {
        answerData: "10519",
        answerGroup: { descendants: [], groupId: "5", id: "600" },
        answerGroupId: "600",
        answerType: "enumerable",
        question: {
          calculationTarget: false,
          edgeTarget: false,
          emphasis: false,
          groupId: "5",
          hasHelpInformation: false,
          id: "2956",
          prompt: "ACE (Any)",
          questionEnumerables: [
            {
              description: "Not Prescribed - Patient Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10521"
            },
            {
              description: "Not Prescribed - Medical Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10520"
            },
            {
              description: "Not Prescribed - No Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10519"
            },
            {
              description: "Yes - Prescribed",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10518"
            }
          ],
          type: "Enumerable"
        },
        questionId: "2956"
      },
      {
        answerData: "10523",
        answerGroup: { descendants: [], groupId: "5", id: "600" },
        answerGroupId: "600",
        answerType: "enumerable",
        question: {
          calculationTarget: false,
          edgeTarget: false,
          emphasis: false,
          groupId: "5",
          hasHelpInformation: false,
          id: "2957",
          prompt: "ARB (Any)",
          questionEnumerables: [
            {
              description: "Not Prescribed - Patient Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10525"
            },
            {
              description: "Not Prescribed - Medical Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10524"
            },
            {
              description: "Not Prescribed - No Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10523"
            },
            {
              description: "Yes - Prescribed",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10522"
            }
          ],
          type: "Enumerable"
        },
        questionId: "2957"
      },
      {
        answerData: "10527",
        answerGroup: { descendants: [], groupId: "5", id: "600" },
        answerGroupId: "600",
        answerType: "enumerable",
        question: {
          calculationTarget: false,
          edgeTarget: false,
          emphasis: false,
          groupId: "5",
          hasHelpInformation: false,
          id: "2958",
          prompt: "ABA (Any)",
          questionEnumerables: [
            {
              description: "Not Prescribed - Patient Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10529"
            },
            {
              description: "Not Prescribed - Medical Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10528"
            },
            {
              description: "Not Prescribed - No Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10527"
            },
            {
              description: "Yes - Prescribed",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10526"
            }
          ],
          type: "Enumerable"
        },
        questionId: "2958"
      },
      {
        answerData: "10531",
        answerGroup: { descendants: [], groupId: "5", id: "600" },
        answerGroupId: "600",
        answerType: "enumerable",
        question: {
          calculationTarget: false,
          edgeTarget: false,
          emphasis: false,
          groupId: "5",
          hasHelpInformation: false,
          id: "2959",
          prompt: "Warfarin",
          questionEnumerables: [
            {
              description: "Not Prescribed - Patient Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10533"
            },
            {
              description: "Not Prescribed - Medical Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10532"
            },
            {
              description: "Not Prescribed - No Reason",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10531"
            },
            {
              description: "Yes - Prescribed",
              displayOrder: null,
              effectiveOn: null,
              expirationOn: null,
              id: "10530"
            }
          ],
          type: "Enumerable"
        },
        questionId: "2959"
      }
    ]);
  });

  test("when button is clicked saveAnswers is called ", () => {
    const { component } = render(fillButton);
    const instance = component.root;
    const button = instance.findByType("button");
    const preventDefault = jest.fn();

    act(() => button.props.onClick({ preventDefault }));

    expect(Engine.saveAnswers).toHaveBeenCalled();
  });

  test("when button is keyDown saveAnswers is called", () => {
    const { component } = render(fillButton);
    const instance = component.root;
    const button = instance.findByType("button");
    const preventDefault = jest.fn();

    act(() => button.props.onKeyDown({ preventDefault, keyCode: 13 }));

    expect(Engine.saveAnswers).toHaveBeenCalled();
  });

  test("correct values are set in state", () => {
    const { component, dispatch } = render(fillButton);
    const instance = component.root;
    const button = instance.findByType("button");
    const preventDefault = jest.fn();

    act(() => button.props.onClick({ preventDefault }));

    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "20|675", value: { selected: "42" } },
      type: "~questions/SET_FIELD_VALUE"
    });
    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "61|675", value: "105" },
      type: "~questions/SET_FIELD_VALUE"
    });
  });
});
