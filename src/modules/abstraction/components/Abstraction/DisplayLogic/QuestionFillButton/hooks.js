import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  any,
  values,
  __,
  always,
  either,
  find,
  forEach,
  ifElse,
  isNil,
  pipe,
  prop,
  propEq,
  when,
  reduce,
  path,
  tap,
  applySpec,
  complement,
  head,
  append
} from "ramda";
import Questionnaire from "modules/questionnaire/selectors";
import Question from "modules/question/selectors";
import Engine from "modules/engine";
import Mutations from "modules/question/components/Question/mutations";
import { setMutationLoading } from "modules/app/actions";
import { pushError, pushWarning } from "modules/questionnaire/actions";
import {
  isCompoundField,
  isCompoundIncludingField
} from "modules/questionnaire/services/specialFields";
import {
  setFieldValue,
  setOptimistic,
  savePendingFields,
  changeFocusToRealField
} from "modules/question/actions";
import { isNullOrEmpty } from "utils/fp";
import { buildDisplayLogicName } from "modules/questionnaire/services/displayLogics";
import { fieldsToBeChanged } from "modules/questionnaire/components/Questionnaire/DisplayLogic/QuestionFillButton/enhancers";
import { useMutation } from "@apollo/client";
import { useLinkButtonKeyboard } from "modules/abstraction/hooks";
import { useFocus } from "modules/fields/components/hooks/useFocus";

export const fillAnswers = props => {
  const { displayLogic, answerGroup, currentSection, allAnswers, setValue } =
    props;
  const fieldsToChange = fieldsToBeChanged({
    displayLogic,
    answerGroup,
    currentSection,
    answers: allAnswers
  });
  const valueToSet = pipe(
    prop("question"),
    prop("questionEnumerables"),
    find(propEq("description", displayLogic.valueToSet)),
    prop("id")
  );
  const setDisplayValue = field =>
    pipe(
      ifElse(
        pipe(
          path(["question", "type"]),
          either(isCompoundField, isCompoundIncludingField)
        ),
        applySpec({
          selected: valueToSet
        }),
        valueToSet
      ),
      val => {
        setValue(field.name, val);
      }
    )(field);
  const addAnswer = (answers, field) =>
    ifElse(
      prop("answerGroup"),
      pipe(
        applySpec({
          answerData: valueToSet,
          question: prop("question"),
          answerGroup: prop("answerGroup"),
          answerType: always("enumerable"),
          answerGroupId: path(["answerGroup", "id"]),
          questionId: path(["question", "id"])
        }),
        tap(() => setDisplayValue(field)),
        append(__, answers)
      ),
      always(answers)
    )(field);

  pipe(
    reduce(addAnswer, []),
    when(complement(isNullOrEmpty), newAnswers => {
      Engine.saveAnswers(props, newAnswers);
    })
  )(fieldsToChange);
};

// eslint-disable-next-line max-statements
const useProps = props => {
  const { answerGroup, displayLogic } = props;

  const answers = useSelector(state => Questionnaire.getAnswers(state));
  const name = buildDisplayLogicName(displayLogic, answerGroup);
  const questionnaire = useSelector(state =>
    Questionnaire.getQuestionnaire(state)
  );
  const allAnswerGroups = useSelector(state =>
    Questionnaire.getAnswerGroups(state)
  );
  const query = useSelector(state => Question.getResponseQuery(state));
  const objectName = useSelector(state =>
    Question.getResponseObjectName(state)
  );
  const writeLocked = useSelector(state => Question.getWriteLocked(state));
  const visibleQuestions = useSelector(state =>
    Question.getVisibleQuestions(state)
  );
  const dispatch = useDispatch();
  const answerError = useCallback(
    ({ errors, warnings }) => {
      const formatMessage = messages => ({
        fieldName: "",
        messages,
        question: null
      });

      const send = ([val, sendFn]) => {
        dispatch(sendFn(val));
      };

      forEach(when(head, pipe(formatMessage, send)), [
        ([errors, pushError], [warnings, pushWarning])
      ]);
    },
    [dispatch]
  );
  const setSaving = useCallback(
    (saving, formattedAnswers) => {
      const setLoading = answer => {
        dispatch(
          setMutationLoading(
            `${answer.questionId}|${answer.answerGroupId}`,
            saving
          )
        );
      };

      return ifElse(
        isNil,
        () => dispatch(setMutationLoading("", saving)),
        forEach(setLoading)
      )(formattedAnswers);
    },
    [dispatch]
  );
  const setValue = useCallback(
    (fieldName, value) => {
      dispatch(setFieldValue(fieldName, value));
    },
    [dispatch]
  );
  const handleOptimistic = useCallback(
    (isOptimistic, fieldName) => {
      dispatch(setOptimistic(fieldName, isOptimistic));
    },
    [dispatch]
  );
  const handlePendingAnswers = useCallback(
    addedVisibleQuestions => {
      dispatch(savePendingFields(addedVisibleQuestions));
    },
    [dispatch]
  );
  const focusRealField = useCallback(
    addedVisibleQuestions => {
      dispatch(changeFocusToRealField(addedVisibleQuestions));
    },
    [dispatch]
  );

  return {
    ...props,
    name,
    questionnaire,
    allAnswerGroups,
    allAnswers: answers,
    query,
    objectName,
    writeLocked,
    visibleQuestions,
    isOptimistic: false,
    pendingSaveFields: {},
    answerError,
    setSaving,
    setValue,
    setOptimistic: handleOptimistic,
    setPendingAnswers: handlePendingAnswers,
    focusRealField
  };
};

export const useButtonHandlers = (props, onFillAnswers) => {
  const ownProps = useProps(props);
  const [answersMutate] = useMutation(Mutations.answersMutation);
  const [updateAnswerGroups] = useMutation(
    Mutations.updateAnswerGroupsMutation
  );
  const [refreshData] = useMutation(Mutations.refreshDataMutation);
  const handleFillAnswers = e => {
    e.preventDefault();
    const answerQuestions = newAnswers => {
      Engine.handleSaveAnswersMutation({
        mutate: answersMutate,
        props: ownProps,
        context: {
          answers: newAnswers,
          mutationName: "answerQuestions"
        }
      });
    };

    onFillAnswers({
      ...ownProps,
      answerQuestions,
      updateAnswerGroups,
      refreshData
    });
  };
  const { handleKeyBoardAction } = useLinkButtonKeyboard({
    actionDisabled: ownProps.writeLocked,
    onKeyboardAction: handleFillAnswers
  });
  const { focusRef } = useFocus();
  const allFieldsSavingStatus = useSelector(
    state => state.app.status.mutationLoading
  );
  const isAnyFieldSaving = any(Boolean, values(allFieldsSavingStatus));

  return {
    writeLocked: ownProps.writeLocked,
    fillAnswers: handleFillAnswers,
    handleKeyBoardAction,
    onFocusRef: focusRef,
    isSaving: isAnyFieldSaving
  };
};

export const useComponentLogic = props => useButtonHandlers(props, fillAnswers);
