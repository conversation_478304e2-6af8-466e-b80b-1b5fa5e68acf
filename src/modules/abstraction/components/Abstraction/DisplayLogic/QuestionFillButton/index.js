import { useComponentLogic } from "./hooks";

export const QuestionFillButton = props => {
  const { displayLogic } = props;
  const { fillAnswers, writeLocked, onFocusRef, handleKeyBoardAction } =
    useComponentLogic(props);

  return (
    <div>
      <button
        type="button"
        disabled={writeLocked}
        onClick={fillAnswers}
        onKeyDown={handleKeyBoardAction}
        ref={onFocusRef}
        className="display-logic-button"
      >
        <i className="fa fa-circle-check" />
        {displayLogic.buttonText}
      </button>
    </div>
  );
};

export default QuestionFillButton;
