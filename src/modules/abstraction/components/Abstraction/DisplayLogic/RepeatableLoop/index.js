import { ifElse } from "ramda";
import Repeatable from "../Repeatable";
import { isNullOrEmpty } from "utils/fp";
import { useComponentLogic } from "./hooks";

export const RepeatableLoop = props => {
  const { displayLogic, answerGroups, ...otherProps } = props;
  const { childInstances } = useComponentLogic(displayLogic, answerGroups);

  if (!answerGroups) return null;

  return ifElse(
    isNullOrEmpty,
    () => (
      <Repeatable
        displayLogic={displayLogic}
        counterIndex={0}
        {...otherProps}
      />
    ),
    () => (
      <div>
        {childInstances.map((ag, index) => (
          <Repeatable
            key={ag.id}
            displayLogic={displayLogic}
            answerGroup={ag}
            counterIndex={index}
            {...otherProps}
          />
        ))}
      </div>
    )
  )(childInstances);
};

export default RepeatableLoop;
