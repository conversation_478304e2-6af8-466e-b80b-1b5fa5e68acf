// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RepeatableLoop renders component correctly 1`] = `
<div>
  <Repeatable
    answerGroup={
      Object {
        "groupId": "5",
        "id": "33",
      }
    }
    counterIndex={0}
    displayLogic={
      Object {
        "answerGroups": Array [
          Object {
            "groupId": "5",
            "id": "33",
          },
          Object {
            "groupId": "5",
            "id": "34",
          },
        ],
        "children": Array [
          Object {
            "groupId": "5",
            "id": "58",
          },
          Object {
            "groupId": "5",
            "id": "64",
          },
        ],
        "groupId": "5",
        "id": "42",
        "isTopLevel": true,
      }
    }
  />
  <Repeatable
    answerGroup={
      Object {
        "groupId": "5",
        "id": "34",
      }
    }
    counterIndex={1}
    displayLogic={
      Object {
        "answerGroups": Array [
          Object {
            "groupId": "5",
            "id": "33",
          },
          Object {
            "groupId": "5",
            "id": "34",
          },
        ],
        "children": Array [
          Object {
            "groupId": "5",
            "id": "58",
          },
          Object {
            "groupId": "5",
            "id": "64",
          },
        ],
        "groupId": "5",
        "id": "42",
        "isTopLevel": true,
      }
    }
  />
</div>
`;

exports[`RepeatableLoop renders component correctly when no ag match 1`] = `
<Repeatable
  counterIndex={0}
  displayLogic={
    Object {
      "answerGroups": Array [
        Object {
          "groupId": "5",
          "id": "33",
        },
        Object {
          "groupId": "5",
          "id": "34",
        },
      ],
      "children": Array [
        Object {
          "groupId": "5",
          "id": "58",
        },
        Object {
          "groupId": "5",
          "id": "64",
        },
      ],
      "groupId": "5",
      "id": "42",
      "isTopLevel": true,
    }
  }
/>
`;

exports[`RepeatableLoop renders null when no answerGroups 1`] = `null`;
