import { Provider } from "react-redux";
import { create } from "react-test-renderer";
import { getStore } from "utils/test/decorated";
import { RepeatableLoop } from "..";

jest.mock("../../Repeatable", () => "Repeatable");

const store = getStore(
  {},
  {
    questionnaire: {
      answers: {
        1: { answerGroupId: "33", answerData: "5" },
        2: { answerGroupId: "34", answerData: "10" }
      }
    }
  }
);

describe("RepeatableLoop", () => {
  const answerGroup = {
    id: "33",
    groupId: "5"
  };
  const answerGroup2 = {
    id: "34",
    groupId: "5"
  };
  const repeatable = {
    id: "42",
    isTopLevel: true,
    children: [
      { id: "58", groupId: "5" },
      { id: "64", groupId: "5" }
    ],
    groupId: "5",
    answerGroups: [answerGroup, answerGroup2]
  };

  function renderComponent(displayLogic, answerGroups) {
    return create(
      <Provider store={store}>
        <RepeatableLoop
          displayLogic={displayLogic}
          answerGroups={answerGroups}
        />
      </Provider>
    );
  }

  test("renders component correctly", () => {
    const output = renderComponent(repeatable, [{ id: "33" }, { id: "34" }]);

    expect(output).toMatchSnapshot();
  });

  test("renders null when no answerGroups", () => {
    const output = renderComponent(repeatable, null);

    expect(output).toMatchSnapshot();
  });

  test("renders component correctly when no ag match", () => {
    const output = renderComponent(repeatable, [{ id: "35" }]);

    expect(output).toMatchSnapshot();
  });
});
