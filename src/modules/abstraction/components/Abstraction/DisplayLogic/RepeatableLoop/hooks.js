import { useMemo } from "react";
import { useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";
import { always, ifElse, innerJoin, isNil, pipe, sortBy } from "ramda";

export const useComponentLogic = (displayLogic, answerGroups) => {
  const answers = useSelector(state => Questionnaire.getAnswers(state));

  const childInstances = useMemo(() => {
    if (!answers) return [];

    const answersArray = Object.values(answers);

    const getAnswerData = groupId => {
      const match = answersArray.find(
        a => String(a.answerGroupId) === String(groupId)
      );
      const data = match?.answerData;
      const parsed = parseFloat(data);

      return isNaN(parsed) ? Infinity : parsed;
    };

    return ifElse(
      isNil,
      always([]),
      pipe(
        innerJoin((a, b) => a.id === b.id, displayLogic.answerGroups),
        sortBy(group => getAnswerData(group.id))
      )
    )(answerGroups);
  }, [answerGroups, displayLogic, answers]);

  return { childInstances };
};
