import { act, create } from "react-test-renderer";
import { decoratedApolloWithDispatch } from "utils/test/decorated";
import Engine from "modules/engine";
import QuestionCopyButton from "..";
import { fillAnswers } from "../hooks";

jest.mock("modules/engine", () => ({
  __esModule: true,
  default: {
    saveAnswers: jest.fn()
  }
}));

const copyButton = {
  id: "121",
  children: [],
  questions: [],
  questionCopyMap:
    '{"57": 58, "59": 60, "61": 62, "63": 64, "66": 68, "69": 70, "71": 72}',
  buttonText: "copy answers above",
  groupId: "2"
};

const sourceAnswergroup = {
  id: "675",
  groupId: "3",
  descendants: []
};

const destinationAnswergroup = {
  id: "676",
  groupId: "4",
  descendants: []
};

const answerGroup = {
  id: "674",
  groupId: "2",
  descendants: [sourceAnswergroup, destinationAnswergroup]
};

const currentSection = {
  id: "51",
  groupId: "1",
  children: [
    {
      children: [],
      questions: [
        {
          id: "232",
          type: "NumberWithUnit",
          groupId: "3"
        },
        {
          id: "233",
          type: "NumberWithUnit",
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: []
    },
    copyButton,
    {
      children: [],
      questions: [
        {
          id: "57",
          type: "Open",
          groupId: "3"
        },
        {
          id: "58",
          type: "EnumerableOrOpen",
          groupId: "4"
        },
        {
          id: "59",
          type: "Open",
          groupId: "3"
        },
        {
          id: "60",
          type: "Open",
          groupId: "4"
        },
        {
          id: "61",
          type: "Number",
          groupId: "3"
        },
        {
          id: "62",
          type: "Number",
          groupId: "4"
        },
        {
          id: "63",
          type: "Date",
          groupId: "3"
        },
        {
          id: "64",
          type: "Date",
          groupId: "4"
        },
        {
          id: "20",
          type: "NumberOrEnumerable",
          questionEnumerables: [
            { id: "40", description: "Yes" },
            { id: "42", description: "No" }
          ],
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: [{ id: "65", type: "Date" }]
    },
    {
      children: [],
      questions: [
        {
          id: "66",
          type: "Open",
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: [{ id: "67", type: "Date" }]
    },
    {
      children: [],
      questions: [
        {
          id: "68",
          type: "EnumerableOrOpen",
          groupId: "4",
          questionEnumerables: [
            { id: "130", description: "No" },
            { id: "129", description: "Yes" }
          ]
        },
        {
          id: "69",
          type: "Open",
          groupId: "3"
        },
        {
          id: "70",
          type: "Open",
          groupId: "4"
        },
        {
          id: "71",
          type: "Enumerable",
          questionEnumerables: [
            { id: "114", description: "Yes" },
            { id: "115", description: "No" }
          ],
          groupId: "3"
        }
      ]
    },
    {
      children: [],
      questions: [
        {
          id: "72",
          type: "EnumerableOrOpen",
          questionEnumerables: [
            { id: "116", description: "Yes" },
            { id: "117", description: "No" }
          ],
          groupId: "4"
        },
        { id: "73", groupId: "4" }
      ]
    }
  ],
  questions: [],
  answerGroups: [answerGroup]
};

const answers = {
  "57|675": { answerData: "text", answerType: "open" },
  "59|675": { answerData: "text2", answerType: "open" },
  "61|675": { answerData: "30", answerType: "number" },
  "63|675": { answerData: "10/10/2022", answerType: "date" },
  "66|675": { answerData: "156", answerType: "open" },
  "68|676": { answerData: "130", answerType: "open" },
  "69|675": { answerData: "110", answerType: "open" },
  "70|676": { answerData: "112", answerType: "open" },
  "71|675": { answerData: "114", answerType: "enumerable" },
  "2956|600": { answerData: "10521", answerType: "enumerable" }
};

describe("QuestionCopyButton", () => {
  const render = (displayLogic, writeLocked, isSaving) => {
    const { component, dispatch } = decoratedApolloWithDispatch({
      component: QuestionCopyButton,
      props: {
        answerGroup,
        displayLogic,
        currentSection,
        writeLocked,
        isSaving
      },
      initialAppValues: {
        questions: { writeLocked },
        questionnaire: { answers },
        status: { mutationLoading: isSaving }
      },
      apolloMocks: []
    });

    return { component: create(component), dispatch };
  };

  test("renders correctly", () => {
    const { component } = render(copyButton);

    expect(component).toMatchSnapshot();
  });

  test("is disabled when writeLocked is true", () => {
    const { component } = render(copyButton, true, false);

    expect(component).toMatchSnapshot();
  });
  test("is disabled when isSaving is true", () => {
    const { component } = render(copyButton, true, false);

    expect(component).toMatchSnapshot();
  });

  test("valid destination found and copy from source", () => {
    const setValue = jest.fn();

    const props = {
      displayLogic: copyButton,
      answerGroup,
      currentSection,
      allAnswers: answers,
      setValue
    };

    fillAnswers(props);

    expect(Engine.saveAnswers).toHaveBeenCalledWith(props, [
      {
        answerData: "text",
        question: { id: "58", type: "EnumerableOrOpen", groupId: "4" },
        answerGroup: { id: "676", groupId: "4", descendants: [] },
        answerType: "open",
        answerGroupId: "676",
        questionId: "58"
      },
      {
        answerData: "text2",
        question: { id: "60", type: "Open", groupId: "4" },
        answerGroup: { id: "676", groupId: "4", descendants: [] },
        answerType: "open",
        answerGroupId: "676",
        questionId: "60"
      },
      {
        answerData: "30",
        question: { id: "62", type: "Number", groupId: "4" },
        answerGroup: { id: "676", groupId: "4", descendants: [] },
        answerType: "number",
        answerGroupId: "676",
        questionId: "62"
      },
      {
        answerData: "10/10/2022",
        question: { id: "64", type: "Date", groupId: "4" },
        answerGroup: { id: "676", groupId: "4", descendants: [] },
        answerType: "date",
        answerGroupId: "676",
        questionId: "64"
      },
      {
        answerData: "116",
        question: {
          id: "72",
          type: "EnumerableOrOpen",
          groupId: "4",
          questionEnumerables: [
            { id: "116", description: "Yes" },
            { id: "117", description: "No" }
          ]
        },
        answerGroup: { id: "676", groupId: "4", descendants: [] },
        answerType: "enumerable",
        answerGroupId: "676",
        questionId: "72"
      }
    ]);
  });

  test("when button is clicked saveAnswers is called ", () => {
    const { component } = render(copyButton);
    const instance = component.root;
    const button = instance.findByType("button");
    const preventDefault = jest.fn();

    act(() => button.props.onClick({ preventDefault }));

    expect(Engine.saveAnswers).toHaveBeenCalled();
  });

  test("when button is keyDown saveAnswers is called", () => {
    const { component } = render(copyButton);
    const instance = component.root;
    const button = instance.findByType("button");
    const preventDefault = jest.fn();

    act(() => button.props.onKeyDown({ preventDefault, keyCode: 13 }));

    expect(Engine.saveAnswers).toHaveBeenCalled();
  });

  test("correct values are set in state", () => {
    const { component, dispatch } = render(copyButton);
    const instance = component.root;
    const button = instance.findByType("button");
    const preventDefault = jest.fn();

    act(() => button.props.onClick({ preventDefault }));

    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "58|676", value: { value: "text" } },
      type: "~questions/SET_FIELD_VALUE"
    });
    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "60|676", value: "text2" },
      type: "~questions/SET_FIELD_VALUE"
    });
    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "62|676", value: "30" },
      type: "~questions/SET_FIELD_VALUE"
    });
    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "64|676", value: "10/10/2022" },
      type: "~questions/SET_FIELD_VALUE"
    });
    expect(dispatch).toHaveBeenCalledWith({
      payload: { name: "72|676", value: { selected: "116" } },
      type: "~questions/SET_FIELD_VALUE"
    });
  });
});
