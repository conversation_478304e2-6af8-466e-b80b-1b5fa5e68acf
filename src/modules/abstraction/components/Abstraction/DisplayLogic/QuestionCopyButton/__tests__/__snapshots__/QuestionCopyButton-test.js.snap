// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Question<PERSON><PERSON>Button is disabled when isSaving is true 1`] = `
<div>
  <button
    className="display-logic-button"
    disabled={true}
    onClick={[Function]}
    onKeyDown={[Function]}
    type="button"
  >
    <i
      className="fa fa-circle-check"
    />
    copy answers above
  </button>
</div>
`;

exports[`QuestionC<PERSON>Button is disabled when writeLocked is true 1`] = `
<div>
  <button
    className="display-logic-button"
    disabled={true}
    onClick={[Function]}
    onKeyDown={[Function]}
    type="button"
  >
    <i
      className="fa fa-circle-check"
    />
    copy answers above
  </button>
</div>
`;

exports[`Question<PERSON><PERSON>Button renders correctly 1`] = `
<div>
  <button
    className="display-logic-button"
    disabled={false}
    onClick={[Function]}
    onKeyDown={[Function]}
    type="button"
  >
    <i
      className="fa fa-circle-check"
    />
    copy answers above
  </button>
</div>
`;
