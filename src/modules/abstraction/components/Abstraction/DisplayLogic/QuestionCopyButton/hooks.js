import {
  __,
  always,
  append,
  applySpec,
  assoc,
  both,
  chain,
  complement,
  defaultTo,
  either,
  filter,
  find,
  head,
  identity,
  ifElse,
  includes,
  keys,
  map,
  nth,
  path,
  pathEq,
  pathOr,
  pipe,
  prop,
  propEq,
  propOr,
  reduce,
  tap,
  toPairs,
  toString,
  values,
  when
} from "ramda";
import { useButtonHandlers } from "../QuestionFillButton/hooks";
import {
  findQuestionInDisplayLogic,
  getFieldsWithoutAnswer,
  getQuestionsFields
} from "modules/questionnaire/components/Questionnaire/DisplayLogic/QuestionFillButton/enhancers";
import { isNullOrEmpty } from "utils/fp";
import Engine from "modules/engine";
import {
  isCompoundField,
  isCompoundIncludingField
} from "modules/questionnaire/services/specialFields";

const ENUMERABLE_TYPES = ["enumerable", "enumerable-searchable"];
const filterFieldsWithAnswerGroup = filter(prop("answerGroup"));
const setDisplayValue = (field, answer, setValue) =>
  pipe(
    ifElse(
      pipe(
        path(["question", "type"]),
        either(isCompoundField, isCompoundIncludingField)
      ),
      ifElse(
        pipe(prop("answerType"), includes(__, ENUMERABLE_TYPES)),
        applySpec({
          selected: prop("answerData")
        }),
        applySpec({
          value: prop("answerData")
        })
      ),
      prop("answerData")
    ),
    val => {
      setValue(field.name, val);
    }
  )(answer);
const isEnumerableAnswer = both(
  identity,
  pipe(prop("answerType"), includes(__, ENUMERABLE_TYPES))
);
const getEnumerableDescription = field => answer =>
  pipe(
    pathOr([], ["question", "questionEnumerables"]),
    find(propEq("id", answer.answerData)),
    defaultTo({}),
    prop("description")
  )(field);
const valueToSet = source =>
  pipe(
    pathOr([], ["question", "questionEnumerables"]),
    find(propEq("description", source.answerData)),
    defaultTo({}),
    prop("id")
  );

export const fillAnswers = props => {
  const { displayLogic, answerGroup, allAnswers, currentSection, setValue } =
    props;

  const getQuestions = pipe(
    chain(pipe(toString, findQuestionInDisplayLogic(currentSection))),
    getQuestionsFields(answerGroup)
  );

  const parsedMap = pipe(propOr("{}", "questionCopyMap"), val =>
    JSON.parse(val)
  )(displayLogic);
  const mapPairs = toPairs(parsedMap);
  const sourceFields = pipe(keys, map(Number), getQuestions)(parsedMap);

  const getSourceAnswer = field =>
    pipe(
      find(pipe(nth(1), toString, pathEq(["question", "id"], __, field))),
      head,
      sourceId => find(pathEq(["question", "id"], sourceId), sourceFields),
      when(identity, sourceField =>
        pipe(
          prop("name"),
          prop(__, allAnswers),
          when(isEnumerableAnswer, answer =>
            pipe(
              getEnumerableDescription(sourceField),
              when(identity, assoc("answerData", __, answer))
            )(answer)
          )
        )(sourceField)
      )
    )(mapPairs);

  const addAnswer = (answers, field) =>
    pipe(
      getSourceAnswer,
      ifElse(
        isNullOrEmpty,
        always(answers),
        pipe(
          source =>
            applySpec({
              answerData: ifElse(
                () => isEnumerableAnswer(source),
                valueToSet(source),
                () => source.answerData
              ),
              question: prop("question"),
              answerGroup: prop("answerGroup"),
              answerType: always(source.answerType),
              answerGroupId: path(["answerGroup", "id"]),
              questionId: path(["question", "id"])
            })(field),
          tap(val => setDisplayValue(field, val, setValue)),
          append(__, answers)
        )
      )
    )(field);

  pipe(
    // get destination Ids
    values,
    getQuestions,
    getFieldsWithoutAnswer(allAnswers),
    filterFieldsWithAnswerGroup,
    reduce(addAnswer, []),
    when(complement(isNullOrEmpty), newAnswers => {
      Engine.saveAnswers(props, newAnswers);
    })
  )(parsedMap);
};

export const useComponentLogic = props => useButtonHandlers(props, fillAnswers);
