import { FormattedMessage } from "react-intl";
import { useComponentLogic } from "./hooks";

export const QuestionCopyButton = props => {
  const { displayLogic } = props;
  const {
    fillAnswers,
    writeLocked,
    onFocusRef,
    isSaving,
    handleKeyBoardAction
  } = useComponentLogic(props);

  return (
    <div>
      <button
        type="button"
        disabled={writeLocked || isSaving}
        onClick={fillAnswers}
        onKeyDown={handleKeyBoardAction}
        ref={onFocusRef}
        className="display-logic-button"
      >
        {isSaving ? (
          <>
            <i className="fa fa-spinner fa-spin" />
            <FormattedMessage
              id="components.wait"
              defaultMessage="Please wait ..."
            />
          </>
        ) : (
          <>
            <i className="fa fa-circle-check" />
            {displayLogic.buttonText}
          </>
        )}
      </button>
    </div>
  );
};

export default QuestionCopyButton;
