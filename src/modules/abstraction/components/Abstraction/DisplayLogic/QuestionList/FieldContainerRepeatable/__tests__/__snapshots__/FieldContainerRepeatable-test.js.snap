// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FieldContainerRepeatable renders component when disabled 1`] = `
<div
  className="field-container indent-0 disabled"
>
  <Field
    answerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    disabled={true}
    dispatch={[Function]}
    helpInfo={
      Object {
        "test": "this is a helpInfo test",
      }
    }
    hideable={false}
    indentLevelClassName="indent-0"
    name="42|22"
    parentAnswerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    question={
      Object {
        "groupId": "1",
        "id": "2",
        "prompt": "test question 2",
        "type": "Open",
      }
    }
    questionAnswerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
  />
  <Validation
    hideIfNoValidations={true}
    name="2|1"
  />
</div>
`;

exports[`FieldContainerRepeatable renders component when enabled 1`] = `
<div
  className="field-container indent-0"
>
  <Field
    answerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    disabled={false}
    dispatch={[Function]}
    helpInfo={
      Object {
        "test": "this is a helpInfo test",
      }
    }
    hideable={false}
    indentLevelClassName="indent-0"
    name="42|22"
    parentAnswerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    question={
      Object {
        "groupId": "1",
        "id": "1",
        "prompt": "test question 1",
        "type": "Open",
      }
    }
    questionAnswerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
  />
  <Validation
    hideIfNoValidations={true}
    name="1|1"
  />
</div>
`;

exports[`FieldContainerRepeatable renders component when hideable 1`] = `
<div
  className="field-container indent-0 disabled hidden"
>
  <Field
    answerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    disabled={true}
    dispatch={[Function]}
    helpInfo={
      Object {
        "test": "this is a helpInfo test",
      }
    }
    hideable={true}
    indentLevelClassName="indent-0"
    name="42|22"
    parentAnswerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
    question={
      Object {
        "groupId": "1",
        "id": "2",
        "prompt": "test question 2",
        "type": "Open",
      }
    }
    questionAnswerGroup={
      Object {
        "groupId": "1",
        "id": "1",
      }
    }
  />
  <Validation
    hideIfNoValidations={true}
    name="2|1"
  />
</div>
`;
