import classnames from "classnames";
import { useComponentLogic } from "../FieldContainer/hooks";
import Field from "modules/abstraction/components/Abstraction/Field";
import Validation from "modules/abstraction/components/Abstraction/Field/Validation";
import "../../../../../styles/field.scss";

export const FieldContainerRepeatable = props => {
  const { answerGroup, indentLevelClassName } = props;
  const { disabled, hidden, name, fieldAnswerGroup } = useComponentLogic(props);
  const containerClasses = classnames("field-container", indentLevelClassName, {
    disabled,
    hidden
  });

  return (
    <div className={containerClasses}>
      <Field
        disabled={disabled}
        questionAnswerGroup={fieldAnswerGroup}
        name={name}
        parentAnswerGroup={answerGroup}
        {...props}
      />
      <Validation name={name} hideIfNoValidations />
    </div>
  );
};

export default FieldContainerRepeatable;
