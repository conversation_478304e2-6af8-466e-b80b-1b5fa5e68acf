import TestRenderer from "react-test-renderer";
import decorated from "utils/test/decorated";
import FieldContainerRepeatable from "..";

// Mock Field component
jest.mock("modules/abstraction/components/Abstraction/Field", () => "Field");
jest.mock(
  "modules/abstraction/components/Abstraction/Field/Validation",
  () => "Validation"
);

describe("FieldContainerRepeatable", () => {
  const appState = {
    questions: {
      visibleQuestions: {
        "1|1": { id: "1|1" },
        "3|1": { id: "3|1" },
        "1|2": { id: "1|2" }
      },
      validationFailures: {
        "1|1": [{ error: true }],
        "1|2": [{ warning: true }]
      },
      focusedFieldName: "1|1"
    }
  };
  const answerGroup1 = { id: "1", groupId: "1" };
  const question1 = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open"
  };
  const question2 = {
    id: "2",
    groupId: "1",
    prompt: "test question 2",
    type: "Open"
  };

  const getComponent = (answerGroup, question, hideable) =>
    TestRenderer.create(
      decorated(
        FieldContainerRepeatable,
        {
          question,
          answerGroup,
          helpInfo: { test: "this is a helpInfo test" },
          hideable,
          indentLevelClassName: "indent-0"
        },
        null,
        appState
      )
    );

  test("renders component when enabled", () => {
    const result = getComponent(answerGroup1, question1, false);

    expect(result).toMatchSnapshot();
  });

  test("renders component when disabled", () => {
    const result = getComponent(answerGroup1, question2, false);

    expect(result).toMatchSnapshot();
  });

  test("renders component when hideable", () => {
    const result = getComponent(answerGroup1, question2, true);

    expect(result).toMatchSnapshot();
  });
});
