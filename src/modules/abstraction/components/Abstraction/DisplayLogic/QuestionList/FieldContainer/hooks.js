import { useSelector } from "react-redux";
import { useQuestionAnswerGroupAndName } from "modules/abstraction/hooks";
import { getFieldSuffix } from "modules/questionnaire/services/specialFields";
import Questions from "modules/question/selectors";
import { useIsFocused } from "modules/fields/components/hooks/useFocus";
import { useValidation } from "modules/fields/components/hooks/useValidations";

export const useComponentLogic = ({ answerGroup, question, hideable }) => {
  const { fieldAnswerGroup, name } = useQuestionAnswerGroupAndName({
    answerGroup,
    question
  });
  const isEnabled = useSelector(state =>
    Questions.hasVisibleQuestion(name, state)
  );
  const fieldName = `${name}${getFieldSuffix(question.type)}`;
  const { hasValidationErrors, hasValidationWarnings } = useValidation(name);
  const isFocused = useIsFocused(name);

  return {
    disabled: !isEnabled,
    fieldAnswerGroup,
    name,
    hidden: !isEnabled && hideable,
    fieldName,
    hasValidationErrors,
    hasValidationWarnings,
    isFocused
  };
};
