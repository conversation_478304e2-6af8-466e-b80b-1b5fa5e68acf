// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FieldContainer renders component when disabled 1`] = `
<div
  className="field-container indent-0"
>
  <div
    className="field-prompt-control"
  >
    <QuestionPrompt
      disabled={true}
      helpInfo={
        Object {
          "test": "this is a helpInfo test",
        }
      }
      name="2|1"
      question={
        Object {
          "groupId": "1",
          "id": "2",
          "prompt": "test question 2",
          "type": "Open",
        }
      }
      questionAnswerGroup={
        Object {
          "groupId": "1",
          "id": "1",
        }
      }
    />
    <div
      className="field-holder disabled"
    >
      <Field
        answerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        disabled={true}
        dispatch={[Function]}
        helpInfo={
          Object {
            "test": "this is a helpInfo test",
          }
        }
        hideable={false}
        indentLevelClassName="indent-0"
        name="42|22"
        parentAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        question={
          Object {
            "groupId": "1",
            "id": "2",
            "prompt": "test question 2",
            "type": "Open",
          }
        }
        questionAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
      />
    </div>
  </div>
  <Validation
    name="2|1"
    prompt="test question 2"
  />
</div>
`;

exports[`FieldContainer renders component when enabled, not focused and no validations 1`] = `
<div
  className="field-container indent-0"
>
  <div
    className="field-prompt-control"
  >
    <QuestionPrompt
      disabled={false}
      helpInfo={
        Object {
          "test": "this is a helpInfo test",
        }
      }
      name="3|1"
      question={
        Object {
          "groupId": "1",
          "id": "3",
          "prompt": "test question 3",
          "type": "Open",
        }
      }
      questionAnswerGroup={
        Object {
          "groupId": "1",
          "id": "1",
        }
      }
    />
    <div
      className="field-holder"
    >
      <Field
        answerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        disabled={false}
        dispatch={[Function]}
        helpInfo={
          Object {
            "test": "this is a helpInfo test",
          }
        }
        hideable={true}
        indentLevelClassName="indent-0"
        name="42|22"
        parentAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        question={
          Object {
            "groupId": "1",
            "id": "3",
            "prompt": "test question 3",
            "type": "Open",
          }
        }
        questionAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
      />
    </div>
  </div>
  <Validation
    name="3|1"
    prompt="test question 3"
  />
</div>
`;

exports[`FieldContainer renders component when enabled, not focused awith warnings 1`] = `
<div
  className="field-container indent-0 has-warnings"
>
  <div
    className="field-prompt-control"
  >
    <QuestionPrompt
      disabled={false}
      helpInfo={
        Object {
          "test": "this is a helpInfo test",
        }
      }
      name="1|2"
      question={
        Object {
          "groupId": "1",
          "id": "1",
          "prompt": "test question 1",
          "type": "Open",
        }
      }
      questionAnswerGroup={
        Object {
          "groupId": "1",
          "id": "2",
        }
      }
    />
    <div
      className="field-holder"
    >
      <Field
        answerGroup={
          Object {
            "groupId": "1",
            "id": "2",
          }
        }
        disabled={false}
        dispatch={[Function]}
        helpInfo={
          Object {
            "test": "this is a helpInfo test",
          }
        }
        hideable={false}
        indentLevelClassName="indent-0"
        name="42|22"
        parentAnswerGroup={
          Object {
            "groupId": "1",
            "id": "2",
          }
        }
        question={
          Object {
            "groupId": "1",
            "id": "1",
            "prompt": "test question 1",
            "type": "Open",
          }
        }
        questionAnswerGroup={
          Object {
            "groupId": "1",
            "id": "2",
          }
        }
      />
    </div>
  </div>
  <Validation
    name="1|2"
    prompt="test question 1"
  />
</div>
`;

exports[`FieldContainer renders component when focused, with validations and enabled 1`] = `
<div
  className="field-container indent-0 has-errors is-focused"
>
  <div
    className="field-prompt-control"
  >
    <QuestionPrompt
      disabled={false}
      helpInfo={
        Object {
          "test": "this is a helpInfo test",
        }
      }
      name="1|1"
      question={
        Object {
          "groupId": "1",
          "id": "1",
          "prompt": "test question 1",
          "type": "Open",
        }
      }
      questionAnswerGroup={
        Object {
          "groupId": "1",
          "id": "1",
        }
      }
    />
    <div
      className="field-holder"
    >
      <Field
        answerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        disabled={false}
        dispatch={[Function]}
        helpInfo={
          Object {
            "test": "this is a helpInfo test",
          }
        }
        hideable={false}
        indentLevelClassName="indent-0"
        name="42|22"
        parentAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        question={
          Object {
            "groupId": "1",
            "id": "1",
            "prompt": "test question 1",
            "type": "Open",
          }
        }
        questionAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
      />
    </div>
  </div>
  <Validation
    name="1|1"
    prompt="test question 1"
  />
</div>
`;

exports[`FieldContainer renders component when hideable 1`] = `
<div
  className="field-container indent-0 hidden"
>
  <div
    className="field-prompt-control"
  >
    <QuestionPrompt
      disabled={true}
      helpInfo={
        Object {
          "test": "this is a helpInfo test",
        }
      }
      name="2|1"
      question={
        Object {
          "groupId": "1",
          "id": "2",
          "prompt": "test question 2",
          "type": "Open",
        }
      }
      questionAnswerGroup={
        Object {
          "groupId": "1",
          "id": "1",
        }
      }
    />
    <div
      className="field-holder disabled"
    >
      <Field
        answerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        disabled={true}
        dispatch={[Function]}
        helpInfo={
          Object {
            "test": "this is a helpInfo test",
          }
        }
        hideable={true}
        indentLevelClassName="indent-0"
        name="42|22"
        parentAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        question={
          Object {
            "groupId": "1",
            "id": "2",
            "prompt": "test question 2",
            "type": "Open",
          }
        }
        questionAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
      />
    </div>
  </div>
  <Validation
    name="2|1"
    prompt="test question 2"
  />
</div>
`;

exports[`FieldContainer renders component with longPrompt true 1`] = `
<div
  className="field-container indent-0"
>
  <div
    className="field-prompt-control"
  >
    <QuestionPrompt
      disabled={false}
      helpInfo={
        Object {
          "test": "this is a helpInfo test",
        }
      }
      longPrompt={true}
      name="3|1"
      question={
        Object {
          "groupId": "1",
          "id": "3",
          "prompt": "test question 3",
          "type": "Open",
        }
      }
      questionAnswerGroup={
        Object {
          "groupId": "1",
          "id": "1",
        }
      }
    />
    <div
      className="field-holder long-prompt"
    >
      <Field
        answerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        disabled={false}
        dispatch={[Function]}
        helpInfo={
          Object {
            "test": "this is a helpInfo test",
          }
        }
        hideable={true}
        indentLevelClassName="indent-0"
        longPrompt={true}
        name="42|22"
        parentAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
        question={
          Object {
            "groupId": "1",
            "id": "3",
            "prompt": "test question 3",
            "type": "Open",
          }
        }
        questionAnswerGroup={
          Object {
            "groupId": "1",
            "id": "1",
          }
        }
      />
    </div>
  </div>
  <Validation
    name="3|1"
    prompt="test question 3"
  />
</div>
`;
