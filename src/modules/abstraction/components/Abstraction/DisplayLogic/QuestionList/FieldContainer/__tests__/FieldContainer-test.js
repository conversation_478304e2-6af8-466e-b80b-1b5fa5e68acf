import TestRenderer from "react-test-renderer";
import decorated from "utils/test/decorated";
import Field<PERSON>ontainer from "..";

// Mock Field component to avoid shallow render to mark it as undefined due HOC
jest.mock("modules/abstraction/components/Abstraction/Field", () => "Field");
jest.mock(
  "modules/abstraction/components/Abstraction/Field/Validation",
  () => "Validation"
);

jest.mock(
  "modules/abstraction/components/Abstraction/Field/QuestionPrompt",
  () => "QuestionPrompt"
);

describe("FieldContainer", () => {
  const appState = {
    questions: {
      visibleQuestions: {
        "1|1": { id: "1|1" },
        "3|1": { id: "3|1" },
        "1|2": { id: "1|2" }
      },
      validationFailures: {
        "1|1": [{ error: true }],
        "1|2": [{ warning: true }]
      },
      focusedFieldName: "1|1"
    }
  };
  const answerGroup1 = { id: "1", groupId: "1" };
  const answerGroup2 = { id: "2", groupId: "1" };
  const question1 = {
    id: "1",
    groupId: "1",
    prompt: "test question 1",
    type: "Open"
  };
  const question2 = {
    id: "2",
    groupId: "1",
    prompt: "test question 2",
    type: "Open"
  };
  const question3 = {
    id: "3",
    groupId: "1",
    prompt: "test question 3",
    type: "Open"
  };

  const getComponent = ({ answerGroup, question, hideable, longPrompt }) =>
    TestRenderer.create(
      decorated(
        FieldContainer,
        {
          question,
          answerGroup,
          helpInfo: { test: "this is a helpInfo test" },
          hideable,
          indentLevelClassName: "indent-0",
          longPrompt
        },
        null,
        appState
      )
    );

  test("renders component when focused, with validations and enabled", () => {
    const result = getComponent({
      answerGroup: answerGroup1,
      question: question1,
      hideable: false
    });

    expect(result).toMatchSnapshot();
  });

  test("renders component when disabled", () => {
    const result = getComponent({
      answerGroup: answerGroup1,
      question: question2,
      hideable: false
    });

    expect(result).toMatchSnapshot();
  });

  test("renders component when hideable", () => {
    const result = getComponent({
      answerGroup: answerGroup1,
      question: question2,
      hideable: true
    });

    expect(result).toMatchSnapshot();
  });

  test("renders component when enabled, not focused and no validations", () => {
    const result = getComponent({
      answerGroup: answerGroup1,
      question: question3,
      hideable: true
    });

    expect(result).toMatchSnapshot();
  });

  test("renders component when enabled, not focused awith warnings", () => {
    const result = getComponent({
      answerGroup: answerGroup2,
      question: question1,
      hideable: false
    });

    expect(result).toMatchSnapshot();
  });

  test("renders component with longPrompt true", () => {
    const result = getComponent({
      answerGroup: answerGroup1,
      question: question3,
      hideable: true,
      longPrompt: true
    });

    expect(result).toMatchSnapshot();
  });
});
