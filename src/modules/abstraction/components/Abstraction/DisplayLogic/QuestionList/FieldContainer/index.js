import classnames from "classnames";

import { useComponentLogic } from "./hooks";
import Validation from "modules/abstraction/components/Abstraction/Field/Validation";
import QuestionPrompt from "modules/abstraction/components/Abstraction/Field/QuestionPrompt";
import Field from "modules/abstraction/components/Abstraction/Field";
import "../../../../../styles/field.scss";

export const FieldContainer = props => {
  const {
    question,
    answerGroup,
    indentLevelClassName,
    helpInfo,
    questionnaireResponseId,
    longPrompt
  } = props;
  const {
    disabled,
    hasValidationErrors,
    hasValidationWarnings,
    hidden,
    isFocused,
    name,
    fieldAnswerGroup
  } = useComponentLogic(props);
  const containerClasses = classnames("field-container", indentLevelClassName, {
    hidden,
    "has-errors": hasValidationErrors,
    "has-warnings": !hasValidationErrors && hasValidationWarnings,
    "is-focused": isFocused
  });
  const fieldClasses = classnames("field-holder", {
    disabled,
    "long-prompt": longPrompt
  });

  return (
    <div className={containerClasses}>
      <div className="field-prompt-control">
        <QuestionPrompt
          question={question}
          helpInfo={helpInfo}
          name={name}
          questionAnswerGroup={fieldAnswerGroup}
          questionnaireResponseId={questionnaireResponseId}
          longPrompt={longPrompt}
          disabled={disabled}
        />
        <div className={fieldClasses}>
          <Field
            disabled={disabled}
            questionAnswerGroup={fieldAnswerGroup}
            key={name}
            name={name}
            parentAnswerGroup={answerGroup}
            {...props}
          />
        </div>
      </div>
      <Validation name={name} prompt={question.prompt} />
    </div>
  );
};

export default FieldContainer;
