import shallowRender from "utils/shallowRender";
import { QuestionList } from "..";

describe("QuestionList", () => {
  const dlWithQuestions = {
    questions: [{ id: "31" }, { id: "32" }],
    collapsible: false,
    inline: false,
    hideable: false,
    helpInformation: [],
    indentLevels: [0],
    questionModifiers: {}
  };
  const dlWithQuestionsAndModifiers = {
    questions: [{ id: "31" }, { id: "32" }],
    collapsible: false,
    inline: false,
    hideable: false,
    helpInformation: [],
    indentLevels: [0],
    // eslint-disable-next-line camelcase
    questionModifiers: { 31: { columns_amount: 2 } }
  };
  const dlWithoutQuestions = {
    questions: [],
    collapsible: false,
    inline: false,
    hideable: false,
    helpInformation: [],
    indentLevels: [0],
    questionModifiers: {}
  };
  const dlRepeatable = {
    questions: [{ id: "31" }, { id: "32" }],
    collapsible: true,
    inline: true,
    hideable: true,
    helpInformation: [],
    indentLevels: [0],
    questionModifiers: {}
  };

  const getComponent = ({
    displayLogic,
    isInRepeatable,
    index,
    longPrompt = false
  }) => (
    <QuestionList
      displayLogic={displayLogic}
      isInRepeatable={isInRepeatable}
      index={index}
      longPrompt={longPrompt}
    />
  );

  test("renders component correctly", () => {
    const result = shallowRender(
      getComponent({ displayLogic: dlWithQuestions })
    );

    expect(result).toMatchSnapshot();
  });

  test("renders component with long prompt correctly", () => {
    const result = shallowRender(
      getComponent({ displayLogic: dlWithQuestions, longPrompt: true })
    );

    expect(result).toMatchSnapshot();
  });

  test("renders component with question modifiers correctly", () => {
    const result = shallowRender(
      getComponent({ displayLogic: dlWithQuestionsAndModifiers })
    );

    expect(result).toMatchSnapshot();
  });

  test("renders component empty when no questions", () => {
    const result = shallowRender(
      getComponent({ displayLogic: dlWithoutQuestions })
    );

    expect(result).toMatchSnapshot();
  });

  test("renders component in repeatable mode first element", () => {
    const result = shallowRender(
      getComponent({
        displayLogic: dlRepeatable,
        isInRepeatable: true,
        index: 0
      })
    );

    expect(result).toMatchSnapshot();
  });

  test("renders component in none repeatable mode when no first element", () => {
    const result = shallowRender(
      getComponent({
        displayLogic: dlRepeatable,
        isInRepeatable: true,
        index: 1
      })
    );

    expect(result).toMatchSnapshot();
  });
});
