// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QuestionList renders component correctly 1`] = `
<div
  className="question-list"
>
  <FieldContainer
    collapsible={false}
    columnSpan={null}
    hideable={false}
    indentLevelClassName="indent-0"
    isInline={false}
    longPrompt={false}
    question={
      Object {
        "id": "31",
      }
    }
  />
  <FieldContainer
    collapsible={false}
    columnSpan={null}
    hideable={false}
    indentLevelClassName="indent-undefined"
    isInline={false}
    longPrompt={false}
    question={
      Object {
        "id": "32",
      }
    }
  />
</div>
`;

exports[`QuestionList renders component empty when no questions 1`] = `
<div
  className="question-list"
/>
`;

exports[`QuestionList renders component in none repeatable mode when no first element 1`] = `
<div
  className="question-list"
>
  <FieldContainer
    collapsible={true}
    columnSpan={null}
    hideable={true}
    indentLevelClassName="indent-0"
    index={1}
    isInRepeatable={true}
    isInline={true}
    longPrompt={false}
    question={
      Object {
        "id": "31",
      }
    }
  />
  <FieldContainer
    collapsible={true}
    columnSpan={null}
    hideable={true}
    indentLevelClassName="indent-undefined"
    index={1}
    isInRepeatable={true}
    isInline={true}
    longPrompt={false}
    question={
      Object {
        "id": "32",
      }
    }
  />
</div>
`;

exports[`QuestionList renders component in repeatable mode first element 1`] = `
<div
  className="question-list"
>
  <FieldContainerRepeatable
    collapsible={true}
    columnSpan={null}
    hideable={true}
    indentLevelClassName="indent-0"
    index={0}
    isInRepeatable={true}
    isInline={true}
    longPrompt={false}
    question={
      Object {
        "id": "31",
      }
    }
  />
  <FieldContainerRepeatable
    collapsible={true}
    columnSpan={null}
    hideable={true}
    indentLevelClassName="indent-undefined"
    index={0}
    isInRepeatable={true}
    isInline={true}
    longPrompt={false}
    question={
      Object {
        "id": "32",
      }
    }
  />
</div>
`;

exports[`QuestionList renders component with long prompt correctly 1`] = `
<div
  className="question-list"
>
  <FieldContainer
    collapsible={false}
    columnSpan={null}
    hideable={false}
    indentLevelClassName="indent-0"
    isInline={false}
    longPrompt={true}
    question={
      Object {
        "id": "31",
      }
    }
  />
  <FieldContainer
    collapsible={false}
    columnSpan={null}
    hideable={false}
    indentLevelClassName="indent-undefined"
    isInline={false}
    longPrompt={true}
    question={
      Object {
        "id": "32",
      }
    }
  />
</div>
`;

exports[`QuestionList renders component with question modifiers correctly 1`] = `
<div
  className="question-list"
>
  <FieldContainer
    collapsible={false}
    columnSpan={2}
    hideable={false}
    indentLevelClassName="indent-0"
    isInline={false}
    longPrompt={false}
    question={
      Object {
        "id": "31",
      }
    }
  />
  <FieldContainer
    collapsible={false}
    columnSpan={null}
    hideable={false}
    indentLevelClassName="indent-undefined"
    isInline={false}
    longPrompt={false}
    question={
      Object {
        "id": "32",
      }
    }
  />
</div>
`;
