import {
  always,
  both,
  cond,
  ifElse,
  prop,
  propEq,
  T,
  pipe,
  has,
  path
} from "ramda";
import classnames from "classnames";
import { isNullOrEmpty } from "utils/fp";

import "../../../../styles/display-logic.scss";
import { FieldContainer } from "./FieldContainer";
import { FieldContainerRepeatable } from "./FieldContainerRepeatable";

export const QuestionList = props => {
  const { displayLogic, isBooleanSequence, longPrompt, ...otherProps } = props;
  const containerClass = classnames("question-list");
  const FieldContainerType = cond([
    [
      both(prop("isInRepeatable"), propEq("index", 0)),
      always(FieldContainerRepeatable)
    ],
    [T, always(FieldContainer)]
  ])(props);
  const columnSpan = id =>
    ifElse(
      pipe(prop("questionModifiers"), has(id)),
      // eslint-disable-next-line camelcase
      path(["questionModifiers", id, "columns_amount"]),
      always(null)
    )(displayLogic);

  return (
    <div className={containerClass}>
      {!isNullOrEmpty(displayLogic.questions) &&
        displayLogic.questions.map((question, index) => (
          <FieldContainerType
            key={question.id}
            question={question}
            helpInfo={displayLogic.helpInformation[index]}
            isBooleanSequence={isBooleanSequence}
            isInline={displayLogic.inline}
            collapsible={displayLogic.collapsible}
            hideable={displayLogic.hideable}
            indentLevelClassName={indentLevel(displayLogic, index)}
            {...otherProps}
            longPrompt={longPrompt}
            columnSpan={columnSpan(question.id)}
          />
        ))}
    </div>
  );
};

function indentLevel({ indentLevels }, index) {
  return `indent-${indentLevels[index]}`;
}

export default QuestionList;
