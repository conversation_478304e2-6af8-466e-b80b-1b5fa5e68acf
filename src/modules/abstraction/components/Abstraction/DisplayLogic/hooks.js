import { useMemo } from "react";
import { defaultTo, head, isEmpty, pipe } from "ramda";
import { getInstances } from "modules/questionnaire/components/Questionnaire/Section/enhancers";

export const useComponentLogic = ({ sectionAnswerGroup, displayLogic }) => {
  const answerGroups = useMemo(
    () => getInstances(sectionAnswerGroup, displayLogic),
    [sectionAnswerGroup, displayLogic]
  );
  const isRepeatable = !isEmpty(displayLogic.children);
  const answerGroup = pipe(defaultTo([]), head)(answerGroups);

  return { answerGroup, answerGroups, isRepeatable };
};
