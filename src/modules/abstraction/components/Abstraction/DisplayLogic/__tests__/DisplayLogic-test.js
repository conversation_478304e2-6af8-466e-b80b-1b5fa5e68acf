import shallowRender from "utils/shallowRender";
import { DisplayLogic } from "..";

jest.mock("../RepeatableLoop", () => "RepeatableLoop");

describe("DisplayLogic", () => {
  const answerGroup = { id: "33", descendants: [{ id: "35" }] };
  const noRepeatable = {
    type: "QuestionList",
    children: [],
    answerGroups: [answerGroup]
  };
  const repeatable = {
    type: "QuestionList",
    children: [{ id: 42 }],
    answerGroups: [answerGroup]
  };

  const getComponent = displayLogic => (
    <DisplayLogic
      displayLogic={displayLogic}
      sectionAnswerGroup={answerGroup}
    />
  );

  test("renders component correctly no repeatable", () => {
    const result = shallowRender(getComponent(noRepeatable));

    expect(result).toMatchSnapshot();
  });

  test("renders component correctly repeatable", () => {
    const result = shallowRender(getComponent(repeatable));

    expect(result).toMatchSnapshot();
  });
});
