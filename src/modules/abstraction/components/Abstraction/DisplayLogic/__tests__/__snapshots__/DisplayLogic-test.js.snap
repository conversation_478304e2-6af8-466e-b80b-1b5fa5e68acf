// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DisplayLogic renders component correctly no repeatable 1`] = `
<QuestionList
  answerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "35",
        },
      ],
      "id": "33",
    }
  }
  displayLogic={
    Object {
      "answerGroups": Array [
        Object {
          "descendants": Array [
            Object {
              "id": "35",
            },
          ],
          "id": "33",
        },
      ],
      "children": Array [],
      "type": "QuestionList",
    }
  }
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "35",
        },
      ],
      "id": "33",
    }
  }
/>
`;

exports[`DisplayLogic renders component correctly repeatable 1`] = `
<RepeatableLoop
  answerGroups={
    Array [
      Object {
        "descendants": Array [
          Object {
            "id": "35",
          },
        ],
        "id": "33",
      },
    ]
  }
  displayLogic={
    Object {
      "answerGroups": Array [
        Object {
          "descendants": Array [
            Object {
              "id": "35",
            },
          ],
          "id": "33",
        },
      ],
      "children": Array [
        Object {
          "id": 42,
        },
      ],
      "type": "QuestionList",
    }
  }
  sectionAnswerGroup={
    Object {
      "descendants": Array [
        Object {
          "id": "35",
        },
      ],
      "id": "33",
    }
  }
/>
`;
