import DisplayLogic, { type } from "..";
import { useComponentLogic } from "./hooks";

export const Repeatable = props => {
  const { displayLogic, ...otherProps } = props;
  const { isCollapsed, handleToggleCollapse } = useComponentLogic();
  const Type = type(displayLogic.type);

  return (
    <div className="registry-repeatable">
      <Type
        displayLogic={displayLogic}
        isCollapsed={isCollapsed}
        onToogle={handleToggleCollapse}
        isInRepeatable
        index={0}
        {...otherProps}
      />
      <div style={{ display: isCollapsed ? "none" : "" }}>
        {displayLogic?.children?.map((child, index) => (
          <DisplayLogic
            key={child.id}
            displayLogic={child}
            isInRepeatable
            index={index + 1}
            {...otherProps}
          />
        ))}
      </div>
    </div>
  );
};

export default Repeatable;
