import shallowRender from "utils/shallowRender";
import { Repeatable } from "..";

describe("Repeatable", () => {
  const repeatable = {
    type: "QuestionList",
    children: [{ id: "31" }, { id: "32" }],
    collapsible: false,
    inline: false,
    hideable: false,
    helpInformation: [],
    indentLevels: [0]
  };

  const getComponent = displayLogic => (
    <Repeatable displayLogic={displayLogic} />
  );

  test("renders component correctly", () => {
    const result = shallowRender(getComponent(repeatable));

    expect(result).toMatchSnapshot();
  });
});
