// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Repeatable renders component correctly 1`] = `
<div
  className="registry-repeatable"
>
  <QuestionList
    displayLogic={
      Object {
        "children": Array [
          Object {
            "id": "31",
          },
          Object {
            "id": "32",
          },
        ],
        "collapsible": false,
        "helpInformation": Array [],
        "hideable": false,
        "indentLevels": Array [
          0,
        ],
        "inline": false,
        "type": "QuestionList",
      }
    }
    index={0}
    isCollapsed={false}
    isInRepeatable={true}
    onToogle={[Function]}
  />
  <div
    style={
      Object {
        "display": "",
      }
    }
  >
    <DisplayLogic
      displayLogic={
        Object {
          "id": "31",
        }
      }
      index={1}
      isInRepeatable={true}
    />
    <DisplayLogic
      displayLogic={
        Object {
          "id": "32",
        }
      }
      index={2}
      isInRepeatable={true}
    />
  </div>
</div>
`;
