import { useCurrentSectionAndSectionAnswerGroup } from "modules/abstraction/hooks";
import {
  setFocusToField,
  setFocusedField
} from "modules/question/actions/index";
import { propOr, equals } from "ramda";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Questionnaire from "modules/questionnaire/selectors";

export const useComponentLogic = id => {
  const { currentSection, sectionAnswerGroup } =
    useCurrentSectionAndSectionAnswerGroup(id);

  const longPrompt = propOr(false, "longPrompt")(currentSection);

  const isOldestResponse = useSelector(state =>
    Questionnaire.getIsOldestResponse(state)
  );

  const shouldShowFeedbackInput = equals(isOldestResponse, false);

  return {
    currentSection,
    sectionAnswerGroup,
    longPrompt,
    shouldShowFeedbackInput
  };
};

export const useFocusField = field => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setFocusedField(field));
    dispatch(setFocusToField(field));
  }, [dispatch, field]);
};
