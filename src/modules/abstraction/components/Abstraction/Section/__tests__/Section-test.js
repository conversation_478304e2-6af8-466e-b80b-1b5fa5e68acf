import TestRenderer from "react-test-renderer";
import decorated from "utils/test/decorated";
import { Section } from "..";

jest.mock("../../DisplayLogic", () => "DisplayLogic");
jest.mock("../../SectionFooter", () => "SectionFooter");
jest.mock(
  "modules/questionnaire/components/Questionnaire/ErrorsModal",
  () => "ErrorsModal"
);

jest.mock("@q-centrix/q-components-react", () => ({
  InputFeedbackMessage: "InputFeedbackMessage"
}));

describe("Section", () => {
  const answerGroup = {
    id: "33",
    groupId: "5"
  };
  const questionnaireResponseId = "11";
  const sections = [
    {
      id: "42",
      isTopLevel: true,
      children: [
        { id: "58", groupId: "5" },
        { id: "64", groupId: "5" }
      ],
      groupId: "5",
      answerGroups: [answerGroup]
    }
  ];
  const questionnaire = {
    sections,
    isOldestResponse: false
  };

  test("renders component correctly", () => {
    const component = decorated(
      Section,
      { questionnaireResponseId, id: "33" },
      null,
      { questionnaire }
    );
    const result = TestRenderer.create(component);

    expect(result).toMatchSnapshot();
  });
});
