// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Section renders component correctly 1`] = `
<div
  className="questionnaire abstraction-section-form-wrap"
>
  <input
    type="hidden"
  />
  <ErrorsModal />
  <div
    className="tw-pb-2.5"
  >
    <InputFeedbackMessage
      text={
        <p>
          <strong>
            This is a copy.
          </strong>
           Carefully verify all data fields are accurate for this abstract.
        </p>
      }
      type="neutral"
    />
  </div>
  <DisplayLogic
    currentSection={
      Object {
        "answerGroups": Array [
          Object {
            "groupId": "5",
            "id": "33",
          },
        ],
        "children": Array [
          Object {
            "groupId": "5",
            "id": "58",
          },
          Object {
            "groupId": "5",
            "id": "64",
          },
        ],
        "groupId": "5",
        "id": "42",
        "isTopLevel": true,
      }
    }
    displayLogic={
      Object {
        "groupId": "5",
        "id": "58",
      }
    }
    longPrompt={false}
    questionnaireResponseId="11"
    sectionAnswerGroup={
      Object {
        "groupId": "5",
        "id": "33",
      }
    }
  />
  <DisplayLogic
    currentSection={
      Object {
        "answerGroups": Array [
          Object {
            "groupId": "5",
            "id": "33",
          },
        ],
        "children": Array [
          Object {
            "groupId": "5",
            "id": "58",
          },
          Object {
            "groupId": "5",
            "id": "64",
          },
        ],
        "groupId": "5",
        "id": "42",
        "isTopLevel": true,
      }
    }
    displayLogic={
      Object {
        "groupId": "5",
        "id": "64",
      }
    }
    longPrompt={false}
    questionnaireResponseId="11"
    sectionAnswerGroup={
      Object {
        "groupId": "5",
        "id": "33",
      }
    }
  />
  <SectionFooter
    currentAnswerGroupId="33"
    questionnaireResponseId="11"
  />
</div>
`;
