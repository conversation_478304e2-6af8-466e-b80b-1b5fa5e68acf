import classnames from "classnames";
import isNullOrEmpty from "utils/fp/isNullOrEmpty";
import DisplayLogic from "../DisplayLogic";
import SectionFooter from "../SectionFooter";
import { useComponentLogic, useFocusField } from "./hooks";
import { LoadedSection } from "modules/questionnaire/components/Questionnaire/Section/LoadedSection";
import ErrorsModal from "modules/questionnaire/components/Questionnaire/ErrorsModal";
import { InputFeedbackMessage } from "@q-centrix/q-components-react";
// eslint-disable-next-line complexity
export const Section = props => {
  const { focusFieldName, id, questionnaireResponseId } = props;
  const {
    currentSection,
    sectionAnswerGroup,
    longPrompt,
    shouldShowFeedbackInput
  } = useComponentLogic(id);

  useFocusField(focusFieldName);

  if (isNullOrEmpty(currentSection)) return null;

  const sectionClassName = classnames(
    "questionnaire",
    "abstraction-section-form-wrap"
  );

  return (
    <div className={sectionClassName}>
      <LoadedSection
        focusFieldName={focusFieldName}
        sectionId={id}
        currentSection={currentSection}
      />
      <ErrorsModal />
      {shouldShowFeedbackInput && (
        <div className="tw-pb-2.5">
          <InputFeedbackMessage
            type="neutral"
            text={
              <p>
                <strong>This is a copy.</strong> Carefully verify all data
                fields are accurate for this abstract.
              </p>
            }
          />
        </div>
      )}

      {currentSection.children.map(child => (
        <DisplayLogic
          key={child.id}
          displayLogic={child}
          sectionAnswerGroup={sectionAnswerGroup}
          questionnaireResponseId={questionnaireResponseId}
          longPrompt={longPrompt}
          currentSection={currentSection}
        />
      ))}
      <SectionFooter
        currentAnswerGroupId={id}
        questionnaireResponseId={questionnaireResponseId}
      />
    </div>
  );
};

export default Section;
