import {
  pipe,
  ifElse,
  prop,
  append,
  __,
  map,
  join,
  groupBy,
  always,
  toPairs,
  filter,
  find,
  propEq,
  mergeAll,
  assoc,
  complement
} from "ramda";
import { isNullOrEmpty } from "utils/fp";

export const getValidationFailureQuestionPrompts = (
  validationReportValidationFailures,
  questionnaire
) =>
  ifElse(
    isNullOrEmpty,
    always({}),
    pipe(
      toPairs,
      map(([key, value]) => {
        const matchingObjects = find(propEq("questionId", key))(
          validationReportValidationFailures
        );

        return (
          matchingObjects && {
            [value.prompt]: [
              pipe(
                assoc("prompt", value.prompt),
                ifElse(
                  prop("warning"),
                  assoc("failureMessage", "should be present"),
                  assoc("failureMessage", "must be present")
                )
              )(matchingObjects)
            ]
          }
        );
      }),
      filter(complement(isNullOrEmpty)),
      mergeAll
    )
  )(prop("questions", questionnaire));

export const getPrompt = failure =>
  pipe(
    ifElse(
      prop("questions"),
      prop("questions"),
      pipe(prop("question"), append(__, []))
    ),
    map(prop("prompt")),
    join(" / ")
  )(failure);

export const groupByFn = failure =>
  ifElse(isNullOrEmpty, always({}), groupBy(getPrompt))(failure);
