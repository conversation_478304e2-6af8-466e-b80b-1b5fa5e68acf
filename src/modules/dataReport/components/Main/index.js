import { Apollo<PERSON>rovider } from "@apollo/client";
import apolloClient from "base/apolloClient";
import Layout from "shared/components/Layout";
import Earnings from "shared/widgets/Earnings";
import LastLogInTile from "shared/widgets/LastLogInTile";
import QPoints from "shared/widgets/QPoints";
import Content from "./Content";
import SecondaryToolbar from "./Content/SecondaryToolBar";
import "styles/submission.scss";
import redirectToRoot from "utils/redirectToRoot";

const client = apolloClient("/qapps/graphql");

const DataReport = () => {
  const tbChildren = (
    <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
      <h1 className="tw-text-black tw-text-2xl tw-font-semibold">
        On-Demand Data
      </h1>
      <div className="tw-flex tw-items-center tw-gap-5">
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  );

  const secondaryToolbarTitle = <SecondaryToolbar />;

  return (
    <ApolloProvider client={client}>
      <Layout
        stChildren={secondaryToolbarTitle}
        stContainerClassName="tw-px-5 tw-py-3 tw-h-[60px]"
        tbChildren={tbChildren}
        onLogoClick={redirectToRoot}
      >
        <Content />
      </Layout>
    </ApolloProvider>
  );
};

export default DataReport;
