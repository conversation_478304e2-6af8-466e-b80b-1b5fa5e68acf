import faker from "faker";
import { append, identity, reduce, times } from "ramda";
import { GET_REPORT_LIBRARY_LIST } from "modules/dataReport/graphql/query";

const statuses = ["PENDING", "ERROR", "SUCCESS", "COMPLETE", "PROCESSING"];

export const createReportData = val => ({
  id: val,
  jobStatus: {
    details: "test details",
    id: val,
    status: statuses[faker.random.number(statuses.length - 1)]
  },
  lastRunPatientCount: faker.random.number({ max: 500, min: 30 }),
  lastRunRowCount: faker.random.number({ max: 1000, min: 30 }),
  name: `Report ${val}`,
  updatedAt: faker.date.recent(),
  user: {
    firstName: faker.name.firstName(),
    lastName: faker.name.lastName()
  }
});
const addReportRecord = (acc, val) => append(createReportData(val), acc);
const getTableData = (records = 25) =>
  reduce(addReportRecord, [], times(identity, records));

const libraryMocks = [
  {
    request: {
      query: GET_REPORT_LIBRARY_LIST,
      variables: {
        currentPage: 1,
        registry: "Oncology",
        search: ""
      }
    },
    result: {
      data: {
        reportLibraryList: {
          currentPage: 1,
          tableData: getTableData(),
          totalRecords: 35
        }
      }
    }
  }
];

export default [...libraryMocks];
