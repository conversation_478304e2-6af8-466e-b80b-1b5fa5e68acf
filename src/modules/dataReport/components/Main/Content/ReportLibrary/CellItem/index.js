import { TableCell } from "@q-centrix/q-components-react";
import { propOr } from "ramda";
import Count from "./Count";
import DateTime from "./DateTime";
import Status from "./Status";
import Text from "./Text";
import UserName from "./UserName";

const cellTypes = {
  Count,
  DateTime,
  Status,
  Text,
  UserName
};

export const CellItem = props => {
  const { column, data } = props;
  const CellContent = propOr(Text, column.type, cellTypes);

  return (
    <TableCell className="tw-px-5">
      <div className="tw-max-w-[250px] tw-truncate">
        <CellContent data={data} />
      </div>
    </TableCell>
  );
};

export default CellItem;
