// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CellItem it renders Count cell correctly 1`] = `
<td
  className="tw-p-4 tw-table-cell tw-text-sm tw-px-5"
>
  <div
    className="tw-max-w-[250px] tw-truncate"
  >
    <Count
      data={15}
    />
  </div>
</td>
`;

exports[`CellItem it renders DateTime cell correctly 1`] = `
<td
  className="tw-p-4 tw-table-cell tw-text-sm tw-px-5"
>
  <div
    className="tw-max-w-[250px] tw-truncate"
  >
    <DateTime
      data="2024-03-12T13:39:21Z"
    />
  </div>
</td>
`;

exports[`CellItem it renders Status cell correctly 1`] = `
<td
  className="tw-p-4 tw-table-cell tw-text-sm tw-px-5"
>
  <div
    className="tw-max-w-[250px] tw-truncate"
  >
    <Status
      data={
        Object {
          "status": "PROCESSING",
        }
      }
    />
  </div>
</td>
`;

exports[`CellItem it renders Text cell correctly 1`] = `
<td
  className="tw-p-4 tw-table-cell tw-text-sm tw-px-5"
>
  <div
    className="tw-max-w-[250px] tw-truncate"
  >
    <Text
      data="test"
    />
  </div>
</td>
`;

exports[`CellItem it renders UserName cell correctly 1`] = `
<td
  className="tw-p-4 tw-table-cell tw-text-sm tw-px-5"
>
  <div
    className="tw-max-w-[250px] tw-truncate"
  >
    <UserName
      data={
        Object {
          "firstName": "Joe",
          "lastName": "Doe",
        }
      }
    />
  </div>
</td>
`;
