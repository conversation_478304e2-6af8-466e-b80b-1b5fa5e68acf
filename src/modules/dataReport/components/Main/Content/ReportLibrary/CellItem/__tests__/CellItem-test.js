import { create } from "react-test-renderer";
import CellItem from "..";

jest.mock("../Status", () => "Status");
jest.mock("../Count", () => "Count");
jest.mock("../DateTime", () => "DateTime");
jest.mock("../Text", () => "Text");
jest.mock("../UserName", () => "UserName");

const columns = [
  { id: "reportName", path: ["name"], text: "Report Name" },
  { id: "createdBy", path: ["user"], text: "Created By", type: "UserName" },
  {
    id: "lastUpdated",
    path: ["updatedAt"],
    text: "Last Updated",
    type: "DateTime"
  },
  { id: "numRows", path: ["lastRunRowCount"], text: "# Rows", type: "Count" },
  { id: "status", path: ["jobStatus"], text: "Status", type: "Status" }
];

describe("CellItem", () => {
  test("it renders Text cell correctly", () => {
    const component = create(<CellItem column={columns[0]} data="test" />);

    expect(component).toMatchSnapshot();
  });

  test("it renders UserName cell correctly", () => {
    const component = create(
      <CellItem
        column={columns[1]}
        data={{ firstName: "Joe", lastName: "Doe" }}
      />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders DateTime cell correctly", () => {
    const component = create(
      <CellItem column={columns[2]} data="2024-03-12T13:39:21Z" />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders Count cell correctly", () => {
    const component = create(<CellItem column={columns[3]} data={15} />);

    expect(component).toMatchSnapshot();
  });

  test("it renders Status cell correctly", () => {
    const component = create(
      <CellItem column={columns[4]} data={{ status: "PROCESSING" }} />
    );

    expect(component).toMatchSnapshot();
  });
});
