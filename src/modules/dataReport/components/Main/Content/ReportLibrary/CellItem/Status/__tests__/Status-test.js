import { create } from "react-test-renderer";
import Status from "..";

describe("Status", () => {
  test("it renders correctly (warning)", () => {
    const component = create(
      <Status
        data={{
          status: "pending"
        }}
      />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders correctly (main)", () => {
    const component = create(
      <Status
        data={{
          status: "processing"
        }}
      />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders correctly (danger)", () => {
    const component = create(
      <Status
        data={{
          status: "error"
        }}
      />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders correctly (success)", () => {
    const component = create(
      <Status
        data={{
          status: "success"
        }}
      />
    );

    expect(component).toMatchSnapshot();
  });
});
