import { Tag } from "@q-centrix/q-components-react";
import { propOr, toLower } from "ramda";

const statusMap = {
  complete: "success",
  error: "danger",
  pending: "warning",
  processing: "main",
  success: "success"
};

const Status = ({ data }) => {
  const { status = "pending" } = data;

  return (
    <Tag
      className="tw-text-xs tw-capitalize"
      status={propOr("neutral", status, statusMap)}
      text={toLower(status)}
    />
  );
};

export default Status;
