import {
  Table,
  TableBody,
  TableHead,
  TableHeadCell,
  TableHeadRow,
  TableContainer,
  TablePagination
} from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import RowItem from "./RowItem";
import CollapsibleRightPanel from "./CollapsibleRightPanel";
import Columns from "./Columns";
import classnames from "classnames";

export const ReportLibrary = props => {
  const {
    tableData,
    loading,
    totalRecords,
    currentPage,
    rowsPerPage,
    handleChangePage,
    handleChangeRowsPerPage,
    isColumnPanelOpen,
    handleColumnPanelToggle,
    selectedColumns
  } = useComponentLogic(props);

  const tableContainerClassName = classnames(
    "tw-absolute tw-left-0 tw-top-0 tw-h-full tw-transition-all tw-duration-500 tw-ease-in-out",
    {
      "tw-translate-x-[0px] tw-w-[calc(100%-414px)]": isColumnPanelOpen,
      "tw-w-full": !isColumnPanelOpen
    }
  );

  return (
    <>
      <div className={tableContainerClassName}>
        <TableContainer className="tw-flex tw-h-full tw-flex-col">
          <div className="tw-h-96 tw-flex-grow tw-overflow-auto">
            <Table loading={loading}>
              <TableHead className="tw-bg-white tw-sticky tw-top-0 tw-z-10 tw-shadow-tableHeader">
                <TableHeadRow>
                  {selectedColumns.map(item => (
                    <TableHeadCell
                      key={item.id}
                      className="tw-whitespace-nowrap tw-px-5 tw-py-4"
                      id={item.id}
                    >
                      {item.label}
                    </TableHeadCell>
                  ))}
                  <TableHeadCell />
                </TableHeadRow>
              </TableHead>
              <TableBody loading={loading}>
                {tableData?.map(row => (
                  <RowItem
                    key={row.id}
                    columns={selectedColumns}
                    currentPage={currentPage}
                    row={row}
                    rowsPerPage={rowsPerPage}
                  />
                ))}
              </TableBody>
            </Table>
          </div>
          <TablePagination
            showRowsPerPage
            count={totalRecords}
            loading={loading}
            numberOfRows={totalRecords}
            page={currentPage}
            paginationShowLimit={rowsPerPage}
            rowsPerPage={rowsPerPage}
            wrapperClassname="tw-w-full"
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </TableContainer>
      </div>
      <CollapsibleRightPanel showPanel={isColumnPanelOpen}>
        <Columns toggleEditColumnBar={handleColumnPanelToggle} />
      </CollapsibleRightPanel>
    </>
  );
};

export default ReportLibrary;
