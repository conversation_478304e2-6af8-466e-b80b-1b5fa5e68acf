import columnsData from "modules/dataReport/utils/columns.json";
import { useCallback, useEffect, useState, useMemo } from "react";
import { useDispatch } from "react-redux";
import { updateSelectedColumns } from "modules/dataReport/redux/slices/index";
import useLocalStorage from "modules/dataReport/utils/useLocalStorage";
import {
  assoc,
  filter,
  map,
  prop,
  propEq,
  when,
  reduce,
  values,
  pipe,
  sortBy
} from "ramda";

export const useComponentLogic = props => {
  const { toggleEditColumnBar } = props;
  const dispatch = useDispatch();

  const sortedColumns = useMemo(
    () => pipe(values, sortBy(prop("order")))(columnsData),
    [columnsData]
  );

  const [columnsValues, setColumnsValues] = useState([]);

  const [savedColumnsValues, setSavedColumnsValues] = useLocalStorage(
    "selectedReportLibraryColumns",
    {}
  );

  const handleReset = useCallback(() => {
    setColumnsValues(
      map(column => assoc("checked", column.isDefault, column), sortedColumns)
    );
  }, [sortedColumns]);

  const handleColumnValueChange = useCallback(
    ({ target: { name, checked } }) => {
      setColumnsValues(
        map(when(propEq("name", name), assoc("checked", checked)))
      );
    },
    []
  );

  const handleSubmit = event => {
    event.preventDefault();
    const currentColumnsValues = reduce(
      (acc, currColumn) => assoc(currColumn.key, currColumn.checked, acc),
      {},
      columnsValues
    );

    setSavedColumnsValues(currentColumnsValues);

    dispatch(updateSelectedColumns(filter(prop("checked"), columnsValues)));
    toggleEditColumnBar();
  };

  useEffect(() => {
    if (savedColumnsValues) {
      const sortedColumnsWithSavedColumnValues = map(column => {
        const checkValue = savedColumnsValues[column.key] ?? column.isDefault;

        return assoc("checked", checkValue, column);
      }, sortedColumns);

      setColumnsValues(sortedColumnsWithSavedColumnValues);
      dispatch(
        updateSelectedColumns(
          filter(prop("checked"), sortedColumnsWithSavedColumnValues)
        )
      );
    } else if (sortedColumns) {
      setColumnsValues(
        map(column => assoc("checked", column.isDefault, column), sortedColumns)
      );
    }
  }, [sortedColumns, savedColumnsValues]);

  return {
    sortedColumns,
    handleReset,
    columnsValues,
    handleColumnValueChange,
    handleSubmit
  };
};
