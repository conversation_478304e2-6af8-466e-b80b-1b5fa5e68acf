import { Button } from "@q-centrix/q-components-react";
import CheckboxList from "./CheckboxList";
import { useComponentLogic } from "./hooks";

const Columns = props => {
  const {
    sortedColumns,
    columnsValues,
    handleReset,
    handleColumnValueChange,
    handleSubmit
  } = useComponentLogic(props);

  return (
    <form autoComplete="off">
      <div className="tw-flex tw-h-32 tw-flex-col tw-justify-between tw-border-b tw-border-gray-200 tw-p-5">
        <h3 className="tw-text-xl tw-font-semibold">Table Columns</h3>
        <div className="tw-flex tw-justify-between">
          <Button type="button" bg="main" onClick={handleReset}>
            <i className="fa-solid fa-arrow-rotate-left tw-mr-2" />
            Restore Defaults
          </Button>
          <Button type="submit" bg="success" onClick={handleSubmit}>
            <i className="fa-regular fa-floppy-disk tw-mr-2" />
            Save
          </Button>
        </div>
      </div>
      <div className="tw-p-7">
        <div className="tw-flex tw-flex-col tw-gap-5">
          <CheckboxList
            columns={sortedColumns}
            columnsValues={columnsValues}
            handleColumnValueChange={handleColumnValueChange}
          />
        </div>
      </div>
    </form>
  );
};

export default Columns;
