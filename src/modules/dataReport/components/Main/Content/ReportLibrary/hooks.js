import { useQuery } from "@apollo/client";
import { GET_REPORT_LIBRARY_LIST } from "modules/dataReport/graphql/query";
import DataReport from "modules/dataReport/redux/selectors";
import { setIsColumnPanelOpen } from "modules/dataReport/redux/slices";
import {
  anyPass,
  assocPath,
  either,
  equals,
  filter,
  isNil,
  map,
  mergeLeft,
  none,
  path,
  pipe,
  prop,
  reduce
} from "ramda";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { isNullOrEmpty } from "utils/fp";

export const usePagination = ({ initialPage = 1, initialRowsPerPage = 25 }) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);

  const handleChangePage = useCallback(cb => {
    setCurrentPage(cb);
  }, []);

  const handleChangeRowsPerPage = useCallback(value => {
    setRowsPerPage(value);
  }, []);

  return {
    currentPage,
    handleChangePage,
    handleChangeRowsPerPage,
    rowsPerPage
  };
};

const filterTableDataByColumns = (tableData, selectedColumns) =>
  map(
    row =>
      mergeLeft(
        { id: row.id },
        reduce(
          (rowWithSelectedColumns, column) =>
            assocPath(
              column.path,
              path(column.path, row),
              rowWithSelectedColumns
            ),
          {},
          filter(prop("checked"), selectedColumns)
        )
      ),
    tableData
  );
const allJobsDoneOrNoData = either(
  isNullOrEmpty,
  pipe(
    map(path(["jobStatus", "status"])),
    none(anyPass([isNil, equals("pending"), equals("processing")]))
  )
);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({ initialPage }) => {
  const {
    currentPage,
    rowsPerPage,
    handleChangePage,
    handleChangeRowsPerPage
  } = usePagination({ initialPage });

  const dispatch = useDispatch();

  const searchTerm = useSelector(DataReport.getLibrarySearch);
  const isColumnPanelOpen = useSelector(DataReport.getisColumnPanelOpen);
  const selectedColumns = useSelector(DataReport.getSelectedColumns);

  const handleColumnPanelToggle = () =>
    dispatch(setIsColumnPanelOpen(!isColumnPanelOpen));

  const selectedRegistry = useSelector(DataReport.getSelectedRegistry);

  const {
    data: { reportLibraryList } = { reportLibraryList: {} },
    loading,
    error,
    stopPolling,
    startPolling
  } = useQuery(GET_REPORT_LIBRARY_LIST, {
    variables: {
      currentPage,
      registry: selectedRegistry,
      rowsPerPage: rowsPerPage.toString(),
      searchTerm: searchTerm || ""
    },
    pollInterval: 2000,
    fetchPolicy: "network-only"
  });

  const { totalRecords = 0, tableData = [] } = reportLibraryList;

  const filteredTableData = filterTableDataByColumns(
    tableData,
    selectedColumns
  );

  useEffect(() => {
    if (allJobsDoneOrNoData(tableData)) {
      stopPolling();
    } else {
      startPolling(2000);
    }
  }, [tableData, startPolling, stopPolling]);

  return {
    currentPage,
    error,
    handleChangePage,
    handleChangeRowsPerPage,
    loading,
    rowsPerPage,
    tableData: filteredTableData,
    totalRecords,
    isColumnPanelOpen,
    handleColumnPanelToggle,
    selectedColumns
  };
};
