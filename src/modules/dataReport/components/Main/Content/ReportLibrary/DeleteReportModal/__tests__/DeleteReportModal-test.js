import { create } from "react-test-renderer";
import DeleteReportModal from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal"
}));

const mockedProps = {
  onCloseModal: () => jest.fn(),
  onConfirm: () => jest.fn(),
  isModalOpen: true
};

const render = props => create(<DeleteReportModal {...props} />);

describe("DeleteReportModal", () => {
  test("it renders component correctly", () => {
    const component = render(mockedProps);

    expect(component).toMatchSnapshot();
  });
});
