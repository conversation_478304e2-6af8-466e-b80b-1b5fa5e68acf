import { Button, ConfirmationModal } from "@q-centrix/q-components-react";

const DeleteReportModal = ({ isModalOpen, onCloseModal, onConfirm }) => (
  <ConfirmationModal isOpen={isModalOpen} title="Delete Report?" isDanger>
    <p className="tw-text-sm">
      Are you sure you want to permanently delete this report?
    </p>
    <div className="tw-flex tw-flex-row tw-justify-center tw-gap-4">
      <Button
        bg="neutral"
        customStyle="tw-w-1/2 tw-h-10 tw-flex tw-flex-row tw-gap-3 tw-items-center tw-text-qcNeutrals-800 tw-bg-qcNeutrals-300 tw-border-2 tw-border-qcNeutrals-400 hover:tw-bg-qcNeutrals-300"
        onClick={onCloseModal}
      >
        <i className="fa fa-xmark" />
        Cancel
      </Button>
      <Button
        bg="danger"
        customStyle="tw-w-1/2 tw-h-10 tw-flex tw-flex-row tw-gap-3 tw-items-center"
        onClick={onConfirm}
      >
        <i className="fa-solid fa-trash" />
        Yes, delete
      </Button>
    </div>
  </ConfirmationModal>
);

export default DeleteReportModal;
