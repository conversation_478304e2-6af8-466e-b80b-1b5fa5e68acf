// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RowItem it renders component correctly 1`] = `
Array [
  <TableRow
    className="tw-group tw-px-5 tw-py-2.5"
  >
    <CellItem
      column={
        Object {
          "id": "reportName",
          "path": Array [
            "name",
          ],
          "text": "Report Name",
        }
      }
      data="Report 1"
    />
    <CellItem
      column={
        Object {
          "id": "createdBy",
          "path": <PERSON>rray [
            "user",
          ],
          "text": "Created By",
          "type": "UserName",
        }
      }
      data={
        Object {
          "firstName": "John",
          "lastName": "Doe",
        }
      }
    />
    <CellItem
      column={
        Object {
          "id": "lastUpdated",
          "path": Array [
            "updatedAt",
          ],
          "text": "Last Updated",
          "type": "DateTime",
        }
      }
      data="2024-03-12T13:39:32Z"
    />
    <CellItem
      column={
        Object {
          "id": "numRows",
          "path": Array [
            "lastRunRowCount",
          ],
          "text": "# Rows",
          "type": "Count",
        }
      }
      data={100}
    />
    <CellItem
      column={
        Object {
          "id": "numPatients",
          "path": Array [
            "lastRunPatientCount",
          ],
          "text": "# Patients",
          "type": "Count",
        }
      }
      data={80}
    />
    <CellItem
      column={
        Object {
          "id": "status",
          "path": Array [
            "jobStatus",
          ],
          "text": "Status",
          "type": "Status",
        }
      }
      data={
        Object {
          "details": "test details",
          "id": 2,
          "status": "processing",
        }
      }
    />
    <TableCell>
      <div
        className="tw-invisible tw-flex tw-gap-2 group-hover:tw-visible"
      >
        <a
          href="/data-report/1"
          onClick={[Function]}
        >
          <i
            className="fa-solid fa-pen icon-button tw-text-white tw-bg-qc-blue-800 tw-border-qc-blue-800"
          />
        </a>
      </div>
    </TableCell>
  </TableRow>,
  <DeleteReportModal
    isModalOpen={false}
    onCloseModal={[Function]}
    onConfirm={[Function]}
  />,
]
`;

exports[`RowItem it renders component correctly when url available 1`] = `
Array [
  <TableRow
    className="tw-group tw-px-5 tw-py-2.5"
  >
    <CellItem
      column={
        Object {
          "id": "reportName",
          "path": Array [
            "name",
          ],
          "text": "Report Name",
        }
      }
      data="Report 1"
    />
    <CellItem
      column={
        Object {
          "id": "createdBy",
          "path": Array [
            "user",
          ],
          "text": "Created By",
          "type": "UserName",
        }
      }
      data={
        Object {
          "firstName": "John",
          "lastName": "Doe",
        }
      }
    />
    <CellItem
      column={
        Object {
          "id": "lastUpdated",
          "path": Array [
            "updatedAt",
          ],
          "text": "Last Updated",
          "type": "DateTime",
        }
      }
      data="2024-03-12T13:39:32Z"
    />
    <CellItem
      column={
        Object {
          "id": "numRows",
          "path": Array [
            "lastRunRowCount",
          ],
          "text": "# Rows",
          "type": "Count",
        }
      }
      data={100}
    />
    <CellItem
      column={
        Object {
          "id": "numPatients",
          "path": Array [
            "lastRunPatientCount",
          ],
          "text": "# Patients",
          "type": "Count",
        }
      }
      data={80}
    />
    <CellItem
      column={
        Object {
          "id": "status",
          "path": Array [
            "jobStatus",
          ],
          "text": "Status",
          "type": "Status",
        }
      }
      data={
        Object {
          "dataFile": Object {
            "url": "/admin/facility_registry_configuration/1/file/3",
          },
          "details": "test details",
          "id": 2,
          "status": "processing",
        }
      }
    />
    <TableCell>
      <div
        className="tw-invisible tw-flex tw-gap-2 group-hover:tw-visible"
      >
        <a
          href="/data-report/1"
          onClick={[Function]}
        >
          <i
            className="fa-solid fa-pen icon-button tw-text-white tw-bg-qc-blue-800 tw-border-qc-blue-800"
          />
        </a>
        <a
          href="http://localhost:3000/admin/facility_registry_configuration/1/file/3"
        >
          <i
            className="fa-solid fa-arrow-down-to-line icon-button tw-text-qc-blue-800 tw-bg-white tw-border-qc-blue-800"
          />
        </a>
      </div>
    </TableCell>
  </TableRow>,
  <DeleteReportModal
    isModalOpen={false}
    onCloseModal={[Function]}
    onConfirm={[Function]}
  />,
]
`;
