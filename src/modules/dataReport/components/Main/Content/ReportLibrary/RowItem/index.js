import { TableCell, TableRow } from "@q-centrix/q-components-react";
import { pathOr } from "ramda";
import { Link } from "react-router-dom";
import { useComponentLogic } from "./hooks";
import CellItem from "../CellItem";
import DeleteReportModal from "../DeleteReportModal";

const editClass =
  "fa-solid fa-pen icon-button tw-text-white tw-bg-qc-blue-800 tw-border-qc-blue-800";
const deleteClass =
  "fa-solid fa-trash icon-button tw-text-error-800 tw-bg-white tw-border-error-800";

const downloadClass =
  "fa-solid fa-arrow-down-to-line icon-button tw-text-qc-blue-800 tw-bg-white tw-border-qc-blue-800";

export const RowItem = props => {
  const { row, columns } = props;
  const { fileUrl, deleteReport, isOwner, handleModal, isModalOpen } =
    useComponentLogic(props);

  return (
    <>
      <TableRow className="tw-group tw-px-5 tw-py-2.5">
        {columns?.map(column => (
          <CellItem
            key={column.id}
            column={column}
            data={pathOr("N/A", column.path, row)}
          />
        ))}

        <TableCell>
          <div className="tw-invisible tw-flex tw-gap-2 group-hover:tw-visible">
            <Link to={`/data-report/${row.id}`}>
              <i className={editClass} />
            </Link>
            {isOwner && <i className={deleteClass} onClick={handleModal} />}
            {fileUrl && (
              <a href={fileUrl}>
                <i className={downloadClass} />
              </a>
            )}
          </div>
        </TableCell>
      </TableRow>
      <DeleteReportModal
        isModalOpen={isModalOpen}
        onCloseModal={handleModal}
        onConfirm={deleteReport}
      />
    </>
  );
};

export default RowItem;
