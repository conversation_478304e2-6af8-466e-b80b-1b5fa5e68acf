import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import { <PERSON><PERSON><PERSON><PERSON> } from "utils/test/decorated";
import { RowItem } from "..";

const columns = [
  { id: "reportName", path: ["name"], text: "Report Name" },
  { id: "createdBy", path: ["user"], text: "Created By", type: "UserName" },
  {
    id: "lastUpdated",
    path: ["updatedAt"],
    text: "Last Updated",
    type: "DateTime"
  },
  { id: "numRows", path: ["lastRunRowCount"], text: "# Rows", type: "Count" },
  {
    id: "numPatients",
    path: ["lastRunPatientCount"],
    text: "# Patients",
    type: "Count"
  },
  { id: "status", path: ["jobStatus"], text: "Status", type: "Status" }
];

const row = {
  id: 1,
  jobStatus: {
    details: "test details",
    id: 2,
    status: "processing"
  },
  lastRunPatientCount: 80,
  lastRunRowCount: 100,
  name: "Report 1",
  updatedAt: "2024-03-12T13:39:32Z",
  user: {
    firstName: "John",
    lastName: "Doe"
  }
};

const rowWithFile = {
  id: 1,
  jobStatus: {
    dataFile: {
      url: "/admin/facility_registry_configuration/1/file/3"
    },
    details: "test details",
    id: 2,
    status: "processing"
  },
  lastRunPatientCount: 80,
  lastRunRowCount: 100,
  name: "Report 1",
  updatedAt: "2024-03-12T13:39:32Z",
  user: {
    firstName: "John",
    lastName: "Doe"
  }
};

const MockedComponent = props => (
  <MemoryRouter>
    <RowItem {...props} />
  </MemoryRouter>
);

jest.mock("../../CellItem", () => "CellItem");
jest.mock("../../DeleteReportModal", () => "DeleteReportModal");

jest.mock("@q-centrix/q-components-react", () => ({
  TableCell: "TableCell",
  TableRow: "TableRow"
}));

describe("RowItem", () => {
  test("it renders component correctly", () => {
    const component = create(
      decoratedApollo({
        apolloMocks: [],
        component: MockedComponent,
        initialAppValues: {
          accountSettings: { fullName: "John Smith" },
          dataReport: {}
        },
        props: { columns, currentPage: 1, row, rowsPerPage: 25 }
      })
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly when url available", () => {
    const component = create(
      decoratedApollo({
        apolloMocks: [],
        component: MockedComponent,
        initialAppValues: {
          accountSettings: { fullName: "John Smith" },
          dataReport: {}
        },
        props: { columns, currentPage: 1, row: rowWithFile, rowsPerPage: 25 }
      })
    );

    expect(component).toMatchSnapshot();
  });
});
