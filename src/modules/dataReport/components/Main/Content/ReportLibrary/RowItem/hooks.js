import { useMutation } from "@apollo/client";
import { equals, join, not, pipe, prop, props } from "ramda";
import { useSelector } from "react-redux";
import { serverURI } from "base/constants";
import AccountSettingsSelectors from "modules/app/selectors/accountSettings";
import { DELETE_ON_DEMAND_REPORT } from "modules/dataReport/graphql/mutation";
import { GET_REPORT_LIBRARY_LIST } from "modules/dataReport/graphql/query";
import DataReport from "modules/dataReport/redux/selectors";
import { cleanURL } from "utils/fp";
import { useCallback, useState } from "react";

const getReportOwner = pipe(
  prop("user"),
  props(["firstName", "lastName"]),
  join(" ")
);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({ row = {}, currentPage, rowsPerPage }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleModal = useCallback(() => setIsModalOpen(not), []);
  const searchTerm = useSelector(DataReport.getLibrarySearch);

  const selectedRegistry = useSelector(state =>
    DataReport.getSelectedRegistry(state)
  );
  const [deleteReport] = useMutation(DELETE_ON_DEMAND_REPORT, {
    refetchQueries: [
      {
        query: GET_REPORT_LIBRARY_LIST,
        variables: {
          currentPage,
          registry: selectedRegistry,
          rowsPerPage: rowsPerPage.toString(),
          searchTerm: searchTerm || ""
        }
      }
    ],
    variables: { id: row.id },
    onCompleted: () => {
      handleModal();
    }
  });

  const reportOwner = getReportOwner(row);

  const currentUser = useSelector(state =>
    AccountSettingsSelectors.getCurrentUser(state)
  );

  const isOwner = equals(reportOwner, currentUser);
  const fileUrl = row?.jobStatus?.dataFile?.url
    ? cleanURL(`${serverURI}${row?.jobStatus?.dataFile?.url}`)
    : null;

  return {
    deleteReport,
    fileUrl,
    isOwner,
    handleModal,
    isModalOpen
  };
};
