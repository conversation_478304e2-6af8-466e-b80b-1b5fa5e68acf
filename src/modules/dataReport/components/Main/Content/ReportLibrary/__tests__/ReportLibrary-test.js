import { act, create } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import ReportLibrary from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Table: "Table",
  TableBody: "TableBody",
  TableContainer: "TableContainer",
  TableHead: "TableHead",
  TableHeadCell: "TableHeadCell",
  TableHeadRow: "TableHeadRow",
  TablePagination: "TablePagination"
}));

jest.mock("../CollapsibleRightPanel", () => "CollapsibleRightPanel");
jest.mock("../Columns", () => "Columns");

describe("ReportLibrary", () => {
  test("it renders component correctly", async () => {
    const component = create(
      decoratedApollo({
        apolloMocks: mocks,
        component: ReportLibrary,
        initialAppValues: {
          dataReport: {
            librarySearch: "",
            selectedColumns: [
              {
                key: "reportName",
                name: "reportName",
                path: ["name"],
                label: "Report Name",
                withSort: true,
                order: 1,
                isDefault: false
              }
            ]
          }
        },
        props: {}
      })
    );

    await act(() => {
      wait(100);
    });
    expect(component).toMatchSnapshot();
  });
});
