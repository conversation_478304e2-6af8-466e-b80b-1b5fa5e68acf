// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReportLibrary it renders component correctly 1`] = `
Array [
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-h-full tw-transition-all tw-duration-500 tw-ease-in-out tw-w-full"
  >
    <TableContainer
      className="tw-flex tw-h-full tw-flex-col"
    >
      <div
        className="tw-h-96 tw-flex-grow tw-overflow-auto"
      >
        <Table
          loading={true}
        >
          <TableHead
            className="tw-bg-white tw-sticky tw-top-0 tw-z-10 tw-shadow-tableHeader"
          >
            <TableHeadRow>
              <TableHeadCell
                className="tw-whitespace-nowrap tw-px-5 tw-py-4"
              >
                Report Name
              </TableHeadCell>
              <TableHeadCell />
            </TableHeadRow>
          </TableHead>
          <TableBody
            loading={true}
          />
        </Table>
      </div>
      <TablePagination
        count={0}
        loading={true}
        numberOfRows={0}
        onPageChange={[Function]}
        onRowsPerPageChange={[Function]}
        page={1}
        paginationShowLimit={25}
        rowsPerPage={25}
        showRowsPerPage={true}
        wrapperClassname="tw-w-full"
      />
    </TableContainer>
  </div>,
  <CollapsibleRightPanel>
    <Columns
      toggleEditColumnBar={[Function]}
    />
  </CollapsibleRightPanel>,
]
`;
