// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CollapsibleRightPanel renders collapsed CollapsibleRightPanel 1`] = `
<div
  className="tw-w-[414px] tw-h-full tw-bg-white tw-absolute tw-right-[-414px] tw-top-0 tw-transition-all tw-duration-500 tw-ease-in-out tw-border tw-border-gray-200 tw-overflow-y-auto tw-translate-x-0"
>
  <div>
    Mock child
  </div>
</div>
`;

exports[`CollapsibleRightPanel renders visible CollapsibleRightPanel 1`] = `
<div
  className="tw-w-[414px] tw-h-full tw-bg-white tw-absolute tw-right-[-414px] tw-top-0 tw-transition-all tw-duration-500 tw-ease-in-out tw-border tw-border-gray-200 tw-overflow-y-auto tw-translate-x-[-414px]"
>
  <div>
    Mock child
  </div>
</div>
`;
