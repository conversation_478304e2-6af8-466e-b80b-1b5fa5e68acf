import PropTypes from "prop-types";
import classnames from "classnames";

const CollapsibleRightPanel = ({ children, showPanel }) => {
  const className = classnames(
    "tw-w-[414px] tw-h-full tw-bg-white tw-absolute tw-right-[-414px] tw-top-0 tw-transition-all tw-duration-500 tw-ease-in-out tw-border tw-border-gray-200 tw-overflow-y-auto",
    {
      "tw-translate-x-[-414px]": showPanel,
      "tw-translate-x-0": !showPanel
    }
  );

  return <div className={className}>{children}</div>;
};

CollapsibleRightPanel.propTypes = {
  children: PropTypes.node.isRequired,
  showPanel: PropTypes.bool.isRequired
};

export default CollapsibleRightPanel;
