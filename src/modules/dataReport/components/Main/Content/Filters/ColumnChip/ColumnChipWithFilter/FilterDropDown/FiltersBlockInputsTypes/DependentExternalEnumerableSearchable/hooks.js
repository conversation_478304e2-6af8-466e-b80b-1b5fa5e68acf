import { useMemo, useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { useToast } from "@q-centrix/q-components-react";
import { GET_DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES } from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  addNewFilterBlock,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import {
  T,
  propOr,
  isEmpty,
  all,
  always,
  any,
  apply,
  applySpec,
  both,
  complement,
  concat,
  defaultTo,
  equals,
  find,
  ifElse,
  length,
  map,
  of as wrapInArray,
  path,
  pathEq,
  pathOr,
  pipe,
  prop,
  propEq,
  props,
  values,
  identity,
  head,
  F,
  when,
  reject
} from "ramda";
import { useDispatch, useSelector } from "react-redux";
import { isNullOrEmpty, isNotNullOrEmpty } from "utils/fp";

const convertEnumerableOptions = map(
  applySpec({
    label: prop("description"),
    value: prop("externalValue")
  })
);
const validateAndExtractSingle = pipe(
  values,
  apply(concat),
  reject(isNullOrEmpty),
  when(pipe(length, complement(equals(1))), always([])),
  head
);
const areDatesInSameYear = pipe(
  applySpec({
    startDate: pipe(path(["firstValue"]), date => new Date(date).getFullYear()),
    endDate: pipe(path(["secondValue"]), date => new Date(date).getFullYear())
  }),
  ({ startDate, endDate }) => equals(startDate, endDate)
);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  eid,
  filter,
  customContainerClassNames
}) => {
  const dispatch = useDispatch();
  const filters = useSelector(state => ReportSelectors.getFilters(state));
  const [dropDownOptions, setDropDownOptions] = useState([]);
  const [hasLoadedInitialValues, setHasLoadedInitialValues] = useState(false);
  const getSecondFilterValue = () =>
    ifElse(
      equals(0),
      () => pathOr(null, [columnIndex, "firstFilterBlock", "filter"], filters),
      () =>
        pathOr(
          null,
          [
            columnIndex,
            "appliedFilters",
            appliedFilterIndex - 1,
            "filterSelect"
          ],
          filters
        )
    )(appliedFilterIndex);
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );

  const { toast } = useToast();

  const getPrimarySiteIfSingle = useMemo(
    () =>
      pipe(
        find(propEq("id", "primarySite")),
        defaultTo({}),
        applySpec({
          firstFilterBlock: pipe(
            path(["firstFilterBlock"]),
            ifElse(
              both(
                pathEq(["operator", "value"], "equals"),
                pipe(path(["filter", "value"]), isNotNullOrEmpty)
              ),
              path(["filter", "value"]),
              always(null)
            ),
            wrapInArray
          ),
          appliedFilters: pipe(
            pathOr([], ["appliedFilters"]),
            map(
              ifElse(
                both(
                  pathEq(["operatorSelect", "value"], "equals"),
                  pipe(path(["filterSelect", "value"]), isNotNullOrEmpty)
                ),
                identity,
                always(null)
              )
            )
          )
        }),
        validateAndExtractSingle
      )(filters),
    [filters]
  );

  const getDateOfDiagnosisIfSingle = useMemo(
    () =>
      pipe(
        find(propEq("id", "dateOfDiagnosis")),
        defaultTo({}),
        applySpec({
          firstFilterBlock: pipe(
            path(["firstFilterBlock"]),
            ifElse(
              pathEq(["operator", "value"], "equals"),
              pipe(
                path(["filter", "value"]),
                when(isNullOrEmpty, always(null))
              ),
              pipe(
                path(["filter", "value"]),
                ifElse(
                  pipe(
                    props(["firstValue", "secondValue"]),
                    all(complement(isNullOrEmpty))
                  ),
                  ifElse(areDatesInSameYear, prop("firstValue"), always(null)),
                  always(null)
                )
              )
            ),
            wrapInArray
          ),
          appliedFilters: pipe(
            propOr([], ["appliedFilters"]),
            map(
              pipe(
                ifElse(
                  pathEq(["operatorSelect", "value"], "equals"),
                  pipe(
                    path(["filterSelect", "value"]),
                    ifElse(complement(isNullOrEmpty), T, always(null))
                  ),
                  pipe(
                    path(["filterSelect", "value"]),
                    ifElse(
                      pipe(
                        props(["firstValue", "secondValue"]),
                        all(complement(isNullOrEmpty))
                      ),
                      ifElse(areDatesInSameYear, prop("firstValue"), F),
                      always(null)
                    )
                  )
                )
              )
            )
          )
        }),
        validateAndExtractSingle
      )(filters),
    [filters]
  );

  const getHistoricalSiteValueIfSingle = useMemo(
    () =>
      pipe(
        find(propEq("id", "morphTypebehavIcdO3")),
        defaultTo({}),
        applySpec({
          firstFilterBlock: pipe(
            path(["firstFilterBlock", "filter", "value"]),
            wrapInArray
          ),
          appliedFilters: pipe(
            propOr([], ["appliedFilters"]),
            map(path(["filterSelect", "value"]))
          )
        }),
        validateAndExtractSingle
      )(filters),
    [filters]
  );

  const secondFilterValue =
    appliedFilterIndex === 0
      ? pathOr(null, [columnIndex, "firstFilterBlock", "filter"], filters)
      : pathOr(
          null,
          [
            columnIndex,
            "appliedFilters",
            appliedFilterIndex - 1,
            "filterSelect"
          ],
          filters
        );

  const [dependentData, setDependentData] = useState([]);

  const missingRequiredDeesFilters = useSelector(state =>
    ReportSelectors.getMissingDeesFilters(state)
  );

  useEffect(() => {
    if (!missingRequiredDeesFilters) {
      setDependentData([
        {
          questionEid: "primarySite",
          answerData: getPrimarySiteIfSingle
        },
        {
          questionEid: "dateOfDiagnosis",
          answerData: getDateOfDiagnosisIfSingle
        },
        {
          questionEid: "morphTypebehavIcdO3",
          answerData: getHistoricalSiteValueIfSingle
        }
      ]);
    }
  }, [
    missingRequiredDeesFilters,
    getPrimarySiteIfSingle,
    getDateOfDiagnosisIfSingle,
    getHistoricalSiteValueIfSingle
  ]);

  const { loading } = useQuery(GET_DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES, {
    onCompleted: data => {
      const { dependentExternalEnumerables } = data;
      const [enumerableQuestion] = dependentExternalEnumerables;

      const convertedDropdownOptions = pipe(
        defaultTo([]),
        convertEnumerableOptions
      )(enumerableQuestion?.enumerableOptions);

      setDropDownOptions(convertedDropdownOptions);
    },
    skip: any(equals(true), [
      isNullOrEmpty(selectedRegistry),
      isEmpty(dependentData)
    ]),
    variables: {
      dependentData,
      questionEid: eid,
      registryName: selectedRegistry
    }
  });

  // eslint-disable-next-line complexity
  useEffect(() => {
    if (hasLoadedInitialValues) return;

    if (!dropDownOptions?.length) return;

    const loadedValues = getSecondFilterValue();

    if (!loadedValues) return;

    const findOptionByValue = enumerableValue =>
      enumerableValue
        ? find(propEq("value", enumerableValue), dropDownOptions)
        : null;

    ifElse(
      equals(0),
      () =>
        dispatch(
          setSecondValueChange({
            appliedFilterIndex,
            columnIndex,
            value: findOptionByValue(loadedValues?.value)
          })
        ),
      () =>
        dispatch(
          setFilterValueChange({
            appliedFilterIndex: appliedFilterIndex - 1,
            columnIndex,
            value: findOptionByValue(loadedValues?.value)
          })
        )
    )(appliedFilterIndex);

    setHasLoadedInitialValues(true);
  }, [appliedFilterIndex, dropDownOptions, hasLoadedInitialValues]);
  useEffect(() => {
    if (filter?.appliedFilters.length === appliedFilterIndex) {
      dispatch(
        addNewFilterBlock({
          appliedFilterIndex,
          columnIndex
        })
      );
    }
  }, [filter, appliedFilterIndex, columnIndex]);

  const handleSecondFilterValueChange = value => {
    if (missingRequiredDeesFilters) {
      toast({
        description:
          "To filter on this field, you must have exactly one value selected for Primary Site and Histology/Behavior. All Dates of Diagnosis must be in the same calendar year.",
        title: "Invalid inputs",
        variant: "error"
      });
      return;
    }

    if (appliedFilterIndex === 0) {
      dispatch(
        setSecondValueChange({
          appliedFilterIndex,
          columnIndex,
          value
        })
      );
    } else {
      dispatch(
        setFilterValueChange({
          appliedFilterIndex: appliedFilterIndex - 1,
          columnIndex,
          value
        })
      );
    }
    dispatch(
      addNewFilterBlock({
        appliedFilterIndex,
        columnIndex
      })
    );
  };
  const containerClassNames = customContainerClassNames
    ? customContainerClassNames
    : "tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5";

  return {
    containerClassNames,
    handleSecondFilterValueChange,
    loading,
    options: dropDownOptions,
    secondFilterValue,
    missingRequiredDeesFilters
  };
};
