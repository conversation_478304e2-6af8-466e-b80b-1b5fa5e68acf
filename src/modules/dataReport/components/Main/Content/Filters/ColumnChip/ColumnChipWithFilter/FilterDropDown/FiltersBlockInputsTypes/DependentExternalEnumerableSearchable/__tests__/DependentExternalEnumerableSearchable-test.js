import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import DependentExternalEnumerableSearchable from "..";

jest.mock("../EnumsDropDownRange", () => "EnumsDropDownRange");
jest.mock(
  "../EnumsDropDownEqualsOrIncludes",
  () => "EnumsDropDownEqualsOrIncludes"
);

const filterMock = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "or",
      value: "or"
    }
  ],
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "C100",
      value: "C100"
    },
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "dependentExternalEnumerableSearchable",
  label: "DependentExternalEnumerableSearchable label",
  type: "DependentExternalEnumerableSearchable",
  value: "dependentExternalEnumerableSearchable value",
  selectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    }
  ]
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: DependentExternalEnumerableSearchable,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: {
          selectedRegistry: "Oncology",
          filters: [
            {
              id: "primarySite",
              firstFilterBlock: {
                operator: { value: "equals" },
                filter: { value: "C160" }
              },
              appliedFilters: []
            },
            {
              id: "dateOfDiagnosis",
              firstFilterBlock: {
                operator: { value: "equals" },
                filter: { value: "2021-01-14" }
              },
              appliedFilters: []
            },
            {
              id: "morphTypebehavIcdO3",
              firstFilterBlock: {
                filter: { value: "8000/3" }
              },
              appliedFilters: []
            }
          ]
        }
      },
      props
    })
  );

describe("DependentExternalEnumerableSearchable", () => {
  test("it renders equals operator component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock,
      operatorValue: {
        label: "equals",
        value: "equals"
      }
    });

    expect(component).toMatchSnapshot();
  });
});
