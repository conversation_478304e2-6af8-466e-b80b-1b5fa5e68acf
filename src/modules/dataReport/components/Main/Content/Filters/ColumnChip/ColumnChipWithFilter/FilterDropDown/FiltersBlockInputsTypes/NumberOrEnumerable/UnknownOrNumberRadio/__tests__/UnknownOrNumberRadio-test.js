import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import UnknownOrNumberRadio from "..";
import { assocPath, mergeDeepRight } from "ramda";

jest.mock("../hooks", () => ({
  // eslint-disable-next-line complexity
  useComponentLogic: ({ filter }) => {
    const filterValue = filter.firstFilterBlock.filter.value;
    let selectedFilterType = "";

    if (filterValue === "unknown") {
      selectedFilterType = "unknown";
    } else if (filterValue) {
      selectedFilterType = "number";
    }

    return {
      selectedFilterType,
      ...(filterValue !== "unknown" && {
        secondFilterValue: filterValue
      })
    };
  }
}));

jest.mock("@q-centrix/q-components-react", () => ({
  Input: "Input",
  RadioButton: "RadioButton"
}));

const mockUnknownFilter = {
  appliedFilters: [
    {
      comparatorSelect: { value: "" },
      filterSelect: { value: "unknown" },
      operatorSelect: { value: "" }
    }
  ],
  comparatorSelectOptions: [{ label: "AND", value: "AND" }],
  filterTypes: [{ label: "equals", value: "equals" }],
  firstFilterBlock: {
    filter: { value: "unknown" },
    operator: { label: "equals", value: "equals" }
  },
  id: "numberOrEnumerable",
  label: "Number or Enumerable",
  type: "NumberOrEnumerable",
  value: "numberOrEnumerable"
};

const mockNumberFilter = {
  appliedFilters: [
    {
      comparatorSelect: { value: "" },
      filterSelect: { value: "123" },
      operatorSelect: { value: "" }
    }
  ],
  comparatorSelectOptions: [{ label: "OR", value: "OR" }],
  filterTypes: [{ label: "equals", value: "equals" }],
  firstFilterBlock: {
    filter: { value: "123" },
    operator: { label: "equals", value: "equals" }
  },
  id: "numberOrEnumerable",
  label: "Number or Enumerable",
  type: "NumberOrEnumerable",
  value: "numberOrEnumerable"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: UnknownOrNumberRadio,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("UnknownOrNumberRadio", () => {
  test("it renders correctly with 'unknown' selected", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockUnknownFilter
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders correctly with 'number' selected", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockNumberFilter
    });

    expect(component).toMatchSnapshot();
  });

  test("it handles empty filter value correctly", () => {
    const emptyMockNumberFilter = mergeDeepRight(
      mockNumberFilter,
      assocPath(["firstFilterBlock", "filter"], { value: "" }, mockNumberFilter)
    );

    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: emptyMockNumberFilter
    });

    expect(component).toMatchSnapshot();
  });
});
