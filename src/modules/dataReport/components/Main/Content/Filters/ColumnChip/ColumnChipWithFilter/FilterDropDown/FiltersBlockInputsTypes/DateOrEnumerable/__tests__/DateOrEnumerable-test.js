import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import DateOrEnumerable from "..";

jest.mock("../../shared/DateRange", () => "DateRange");
jest.mock("../UnknownOrDateRadio", () => "UnknownOrDateRadio");

const mockEqualsFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: "2025-04-29T00:00:00.000Z"
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      value: "2025-04-29T00:00:00.000Z"
    },
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "DateOrEnumerable",
  label: "Date Or Enumerable",
  type: "DateOrEnumerable",
  value: "DateOrEnumerable"
};

const mockBetweenFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: {
          firstValue: "2025-04-29T00:00:00.000Z",
          secondValue: "2025-05-02T00:00:00.000Z"
        }
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      value: {
        firstValue: "2025-04-29T00:00:00.000Z",
        secondValue: "2025-05-02T00:00:00.000Z"
      }
    },
    operator: {
      label: "between",
      value: "between"
    }
  },
  id: "dateOrEnumerable",
  label: "Date of 1st Contact",
  type: "DateOrEnumerable",
  value: "dateOrEnumerable"
};

const mockUnknownFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: "unknown"
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      value: "unknown"
    },
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "dateOrEnumerable",
  label: "Date of 1st Contact",
  type: "DateOrEnumerable",
  value: "dateOrEnumerable"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: DateOrEnumerable,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("DateOrEnumerable", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockEqualsFilter,
      operatorValue: {
        label: "equals",
        value: "equals"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders between component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockBetweenFilter,
      operatorValue: {
        label: "between",
        value: "between"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders with null when no operators", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockUnknownFilter,
      operatorValue: {
        label: "",
        value: ""
      }
    });

    expect(component).toMatchSnapshot();
  });
});
