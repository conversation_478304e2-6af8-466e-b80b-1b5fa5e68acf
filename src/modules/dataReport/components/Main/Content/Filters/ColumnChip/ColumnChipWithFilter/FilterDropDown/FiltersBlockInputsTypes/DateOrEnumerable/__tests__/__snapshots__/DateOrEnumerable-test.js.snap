// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateOrEnumerable it renders between component correctly 1`] = `
<DateRange
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": Object {
              "firstValue": "2025-04-29T00:00:00.000Z",
              "secondValue": "2025-05-02T00:00:00.000Z",
            },
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "AND",
          "value": "AND",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "between",
          "value": "between",
        },
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "value": Object {
            "firstValue": "2025-04-29T00:00:00.000Z",
            "secondValue": "2025-05-02T00:00:00.000Z",
          },
        },
        "operator": Object {
          "label": "between",
          "value": "between",
        },
      },
      "id": "dateOrEnumerable",
      "label": "Date of 1st Contact",
      "type": "DateOrEnumerable",
      "value": "dateOrEnumerable",
    }
  }
  stacked={true}
/>
`;

exports[`DateOrEnumerable it renders equals component correctly 1`] = `
<UnknownOrDateRadio
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "2025-04-29T00:00:00.000Z",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "OR",
          "value": "OR",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "between",
          "value": "between",
        },
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "value": "2025-04-29T00:00:00.000Z",
        },
        "operator": Object {
          "label": "equals",
          "value": "equals",
        },
      },
      "id": "DateOrEnumerable",
      "label": "Date Or Enumerable",
      "type": "DateOrEnumerable",
      "value": "DateOrEnumerable",
    }
  }
/>
`;

exports[`DateOrEnumerable it renders with null when no operators 1`] = `
<UnknownOrDateRadio
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "unknown",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "OR",
          "value": "OR",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "value": "unknown",
        },
        "operator": Object {
          "label": "equals",
          "value": "equals",
        },
      },
      "id": "dateOrEnumerable",
      "label": "Date of 1st Contact",
      "type": "DateOrEnumerable",
      "value": "dateOrEnumerable",
    }
  }
/>
`;
