import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import EnumerableOrOpen from "..";

jest.mock("react-select/creatable", () => "CreatableSelect");

const filterMock = {
  additionalSelectOptions: [
    {
      label: "unk",
      value: "unk"
    },
    {
      label: "rt",
      value: "rt"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "EQUALS",
      value: "EQUALS"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "unk",
      value: "unk"
    },
    operator: {
      label: "EQUALS",
      value: "EQUALS"
    }
  },
  id: "mrn",
  label: "Medical Record Number",
  mainSelectOptions: {},
  type: "EnumerableOrOpen",
  value: "mrn"
};
const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: EnumerableOrOpen,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: { selectedRegistry: "Oncology" }
      },
      props
    })
  );

describe("EnumsDropDown", () => {
  test("it renders component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock,
      operatorValue: {
        label: "EQUALS",
        value: "EQUALS"
      }
    });

    expect(component).toMatchSnapshot();
  });
});
