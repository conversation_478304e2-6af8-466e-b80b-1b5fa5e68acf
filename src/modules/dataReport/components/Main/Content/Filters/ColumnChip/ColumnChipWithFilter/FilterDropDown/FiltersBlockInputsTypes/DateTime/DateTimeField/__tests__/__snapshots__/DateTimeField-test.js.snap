// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateTimeField it renders equals component correctly 1`] = `
<div
  className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-2 tw-self-center tw-align-middle"
>
  <div>
    <DateInput
      onChange={[MockFunction]}
      value="2025-03-17T07:00:00.000Z"
    />
  </div>
  <div>
    <DatePicker
      customInput={<CustomInput />}
      dateFormat="HH:mm"
      isClearable={true}
      onChange={[MockFunction]}
      placeholderText="Select time"
      popperPlacement="top"
      selected="2025-03-04T19:00:02.189Z"
      showTimeSelect={true}
      showTimeSelectOnly={true}
      timeCaption="Time"
      timeFormat="HH:mm"
      timeIntervals={30}
    />
  </div>
</div>
`;
