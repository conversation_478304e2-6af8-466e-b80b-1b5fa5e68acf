import DateTimeRange from "./DateTimeRange";
import { useComponentLogic } from "./hooks";
import "modules/dataReport/styles/time-entry.scss";
import DateTimeField from "./DateTimeField";

const DateTime = props => {
  const { appliedFilterIndex, columnIndex, filter, operatorValue } = props;
  const { inputValue, handleInputChange } = useComponentLogic(props);

  if (operatorValue.value === "between") {
    return (
      <DateTimeRange
        appliedFilterIndex={appliedFilterIndex}
        columnIndex={columnIndex}
        filter={filter}
        inputValue={inputValue}
        handleInputChange={handleInputChange}
      />
    );
  }

  return (
    <div className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-2 tw-self-center tw-align-middle">
      <DateTimeField
        dateValue={inputValue.firstDate}
        handleDateChange={handleInputChange("firstDate")}
        timeValue={inputValue.firstTime}
        handleTimeChange={handleInputChange("firstTime")}
      />
    </div>
  );
};

export default DateTime;
