import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import DateTime from "..";

jest.mock("react-datepicker", () => "DatePicker");
jest.mock("../DateTimeRange", () => "DateTimeRange");
jest.mock("../DateTimeField", () => "DateTimeField");

const mockEqualsFilter = {
  label: "DateTime",
  type: "DateTime",
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    },
    {
      label: "OR",
      value: "OR"
    }
  ],
  firstFilterBlock: {
    operator: {
      label: "equals",
      value: "equals"
    },
    filter: {
      value: "12/24/2010",
      operator: "and"
    }
  },
  unit: "feet"
};

const mockBetweenFilter = {
  label: "OR Entry Date And Time",
  value: "12/24/2024",
  id: "OREntryDT",
  type: "NumberWithUnit",
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    },
    {
      label: "OR",
      value: "OR"
    }
  ],
  firstFilterBlock: {
    operator: {
      label: "between",
      value: "between"
    },
    filter: {
      value: {
        firstValue: "0",
        secondValue: "60"
      },
      operator: "and"
    }
  },
  unit: "feet"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      component: DateTime,
      props,
      initialAppValues: {
        accountSettings: {
          id: "1",
          fullName: "Russell Reas"
        }
      },
      apolloMocks
    })
  );

describe("DateTime", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      operatorValue: {
        label: "equals",
        value: "equals"
      },
      filter: mockEqualsFilter
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component between correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      operatorValue: {
        label: "between",
        value: "between"
      },
      filter: mockBetweenFilter
    });

    expect(component).toMatchSnapshot();
  });
});
