import { InputDropdown } from "@q-centrix/q-components-react";

export const EnumsDropDownEqualsOrIncludes = ({
  operatorValue,
  containerClassNames,
  loading,
  options,
  filter,
  secondFilterValue,
  handleSecondFilterValueChange
}) => (
  <InputDropdown
    clearable={operatorValue.value !== "includes"}
    clearIndicatorClass="tw-text-gray-600 tw-mr-2.5"
    containerClassName={containerClassNames}
    iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
    indicatorSeparatorStyles={{
      color: "#EAECF6",
      marginRight: "10px"
    }}
    isMulti={operatorValue.value === "includes"}
    isSearchable={false}
    loading={loading}
    menuStyles={{
      position: "relative"
    }}
    options={options}
    placeholder={`Select filter value for ${filter.label}`}
    value={secondFilterValue}
    onChange={handleSecondFilterValueChange}
  />
);

export default EnumsDropDownEqualsOrIncludes;
