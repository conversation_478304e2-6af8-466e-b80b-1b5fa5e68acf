import { find, isEmpty, prop, propEq } from "ramda";
import { useSelector } from "react-redux";
import DataReport from "modules/dataReport/redux/selectors";

export const useComponentLogic = ({ column }) => {
  const filters = useSelector(DataReport.getFilters);
  const currentFilter = find(propEq("id", column.id), filters);

  const onlyFirstFilterBlockSet = isEmpty(
    prop("appliedFilters", currentFilter)
  );

  const hasMoreThanOneFilterBlock = !isEmpty(
    prop("appliedFilters", currentFilter)
  );

  return {
    currentFilter,
    hasMoreThanOneFilterBlock,
    onlyFirstFilterBlockSet
  };
};
