import "modules/dataReport/styles/time-entry.scss";
import DateTimeField from "../DateTimeField";

const DateTimeRange = props => {
  const { inputValue, handleInputChange } = props;

  return (
    <div className="tw-flex tw-flex-col tw-gap-2">
      <DateTimeField
        dateValue={inputValue.firstDate}
        handleDateChange={handleInputChange("firstDate")}
        timeValue={inputValue.firstTime}
        handleTimeChange={handleInputChange("firstTime")}
      />
      <div className="tw-justify-center tw-self-center tw-align-middle">
        <div className="tw-mx-3 tw-flex tw-w-[50px] tw-cursor-default tw-flex-row tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700">
          and
        </div>
      </div>
      <div className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-2 tw-self-center tw-align-middle">
        <DateTimeField
          dateValue={inputValue.secondDate}
          handleDateChange={handleInputChange("secondDate")}
          timeValue={inputValue.secondTime}
          handleTimeChange={handleInputChange("secondTime")}
        />
      </div>
    </div>
  );
};

export default DateTimeRange;
