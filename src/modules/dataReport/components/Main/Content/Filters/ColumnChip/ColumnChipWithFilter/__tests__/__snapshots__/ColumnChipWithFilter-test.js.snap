// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ColumnChipWithFilter it renders null when showDropdown is false 1`] = `
<div
  className="tw-flex tw-flex-col"
>
  <div
    className="tw-text-black-70 tw-border-solid tw-p-2.5 tw-bg-white tw-border-qcSkyblue tw-text-qcSkyblue tw-border tw-rounded-[5px]"
  >
    <div
      className="tw-flex tw-items-center tw-justify-between"
    >
      <div
        className="tw-font-semibold tw-flex tw-items-center tw-text-sm tw-text-qcSkyblue"
      >
        Name--Last
      </div>
      <div
        className="tw-flex tw-items-center"
      >
        <ColumnsChipIcons
          column={
            Object {
              "filterTypes": Array [
                Object {
                  "label": "equals",
                  "value": "equals",
                },
              ],
              "id": "nameLast",
              "label": "Name--Last",
              "type": "Open",
              "value": "nameLast",
            }
          }
          handleFilterClick={[Function]}
          onRemove={[MockFunction]}
          showDropdown={false}
        />
      </div>
    </div>
  </div>
  <FilterDropDown
    column={
      Object {
        "filterTypes": Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ],
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
    columnIndex={0}
    showDropdown={false}
  />
</div>
`;
