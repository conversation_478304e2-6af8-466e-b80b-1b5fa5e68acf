import { useCallback, useState } from "react";
import {
  all,
  always,
  assoc,
  equals,
  identity,
  ifElse,
  path,
  pipe
} from "ramda";
import { useDispatch } from "react-redux";
import {
  addNewFilterBlock,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

const AND_OPERATOR = "and";

export const useComponentLogic = ({
  appliedFilterIndex,
  columnIndex,
  filter
}) => {
  const dispatch = useDispatch();

  const [calculatedFilterValues, setCalculatedFilterValues] = useState(
    pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        path(["firstFilterBlock", "filter", "value"]),
        path([
          "appliedFilters",
          appliedFilterIndex - 1,
          "filterSelect",
          "value"
        ])
      ),
      ifElse(
        isNullOrEmpty,
        always({
          firstValue: "",
          secondValue: ""
        }),
        identity
      )
    )(filter)
  );

  const dispatchFilterChange = useCallback(
    // eslint-disable-next-line complexity
    calculatedInputValues => {
      const actionCreator = equals(appliedFilterIndex, 0)
        ? setSecondValueChange
        : setFilterValueChange;

      const index = equals(appliedFilterIndex, 0)
        ? appliedFilterIndex
        : appliedFilterIndex - 1;

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: {
            operator: AND_OPERATOR,
            value: calculatedInputValues
          }
        })
      );
      const shouldAddNewFilterBlock = all(Boolean, [
        calculatedInputValues.firstValue,
        calculatedInputValues.secondValue
      ]);

      if (shouldAddNewFilterBlock) {
        dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
      }
    },
    [dispatch, columnIndex, appliedFilterIndex]
  );

  const handleCalculatedInputChange = useCallback(
    inputCalculatedName => event => {
      const newInputCalculatedValues = assoc(
        inputCalculatedName,
        event.target.value,
        calculatedFilterValues
      );

      setCalculatedFilterValues(newInputCalculatedValues);
      dispatchFilterChange(newInputCalculatedValues);
    },
    [calculatedFilterValues, dispatchFilterChange]
  );

  return {
    handleCalculatedInputChange,
    calculatedFilterValues
  };
};
