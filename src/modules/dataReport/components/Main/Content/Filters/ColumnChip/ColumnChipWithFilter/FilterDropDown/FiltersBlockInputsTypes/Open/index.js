import { Input } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const Open = ({
  columnIndex,
  appliedFilterIndex,
  filter,
  inputType = "text",
  disabled = false
}) => {
  const { secondFilterValue, handleSecondFilterValueChange } =
    useComponentLogic({
      appliedFilterIndex,
      columnIndex,
      filter
    });

  return (
    <Input
      disabled={disabled}
      type={inputType}
      value={secondFilterValue}
      onChange={handleSecondFilterValueChange}
    />
  );
};

export default Open;
