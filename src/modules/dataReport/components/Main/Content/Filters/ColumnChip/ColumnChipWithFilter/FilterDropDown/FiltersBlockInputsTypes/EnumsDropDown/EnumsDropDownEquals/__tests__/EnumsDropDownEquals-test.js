import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import EnumsDropDownEquals from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

jest.mock("../../EnumsDropDownEquals", () => "EnumsDropDownEquals");

const filterMock = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "or",
      value: "or"
    }
  ],
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "C100",
      value: "C100"
    },
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "birthplaceState",
  label: "Birthplace--State",
  type: "EnumerableSearchable",
  value: "birthplaceState"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: EnumsDropDownEquals,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: { selectedRegistry: "Oncology" }
      },
      props
    })
  );

describe("EnumsDropDownEquals", () => {
  test("it renders equals operator component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock
    });

    expect(component).toMatchSnapshot();
  });
});
