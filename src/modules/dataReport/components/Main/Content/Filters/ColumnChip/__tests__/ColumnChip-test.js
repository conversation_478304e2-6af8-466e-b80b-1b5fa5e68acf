import { create } from "react-test-renderer";
import ColumnChip from "..";

jest.mock("../ColumnChipWithFilter", () => "ColumnChipWithFilter");
jest.mock("../ColumnChipWithoutFilter", () => "ColumnChipWithoutFilter");
jest.mock("framer-motion", () => ({
  motion: {
    div: "motion.div"
  }
}));
const columnMock = {
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const columnMockWithoutFilter = {
  filterTypes: [],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

describe("ColumnChip", () => {
  const mockOnRemove = jest.fn();
  const mockHandleRemoveDependentFields = jest.fn();
  const mockHandleCloseModal = jest.fn();
  const defaultProps = {
    columnIndex: 0,
    handleCloseModal: mockHandleCloseModal,
    handleRemoveDependentFields: mockHandleRemoveDependentFields,
    isModalOpen: false,
    columnToRemove: { id: "test", label: "Test" },
    onRemove: mockOnRemove
  };

  test("renders correctly with filter", () => {
    const renderComponentWithFilter = create(
      <ColumnChip {...defaultProps} column={columnMock} />
    );

    expect(renderComponentWithFilter).toMatchSnapshot();
  });

  test("renders correctly without filter", () => {
    const renderComponentWithoutFilter = create(
      <ColumnChip {...defaultProps} column={columnMockWithoutFilter} />
    );

    expect(renderComponentWithoutFilter).toMatchSnapshot();
  });
});
