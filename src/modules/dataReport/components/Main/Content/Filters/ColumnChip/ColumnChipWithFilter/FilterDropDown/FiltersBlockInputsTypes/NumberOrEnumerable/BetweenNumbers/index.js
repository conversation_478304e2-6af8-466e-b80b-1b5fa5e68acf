import { Input } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const BetweenNumbers = ({ appliedFilterIndex, columnIndex, filter }) => {
  const { numberFilterValues, handleNumberInputChange, error } =
    useComponentLogic({
      appliedFilterIndex,
      columnIndex,
      filter
    });

  return (
    <div className="tw-flex tw-flex-col">
      <Input
        inputClassName="tw-h-[35px] tw-w-full"
        type="number"
        value={numberFilterValues.firstValue}
        onChange={handleNumberInputChange("firstValue")}
        min={0}
        error={Bo<PERSON>an(error)}
      />
      <div className="tw-flex tw-items-center tw-justify-center tw-py-2">
        <div className="tw-flex tw-h-6 tw-w-[50px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-2 tw-py-0.5 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700">
          and
        </div>
      </div>
      <Input
        inputClassName="tw-h-[35px] tw-w-full"
        type="number"
        value={numberFilterValues.secondValue}
        onChange={handleNumberInputChange("secondValue")}
        min={0}
        error={Boolean(error)}
      />
      {error && (
        <div className="tw-mt-1 tw-flex tw-items-center tw-border tw-border-qcDanger-700 tw-bg-qcDanger-50 tw-px-2 tw-py-1 tw-text-sm tw-text-qcDanger-500">
          <i className="fa-sharp fa-solid fa-circle-exclamation tw-text-qcDanger-500" />
          <span className="tw-ml-2 tw-text-qcDanger-700">{error}</span>
        </div>
      )}
    </div>
  );
};

export default BetweenNumbers;
