import { useCallback, useState, useEffect } from "react";
import { equals, ifElse, path, pipe, defaultTo, cond, T, and } from "ramda";
import { isNullOrEmpty, isNotNullOrEmpty } from "utils/fp";
import { useDispatch } from "react-redux";
import {
  setSecondValueChange,
  addNewFilterBlock,
  removeFilterBlock,
  setFilterValueChange
} from "modules/dataReport/redux/slices";

// eslint-disable-next-line max-params
const setFilterValue = (appliedFilterIndex, columnIndex, value, dispatch) =>
  pipe(
    ifElse(
      () => equals(appliedFilterIndex, 0),
      () =>
        dispatch(
          setSecondValueChange({ appliedFilterIndex, columnIndex, value })
        ),
      () =>
        dispatch(
          setFilterValueChange({
            appliedFilterIndex: appliedFilterIndex - 1,
            columnIndex,
            value
          })
        )
    )
  );

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  filter
}) => {
  const dispatch = useDispatch();

  const [selectedFilterType, setSelectedFilterType] = useState("");

  const handleRadioChange = useCallback(
    // eslint-disable-next-line complexity
    filterType => {
      setSelectedFilterType(filterType);
      const enumValue = { value: filterType, label: filterType };

      setFilterValue(appliedFilterIndex, columnIndex, enumValue, dispatch)();

      if (filterType === "number") return;

      dispatch(
        isNullOrEmpty(enumValue.value)
          ? removeFilterBlock({ appliedFilterIndex, columnIndex })
          : addNewFilterBlock({ appliedFilterIndex, columnIndex })
      );
    },
    [appliedFilterIndex, columnIndex, filter, dispatch]
  );

  const [secondFilterValue, setSecondFilterValue] = useState("");

  useEffect(() => {
    const savedFilterValue = pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        path(["firstFilterBlock", "filter", "value"]),
        pipe(
          path([
            "appliedFilters",
            appliedFilterIndex - 1,
            "filterSelect",
            "value"
          ]),
          text => {
            if (filter.appliedFilters.length === appliedFilterIndex) {
              dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
            }
            return text;
          }
        )
      ),
      defaultTo("")
    )(filter);

    setSecondFilterValue(savedFilterValue);

    cond([
      [equals("unknown"), () => setSelectedFilterType("unknown")],
      [
        value => value !== "" && !isNaN(Number(value)),
        () => setSelectedFilterType("number")
      ],
      // eslint-disable-next-line no-empty-function
      [T, () => {}]
    ])(savedFilterValue);
  }, [filter, appliedFilterIndex, columnIndex, dispatch]);

  const handleNumberInputChange = event => {
    if (selectedFilterType === "unknown") return;

    setSecondFilterValue(event.target.value);
    const value = { value: event.target.value };

    setFilterValue(appliedFilterIndex, columnIndex, value, dispatch)();

    dispatch(
      isNullOrEmpty(event.target.value)
        ? removeFilterBlock({ appliedFilterIndex, columnIndex })
        : addNewFilterBlock({ appliedFilterIndex, columnIndex })
    );
  };

  const radioName = `filter-${appliedFilterIndex}-${columnIndex}`;
  const isUnknown = equals("unknown", selectedFilterType);
  const hasNumberValue = isNotNullOrEmpty(secondFilterValue);
  const isNumberChecked = and(
    equals("number", selectedFilterType),
    hasNumberValue
  );

  return {
    handleNumberInputChange,
    secondFilterValue,
    handleRadioChange,
    selectedFilterType,
    setSelectedFilterType,
    radioName,
    isUnknown,
    hasNumberValue,
    isNumberChecked
  };
};
