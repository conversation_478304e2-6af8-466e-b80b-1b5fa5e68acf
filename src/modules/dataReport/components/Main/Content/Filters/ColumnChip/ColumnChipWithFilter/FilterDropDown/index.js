import classNames from "classnames";
import { motion } from "framer-motion";
import FilterBlock from "./FilterBlock";
import FirstFilterBlock from "./FirstFilterBlock";
import { useComponentLogic } from "./hooks";

const variants = {
  open: {
    height: "auto",
    display: "block",
    transition: {
      duration: 0.4,
      bounce: 0
    },
    overflow: "hidden",
    transitionEnd: { overflow: "visible" }
  },
  close: {
    height: 0,
    transition: { duration: 0.4, bounce: 0 },
    overflow: "hidden",
    transitionEnd: { display: "none" }
  }
};

// eslint-disable-next-line complexity
export const FilterDropDown = ({ column, columnIndex, showDropdown }) => {
  const { onlyFirstFilterBlockSet, hasMoreThanOneFilterBlock, currentFilter } =
    useComponentLogic({ column });

  const filterBlockClasses = classNames(
    "tw-rounded-b-[5px] tw-border-b tw-border-x tw-border-t-0 tw-border-qcIris-700 tw-divide-y tw-divide-qcIris-700 tw-text-sm",
    {
      "tw-bg-qcIris-100": onlyFirstFilterBlockSet,
      "tw-bg-qcIris-50": hasMoreThanOneFilterBlock
    }
  );

  if (!currentFilter) return null;

  return (
    <motion.div
      key="filter-dropdown"
      className={filterBlockClasses}
      animate={showDropdown ? "open" : "close"}
      initial="close"
      variants={variants}
    >
      <FirstFilterBlock
        column={column}
        columnIndex={columnIndex}
        filter={currentFilter}
      />
      {hasMoreThanOneFilterBlock &&
        currentFilter?.appliedFilters?.map(
          (appliedFilter, appliedFilterIndex) => (
            <FilterBlock
              key={`${columnIndex}-${appliedFilter.id}`}
              appliedFilter={appliedFilter}
              appliedFilterIndex={appliedFilterIndex}
              column={column}
              columnIndex={columnIndex}
              filter={currentFilter}
            />
          )
        )}
    </motion.div>
  );
};

export default FilterDropDown;
