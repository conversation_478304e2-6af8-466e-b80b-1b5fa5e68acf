import { create } from "react-test-renderer";
import { decoratedApollo } from "utils/test/decorated";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import DateTimeRange from "..";

jest.mock("../../DateTimeField", () => "DateTimeField");

const mockedProps = {
  inputValue: {
    firstDate: "2025-03-17T07:00:00.000Z",
    firstTime: "2025-03-04T19:00:02.189Z",
    secondDate: "2025-03-24T07:00:00.000Z",
    secondTime: "2025-03-04T19:30:05.228Z"
  },
  handleInputChange: jest.fn()
};

const mockedComponent = (props = mockedProps) =>
  create(
    decoratedApollo({
      component: DateTimeRange,
      props,
      initialAppValues: {
        accountSettings: {
          id: "1",
          fullName: "Russell Reas"
        }
      },
      apolloMocks
    })
  );

describe("DateTimeRange", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
});
