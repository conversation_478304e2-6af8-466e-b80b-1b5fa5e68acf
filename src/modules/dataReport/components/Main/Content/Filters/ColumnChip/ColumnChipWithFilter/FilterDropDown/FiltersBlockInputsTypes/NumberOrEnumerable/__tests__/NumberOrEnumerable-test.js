import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import NumberOrEnumerable from "..";

jest.mock("../BetweenNumbers", () => "BetweenNumbers");

jest.mock("../UnknownOrNumberRadio", () => "UnknownOrNumberRadio");

const mockEqualsFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      value: "888"
    },
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "accessionYear",
  label: "Accession Year",
  type: "NumberOrEnumerable",
  value: "accessionYear"
};

const mockBetweenFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      operator: "and",
      value: {
        firstValue: "1",
        secondValue: "2"
      }
    },
    operator: {
      label: "between",
      value: "between"
    }
  },
  id: "numberOrEnumerable",
  label: "Number or Enumerable",
  type: "NumberOrEnumerable",
  value: "numberOrEnumerable"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: NumberOrEnumerable,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("NumberOrEnumerable", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockEqualsFilter,
      operatorValue: {
        label: "equals",
        value: "equals"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("renders correctly when operator is 'between'", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockBetweenFilter,
      operatorValue: {
        label: "between",
        value: "between"
      }
    });

    expect(component).toMatchSnapshot();
  });
});
