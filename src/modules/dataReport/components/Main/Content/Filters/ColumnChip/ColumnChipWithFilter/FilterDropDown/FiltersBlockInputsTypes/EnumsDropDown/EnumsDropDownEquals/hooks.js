import { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import {
  applySpec,
  pathOr,
  prop,
  map,
  pipe,
  defaultTo,
  find,
  propEq,
  ifElse,
  equals
} from "ramda";
import { useDispatch, useSelector } from "react-redux";
import { GET_QUESTION_ENUMERABLES } from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  setSecondValueChange,
  addNewFilterBlock,
  setFilterValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";
import { useMapStoredFiltersToOptions } from "../shared/useMapStoredFiltersToOptions";

const convertEnumerableOptions = map(
  applySpec({
    label: prop("description"),
    value: prop("externalValue")
  })
);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  appliedFilterIndex,
  columnIndex,
  customContainerClassNames,
  eid,
  filter
}) => {
  const dispatch = useDispatch();
  const [dropDownOptions, setDropDownOptions] = useState([]);
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );
  const filters = useSelector(state => ReportSelectors.getFilters(state));

  const getFilterValue = () =>
    ifElse(
      equals(0),
      () => pathOr(null, [columnIndex, "firstFilterBlock", "filter"], filters),
      () =>
        pathOr(
          null,
          [
            columnIndex,
            "appliedFilters",
            appliedFilterIndex - 1,
            "filterSelect"
          ],
          filters
        )
    )(appliedFilterIndex);

  const secondFilterValue = getFilterValue();
  const findOptionsByValue = (loadedValues, options) => {
    const enumerableValue = loadedValues?.value;

    return enumerableValue
      ? find(propEq("value", enumerableValue), options)
      : null;
  };

  useMapStoredFiltersToOptions({
    appliedFilterIndex,
    columnIndex,
    dropDownOptions,
    findOptionsByValue,
    getFilterValue
  });

  const { loading } = useQuery(GET_QUESTION_ENUMERABLES, {
    onCompleted: data => {
      const { questionEnumerables } = data;
      const [enumerableQuestion] = questionEnumerables;

      const convertedDropdownOptions = pipe(
        defaultTo([]),
        convertEnumerableOptions
      )(enumerableQuestion.enumerableOptions);

      setDropDownOptions(convertedDropdownOptions);
    },
    skip: isNullOrEmpty(selectedRegistry),
    variables: {
      questionEid: eid,
      registryName: selectedRegistry
    }
  });

  useEffect(() => {
    if (filter?.appliedFilters.length === appliedFilterIndex) {
      dispatch(
        addNewFilterBlock({
          appliedFilterIndex,
          columnIndex
        })
      );
    }
  }, [filter, appliedFilterIndex, columnIndex]);

  const handleSecondFilterValueChange = value => {
    if (appliedFilterIndex === 0) {
      dispatch(
        setSecondValueChange({
          appliedFilterIndex,
          columnIndex,
          value
        })
      );
    } else {
      dispatch(
        setFilterValueChange({
          appliedFilterIndex: appliedFilterIndex - 1,
          columnIndex,
          value
        })
      );
    }
    dispatch(
      addNewFilterBlock({
        appliedFilterIndex,
        columnIndex
      })
    );
  };
  const containerClassNames = customContainerClassNames
    ? customContainerClassNames
    : "tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5";

  return {
    containerClassNames,
    handleSecondFilterValueChange,
    loading,
    options: dropDownOptions,
    secondFilterValue
  };
};
