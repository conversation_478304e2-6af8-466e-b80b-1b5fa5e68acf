// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DependentExternalEnumerableSearchable it renders equals operator component correctly 1`] = `
<EnumsDropDownEqualsOrIncludes
  containerClassNames="tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5"
  filter={
    Object {
      "additionalSelectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
        Object {
          "label": "C300",
          "value": "C300",
        },
      ],
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "or",
          "value": "or",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "label": "C100",
          "value": "C100",
        },
        "operator": Object {
          "label": "equals",
          "value": "equals",
        },
      },
      "id": "dependentExternalEnumerableSearchable",
      "label": "DependentExternalEnumerableSearchable label",
      "selectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
      ],
      "type": "DependentExternalEnumerableSearchable",
      "value": "dependentExternalEnumerableSearchable value",
    }
  }
  handleSecondFilterValueChange={[Function]}
  operatorValue={
    Object {
      "label": "equals",
      "value": "equals",
    }
  }
  options={Array []}
  secondFilterValue={
    Object {
      "value": "C160",
    }
  }
/>
`;
