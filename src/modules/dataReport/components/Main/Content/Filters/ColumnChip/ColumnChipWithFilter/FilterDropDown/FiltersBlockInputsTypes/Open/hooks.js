import { useEffect, useState } from "react";
import { pipe, ifElse, equals, path, defaultTo } from "ramda";
import { useDispatch } from "react-redux";
import {
  setSecondValueChange,
  addNewFilterBlock,
  removeFilterBlock,
  setFilterValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  filter
}) => {
  const dispatch = useDispatch();
  const [secondFilterValue, setSecondFilterValue] = useState("");

  useEffect(() => {
    setSecondFilterValue(
      pipe(
        ifElse(
          () => equals(appliedFilterIndex, 0),
          path(["firstFilterBlock", "filter", "value"]),
          pipe(
            path([
              "appliedFilters",
              appliedFilterIndex - 1,
              "filterSelect",
              "value"
            ]),
            text => {
              if (filter.appliedFilters.length === appliedFilterIndex) {
                dispatch(
                  addNewFilterBlock({ appliedFilterIndex, columnIndex })
                );
              }
              return text;
            }
          )
        ),
        defaultTo("")
      )(filter)
    );
  }, [filter]);

  const handleSecondFilterValueChange = event => {
    setSecondFilterValue(event.target.value);
    const value = { value: event.target.value };

    pipe(
      ifElse(
        () => appliedFilterIndex === 0,
        () =>
          dispatch(
            setSecondValueChange({ appliedFilterIndex, columnIndex, value })
          ),
        () =>
          dispatch(
            setFilterValueChange({
              appliedFilterIndex: appliedFilterIndex - 1,
              columnIndex,
              value
            })
          )
      )
    )();

    dispatch(
      isNullOrEmpty(event.target.value)
        ? removeFilterBlock({ appliedFilterIndex, columnIndex })
        : addNewFilterBlock({ appliedFilterIndex, columnIndex })
    );
  };

  return {
    handleSecondFilterValueChange,
    secondFilterValue
  };
};
