import BetweenNumbers from "./BetweenNumbers";
import UnknownOrNumberRadio from "./UnknownOrNumberRadio";

const NumberOrEnumerable = ({
  appliedFilterIndex,
  columnIndex,
  filter,
  operatorValue
}) => {
  if (operatorValue.value === "equals") {
    return (
      <UnknownOrNumberRadio
        appliedFilterIndex={appliedFilterIndex}
        columnIndex={columnIndex}
        filter={filter}
      />
    );
  }

  return (
    <BetweenNumbers
      appliedFilterIndex={appliedFilterIndex}
      columnIndex={columnIndex}
      filter={filter}
    />
  );
};

export default NumberOrEnumerable;
