import { create } from "react-test-renderer";
import RemoveFilterModal from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal"
}));

const mockedProps = {
  columnToRemove: { id: "primarySite", label: "Primary Site" },
  handleCloseModal: () => jest.fn(),
  handleConfirm: () => jest.fn(),
  isModalOpen: true
};

const render = props => create(<RemoveFilterModal {...props} />);

describe("RemoveFilterModal", () => {
  test("it renders component correctly", () => {
    const component = render(mockedProps);

    expect(component).toMatchSnapshot();
  });
});
