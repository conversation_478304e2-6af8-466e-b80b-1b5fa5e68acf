import { useCallback, useState } from "react";
import { useQuery } from "@apollo/client";
import {
  applySpec,
  prop,
  map,
  pipe,
  defaultTo,
  all,
  always,
  assoc,
  equals,
  identity,
  ifElse,
  path,
  complement,
  pathOr
} from "ramda";
import { useDispatch, useSelector } from "react-redux";
import { GET_QUESTION_ENUMERABLES } from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  addNewFilterBlock,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

const convertEnumerableOptions = map(
  applySpec({
    label: prop("description"),
    value: prop("externalValue")
  })
);

const AND_OPERATOR = "and";

export const useComponentLogic = ({
  appliedFilterIndex,
  columnIndex,
  filter
}) => {
  const dispatch = useDispatch();
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );
  const [enumFilterValues, setEnumFilterValues] = useState(
    pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        path(["firstFilterBlock", "filter", "value"]),
        path([
          "appliedFilters",
          appliedFilterIndex - 1,
          "filterSelect",
          "value"
        ])
      ),
      ifElse(
        isNullOrEmpty,
        always({
          firstValue: null,
          secondValue: null
        }),
        identity
      )
    )(filter)
  );
  const [options, setOptions] = useState([]);

  const { loading } = useQuery(GET_QUESTION_ENUMERABLES, {
    onCompleted: data => {
      const { questionEnumerables } = data;
      const [enumerableQuestion] = questionEnumerables || [];

      const convertedDropdownOptions = pipe(
        defaultTo([]),
        convertEnumerableOptions
      )(enumerableQuestion?.enumerableOptions);

      setOptions(convertedDropdownOptions);
    },
    skip: isNullOrEmpty(selectedRegistry),
    variables: {
      questionEid: filter.id,
      registryName: selectedRegistry
    }
  });

  const dispatchFilterChange = useCallback(
    // eslint-disable-next-line complexity
    enumInputValues => {
      const actionCreator = equals(appliedFilterIndex, 0)
        ? setSecondValueChange
        : setFilterValueChange;

      const index = equals(appliedFilterIndex, 0)
        ? appliedFilterIndex
        : appliedFilterIndex - 1;

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: {
            operator: AND_OPERATOR,
            value: pipe(
              applySpec({
                firstValue: path(["firstValue", "value"]),
                secondValue: path(["secondValue", "value"])
              })
            )(enumInputValues)
          }
        })
      );

      const shouldAddNewFilterBlock = all(Boolean, [
        enumInputValues.firstValue,
        enumInputValues.secondValue
      ]);

      if (shouldAddNewFilterBlock) {
        dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
      }
    },
    [dispatch, columnIndex, appliedFilterIndex]
  );

  const handleEnumInputChange = useCallback(
    inputEnumName => value => {
      const newInputEnumValues = assoc(inputEnumName, value, enumFilterValues);

      setEnumFilterValues(newInputEnumValues);
      dispatchFilterChange(newInputEnumValues);
    },
    [enumFilterValues, dispatchFilterChange]
  );

  const isFirstValueSet = pipe(
    pathOr(null, ["firstValue"]),
    complement(isNullOrEmpty)
  );

  const isStartValueSet = isFirstValueSet(enumFilterValues);

  return {
    enumFilterValues,
    handleEnumInputChange,
    isFirstValueSet,
    isStartValueSet,
    loading,
    options
  };
};
