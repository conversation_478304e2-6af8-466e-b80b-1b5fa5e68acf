import { create } from "react-test-renderer";
import { decoratedA<PERSON>lo } from "utils/test/decorated";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import DateTimeField from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  DateInput: "DateInput"
}));

jest.mock("react-datepicker", () => "DatePicker");

const mockedProps = {
  dateValue: "2025-03-17T07:00:00.000Z",
  handleDateChange: jest.fn(),
  timeValue: "2025-03-04T19:00:02.189Z",
  handleTimeChange: jest.fn()
};

const mockedComponent = (props = mockedProps) =>
  create(
    decoratedApollo({
      component: DateTimeField,
      props,
      initialAppValues: {
        accountSettings: {
          id: "1",
          fullName: "Russell Reas"
        }
      },
      apolloMocks
    })
  );

describe("DateTimeField", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
});
