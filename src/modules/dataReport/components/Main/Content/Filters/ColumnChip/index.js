import { isNullOrEmpty } from "utils/fp";
import { motion } from "framer-motion";
import ColumnChipWithFilter from "./ColumnChipWithFilter";
import RemoveFilterModal from "./ColumnChipWithFilter/FilterDropDown/RemoveFilterModal";
import ColumnChipWithoutFilter from "./ColumnChipWithoutFilter";

const variants = {
  open: {
    opacity: 1
  },
  closed: {
    opacity: 0
  }
};

export const ColumnChip = ({
  column,
  columnIndex,
  columnToRemove,
  handleCloseModal,
  handleRemoveDependentFields,
  isModalOpen,
  onRemove
}) => {
  const hasFilter = !isNullOrEmpty(column.filterTypes);

  if (hasFilter) {
    return (
      <motion.div layout="position" variants={variants}>
        <ColumnChipWithFilter
          column={column}
          columnIndex={columnIndex}
          hasFilter={hasFilter}
          onRemove={onRemove}
        />
        <RemoveFilterModal
          columnToRemove={columnToRemove}
          handleCloseModal={handleCloseModal}
          handleConfirm={() => {
            handleRemoveDependentFields(columnToRemove.id);
          }}
          isModalOpen={isModalOpen}
        />
      </motion.div>
    );
  }

  return (
    <motion.div layout="position" variants={variants}>
      <ColumnChipWithoutFilter column={column} onRemove={onRemove} />
    </motion.div>
  );
};

export default ColumnChip;
