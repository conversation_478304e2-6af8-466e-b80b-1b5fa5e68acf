import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import EnumsDropDownIncludes from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

jest.mock("../../EnumsDropDownIncludes", () => "EnumsDropDownIncludes");

const filterMock = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "or",
      value: "or"
    }
  ],
  filterTypes: [
    {
      label: "includes",
      value: "includes"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "C100",
      value: "C100"
    },
    operator: {
      label: "includes",
      value: "includes"
    }
  },
  id: "birthplaceState",
  label: "Birthplace--State",
  type: "EnumerableSearchable",
  value: "birthplaceState"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: EnumsDropDownIncludes,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: { selectedRegistry: "Oncology" }
      },
      props
    })
  );

describe("EnumsDropDownIncludes", () => {
  test("it renders includes operator component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock
    });

    expect(component).toMatchSnapshot();
  });
});
