import { create, act } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import { ColumnsChipIcons } from "..";

const mockUseComponentLogic = jest.fn(() => ({ shouldShowWarningIcon: false }));

jest.mock("../hooks", () => ({
  useComponentLogic: props => mockUseComponentLogic(props)
}));

jest.mock("@q-centrix/q-components-react", () => ({
  CustomTooltip: "CustomTooltip"
}));

const columnMock = {
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const defaultProps = {
  column: columnMock,
  handleFilterClick: jest.fn(),
  onRemove: jest.fn(),
  showDropdown: false
};

const mockedComponent = (customProps = {}) =>
  create(
    decoratedApollo({
      apolloMocks,
      component: ColumnsChipIcons,
      initialAppValues: {
        dataReport: {
          filters: [
            {
              additionalSelectOptions: [
                {
                  label: "C100",
                  value: "C100"
                },
                {
                  label: "C200",
                  value: "C200"
                },
                {
                  label: "C300",
                  value: "C300"
                }
              ],
              appliedFilters: [],
              comparatorSelectOptions: [
                {
                  label: "or",
                  value: "or"
                }
              ],
              filterTypes: [
                {
                  label: "equals",
                  value: "equals"
                }
              ],
              firstFilterBlock: {
                operator: {
                  label: "equals",
                  value: "equals"
                }
              },
              id: "nameLast",
              label: "Name--Last",
              type: "Open",
              value: "nameLast"
            }
          ],
          isMissingDeesFilters: true,
          isMissingDfesFilters: true
        }
      },
      props: { ...defaultProps, ...customProps }
    })
  );

describe("ColumnsChipIcons", () => {
  beforeEach(() => {
    mockUseComponentLogic.mockReturnValue({ shouldShowWarningIcon: false });
  });

  test("renders chevron-down and xmark icons when showDropdown is false", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });

  test("renders chevron-up and xmark icons when showDropdown is true", () => {
    const component = mockedComponent({ showDropdown: true });

    expect(component).toMatchSnapshot();
  });

  test("renders warning icon when showDropdown is true", () => {
    mockUseComponentLogic.mockReturnValue({ shouldShowWarningIcon: true });
    const component = mockedComponent({ showDropdown: true });

    expect(component).toMatchSnapshot();
  });

  test("calls handleFilterClick when chevron icon is clicked", () => {
    const handleFilterClick = jest.fn();

    mockUseComponentLogic.mockReturnValue({
      shouldShowWarningIcon: false,
      handleIconClick: handleFilterClick
    });
    const component = mockedComponent({ handleFilterClick });
    const customTooltip = component.root.findByType("CustomTooltip");
    const chevronIcon = customTooltip.findByType("i");

    act(() => chevronIcon.props.onClick());
    expect(handleFilterClick).toHaveBeenCalled();
  });

  test("calls onRemove with column.id when xmark icon is clicked", () => {
    const onRemove = jest.fn();
    const component = mockedComponent({ onRemove });
    const xmarkIcon = component.root
      .findAllByType("i")
      .find(icon => icon.props.className.includes("fa-xmark"));

    act(() => xmarkIcon.props.onClick());
    expect(onRemove).toHaveBeenCalledWith("nameLast");
  });

  test("renders warning icon when shouldShowWarningIcon is true", () => {
    mockUseComponentLogic.mockReturnValue({ shouldShowWarningIcon: true });
    const component = mockedComponent();
    const customTooltip = component.root.findByType("CustomTooltip");

    expect(customTooltip.props.className).toContain("!tw-w-[220px]");
  });

  test("renders chevron icon when shouldShowWarningIcon is false", () => {
    mockUseComponentLogic.mockReturnValue({ shouldShowWarningIcon: false });
    const component = mockedComponent();
    const customTooltip = component.root.findByType("CustomTooltip");

    expect(customTooltip.props.className).toContain("tw-hidden");
  });
});
