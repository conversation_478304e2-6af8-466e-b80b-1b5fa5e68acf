// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ColumnsChipIcons renders chevron-down and xmark icons when showDropdown is false 1`] = `
Array [
  <div
    className="tw-mr-2.5"
  >
    <CustomTooltip
      className="!tw-w-[220px] tw-hidden"
      content="To filter on this field, you must select 1 Primary Site, 1 Histology/Behavior, and have all Dates of Diagnosis in the same year."
      dark="true"
      id="custom-tooltip-data-report"
      place="top"
    >
      <i
        className="fa-solid tw-font-black tw-text-sm fa-chevron-down tw-text-qcSkyblue tw-cursor-pointer"
      />
    </CustomTooltip>
  </div>,
  <i
    className="fa-solid fa-xmark tw-cursor-pointer tw-text-sm tw-text-qcSkyblue"
    onClick={[Function]}
  />,
]
`;

exports[`ColumnsChipIcons renders chevron-up and xmark icons when showDropdown is true 1`] = `
Array [
  <div
    className="tw-mr-2.5"
  >
    <CustomTooltip
      className="!tw-w-[220px] tw-hidden"
      content="To filter on this field, you must select 1 Primary Site, 1 Histology/Behavior, and have all Dates of Diagnosis in the same year."
      dark="true"
      id="custom-tooltip-data-report"
      place="top"
    >
      <i
        className="fa-solid tw-font-black tw-text-sm fa-chevron-up tw-text-qcIris-700 tw-cursor-pointer"
      />
    </CustomTooltip>
  </div>,
  <i
    className="fa-solid fa-xmark tw-cursor-pointer tw-text-sm tw-text-qcIris-700"
    onClick={[Function]}
  />,
]
`;

exports[`ColumnsChipIcons renders warning icon when showDropdown is true 1`] = `
Array [
  <div
    className="tw-mr-2.5"
  >
    <CustomTooltip
      className="!tw-w-[220px] "
      content="To filter on this field, you must select 1 Primary Site, 1 Histology/Behavior, and have all Dates of Diagnosis in the same year."
      dark="true"
      id="custom-tooltip-data-report"
      place="top"
    >
      <i
        className="far fa-question-circle tw-text-qcSkyblue tw-cursor-help  tw-text-sm"
      />
    </CustomTooltip>
  </div>,
  <i
    className="fa-solid fa-xmark tw-cursor-pointer tw-text-sm tw-text-qcIris-700"
    onClick={[Function]}
  />,
]
`;
