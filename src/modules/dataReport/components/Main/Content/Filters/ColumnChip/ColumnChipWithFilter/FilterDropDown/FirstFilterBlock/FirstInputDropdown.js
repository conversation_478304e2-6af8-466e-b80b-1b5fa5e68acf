import { InputDropdown } from "@q-centrix/q-components-react";

export const FirstInputDropdown = ({
  column,
  filter,
  firstOperatorValue,
  handleFirstOperatorChange
}) => (
  <InputDropdown
    clearable
    clearIndicatorClass="tw-text-qcNeutrals-400"
    containerClassName="tw-font-inter"
    iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
    indicatorSeparatorStyles={{
      marginRight: "10px"
    }}
    inputClassName="tw-text-black-70"
    labelClassName="tw-text-sm tw-text-black-70"
    menuStyles={{
      position: "relative"
    }}
    options={column.filterTypes}
    placeholder={`Select filter value for ${filter.label}`}
    value={firstOperatorValue}
    onChange={handleFirstOperatorChange}
  />
);
