// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateRange it renders equals component correctly 1`] = `
<div
  className="tw-flex tw-w-full tw-gap-2"
>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="Select Date"
            readOnly={false}
            type="text"
            value=""
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="Select Date"
            readOnly={false}
            type="text"
            value=""
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;
