import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import { FirstFilterBlock } from "..";

jest.mock("react-select", () => "Select");
jest.mock("../../FiltersBlockInputsTypes/Open", () => "Open");
jest.mock("../../FiltersBlockInputsTypes/Date", () => "Date");
jest.mock("../../FiltersBlockInputsTypes/EnumsDropDown", () => "EnumsDropDown");
jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

const columnMock = {
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const filterMock = {
  filters: [
    {
      additionalSelectOptions: [
        {
          label: "C100",
          value: "C100"
        },
        {
          label: "C200",
          value: "C200"
        },
        {
          label: "C300",
          value: "C300"
        }
      ],
      appliedFilters: [],
      comparatorSelectOptions: [
        {
          label: "or",
          value: "or"
        }
      ],
      filterTypes: [
        {
          label: "equals",
          value: "equals"
        }
      ],
      firstFilterBlock: {
        operator: {
          label: "equals",
          value: "equals"
        }
      },
      id: "nameLast",
      label: "Name--Last",
      mainSelectOptions: {},
      type: "Open",
      value: "nameLast"
    }
  ]
};

const firstFilterBlockValues = {
  column: columnMock,
  columnIndex: 0,
  filter: filterMock
};

const mockedComponent = (props = firstFilterBlockValues) =>
  create(
    decoratedApollo({
      apolloMocks,
      component: FirstFilterBlock,
      initialAppValues: {
        dataReport: {
          filters: [
            {
              additionalSelectOptions: [
                {
                  label: "C100",
                  value: "C100"
                },
                {
                  label: "C200",
                  value: "C200"
                },
                {
                  label: "C300",
                  value: "C300"
                }
              ],
              appliedFilters: [],
              comparatorSelectOptions: [
                {
                  label: "or",
                  value: "or"
                }
              ],
              filterTypes: [
                {
                  label: "equals",
                  value: "equals"
                }
              ],
              firstFilterBlock: {
                operator: {
                  label: "equals",
                  value: "equals"
                }
              },
              id: "nameLast",
              label: "Name--Last",
              mainSelectOptions: {},
              type: "Open",
              value: "nameLast"
            }
          ]
        }
      },
      props
    })
  );

describe("FirstFilterBlock", () => {
  test("it renders correctly with", () => {
    const FirstFilterBlockMock = mockedComponent({
      column: {
        label: "Column 1"
      },
      columnIndex: 0,
      filter: {
        label: "Filter 1"
      }
    });

    expect(FirstFilterBlockMock).toMatchSnapshot();
  });
});
