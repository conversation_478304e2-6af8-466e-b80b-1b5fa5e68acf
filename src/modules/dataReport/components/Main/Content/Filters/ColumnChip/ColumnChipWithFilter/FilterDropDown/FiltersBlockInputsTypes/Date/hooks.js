import { useState, useCallback, useEffect } from "react";
import { format, isValid } from "date-fns";
import { equals, ifElse, identity, always, path, pipe, or } from "ramda";
import { useDispatch } from "react-redux";
import {
  setSecondValueChange,
  setFilterValueChange,
  addNewFilterBlock
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

export const MIN_DATE = "1900-01-01T00:00:00.000Z";

const formatDate = date => (date ? format(date, "MMMM d, yyyy") : null);
const minDate = new Date(MIN_DATE);
const today = new Date();

const handleLoadSavedReportDate = (filter, appliedFilterIndex) =>
  pipe(
    ifElse(
      () => equals(appliedFilterIndex, 0),
      pipe(path(["firstFilterBlock", "filter", "value"])),
      pipe(
        path([
          "appliedFilters",
          appliedFilterIndex - 1,
          "filterSelect",
          "value"
        ])
      )
    ),
    ifElse(
      date => or(isNullOrEmpty(date), !isValid(new Date(date))),
      always(null),
      identity
    )
  )(filter);

export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  filter
}) => {
  const dispatch = useDispatch();
  const [date, setDate] = useState(null);

  useEffect(() => {
    setDate(handleLoadSavedReportDate(filter, appliedFilterIndex));
    dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
  }, [appliedFilterIndex, filter]);

  const handleDateInputChange = useCallback(
    inputDate => {
      setDate(inputDate);
      const formattedDate = formatDate(inputDate);
      const value = { label: formattedDate, value: inputDate };

      const actionCreator =
        appliedFilterIndex === 0 ? setSecondValueChange : setFilterValueChange;

      const index =
        appliedFilterIndex === 0 ? appliedFilterIndex : appliedFilterIndex - 1;

      dispatch(
        actionCreator({ appliedFilterIndex: index, columnIndex, value })
      );

      dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
    },
    [dispatch, columnIndex, appliedFilterIndex]
  );

  return {
    date,
    handleDateInputChange,
    minDate,
    today
  };
};
