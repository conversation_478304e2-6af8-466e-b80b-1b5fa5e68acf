import { DateInput } from "@q-centrix/q-components-react";
import DatePicker from "react-datepicker";
import "modules/dataReport/styles/time-entry.scss";

// eslint-disable-next-line complexity
const CustomInput = ({ value, onClick }) => (
  <div
    className="custom-input tw-bg-white tw-flex tw-h-8 tw-min-h-[35px] tw-w-[160px] tw-cursor-pointer tw-items-center tw-rounded-md tw-border tw-border-qcNeutrals-400 tw-pl-2 tw-pr-4 tw-font-inter tw-text-sm focus-within:tw-border focus-within:tw-border-purple-800"
    onClick={onClick}
    tabIndex={0}
  >
    <span
      className={`tw-flex-1 tw-font-inter tw-text-sm ${
        value ? "tw-text-black" : "tw-text-gray-500"
      }`}
    >
      {value || "Select time"}
    </span>
    {!value && (
      <i className="fa-solid fa-chevron-down tw-pr-1 tw-text-qc-blue-800" />
    )}
  </div>
);

const DateTimeField = props => {
  const { dateValue, handleDateChange, timeValue, handleTimeChange } = props;

  return (
    <div className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-2 tw-self-center tw-align-middle">
      <div>
        <DateInput value={dateValue} onChange={handleDateChange} />
      </div>
      <div>
        <DatePicker
          selected={timeValue}
          onChange={handleTimeChange}
          placeholderText="Select time"
          showTimeSelect
          showTimeSelectOnly
          timeIntervals={30}
          timeFormat="HH:mm"
          timeCaption="Time"
          dateFormat="HH:mm"
          popperPlacement="top"
          customInput={<CustomInput />}
          isClearable
        />
      </div>
    </div>
  );
};

export default DateTimeField;
