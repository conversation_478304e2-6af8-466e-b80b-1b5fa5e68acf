import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  setFirstOperatorChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";

export function useComponentLogic({ columnIndex, column }) {
  const dispatch = useDispatch();

  const [firstOperatorValue, setFirstOperatorValue] = useState(null);

  useEffect(() => {
    setFirstOperatorValue(column?.firstFilterBlock?.operator);
  }, [column]);

  const handleFirstOperatorChange = value => {
    setFirstOperatorValue(value);
    dispatch(setFirstOperatorChange({ columnIndex, value }));
    dispatch(setSecondValueChange({ columnIndex, value: null }));
  };

  return {
    firstOperatorValue,
    handleFirstOperatorChange
  };
}
