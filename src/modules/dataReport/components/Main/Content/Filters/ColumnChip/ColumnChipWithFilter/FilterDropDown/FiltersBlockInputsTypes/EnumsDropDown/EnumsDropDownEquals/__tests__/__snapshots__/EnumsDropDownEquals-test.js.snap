// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumsDropDownEquals it renders equals operator component correctly 1`] = `
<EnumsDropDownEquals
  appliedFilterIndex={0}
  columnIndex={0}
  dispatch={[Function]}
  filter={
    Object {
      "additionalSelectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
        Object {
          "label": "C300",
          "value": "C300",
        },
      ],
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "or",
          "value": "or",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "label": "C100",
          "value": "C100",
        },
        "operator": Object {
          "label": "equals",
          "value": "equals",
        },
      },
      "id": "birthplaceState",
      "label": "Birthplace--State",
      "type": "EnumerableSearchable",
      "value": "birthplaceState",
    }
  }
  name="42|22"
  question={
    Object {
      "id": "42",
    }
  }
/>
`;
