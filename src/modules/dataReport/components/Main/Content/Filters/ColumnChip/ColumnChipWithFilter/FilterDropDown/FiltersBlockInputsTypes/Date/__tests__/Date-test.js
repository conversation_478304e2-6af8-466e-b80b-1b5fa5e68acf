import { act, create } from "react-test-renderer";
import wait from "waait";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedA<PERSON>lo } from "utils/test/decorated";
import Date from "..";

jest.mock("../../shared/DateRange", () => "DateRange");

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: Date,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("Date", () => {
  test("it renders single date component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      operatorValue: {
        value: "equals"
      }
    });

    expect(component).toMatchSnapshot();
  });
  test("it renders date range component correctly", async () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: {
        firstFilterBlock: {
          filter: {
            value: {
              firstValue: "12/01/2024",
              secondValue: "12/31/2024"
            }
          }
        }
      },
      operatorValue: {
        value: "between"
      }
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
