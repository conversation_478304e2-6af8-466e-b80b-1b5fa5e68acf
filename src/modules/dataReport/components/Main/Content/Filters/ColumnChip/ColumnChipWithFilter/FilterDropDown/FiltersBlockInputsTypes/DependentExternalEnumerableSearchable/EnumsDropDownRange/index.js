import { InputDropdown } from "@q-centrix/q-components-react";
import classNames from "classnames";
import { useComponentLogic } from "./hooks";

const EnumsDropDownRange = ({
  columnIndex,
  appliedFilterIndex,
  filter,
  customContainerClassNames
}) => {
  const {
    options,
    loading,
    enumFilterValues,
    handleEnumInputChange,
    isStartValueSet
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    filter
  });

  const containerClassNames = customContainerClassNames
    ? customContainerClassNames
    : "tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5";

  const andDivClassNames = classNames(
    "tw-m-auto tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-px-3 tw-py-1 tw-text-[11px] tw-font-semibold tw-leading-4",
    {
      "tw-bg-qcInfo-100 tw-border-qcInfo-700 tw-text-qcInfo-700 tw-h-[24px] tw-border-2":
        isStartValueSet,
      "tw-bg-qcNeutrals-200 tw-text-qcNeutrals-700 tw-h-[25px] tw-border tw-border-qcNeutrals-400":
        !isStartValueSet
    }
  );

  return (
    <div className="tw-flex tw-h-[35px] tw-flex-row">
      <InputDropdown
        clearable
        clearIndicatorClass="tw-text-gray-600"
        clearIndicatorStyles={{
          paddingLeft: "0px"
        }}
        containerClassName={containerClassNames}
        iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700 tw-pl-2"
        indicatorSeparatorStyles={{
          color: "#EAECF6"
        }}
        isSearchable={false}
        loading={loading}
        menuStyles={{
          fontFamily: "Inter",
          width: "130px"
        }}
        options={options}
        placeholder="Select"
        value={enumFilterValues.firstValue}
        valueContainerStyles={{
          paddingLeft: "10px",
          paddingRight: "0px"
        }}
        onChange={handleEnumInputChange("firstValue")}
      />
      <div className={andDivClassNames}>and</div>
      <InputDropdown
        clearable
        clearIndicatorClass="tw-text-gray-600"
        clearIndicatorStyles={{
          paddingLeft: "0px"
        }}
        containerClassName={containerClassNames}
        iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700 tw-pl-2"
        indicatorSeparatorStyles={{
          color: "#EAECF6"
        }}
        isSearchable={false}
        loading={loading}
        menuStyles={{
          fontFamily: "Inter",
          width: "130px"
        }}
        options={options}
        placeholder="Select"
        value={enumFilterValues.secondValue}
        valueContainerStyles={{
          paddingLeft: "10px",
          paddingRight: "0px"
        }}
        onChange={handleEnumInputChange("secondValue")}
      />
    </div>
  );
};

export default EnumsDropDownRange;
