import { useSelector } from "react-redux";
import DataReport from "modules/dataReport/redux/selectors";
import { propEq, cond, T, always, F } from "ramda";

export const useComponentLogic = ({ column, handleFilterClick }) => {
  const missingDeesFilters = useSelector(DataReport.getMissingDeesFilters);
  const missingDfesFilters = useSelector(DataReport.getMissingDfesFilters);

  const getWarningIconVisibility = cond([
    [
      propEq("type", "DependentExternalEnumerableSearchable"),
      always(missingDeesFilters)
    ],
    [
      propEq("type", "DependentFilteredEnumerableSearchable"),
      always(missingDfesFilters)
    ],
    [T, F]
  ]);
  const handleIconClick = () => {
    if (!getWarningIconVisibility(column)) {
      handleFilterClick();
    }
  };

  return {
    shouldShowWarningIcon: getWarningIconVisibility(column),
    handleIconClick
  };
};
