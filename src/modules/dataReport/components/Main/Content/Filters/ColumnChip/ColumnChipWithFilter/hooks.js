import { useEffect, useState, useCallback } from "react";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = ({ column }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    setShowDropdown(!isNullOrEmpty(column?.firstFilterBlock));
  }, [column]);

  const handleFilterClick = useCallback(() => {
    setShowDropdown(!showDropdown);
  }, [showDropdown]);

  return {
    handleFilterClick,
    showDropdown
  };
};
