import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import { FilterBlock } from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

jest.mock("../../FiltersBlockInputsTypes/Open", () => "Open");

const columnMock = {
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const filterMock = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [],
  comparatorSelectOptions: [
    {
      label: "or",
      value: "or"
    }
  ],
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: FilterBlock,
      initialAppValues: {
        dataReport: {
          filters: [
            {
              additionalSelectOptions: [
                {
                  label: "C100",
                  value: "C100"
                },
                {
                  label: "C200",
                  value: "C200"
                },
                {
                  label: "C300",
                  value: "C300"
                }
              ],
              appliedFilters: [],
              comparatorSelectOptions: [
                {
                  label: "and",
                  value: "and"
                },
                {
                  label: "or",
                  value: "or"
                }
              ],
              filterTypes: [
                {
                  label: "equals",
                  value: "equals"
                }
              ],
              firstFilterBlock: {
                operator: {
                  label: "equals",
                  value: "equals"
                }
              },
              id: "nameLast",
              label: "Name--Last",
              mainSelectOptions: {},
              type: "Open",
              value: "nameLast"
            }
          ]
        }
      },
      props
    })
  );

describe("FilterBlock", () => {
  test("it renders correctly with", () => {
    const FilterBlockMock = mockedComponent({
      appliedFilterIndex: 0,
      column: columnMock,
      columnIndex: 0,
      filter: filterMock
    });

    expect(FilterBlockMock).toMatchSnapshot();
  });
});
