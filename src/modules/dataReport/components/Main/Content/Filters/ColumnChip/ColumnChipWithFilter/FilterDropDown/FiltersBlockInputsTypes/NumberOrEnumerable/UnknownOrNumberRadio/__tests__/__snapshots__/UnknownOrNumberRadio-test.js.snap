// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UnknownOrNumberRadio it handles empty filter value correctly 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <RadioButton
    label="Unknown"
    onChange={[Function]}
    value="unknown"
  />
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-2"
  >
    <RadioButton
      onChange={[Function]}
      value="number"
    />
    <div
      className="tw-flex-1"
    >
      <Input
        type="number"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`UnknownOrNumberRadio it renders correctly with 'number' selected 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <RadioButton
    label="Unknown"
    onChange={[Function]}
    value="unknown"
  />
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-2"
  >
    <RadioButton
      onChange={[Function]}
      value="number"
    />
    <div
      className="tw-flex-1"
    >
      <Input
        type="number"
        value="123"
      />
    </div>
  </div>
</div>
`;

exports[`UnknownOrNumberRadio it renders correctly with 'unknown' selected 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <RadioButton
    label="Unknown"
    onChange={[Function]}
    value="unknown"
  />
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-2"
  >
    <RadioButton
      onChange={[Function]}
      value="number"
    />
    <div
      className="tw-flex-1"
    >
      <Input
        type="number"
      />
    </div>
  </div>
</div>
`;
