import { Input } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import Open from "../Open";

const Integer = ({
  appliedFilterIndex,
  columnIndex,
  filter,
  operatorValue
}) => {
  const { numberFilterValues, handleNumberInputChange } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    filter
  });

  if (operatorValue.value === "equals") {
    return (
      <Open
        appliedFilterIndex={appliedFilterIndex}
        columnIndex={columnIndex}
        filter={filter}
        inputType="number"
      />
    );
  }
  return (
    <div className="tw-mx-auto tw-flex tw-items-center">
      <Input
        inputClassName="tw-w-[74px] tw-h-[35px]"
        type="number"
        value={numberFilterValues.firstValue}
        onChange={handleNumberInputChange("firstValue")}
      />
      <div className="tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700">
        and
      </div>
      <Input
        inputClassName="tw-w-[74px] tw-h-[35px]"
        type="number"
        value={numberFilterValues.secondValue}
        onChange={handleNumberInputChange("secondValue")}
      />
    </div>
  );
};

export default Integer;
