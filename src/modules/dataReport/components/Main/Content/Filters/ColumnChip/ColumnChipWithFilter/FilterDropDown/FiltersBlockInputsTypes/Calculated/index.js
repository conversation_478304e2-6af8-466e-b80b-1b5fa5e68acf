import { Input } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import Open from "../Open";

const Calculated = ({
  appliedFilterIndex,
  columnIndex,
  filter,
  operatorValue
}) => {
  const { calculatedFilterValues, handleCalculatedInputChange } =
    useComponentLogic({
      appliedFilterIndex,
      columnIndex,
      filter
    });

  if (operatorValue.value === "equals") {
    return (
      <Open
        appliedFilterIndex={appliedFilterIndex}
        columnIndex={columnIndex}
        filter={filter}
      />
    );
  }

  return (
    <div className="tw-mx-auto tw-flex tw-items-center">
      <Input
        inputClassName="tw-min-w-[74px] tw-h-[35px]"
        type="number"
        value={calculatedFilterValues.firstValue}
        onChange={handleCalculatedInputChange("firstValue")}
      />
      <div className="tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700">
        and
      </div>
      <Input
        inputClassName="tw-min-w-[74px] tw-h-[35px]"
        type="number"
        value={calculatedFilterValues.secondValue}
        onChange={handleCalculatedInputChange("secondValue")}
      />
    </div>
  );
};

export default Calculated;
