import { useCallback, useState } from "react";
import {
  addNew<PERSON><PERSON><PERSON><PERSON><PERSON>,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import { useDispatch } from "react-redux";
import {
  assoc,
  equals,
  identity,
  ifElse,
  path,
  pipe,
  prop,
  isNil,
  pick
} from "ramda";
import moment from "moment";
import { isNullOrEmpty } from "utils/fp";

// eslint-disable-next-line complexity
const parseDateTime = value => {
  if (!value) return { date: null, time: null };

  const [datePart, timePart] = value.split(" ");

  return {
    date: datePart ? moment.utc(datePart, "MM/DD/YYYY").toDate() : null,
    time: timePart ? moment(timePart, "HH:mm") : null
  };
};

export const useComponentLogic = props => {
  const { appliedFilterIndex, columnIndex, filter, operatorValue } = props;
  const dispatch = useDispatch();

  const formatFilterValue = value => {
    if (typeof value === "object" && value !== null) {
      return {
        firstDate: parseDateTime(value.firstValue).date,
        firstTime: parseDateTime(value.firstValue).time,
        secondDate: parseDateTime(value.secondValue).date,
        secondTime: parseDateTime(value.secondValue).time
      };
    }

    const parsed = parseDateTime(value);

    return {
      firstDate: parsed.date,
      firstTime: parsed.time,
      secondDate: null,
      secondTime: null
    };
  };

  // eslint-disable-next-line complexity
  const getFilterValue = (filterValue, appliedFilterIndexValue) => {
    const isFirstFilter = appliedFilterIndexValue === 0;
    const filterPath = isFirstFilter
      ? path(["firstFilterBlock", "filter", "value"], filterValue)
      : path(
          [
            "appliedFilters",
            appliedFilterIndexValue - 1,
            "filterSelect",
            "value"
          ],
          filterValue
        );

    const operatorPath = isFirstFilter
      ? path(["firstFilterBlock", "operator", "value"], filterValue)
      : path(
          [
            "appliedFilters",
            appliedFilterIndexValue - 1,
            "operatorSelect",
            "value"
          ],
          filterValue
        );

    return operatorPath === "between"
      ? formatFilterValue(filterPath)
      : formatFilterValue(filterPath);
  };

  const [inputValue, setInputValue] = useState(() =>
    isNullOrEmpty(filter)
      ? { firstDate: null, firstTime: null, secondDate: null, secondTime: null }
      : getFilterValue(filter, appliedFilterIndex)
  );

  const handleInputChange = useCallback(
    inputName => value => {
      const updatedInputValue = assoc(inputName, value, inputValue);

      // eslint-disable-next-line complexity
      const formatDateTime = (date, time) => {
        if (!date) return null;

        const formattedDate = moment(date).format("MM/DD/YYYY");
        const formattedTime = time ? moment(time).format("HH:mm") : null;

        return formattedTime
          ? `${formattedDate} ${formattedTime}`
          : formattedDate;
      };

      const formattedValue = {
        value: pipe(
          pick(["firstDate", "firstTime", "secondDate", "secondTime"]),
          obj => ({
            firstValue: formatDateTime(
              prop("firstDate", obj),
              prop("firstTime", obj)
            ),
            secondValue: formatDateTime(
              prop("secondDate", obj),
              prop("secondTime", obj)
            )
          }),
          ifElse(obj => isNil(obj.secondValue), prop("firstValue"), identity)
        )(updatedInputValue)
      };

      const index =
        appliedFilterIndex === 0 ? appliedFilterIndex : appliedFilterIndex - 1;

      const actionCreator = equals(appliedFilterIndex, 0)
        ? setSecondValueChange
        : setFilterValueChange;

      setInputValue(updatedInputValue);

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: formattedValue
        })
      );

      dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
    },
    [inputValue, operatorValue, dispatch, columnIndex, appliedFilterIndex]
  );

  return {
    inputValue,
    handleInputChange
  };
};
