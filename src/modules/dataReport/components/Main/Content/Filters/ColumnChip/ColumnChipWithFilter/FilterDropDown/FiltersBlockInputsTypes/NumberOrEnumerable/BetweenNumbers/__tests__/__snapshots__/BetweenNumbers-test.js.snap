// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BetweenNumbers it renders component correctly 1`] = `
<div
  className="tw-flex tw-flex-col"
>
  <Input
    error={false}
    inputClassName="tw-h-[35px] tw-w-full"
    min={0}
    onChange={[Function]}
    type="number"
    value="1"
  />
  <div
    className="tw-flex tw-items-center tw-justify-center tw-py-2"
  >
    <div
      className="tw-flex tw-h-6 tw-w-[50px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-2 tw-py-0.5 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700"
    >
      and
    </div>
  </div>
  <Input
    error={false}
    inputClassName="tw-h-[35px] tw-w-full"
    min={0}
    onChange={[Function]}
    type="number"
    value="2"
  />
</div>
`;
