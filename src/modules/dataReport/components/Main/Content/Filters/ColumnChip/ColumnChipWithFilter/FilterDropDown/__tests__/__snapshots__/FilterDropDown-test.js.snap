// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FilterDropDown it renders the FilterDropDown component with prop 'animate' as 'hidden' when showDropdown is false 1`] = `
<motion.div
  animate="close"
  className="tw-rounded-b-[5px] tw-border-b tw-border-x tw-border-t-0 tw-border-qcIris-700 tw-divide-y tw-divide-qcIris-700 tw-text-sm tw-bg-qcIris-100"
  initial="close"
  variants={
    Object {
      "close": Object {
        "height": 0,
        "overflow": "hidden",
        "transition": Object {
          "bounce": 0,
          "duration": 0.4,
        },
        "transitionEnd": Object {
          "display": "none",
        },
      },
      "open": Object {
        "display": "block",
        "height": "auto",
        "overflow": "hidden",
        "transition": Object {
          "bounce": 0,
          "duration": 0.4,
        },
        "transitionEnd": Object {
          "overflow": "visible",
        },
      },
    }
  }
>
  <FirstFilterBlock
    column={
      Object {
        "filterTypes": Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ],
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
    columnIndex={0}
    filter={
      Object {
        "additionalSelectOptions": Array [
          Object {
            "label": "C100",
            "value": "C100",
          },
          Object {
            "label": "C200",
            "value": "C200",
          },
          Object {
            "label": "C300",
            "value": "C300",
          },
        ],
        "appliedFilters": Array [],
        "comparatorSelectOptions": Array [
          Object {
            "label": "or",
            "value": "or",
          },
        ],
        "filterTypes": Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ],
        "firstFilterBlock": Object {
          "operator": Object {
            "label": "equals",
            "value": "equals",
          },
        },
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
  />
</motion.div>
`;

exports[`FilterDropDown it renders the FilterDropDown component with prop 'animate' as 'visible' when showDropdown is true 1`] = `
<motion.div
  animate="open"
  className="tw-rounded-b-[5px] tw-border-b tw-border-x tw-border-t-0 tw-border-qcIris-700 tw-divide-y tw-divide-qcIris-700 tw-text-sm tw-bg-qcIris-100"
  initial="close"
  variants={
    Object {
      "close": Object {
        "height": 0,
        "overflow": "hidden",
        "transition": Object {
          "bounce": 0,
          "duration": 0.4,
        },
        "transitionEnd": Object {
          "display": "none",
        },
      },
      "open": Object {
        "display": "block",
        "height": "auto",
        "overflow": "hidden",
        "transition": Object {
          "bounce": 0,
          "duration": 0.4,
        },
        "transitionEnd": Object {
          "overflow": "visible",
        },
      },
    }
  }
>
  <FirstFilterBlock
    column={
      Object {
        "filterTypes": Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ],
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
    columnIndex={0}
    filter={
      Object {
        "additionalSelectOptions": Array [
          Object {
            "label": "C100",
            "value": "C100",
          },
          Object {
            "label": "C200",
            "value": "C200",
          },
          Object {
            "label": "C300",
            "value": "C300",
          },
        ],
        "appliedFilters": Array [],
        "comparatorSelectOptions": Array [
          Object {
            "label": "or",
            "value": "or",
          },
        ],
        "filterTypes": Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ],
        "firstFilterBlock": Object {
          "operator": Object {
            "label": "equals",
            "value": "equals",
          },
        },
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
  />
</motion.div>
`;
