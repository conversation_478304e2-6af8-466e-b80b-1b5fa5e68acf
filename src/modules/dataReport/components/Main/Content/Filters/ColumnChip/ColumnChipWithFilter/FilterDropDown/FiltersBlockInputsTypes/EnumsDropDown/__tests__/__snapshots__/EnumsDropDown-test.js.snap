// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumsDropDown it renders between operator component correctly 1`] = `
<EnumsDropDownRange
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "additionalSelectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
        Object {
          "label": "C300",
          "value": "C300",
        },
      ],
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "or",
          "value": "or",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "EQUALS",
          "value": "EQUALS",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "label": "C100",
          "value": "C100",
        },
        "operator": Object {
          "label": "EQUALS",
          "value": "EQUALS",
        },
      },
      "id": "birthplaceState",
      "label": "Birthplace--State",
      "type": "EnumerableSearchable",
      "value": "birthplaceState",
    }
  }
  operatorValue={
    Object {
      "label": "between",
      "value": "between",
    }
  }
/>
`;

exports[`EnumsDropDown it renders equals operator component correctly 1`] = `
<EnumsDropDownEquals
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "additionalSelectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
        Object {
          "label": "C300",
          "value": "C300",
        },
      ],
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "or",
          "value": "or",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "EQUALS",
          "value": "EQUALS",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "label": "C100",
          "value": "C100",
        },
        "operator": Object {
          "label": "EQUALS",
          "value": "EQUALS",
        },
      },
      "id": "birthplaceState",
      "label": "Birthplace--State",
      "type": "EnumerableSearchable",
      "value": "birthplaceState",
    }
  }
  operatorValue={
    Object {
      "label": "equals",
      "value": "equals",
    }
  }
/>
`;

exports[`EnumsDropDown it renders includes operator component correctly 1`] = `
<EnumsDropDownIncludes
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "additionalSelectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
        Object {
          "label": "C300",
          "value": "C300",
        },
      ],
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "or",
          "value": "or",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "EQUALS",
          "value": "EQUALS",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "label": "C100",
          "value": "C100",
        },
        "operator": Object {
          "label": "EQUALS",
          "value": "EQUALS",
        },
      },
      "id": "birthplaceState",
      "label": "Birthplace--State",
      "type": "EnumerableSearchable",
      "value": "birthplaceState",
    }
  }
  operatorValue={
    Object {
      "label": "includes",
      "value": "includes",
    }
  }
/>
`;
