import { useState } from "react";
import { useQuery } from "@apollo/client";
import {
  applySpec,
  prop,
  map,
  pipe,
  defaultTo,
  find,
  propEq,
  ifElse,
  path,
  always,
  identity,
  equals
} from "ramda";
import { useDispatch, useSelector } from "react-redux";
import { GET_QUESTION_ENUMERABLES } from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  setSecondValueChange,
  addNewFilterBlock,
  setFilterValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

const convertEnumerableOptions = map(
  applySpec({
    label: prop("description"),
    value: prop("externalValue")
  })
);

export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  eid,
  filter
}) => {
  const dispatch = useDispatch();
  const [options, setOptions] = useState([]);
  const [secondFilterValue, setSecondFilterValue] = useState(
    pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        path(["firstFilterBlock", "filter", "value"]),
        path([
          "appliedFilters",
          appliedFilterIndex - 1,
          "filterSelect",
          "value"
        ])
      ),
      ifElse(isNullOrEmpty, always(null), identity)
    )(filter)
  );
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );

  const { loading } = useQuery(GET_QUESTION_ENUMERABLES, {
    onCompleted: data => {
      const { questionEnumerables } = data;
      const [enumerableQuestion] = questionEnumerables;

      const convertedDropdownOptions = pipe(
        defaultTo([]),
        convertEnumerableOptions
      )(enumerableQuestion.enumerableOptions);

      setOptions(convertedDropdownOptions);

      const existingValue = find(propEq("value", secondFilterValue))(
        convertedDropdownOptions
      );

      if (existingValue && !isNullOrEmpty(existingValue)) {
        setSecondFilterValue(existingValue);
        dispatch(
          setSecondValueChange({
            appliedFilterIndex,
            columnIndex,
            value: existingValue
          })
        );
      }
    },
    skip: isNullOrEmpty(selectedRegistry),
    variables: {
      questionEid: eid,
      registryName: selectedRegistry
    }
  });
  // eslint-disable-next-line complexity
  const handleSecondFilterValueChange = value => {
    const isCustomValue =
      typeof value === "string" && !options.some(opt => opt.value === value);
    const finalValue = isCustomValue ? { label: value, value } : value;

    setSecondFilterValue(finalValue);
    if (appliedFilterIndex === 0) {
      dispatch(
        setSecondValueChange({
          appliedFilterIndex,
          columnIndex,
          value: finalValue
        })
      );
    } else {
      dispatch(
        setFilterValueChange({
          appliedFilterIndex: appliedFilterIndex - 1,
          columnIndex,
          value: finalValue
        })
      );
    }
    dispatch(
      addNewFilterBlock({
        appliedFilterIndex,
        columnIndex
      })
    );
  };

  return {
    handleSecondFilterValueChange,
    loading,
    options,
    secondFilterValue
  };
};
