import EnumsDropDownEqualsOrIncludes from "./EnumsDropDownEqualsOrIncludes";
import EnumsDropDownRange from "./EnumsDropDownRange";
import { useComponentLogic } from "./hooks";

const DependentExternalEnumerableSearchable = ({
  appliedFilterIndex,
  columnIndex,
  customContainerClassNames,
  filter,
  loading,
  operatorValue
}) => {
  const {
    secondFilterValue,
    handleSecondFilterValueChange,
    options,
    containerClassNames
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    customContainerClassNames,
    eid: filter.id,
    filter
  });

  if (operatorValue.value === "between") {
    return (
      <EnumsDropDownRange
        appliedFilterIndex={appliedFilterIndex}
        columnIndex={columnIndex}
        customContainerClassNames={customContainerClassNames}
        filter={filter}
        loading={loading}
        operatorValue={operatorValue}
      />
    );
  }
  return (
    <EnumsDropDownEqualsOrIncludes
      containerClassNames={containerClassNames}
      filter={filter}
      handleSecondFilterValueChange={handleSecondFilterValueChange}
      loading={loading}
      operatorValue={operatorValue}
      options={options}
      secondFilterValue={secondFilterValue}
    />
  );
};

export default DependentExternalEnumerableSearchable;
