// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Calculated it renders component between correctly 1`] = `
<div
  className="tw-mx-auto tw-flex tw-items-center"
>
  <Input
    inputClassName="tw-min-w-[74px] tw-h-[35px]"
    onChange={[Function]}
    type="number"
    value="1"
  />
  <div
    className="tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700"
  >
    and
  </div>
  <Input
    inputClassName="tw-min-w-[74px] tw-h-[35px]"
    onChange={[Function]}
    type="number"
    value="2"
  />
</div>
`;

exports[`Calculated it renders equals component correctly 1`] = `
<Open
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "AND",
          "value": "AND",
        },
        Object {
          "label": "OR",
          "value": "OR",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "between",
          "value": "between",
        },
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "value": "TEST",
        },
        "operator": Object {
          "label": "equals",
          "value": "equals",
        },
      },
      "id": "bmiCalculation",
      "label": "BMI Calculation",
      "type": "Calculated",
      "value": "bmiCalculation",
    }
  }
/>
`;
