import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import { FilterDropDown } from "..";

jest.mock("../FirstFilterBlock", () => "FirstFilterBlock");
jest.mock("../FilterBlock", () => "FilterBlock");
jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));
const columnMock = {
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: FilterDropDown,
      initialAppValues: {
        dataReport: {
          filters: [
            {
              additionalSelectOptions: [
                {
                  label: "C100",
                  value: "C100"
                },
                {
                  label: "C200",
                  value: "C200"
                },
                {
                  label: "C300",
                  value: "C300"
                }
              ],
              appliedFilters: [],
              comparatorSelectOptions: [
                {
                  label: "or",
                  value: "or"
                }
              ],
              filterTypes: [
                {
                  label: "equals",
                  value: "equals"
                }
              ],
              firstFilterBlock: {
                operator: {
                  label: "equals",
                  value: "equals"
                }
              },
              id: "nameLast",
              label: "Name--Last",
              type: "Open",
              value: "nameLast"
            }
          ]
        }
      },
      props
    })
  );

describe("FilterDropDown", () => {
  test("it renders the FilterDropDown component with prop 'animate' as 'hidden' when showDropdown is false", () => {
    const FilterDropDownMock = mockedComponent({
      column: columnMock,
      columnIndex: 0,
      showDropdown: false
    });

    expect(FilterDropDownMock).toMatchSnapshot();
  });
  test("it renders the FilterDropDown component with prop 'animate' as 'visible' when showDropdown is true", () => {
    const FilterDropDownMock = mockedComponent({
      column: columnMock,
      columnIndex: 0,
      showDropdown: true
    });

    expect(FilterDropDownMock).toMatchSnapshot();
  });
});
