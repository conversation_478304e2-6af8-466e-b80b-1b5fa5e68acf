import { format, isValid } from "date-fns";
import {
  addNewFilterBlock,
  removeFilter<PERSON><PERSON>,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import {
  always,
  cond,
  equals,
  identity,
  ifElse,
  or,
  path,
  pipe,
  T
} from "ramda";
import { useCallback, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { isNullOrEmpty, isNotNullOrEmpty } from "utils/fp";

export const MIN_DATE = "1900-01-01T00:00:00.000Z";

const formatDate = date => (date ? format(date, "MMMM d, yyyy") : null);
const minDate = new Date(MIN_DATE);

// eslint-disable-next-line max-params
const setFilterValue = (appliedFilterIndex, columnIndex, value, dispatch) =>
  pipe(
    ifElse(
      equals(0),
      filterIndex =>
        dispatch(
          setSecondValueChange({
            appliedFilterIndex: filterIndex,
            columnIndex,
            value
          })
        ),
      filterIndex =>
        dispatch(
          setFilterValueChange({
            appliedFilterIndex: filterIndex - 1,
            columnIndex,
            value
          })
        )
    )
  )(appliedFilterIndex);

const validateSavedFilterValue = cond([
  [equals("unknown"), identity],
  [value => or(isNullOrEmpty(value), !isValid(new Date(value))), always(null)],
  [T, identity]
]);

export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  filter
}) => {
  const dispatch = useDispatch();

  const [selectedFilterType, setSelectedFilterType] = useState("");

  const handleRadioChange = useCallback(
    // eslint-disable-next-line complexity
    filterType => {
      setSelectedFilterType(filterType);

      const valueToDispatch = cond([
        [equals("unknown"), always({ value: "unknown", label: "Unknown" })],
        [equals("date"), always({ value: null, label: null })]
      ])(filterType);

      setFilterValue(
        appliedFilterIndex,
        columnIndex,
        valueToDispatch,
        dispatch
      );

      if (filterType === "date") {
        return;
      }

      dispatch(
        isNullOrEmpty(valueToDispatch.value)
          ? removeFilterBlock({ appliedFilterIndex, columnIndex })
          : addNewFilterBlock({ appliedFilterIndex, columnIndex })
      );
    },
    [appliedFilterIndex, columnIndex, dispatch]
  );

  const [date, setDate] = useState(null);

  useEffect(() => {
    const savedFilterValue = pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        pipe(path(["firstFilterBlock", "filter", "value"])),
        pipe(
          path([
            "appliedFilters",
            appliedFilterIndex - 1,
            "filterSelect",
            "value"
          ])
        )
      ),
      validateSavedFilterValue
    )(filter);

    const handleFilterAction = cond([
      [equals("unknown"), () => setSelectedFilterType("unknown")],
      [
        isNotNullOrEmpty,
        () => {
          setSelectedFilterType("date");
          setDate(savedFilterValue);
        }
      ],
      // eslint-disable-next-line no-empty-function
      [T, () => {}]
    ]);

    handleFilterAction(savedFilterValue);

    dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
  }, [appliedFilterIndex, columnIndex, dispatch, filter]);

  const handleDateInputChange = useCallback(
    inputDate => {
      setDate(inputDate);
      const formattedDate = formatDate(inputDate);
      const value = { label: formattedDate, value: inputDate };

      const actionCreator =
        appliedFilterIndex === 0 ? setSecondValueChange : setFilterValueChange;

      const index =
        appliedFilterIndex === 0 ? appliedFilterIndex : appliedFilterIndex - 1;

      dispatch(
        actionCreator({ appliedFilterIndex: index, columnIndex, value })
      );

      dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
    },
    [dispatch, columnIndex, appliedFilterIndex]
  );

  return {
    date,
    handleDateInputChange,
    minDate,
    handleRadioChange,
    selectedFilterType,
    setSelectedFilterType
  };
};
