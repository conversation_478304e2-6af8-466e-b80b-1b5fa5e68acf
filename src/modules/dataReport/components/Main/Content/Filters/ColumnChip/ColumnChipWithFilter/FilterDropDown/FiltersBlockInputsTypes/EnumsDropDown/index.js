import EnumsDropDownEquals from "./EnumsDropDownEquals";
import EnumsDropDownIncludes from "./EnumsDropDownIncludes";
import EnumsDropDownRange from "./EnumsDropDownRange";

const OPERATOR_COMPONENTS = {
  between: EnumsDropDownRange,
  includes: EnumsDropDownIncludes,
  equals: EnumsDropDownEquals
};

const EnumsDropDown = ({
  appliedFilterIndex,
  columnIndex,
  customContainerClassNames,
  filter,
  loading,
  operatorValue
}) => {
  const Dropdown = OPERATOR_COMPONENTS[operatorValue.value];

  if (!Dropdown) {
    return null;
  }

  return (
    <Dropdown
      appliedFilterIndex={appliedFilterIndex}
      columnIndex={columnIndex}
      customContainerClassNames={customContainerClassNames}
      filter={filter}
      loading={loading}
      operatorValue={operatorValue}
    />
  );
};

export default EnumsDropDown;
