import { InputDropdown } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

export const EnumsDropDownEquals = ({
  loading,
  filter,
  appliedFilterIndex,
  columnIndex,
  customContainerClassNames
}) => {
  const {
    secondFilterValue,
    handleSecondFilterValueChange,
    options,
    containerClassNames
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    customContainerClassNames,
    eid: filter.id,
    filter
  });

  return (
    <InputDropdown
      clearIndicatorClass="tw-text-gray-600 tw-mr-2.5"
      containerClassName={containerClassNames}
      iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
      indicatorSeparatorStyles={{
        color: "#EAECF6",
        marginRight: "10px"
      }}
      isSearchable={false}
      loading={loading}
      menuStyles={{
        position: "relative"
      }}
      options={options}
      placeholder={`Select filter value for ${filter.label}`}
      value={secondFilterValue}
      onChange={handleSecondFilterValueChange}
    />
  );
};

export default EnumsDropDownEquals;
