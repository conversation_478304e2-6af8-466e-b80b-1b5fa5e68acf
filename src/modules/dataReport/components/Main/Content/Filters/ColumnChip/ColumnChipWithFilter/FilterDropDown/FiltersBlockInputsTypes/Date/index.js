import { DateInput } from "@q-centrix/q-components-react";
import DateRange from "../shared/DateRange";
import { useComponentLogic } from "./hooks";

const Date = ({ appliedFilterIndex, columnIndex, filter, operatorValue }) => {
  const { minDate, today, date, handleDateInputChange } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    filter
  });

  if (operatorValue.value === "between") {
    return (
      <DateRange
        appliedFilterIndex={appliedFilterIndex}
        columnIndex={columnIndex}
        filter={filter}
      />
    );
  }

  return (
    <DateInput
      maxDate={today}
      minDate={minDate}
      placeholder="Select Date"
      value={date}
      onChange={handleDateInputChange}
    />
  );
};

export default Date;
