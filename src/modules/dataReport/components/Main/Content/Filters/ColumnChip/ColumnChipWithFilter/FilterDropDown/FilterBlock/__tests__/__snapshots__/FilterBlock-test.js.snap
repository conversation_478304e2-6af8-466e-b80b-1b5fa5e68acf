// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FilterBlock it renders correctly with 1`] = `
<div
  className="last:tw-border-bottom tw-flex tw-flex-col tw-gap-4 tw-bg-qcIris-50 tw-p-5 last:tw-rounded-b-[5px] last:tw-border-qcIris-700 last:tw-bg-qcIris-200"
>
  <div
    className="tw-flex tw-gap-2"
  >
    <InputDropdown
      containerClassName="tw-font-inter tw-w-[100px]"
      iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
      inputClassName="tw-text-black-70"
      isSearchable={false}
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "or",
            "value": "or",
          },
        ]
      }
      placeholder="Select"
      value={null}
    />
    <InputDropdown
      className="tw-w-full"
      containerClassName="tw-font-inter tw-w-[226px]"
      iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
      inputClassName="tw-text-black-70"
      isSearchable={false}
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ]
      }
      placeholder="Select Filter"
      value={null}
    />
  </div>
</div>
`;
