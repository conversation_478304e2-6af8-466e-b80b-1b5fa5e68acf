import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import Calculated from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Input: "Input"
}));
jest.mock("../../Open", () => "Open");

const mockEqualsFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    },
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      value: "TEST"
    },
    operator: {
      label: "equals",
      value: "equals"
    }
  },
  id: "bmiCalculation",
  label: "BMI Calculation",
  type: "Calculated",
  value: "bmiCalculation"
};

const mockBetweenFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    },
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  firstFilterBlock: {
    filter: {
      operator: "and",
      value: {
        firstValue: "1",
        secondValue: "2"
      }
    },
    operator: {
      label: "between",
      value: "between"
    }
  },
  id: "bmiCalculation",
  label: "BMI Calculation",
  type: "Calculated",
  value: "bmiCalculation"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: Calculated,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("Calculated", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockEqualsFilter,
      operatorValue: {
        label: "equals",
        value: "equals"
      }
    });

    expect(component).toMatchSnapshot();
  });
  test("it renders component between correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockBetweenFilter,
      operatorValue: {
        label: "between",
        value: "between"
      }
    });

    expect(component).toMatchSnapshot();
  });
});
