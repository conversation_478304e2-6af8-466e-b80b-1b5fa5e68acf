// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumsDropDownIncludes it renders includes operator component correctly 1`] = `
<EnumsDropDownIncludes
  appliedFilterIndex={0}
  columnIndex={0}
  dispatch={[Function]}
  filter={
    Object {
      "additionalSelectOptions": Array [
        Object {
          "label": "C100",
          "value": "C100",
        },
        Object {
          "label": "C200",
          "value": "C200",
        },
        Object {
          "label": "C300",
          "value": "C300",
        },
      ],
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "or",
          "value": "or",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "includes",
          "value": "includes",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "label": "C100",
          "value": "C100",
        },
        "operator": Object {
          "label": "includes",
          "value": "includes",
        },
      },
      "id": "birthplaceState",
      "label": "Birthplace--State",
      "type": "EnumerableSearchable",
      "value": "birthplaceState",
    }
  }
  name="42|22"
  question={
    Object {
      "id": "42",
    }
  }
/>
`;
