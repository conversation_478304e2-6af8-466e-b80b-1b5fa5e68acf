// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NumberOrEnumerable it renders equals component correctly 1`] = `
<UnknownOrNumberRadio
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "OR",
          "value": "OR",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "between",
          "value": "between",
        },
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "value": "888",
        },
        "operator": Object {
          "label": "equals",
          "value": "equals",
        },
      },
      "id": "accessionYear",
      "label": "Accession Year",
      "type": "NumberOrEnumerable",
      "value": "accessionYear",
    }
  }
/>
`;

exports[`NumberOrEnumerable renders correctly when operator is 'between' 1`] = `
<BetweenNumbers
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "AND",
          "value": "AND",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "between",
          "value": "between",
        },
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "operator": "and",
          "value": Object {
            "firstValue": "1",
            "secondValue": "2",
          },
        },
        "operator": Object {
          "label": "between",
          "value": "between",
        },
      },
      "id": "numberOrEnumerable",
      "label": "Number or Enumerable",
      "type": "NumberOrEnumerable",
      "value": "numberOrEnumerable",
    }
  }
/>
`;
