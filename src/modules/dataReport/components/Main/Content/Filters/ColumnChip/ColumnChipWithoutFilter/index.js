export const ColumnChipWithoutFilter = ({ column, onRemove }) => (
  <div className="tw-bg-white tw-text-black-70 tw-rounded-[5px] tw-border tw-border-solid tw-border-qcSkyblue tw-p-2.5">
    <div className="tw-flex tw-items-center tw-justify-between">
      <div className="tw-flex tw-items-center tw-text-sm tw-font-semibold tw-text-qcSkyblue">
        {column.label}
      </div>
      <div className="tw-flex tw-items-center">
        <i
          className="fa-solid fa-xmark tw-cursor-pointer tw-text-sm tw-text-qcSkyblue"
          onClick={() => onRemove(column.id)}
        />
      </div>
    </div>
  </div>
);

export default ColumnChipWithoutFilter;
