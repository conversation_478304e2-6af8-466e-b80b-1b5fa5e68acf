import { useState, useCallback, useEffect } from "react";
import { format, isValid } from "date-fns";
import { pipe, ifElse, equals, or, always, identity, path } from "ramda";
import { useDispatch } from "react-redux";
import {
  setSecondValueChange,
  addNewFilterBlock,
  setFilterValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

export const MIN_DATE = "1900-01-01T00:00:00.000Z";

const formatDate = date =>
  isValid(date) ? format(date, "MMMM d, yyyy") : null;

const handleLoadSavedReportDateBetween = (
  filter,
  appliedFilterIndex,
  pathValue = "firstValue"
) =>
  pipe(
    ifElse(
      () => equals(appliedFilterIndex, 0),
      pipe(path(["firstFilterBlock", "filter", "value", pathValue]), date =>
        isNullOrEmpty(date) ? null : new Date(date)
      ),
      pipe(
        path([
          "appliedFilters",
          appliedFilterIndex - 1,
          "filterSelect",
          "value",
          pathValue
        ]),
        date => (isNullOrEmpty(date) ? null : new Date(date))
      )
    ),
    ifElse(
      date => or(isNullOrEmpty(date), !isValid(date)),
      always(null),
      identity
    )
  )(filter);

export const useComponentLogic = ({
  columnIndex,
  appliedFilterIndex,
  filter
}) => {
  const dispatch = useDispatch();
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  useEffect(() => {
    setStartDate(handleLoadSavedReportDateBetween(filter, appliedFilterIndex));
    setEndDate(
      handleLoadSavedReportDateBetween(
        filter,
        appliedFilterIndex,
        "secondValue"
      )
    );
    dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
  }, [filter, appliedFilterIndex]);

  const handleDateInputChange = useCallback(
    // eslint-disable-next-line complexity
    isStartDate => inputDate => {
      const setDate = isStartDate ? setStartDate : setEndDate;

      setDate(inputDate);

      const formattedStartDate = formatDate(
        isStartDate ? inputDate : startDate
      );
      const formattedEndDate = formatDate(isStartDate ? endDate : inputDate);

      const actionCreator =
        appliedFilterIndex === 0 ? setSecondValueChange : setFilterValueChange;

      const index =
        appliedFilterIndex === 0 ? appliedFilterIndex : appliedFilterIndex - 1;

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: {
            value: {
              firstValue: formattedStartDate,
              secondValue: formattedEndDate
            }
          }
        })
      );

      dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
    },
    [dispatch, columnIndex, appliedFilterIndex, startDate, endDate]
  );

  return {
    endDate,
    handleDateInputChangeEnd: handleDateInputChange(false),
    handleDateInputChangeStart: handleDateInputChange(true),
    startDate
  };
};
