import { components } from "react-select";
import CreatableSelect from "react-select/creatable";
import { useComponentLogic } from "./hooks";

const EnumerableOrOpen = ({ columnIndex, appliedFilterIndex, filter }) => {
  const {
    handleSecondFilterValueChange,
    options,
    handleCreateOption,
    secondFilterValue
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    eid: filter.id,
    filter
  });

  const customStyles = {
    menu: provided => ({
      ...provided,
      zIndex: 9999
    })
  };

  const CustomClearIndicator = props => (
    <components.ClearIndicator {...props}>
      <div className="custom-clear-indicator">
        <i className="fa-solid fa-circle-xmark tw-mr-2.5 tw-text-gray-600" />
      </div>
    </components.ClearIndicator>
  );

  const CustomDropdownIndicator = () => (
    <div className="tw-px-3 tw-font-inter">
      <i className="fa-solid fa-chevron-down tw-text-qc-info tw-font-black" />
    </div>
  );

  return (
    <CreatableSelect
      isClearable
      components={{
        ClearIndicator: CustomClearIndicator,
        DropdownIndicator: CustomDropdownIndicator
      }}
      formatCreateLabel={inputValue => inputValue}
      options={options}
      placeholder={filter.label}
      styles={customStyles}
      value={secondFilterValue}
      onChange={handleSecondFilterValueChange}
      onCreateOption={handleCreateOption}
    />
  );
};

export default EnumerableOrOpen;
