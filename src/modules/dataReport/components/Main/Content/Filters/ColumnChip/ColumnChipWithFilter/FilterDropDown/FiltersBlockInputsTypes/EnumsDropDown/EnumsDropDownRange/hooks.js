import { useState } from "react";
import { useQuery } from "@apollo/client";
import {
  applySpec,
  prop,
  map,
  pipe,
  defaultTo,
  always,
  equals,
  when,
  path,
  find,
  propEq,
  ifElse
} from "ramda";
import { useDispatch, useSelector } from "react-redux";
import { GET_QUESTION_ENUMERABLES } from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  addNewFilterBlock,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";
import { useMapStoredFiltersToOptions } from "../shared/useMapStoredFiltersToOptions";

const convertEnumerableOptions = map(
  applySpec({
    label: prop("description"),
    value: prop("externalValue")
  })
);

const getEnumRangeValuesFromSavedReport = (filter, appliedFilterIndex) =>
  pipe(
    ifElse(
      () => equals(appliedFilterIndex, 0),
      path(["firstFilterBlock", "filter"]),
      path(["appliedFilters", appliedFilterIndex - 1, "filterSelect"])
    ),
    when(
      isNullOrEmpty,
      always({
        firstValue: null,
        secondValue: null
      })
    )
  )(filter);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  appliedFilterIndex,
  columnIndex,
  filter
}) => {
  const dispatch = useDispatch();
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );

  const [dropDownOptions, setDropDownOptions] = useState([]);
  const [startEnumOption, setStartEnumOption] = useState(null);
  const [endEnumOption, setEndEnumOption] = useState(null);
  const dispatchFilterChange =
    // eslint-disable-next-line complexity
    ({ firstEnumOption, secondEnumOption }) => {
      const actionCreator = equals(appliedFilterIndex, 0)
        ? setSecondValueChange
        : setFilterValueChange;

      const index = equals(appliedFilterIndex, 0)
        ? appliedFilterIndex
        : appliedFilterIndex - 1;

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: {
            value: {
              firstValue: firstEnumOption?.value || null,
              secondValue: secondEnumOption?.value || null
            }
          }
        })
      );

      if (firstEnumOption && secondEnumOption) {
        dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
      }
    };
  const getFilterValue = () =>
    getEnumRangeValuesFromSavedReport(filter, appliedFilterIndex);

  // eslint-disable-next-line complexity
  const findOptionsByValue = (loadedValues, options) => {
    if (!loadedValues) return null;

    const findOption = enumerableValue =>
      enumerableValue ? find(propEq("value", enumerableValue), options) : null;

    const startOption = findOption(loadedValues?.value?.firstValue);
    const endOption = findOption(loadedValues?.value?.secondValue);

    setStartEnumOption(startOption);
    setEndEnumOption(endOption);
    dispatchFilterChange({
      firstEnumOption: startOption,
      secondEnumOption: endOption
    });
    return {
      value: {
        firstValue: loadedValues?.value?.firstValue || null,
        secondValue: loadedValues?.value?.secondValue || null
      }
    };
  };

  useMapStoredFiltersToOptions({
    appliedFilterIndex,
    columnIndex,
    dropDownOptions,
    findOptionsByValue,
    getFilterValue
  });

  const { loading: queryLoading } = useQuery(GET_QUESTION_ENUMERABLES, {
    onCompleted: data => {
      const { questionEnumerables } = data;
      const [enumerableQuestion] = questionEnumerables || [];

      const convertedDropdownOptions = pipe(
        defaultTo([]),
        convertEnumerableOptions
      )(enumerableQuestion?.enumerableOptions);

      setDropDownOptions(convertedDropdownOptions);
    },
    skip: isNullOrEmpty(selectedRegistry),
    variables: {
      questionEid: filter.id,
      registryName: selectedRegistry
    }
  });

  const handleEnumStartChange = enumStartDropdownOption => {
    setStartEnumOption(enumStartDropdownOption);
    dispatchFilterChange({
      firstEnumOption: enumStartDropdownOption,
      secondEnumOption: endEnumOption
    });
  };
  const handleEnumEndChange = enumEndDropdownOption => {
    setEndEnumOption(enumEndDropdownOption);
    dispatchFilterChange({
      firstEnumOption: startEnumOption,
      secondEnumOption: enumEndDropdownOption
    });
  };
  const isStartValueSet = !isNullOrEmpty(startEnumOption);

  return {
    startEnumOption,
    endEnumOption,
    options: dropDownOptions,
    handleEnumStartChange,
    handleEnumEndChange,
    isStartValueSet,
    loading: queryLoading
  };
};
