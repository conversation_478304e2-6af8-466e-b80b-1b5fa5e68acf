import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import DateRange from "..";

const mockedProps = {
  inputValue: {
    firstValue: "12/01/2024",
    secondValue: "12/31/2024"
  },
  handleInputChange: jest.fn()
};

const mockedComponent = (props = mockedProps) =>
  create(
    decoratedApollo({
      component: DateRange,
      props,
      initialAppValues: {
        accountSettings: {
          id: "1",
          fullName: "Russell Reas"
        }
      },
      apolloMocks
    })
  );

describe("DateRange", () => {
  test("it renders equals component correctly", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
});
