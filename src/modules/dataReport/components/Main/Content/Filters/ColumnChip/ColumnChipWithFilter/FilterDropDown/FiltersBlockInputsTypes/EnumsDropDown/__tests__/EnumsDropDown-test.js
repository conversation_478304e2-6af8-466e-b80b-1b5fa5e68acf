import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import EnumsDropDown from "..";

jest.mock("../EnumsDropDownRange", () => "EnumsDropDownRange");
jest.mock("../EnumsDropDownEquals", () => "EnumsDropDownEquals");
jest.mock("../EnumsDropDownIncludes", () => "EnumsDropDownIncludes");
const filterMock = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "or",
      value: "or"
    }
  ],
  filterTypes: [
    {
      label: "EQUALS",
      value: "EQUALS"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "C100",
      value: "C100"
    },
    operator: {
      label: "EQUALS",
      value: "EQUALS"
    }
  },
  id: "birthplaceState",
  label: "Birthplace--State",
  type: "EnumerableSearchable",
  value: "birthplaceState"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: EnumsDropDown,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: { selectedRegistry: "Oncology" }
      },
      props
    })
  );

describe("EnumsDropDown", () => {
  test("it renders equals operator component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock,
      operatorValue: {
        label: "equals",
        value: "equals"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders between operator component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock,
      operatorValue: {
        label: "between",
        value: "between"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders includes operator component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock,
      operatorValue: {
        label: "includes",
        value: "includes"
      }
    });

    expect(component).toMatchSnapshot();
  });
});
