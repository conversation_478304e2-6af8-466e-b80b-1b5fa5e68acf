import { create, act } from "react-test-renderer";
import NumberWithUnit from "..";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import wait from "waait";

jest.mock("@q-centrix/q-components-react", () => ({
  Input: "Input"
}));

const mockEqualsFilter = {
  label: "Height",
  value: "height_ct",
  id: "height_ct",
  type: "NumberWithUnit",
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    },
    {
      label: "OR",
      value: "OR"
    }
  ],
  firstFilterBlock: {
    operator: {
      label: "equals",
      value: "equals"
    },
    filter: {
      value: "72",
      operator: "and"
    }
  },
  unit: "feet"
};

const mockBetweenFilter = {
  label: "Height",
  value: "height_ct",
  id: "height_ct",
  type: "NumberWithUnit",
  filterTypes: [
    {
      label: "between",
      value: "between"
    },
    {
      label: "equals",
      value: "equals"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "AND",
      value: "AND"
    },
    {
      label: "OR",
      value: "OR"
    }
  ],
  firstFilterBlock: {
    operator: {
      label: "between",
      value: "between"
    },
    filter: {
      value: {
        firstValue: "0",
        secondValue: "60"
      },
      operator: "and"
    }
  },
  unit: "feet"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      component: NumberWithUnit,
      props,
      initialAppValues: {
        accountSettings: {
          id: "1",
          fullName: "Russell Reas"
        }
      },
      apolloMocks
    })
  );

describe("NumberWithUnit", () => {
  test("it renders equals component correctly", async () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      operatorValue: {
        label: "equals",
        value: "equals"
      },
      filter: mockEqualsFilter
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component between correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      operatorValue: {
        label: "between",
        value: "between"
      },
      filter: mockBetweenFilter
    });

    expect(component).toMatchSnapshot();
  });
});
