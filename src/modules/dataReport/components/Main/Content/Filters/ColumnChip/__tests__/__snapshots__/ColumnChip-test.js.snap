// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ColumnChip renders correctly with filter 1`] = `
<motion.div
  layout="position"
  variants={
    Object {
      "closed": Object {
        "opacity": 0,
      },
      "open": Object {
        "opacity": 1,
      },
    }
  }
>
  <ColumnChipWithFilter
    column={
      Object {
        "filterTypes": Array [
          Object {
            "label": "equals",
            "value": "equals",
          },
        ],
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
    columnIndex={0}
    hasFilter={true}
    onRemove={[MockFunction]}
  />
</motion.div>
`;

exports[`ColumnChip renders correctly without filter 1`] = `
<motion.div
  layout="position"
  variants={
    Object {
      "closed": Object {
        "opacity": 0,
      },
      "open": Object {
        "opacity": 1,
      },
    }
  }
>
  <ColumnChipWithoutFilter
    column={
      Object {
        "filterTypes": Array [],
        "id": "nameLast",
        "label": "Name--Last",
        "type": "Open",
        "value": "nameLast",
      }
    }
    onRemove={[MockFunction]}
  />
</motion.div>
`;
