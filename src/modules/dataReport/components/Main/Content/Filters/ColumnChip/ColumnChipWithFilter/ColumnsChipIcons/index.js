import classNames from "classnames";
import { CustomTooltip } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
export const ColumnsChipIcons = ({
  showDropdown,
  handleFilterClick,
  onRemove,
  column
}) => {
  const { shouldShowWarningIcon, handleIconClick } = useComponentLogic({
    column,
    handleFilterClick
  });

  const iconClasses = classNames(
    shouldShowWarningIcon
      ? "far fa-question-circle tw-text-qcSkyblue tw-cursor-help  tw-text-sm"
      : "fa-solid tw-font-black tw-text-sm",
    {
      "fa-chevron-down tw-text-qcSkyblue tw-cursor-pointer":
        !shouldShowWarningIcon && !showDropdown,
      "fa-chevron-up tw-text-qcIris-700 tw-cursor-pointer":
        !shouldShowWarningIcon && showDropdown
    }
  );

  const xmarkClasses = classNames(
    "fa-solid fa-xmark tw-cursor-pointer tw-text-sm",
    {
      "tw-text-qcIris-700": showDropdown,
      "tw-text-qcSkyblue": !showDropdown
    }
  );

  return (
    <>
      <div className="tw-mr-2.5">
        <CustomTooltip
          content="To filter on this field, you must select 1 Primary Site, 1 Histology/Behavior, and have all Dates of Diagnosis in the same year."
          dark="true"
          place="top"
          id="custom-tooltip-data-report"
          className={`!tw-w-[220px] ${
            shouldShowWarningIcon ? "" : "tw-hidden"
          }`}
        >
          <i className={iconClasses} onClick={handleIconClick} />
        </CustomTooltip>
      </div>
      <i className={xmarkClasses} onClick={() => onRemove(column.id)} />
    </>
  );
};

export default ColumnsChipIcons;
