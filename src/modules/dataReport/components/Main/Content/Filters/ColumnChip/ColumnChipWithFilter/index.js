import classNames from "classnames";
import ColumnsChipIcons from "./ColumnsChipIcons";
import FilterDropDown from "./FilterDropDown";
import { useComponentLogic } from "./hooks";

export const ColumnChipWithFilter = ({ column, columnIndex, onRemove }) => {
  const { showDropdown, handleFilterClick } = useComponentLogic({
    column
  });

  const chipClasses = classNames("tw-text-black-70 tw-border-solid tw-p-2.5", {
    "tw-bg-qcIris-50 tw-border-qcIris-700 tw-rounded-t-[5px] tw-border-t tw-border-x tw-border-b-0 tw-shadow-[-1px_2px_6px_0px_rgba(0,0,0,0.20)] tw-relative tw-z-1":
      showDropdown,
    "tw-bg-white tw-border-qcSkyblue tw-text-qcSkyblue tw-border tw-rounded-[5px]":
      !showDropdown
  });

  const columnHeaderClasses = classNames(
    "tw-font-semibold tw-flex tw-items-center tw-text-sm",
    {
      "tw-text-qcIris-700": showDropdown,
      "tw-text-qcSkyblue": !showDropdown
    }
  );

  return (
    <div className="tw-flex tw-flex-col">
      <div className={chipClasses}>
        <div className="tw-flex tw-items-center tw-justify-between">
          <div className={columnHeaderClasses}>{column.label}</div>
          <div className="tw-flex tw-items-center">
            <ColumnsChipIcons
              column={column}
              handleFilterClick={handleFilterClick}
              showDropdown={showDropdown}
              onRemove={onRemove}
            />
          </div>
        </div>
      </div>
      <FilterDropDown
        column={column}
        columnIndex={columnIndex}
        showDropdown={showDropdown}
      />
    </div>
  );
};

export default ColumnChipWithFilter;
