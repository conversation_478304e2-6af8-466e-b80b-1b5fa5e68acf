import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import ColumnChipWithFilter from "..";

jest.mock("../FilterDropDown", () => "FilterDropDown");
jest.mock("../ColumnsChipIcons", () => "ColumnsChipIcons");
const columnMock = {
  filterTypes: [
    {
      label: "equals",
      value: "equals"
    }
  ],
  id: "nameLast",
  label: "Name--Last",
  type: "Open",
  value: "nameLast"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: ColumnChipWithFilter,
      initialAppValues: {
        dataReport: {
          filters: [
            {
              additionalSelectOptions: [
                {
                  label: "C100",
                  value: "C100"
                },
                {
                  label: "C200",
                  value: "C200"
                },
                {
                  label: "C300",
                  value: "C300"
                }
              ],
              appliedFilters: [],
              comparatorSelectOptions: [
                {
                  label: "and",
                  value: "and"
                },
                {
                  label: "or",
                  value: "or"
                }
              ],
              filterTypes: [
                {
                  label: "equals",
                  value: "equals"
                }
              ],
              firstFilterBlock: {
                operator: {
                  label: "equals",
                  value: "equals"
                }
              },
              id: "nameLast",
              label: "Name--Last",
              mainSelectOptions: {},
              type: "Open",
              value: "nameLast"
            }
          ]
        }
      },
      props
    })
  );

describe("ColumnChipWithFilter", () => {
  test("it renders null when showDropdown is false", () => {
    const ColumnChipWithFilterMock = mockedComponent({
      column: columnMock,
      columnIndex: 0,
      onRemove: jest.fn()
    });

    expect(ColumnChipWithFilterMock).toMatchSnapshot();
  });
});
