import { useEffect, useState } from "react";
import { equals, ifElse } from "ramda";
import { useDispatch } from "react-redux";
import {
  setSecondValueChange,
  setFilterValueChange
} from "modules/dataReport/redux/slices";

export const useMapStoredFiltersToOptions = ({
  appliedFilterIndex,
  columnIndex,
  dropDownOptions,
  findOptionsByValue,
  getFilterValue
}) => {
  const dispatch = useDispatch();
  const [areInitialStoredValuesMapped, setAreInitialStoredValuesMapped] =
    useState(false);

  // eslint-disable-next-line complexity
  useEffect(() => {
    if (areInitialStoredValuesMapped || !dropDownOptions?.length) return;

    const loadedValues = getFilterValue();

    if (!loadedValues) return;

    const matchedOption = findOptionsByValue(loadedValues, dropDownOptions);

    if (!matchedOption) return;

    ifElse(
      equals(0),
      () =>
        dispatch(
          setSecondValueChange({
            appliedFilterIndex,
            columnIndex,
            value: matchedOption
          })
        ),
      () =>
        dispatch(
          setFilterValueChange({
            appliedFilterIndex: appliedFilterIndex - 1,
            columnIndex,
            value: matchedOption
          })
        )
    )(appliedFilterIndex);

    setAreInitialStoredValuesMapped(true);
  }, [appliedFilterIndex, dropDownOptions, areInitialStoredValuesMapped]);
};
