import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import Open from "..";

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: Open,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("Open", () => {
  test("it renders component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0
    });

    expect(component).toMatchSnapshot();
  });
});
