import { useCallback, useState } from "react";
import {
  addNewFilterBlock,
  setFilterValueChange,
  removeFilter<PERSON><PERSON>,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import { useDispatch } from "react-redux";
import {
  __,
  always,
  assoc,
  equals,
  identity,
  ifElse,
  path,
  pipe,
  prop,
  propOr
} from "ramda";
import { isNullOrEmpty } from "utils/fp";

const AND_OPERATOR = "and";

export const useComponentLogic = props => {
  const { appliedFilterIndex, columnIndex, filter, operatorValue } = props;

  const [question] = useState(assoc("unit", propOr(null, "unit")(filter), {}));
  const dispatch = useDispatch();

  const [inputValue, setInputValue] = useState(
    pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        ifElse(
          item =>
            equals(
              path(["firstFilterBlock", "operator", "value"], item),
              "between"
            ),
          pipe(path(["firstFilterBlock", "filter", "value"]), item => ({
            firstValue: prop("firstValue", item),
            secondValue: prop("secondValue", item)
          })),
          pipe(
            path(["firstFilterBlock", "filter", "value"]),
            assoc("firstValue", __, {})
          )
        ),
        ifElse(
          item =>
            equals(
              path(
                [
                  "appliedFilters",
                  appliedFilterIndex - 1,
                  "operatorSelect",
                  "value"
                ],
                item
              ),
              "between"
            ),
          pipe(
            path([
              "appliedFilters",
              appliedFilterIndex - 1,
              "filterSelect",
              "value"
            ]),
            item => ({
              firstValue: prop("firstValue", item),
              secondValue: prop("secondValue", item)
            })
          ),
          pipe(
            path([
              "appliedFilters",
              appliedFilterIndex - 1,
              "filterSelect",
              "value"
            ]),
            assoc("firstValue", __, {})
          )
        )
      ),
      ifElse(
        isNullOrEmpty,
        always({
          firstValue: "",
          secondValue: ""
        }),
        identity
      )
    )(filter)
  );

  const dispatchFilterChange = useCallback(
    // eslint-disable-next-line complexity
    numberInputValues => {
      const actionCreator = equals(appliedFilterIndex, 0)
        ? setSecondValueChange
        : setFilterValueChange;

      const index = equals(appliedFilterIndex, 0)
        ? appliedFilterIndex
        : appliedFilterIndex - 1;

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: {
            value: numberInputValues,
            operator: AND_OPERATOR
          }
        })
      );
    },
    [dispatch, columnIndex, appliedFilterIndex, inputValue]
  );

  const handleInputValueChange = useCallback(
    inputNumberName => event => {
      const newInputNumberValues = assoc(
        inputNumberName,
        event.target.value,
        inputValue
      );

      const value = ifElse(
        equals("equals"),
        () => prop("firstValue")(newInputNumberValues),
        always(newInputNumberValues)
      )(operatorValue.value);

      setInputValue(newInputNumberValues);
      dispatchFilterChange(value);

      dispatch(
        isNullOrEmpty(event.target.value)
          ? removeFilterBlock({ columnIndex, appliedFilterIndex })
          : addNewFilterBlock({ columnIndex, appliedFilterIndex })
      );
    },
    [inputValue, operatorValue, dispatchFilterChange]
  );

  return {
    question,
    inputValue,
    handleInputValueChange
  };
};
