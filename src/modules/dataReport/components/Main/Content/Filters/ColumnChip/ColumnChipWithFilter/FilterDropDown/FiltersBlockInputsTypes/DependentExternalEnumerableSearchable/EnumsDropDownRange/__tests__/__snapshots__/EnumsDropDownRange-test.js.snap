// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnumsDropDownRange it renders component correctly with range 1`] = `
<div
  className="tw-flex tw-h-[35px] tw-flex-row"
>
  <InputDropdown
    clearIndicatorClass="tw-text-gray-600"
    clearIndicatorStyles={
      Object {
        "paddingLeft": "0px",
      }
    }
    clearable={true}
    containerClassName="tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5"
    iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700 tw-pl-2"
    indicatorSeparatorStyles={
      Object {
        "color": "#EAECF6",
      }
    }
    isSearchable={false}
    loading={true}
    menuStyles={
      Object {
        "fontFamily": "Inter",
        "width": "130px",
      }
    }
    onChange={[Function]}
    options={Array []}
    placeholder="Select"
    valueContainerStyles={
      Object {
        "paddingLeft": "10px",
        "paddingRight": "0px",
      }
    }
  />
  <div
    className="tw-m-auto tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-px-3 tw-py-1 tw-text-[11px] tw-font-semibold tw-leading-4 tw-bg-qcNeutrals-200 tw-text-qcNeutrals-700 tw-h-[25px] tw-border tw-border-qcNeutrals-400"
  >
    and
  </div>
  <InputDropdown
    clearIndicatorClass="tw-text-gray-600"
    clearIndicatorStyles={
      Object {
        "paddingLeft": "0px",
      }
    }
    clearable={true}
    containerClassName="tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5"
    iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700 tw-pl-2"
    indicatorSeparatorStyles={
      Object {
        "color": "#EAECF6",
      }
    }
    isSearchable={false}
    loading={true}
    menuStyles={
      Object {
        "fontFamily": "Inter",
        "width": "130px",
      }
    }
    onChange={[Function]}
    options={Array []}
    placeholder="Select"
    valueContainerStyles={
      Object {
        "paddingLeft": "10px",
        "paddingRight": "0px",
      }
    }
  />
</div>
`;
