import { InputDropdown } from "@q-centrix/q-components-react";
import classNames from "classnames";
import { useComponentLogic } from "./hooks";

const EnumsDropDownRange = ({
  columnIndex,
  appliedFilterIndex,
  filter,
  customContainerClassNames
}) => {
  const {
    options,
    loading,
    startEnumOption,
    endEnumOption,
    isStartValueSet,
    handleEnumStartChange,
    handleEnumEndChange
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    filter
  });

  const containerClassNames = customContainerClassNames
    ? customContainerClassNames
    : "tw-font-inter tw-pt-1.5 tw-pb-5 tw-px-2.5";

  const andDivClassNames = classNames(
    "tw-m-auto tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-px-3 tw-py-1 tw-text-[11px] tw-font-semibold tw-leading-4",
    {
      "tw-bg-qcInfo-100 tw-border-qcInfo-700 tw-text-qcInfo-700 tw-h-6 tw-border-2":
        isStartValueSet,
      "tw-bg-qcNeutrals-200 tw-text-qcNeutrals-700 tw-h-[25px] tw-border tw-border-qcNeutrals-400":
        !isStartValueSet
    }
  );

  return (
    <div className="tw-flex tw-h-[35px] tw-flex-row">
      <InputDropdown
        clearable
        clearIndicatorClass="tw-text-gray-600"
        clearIndicatorStyles={{
          paddingLeft: "0px"
        }}
        containerClassName={containerClassNames}
        controlStyles={{
          borderRadius: "6px",
          fontFamily: "Inter"
        }}
        iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700 tw-pl-2"
        indicatorSeparatorStyles={{
          color: "#EAECF6"
        }}
        isSearchable={false}
        loading={loading}
        menuStyles={{
          fontFamily: "Inter",
          width: "130px"
        }}
        options={options}
        placeholder="Select"
        value={startEnumOption}
        valueContainerStyles={{
          paddingRight: "0px"
        }}
        onChange={handleEnumStartChange}
      />
      <div className={andDivClassNames}>and</div>
      <InputDropdown
        clearable
        clearIndicatorClass="tw-text-gray-600"
        clearIndicatorStyles={{
          paddingLeft: "0px"
        }}
        containerClassName={containerClassNames}
        controlStyles={{
          borderRadius: "6px",
          fontFamily: "Inter"
        }}
        iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700 tw-pl-2"
        indicatorSeparatorStyles={{
          color: "#EAECF6"
        }}
        isSearchable={false}
        loading={loading}
        menuStyles={{
          fontFamily: "Inter",
          width: "130px"
        }}
        options={options}
        placeholder="Select"
        value={endEnumOption}
        valueContainerStyles={{
          paddingRight: "0px"
        }}
        onChange={handleEnumEndChange}
      />
    </div>
  );
};

export default EnumsDropDownRange;
