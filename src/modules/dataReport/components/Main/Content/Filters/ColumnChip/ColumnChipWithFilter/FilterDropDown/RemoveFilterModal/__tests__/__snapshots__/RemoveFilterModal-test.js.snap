// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RemoveFilterModal it renders component correctly 1`] = `
<ConfirmationModal
  isOpen={true}
  title="Remove Primary Site Filter?"
>
  <div
    className="tw-flex tw-flex-col tw-gap-y-3"
  >
    <p>
      This will remove fields dependent on
       
      <span
        className="tw-font-semibold"
      >
        Primary Site
      </span>
      . Are you sure?
    </p>
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-center tw-gap-x-5"
  >
    <Button
      bg="neutral"
      customStyle="tw-w-[120px] tw-h-10 tw-flex tw-flex-row tw-gap-x-3 tw-items-center tw-text-qcNeutrals-700"
      onClick={[Function]}
    >
      <i
        className="fa fa-xmark"
      />
      Cancel
    </Button>
    <Button
      customStyle="tw-w-[120px] tw-h-10 tw-flex tw-flex-row tw-gap-x-3 tw-items-center tw-bg-qcGreen-700 hover:tw-bg-qcGreen-700"
      onClick={[Function]}
    >
      <i
        className="fa-regular fa-check"
      />
      Confirm
    </Button>
  </div>
</ConfirmationModal>
`;
