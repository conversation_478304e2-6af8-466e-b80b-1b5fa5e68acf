import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  setComparatorValueChange,
  setOperatorValueChange,
  setFilterValueChange
} from "modules/dataReport/redux/slices";

const useComponentLogic = ({ columnIndex, appliedFilterIndex, column }) => {
  const dispatch = useDispatch();
  const [comparatorValue, setComparatorValue] = useState(null);
  const [operatorValue, setOperatorValue] = useState(null);

  const handleComparatorValueChange = value => {
    setComparatorValue(value);
    dispatch(
      setComparatorValueChange({
        appliedFilterIndex,
        columnIndex,
        id: column.id,
        value
      })
    );
  };

  const handleOperatorValueChange = value => {
    setOperatorValue(value);
    dispatch(
      setOperatorValueChange({
        appliedFilterIndex,
        columnIndex,
        id: column.id,
        value
      })
    );
    dispatch(
      setFilterValueChange({
        appliedFilterIndex,
        columnIndex,
        value: null
      })
    );
  };

  useEffect(() => {
    if (column.appliedFilters) {
      setComparatorValue(
        column.appliedFilters[appliedFilterIndex]?.comparatorSelect
      );
      setOperatorValue(
        column.appliedFilters[appliedFilterIndex]?.operatorSelect
      );
    }
  }, [column, appliedFilterIndex]);

  return {
    comparatorValue,
    handleComparatorValueChange,
    handleOperatorValueChange,
    operatorValue
  };
};

export default useComponentLogic;
