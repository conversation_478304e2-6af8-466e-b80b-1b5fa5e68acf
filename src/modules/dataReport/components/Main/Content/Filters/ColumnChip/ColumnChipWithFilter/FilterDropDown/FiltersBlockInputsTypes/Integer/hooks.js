import { useCallback, useState } from "react";
import {
  all,
  always,
  assoc,
  equals,
  identity,
  ifElse,
  path,
  pipe
} from "ramda";
import { useDispatch } from "react-redux";
import {
  addNewFilterBlock,
  setFilterValueChange,
  setSecondValueChange
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

const AND_OPERATOR = "and";

export const useComponentLogic = ({
  appliedFilterIndex,
  columnIndex,
  filter
}) => {
  const dispatch = useDispatch();

  const [numberFilterValues, setNumberFilterValues] = useState(
    pipe(
      ifElse(
        () => equals(appliedFilterIndex, 0),
        path(["firstFilterBlock", "filter", "value"]),
        path([
          "appliedFilters",
          appliedFilterIndex - 1,
          "filterSelect",
          "value"
        ])
      ),
      ifElse(
        isNullOrEmpty,
        always({
          firstValue: "",
          secondValue: ""
        }),
        identity
      )
    )(filter)
  );

  const dispatchFilterChange = useCallback(
    // eslint-disable-next-line complexity
    numberInputValues => {
      const actionCreator = equals(appliedFilterIndex, 0)
        ? setSecondValueChange
        : setFilterValueChange;

      const index = equals(appliedFilterIndex, 0)
        ? appliedFilterIndex
        : appliedFilterIndex - 1;

      dispatch(
        actionCreator({
          appliedFilterIndex: index,
          columnIndex,
          value: {
            operator: AND_OPERATOR,
            value: numberInputValues
          }
        })
      );
      const shouldAddNewFilterBlock = all(Boolean, [
        numberInputValues.firstValue,
        numberInputValues.secondValue
      ]);

      if (shouldAddNewFilterBlock) {
        dispatch(addNewFilterBlock({ appliedFilterIndex, columnIndex }));
      }
    },
    [dispatch, columnIndex, appliedFilterIndex, numberFilterValues]
  );

  const handleNumberInputChange = useCallback(
    inputNumberName => event => {
      const newInputNumberValues = assoc(
        inputNumberName,
        event.target.value,
        numberFilterValues
      );

      setNumberFilterValues(newInputNumberValues);
      dispatchFilterChange(newInputNumberValues);
    },
    [numberFilterValues, dispatchFilterChange]
  );

  return {
    handleNumberInputChange,
    numberFilterValues
  };
};
