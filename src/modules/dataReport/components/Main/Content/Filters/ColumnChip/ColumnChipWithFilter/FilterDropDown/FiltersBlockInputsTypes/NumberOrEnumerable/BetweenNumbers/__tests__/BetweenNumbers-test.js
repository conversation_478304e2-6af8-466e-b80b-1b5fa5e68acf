import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import BetweenNumbers from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Input: "Input"
}));

const mockBetweenFilter = {
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    }
  ],
  firstFilterBlock: {
    filter: {
      operator: "and",
      value: {
        firstValue: "1",
        secondValue: "2"
      }
    },
    operator: {
      label: "between",
      value: "between"
    }
  },
  id: "numberOrEnumerable",
  label: "Number or Enumerable",
  type: "NumberOrEnumerable",
  value: "numberOrEnumerable"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: BetweenNumbers,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        }
      },
      props
    })
  );

describe("BetweenNumbers", () => {
  test("it renders component correctly", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: mockBetweenFilter
    });

    expect(component).toMatchSnapshot();
  });
});
