// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateTime it renders component between correctly 1`] = `
<DateTimeRange
  appliedFilterIndex={0}
  columnIndex={0}
  filter={
    Object {
      "appliedFilters": Array [
        Object {
          "comparatorSelect": Object {
            "value": "",
          },
          "filterSelect": Object {
            "value": "",
          },
          "operatorSelect": Object {
            "value": "",
          },
        },
      ],
      "comparatorSelectOptions": Array [
        Object {
          "label": "AND",
          "value": "AND",
        },
        Object {
          "label": "OR",
          "value": "OR",
        },
      ],
      "filterTypes": Array [
        Object {
          "label": "between",
          "value": "between",
        },
        Object {
          "label": "equals",
          "value": "equals",
        },
      ],
      "firstFilterBlock": Object {
        "filter": Object {
          "operator": "and",
          "value": Object {
            "firstValue": "0",
            "secondValue": "60",
          },
        },
        "operator": Object {
          "label": "between",
          "value": "between",
        },
      },
      "id": "OREntryDT",
      "label": "OR Entry Date And Time",
      "type": "NumberWithUnit",
      "unit": "feet",
      "value": "12/24/2024",
    }
  }
  handleInputChange={[Function]}
  inputValue={
    Object {
      "firstDate": Date { NaN },
      "firstTime": null,
      "secondDate": Date { NaN },
      "secondTime": null,
    }
  }
/>
`;

exports[`DateTime it renders equals component correctly 1`] = `
<div
  className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-2 tw-self-center tw-align-middle"
>
  <DateTimeField
    dateValue={2010-12-24T00:00:00.000Z}
    handleDateChange={[Function]}
    handleTimeChange={[Function]}
    timeValue={null}
  />
</div>
`;
