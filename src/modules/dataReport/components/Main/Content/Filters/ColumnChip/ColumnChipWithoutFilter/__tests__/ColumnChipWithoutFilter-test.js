import { create, act } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import ColumnChipWithoutFilter from "..";

const columnMock = { label: "Column 1", value: "Column 1" };

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: ColumnChipWithoutFilter,
      initialAppValues: {
        dataReport: {
          filters: []
        }
      },
      props
    })
  );

describe("ColumnChipWithoutFilter", () => {
  test("renders the component correctly", () => {
    const onClick = jest.fn();
    const component = mockedComponent({
      column: columnMock,
      onRemove: onClick
    });

    expect(component).toMatchSnapshot();
  });
  test("calls onClick event when icon is clicked", () => {
    const onClick = jest.fn();
    const component = mockedComponent({
      column: columnMock,
      onRemove: onClick
    });
    const instance = component.root;
    const [icon] = instance.findAllByType("i");

    act(() => icon.props.onClick());
    expect(onClick).toHaveBeenCalled();
  });
});
