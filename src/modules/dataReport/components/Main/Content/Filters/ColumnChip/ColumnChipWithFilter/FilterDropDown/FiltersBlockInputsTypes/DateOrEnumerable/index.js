import DateRange from "../shared/DateRange";
import UnknownOrDateRadio from "./UnknownOrDateRadio";

const DateOrEnumerable = ({
  appliedFilterIndex,
  columnIndex,
  filter,
  operatorValue
}) =>
  operatorValue.value === "between" ? (
    <DateRange
      appliedFilterIndex={appliedFilterIndex}
      columnIndex={columnIndex}
      filter={filter}
      stacked
    />
  ) : (
    <UnknownOrDateRadio
      appliedFilterIndex={appliedFilterIndex}
      columnIndex={columnIndex}
      filter={filter}
    />
  );

export default DateOrEnumerable;
