import { DateInput, RadioButton } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

export const UnknownOrDateRadio = ({
  appliedFilterIndex,
  columnIndex,
  filter
}) => {
  const {
    minDate,
    date,
    handleDateInputChange,
    handleRadioChange,
    selectedFilterType
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    filter
  });

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      <RadioButton
        name={`filter-${appliedFilterIndex}-${columnIndex}`}
        value="unknown"
        label="Unknown"
        checked={selectedFilterType === "unknown"}
        onChange={() => handleRadioChange("unknown")}
      />
      <div className="tw-flex tw-w-full tw-items-center tw-gap-2">
        <RadioButton
          name={`filter-${appliedFilterIndex}-${columnIndex}`}
          value="date"
          checked={selectedFilterType === "date"}
          onChange={() => handleRadioChange("date")}
        />
        <div className="tw-flex-1">
          <DateInput
            minDate={minDate}
            placeholder="Select Date"
            value={date}
            onChange={handleDateInputChange}
            disabled={selectedFilterType === "unknown"}
          />
        </div>
      </div>
    </div>
  );
};

export default UnknownOrDateRadio;
