import { determineFilterType } from "modules/dataReport/components/Main/Content/ReportBuilder/ReportDetails/FilterBlockRows/shared/determineFilterType";
import { __, propOr } from "ramda";
import Date from "../FiltersBlockInputsTypes/Date";
import DateOrEnumerable from "../FiltersBlockInputsTypes/DateOrEnumerable";
import DateTime from "../FiltersBlockInputsTypes/DateTime";
import DependentExternalEnumerableSearchable from "../FiltersBlockInputsTypes/DependentExternalEnumerableSearchable";
import EnumerableOrOpen from "../FiltersBlockInputsTypes/EnumerableOrOpen";
import EnumsDropDown from "../FiltersBlockInputsTypes/EnumsDropDown";
import Integer from "../FiltersBlockInputsTypes/Integer";
import NumberOrEnumerable from "../FiltersBlockInputsTypes/NumberOrEnumerable";
import NumberWithUnit from "../FiltersBlockInputsTypes/NumberWithUnit";
import Open from "../FiltersBlockInputsTypes/Open";
import Calculated from "../FiltersBlockInputsTypes/Calculated";
import { FirstInputDropdown } from "./FirstInputDropdown";
import { useComponentLogic } from "./hooks";

function Unimplemented() {
  return <div className="tw-text-error-500">Field Unimplemented</div>;
}

const type = propOr(Unimplemented, __, {
  Date,
  DateOrEnumerable,
  DependentExternalEnumerableSearchable,
  DependentExternalEnumerableSearchableFavoritable: EnumsDropDown,
  DependentFilteredEnumerableSearchable: EnumsDropDown,
  Enumerable: EnumsDropDown,
  EnumerableCollection: EnumsDropDown,
  EnumerableOrOpen,
  EnumerableSearchable: EnumsDropDown,
  EnumerableSearchableFavoritable: EnumsDropDown,
  Number: Integer,
  NumberOrEnumerable,
  NumberSearchable: EnumsDropDown,
  NumberSearchableFavoritable: EnumsDropDown,
  NumberWithUnit,
  RepeatableEnumerable: EnumsDropDown,
  Open,
  Generated: Open,
  Calculated,
  DateTime
});

export const FirstFilterBlock = ({ filter, column, columnIndex }) => {
  const { firstOperatorValue, handleFirstOperatorChange } = useComponentLogic({
    column,
    columnIndex
  });
  const GeneratedFilterComponent = type(determineFilterType(column));

  return (
    <div className="tw-flex tw-flex-col tw-gap-4 tw-p-5">
      <FirstInputDropdown
        column={column}
        filter={filter}
        firstOperatorValue={firstOperatorValue}
        handleFirstOperatorChange={handleFirstOperatorChange}
      />
      {firstOperatorValue && (
        <GeneratedFilterComponent
          appliedFilterIndex={0}
          columnIndex={columnIndex}
          customContainerClassNames="tw-w-full tw-font-inter"
          filter={filter}
          operatorValue={firstOperatorValue}
        />
      )}
    </div>
  );
};

export default FirstFilterBlock;
