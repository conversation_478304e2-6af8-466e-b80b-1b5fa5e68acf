import { Input, RadioButton } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const UnknownOrNumberRadio = ({ appliedFilterIndex, columnIndex, filter }) => {
  const {
    handleRadioChange,
    handleNumberInputChange,
    secondFilterValue,
    radioName,
    isUnknown,
    hasNumberValue,
    isNumberChecked
  } = useComponentLogic({
    appliedFilterIndex,
    columnIndex,
    filter
  });

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      <RadioButton
        name={radioName}
        value="unknown"
        label="Unknown"
        checked={isUnknown && hasNumberValue}
        onChange={() => handleRadioChange("unknown")}
      />
      <div className="tw-flex tw-w-full tw-items-center tw-gap-2">
        <RadioButton
          name={radioName}
          value="number"
          checked={isNumberChecked}
          onChange={() => handleRadioChange("number")}
        />
        <div className="tw-flex-1">
          <Input
            disabled={isUnknown && hasNumberValue}
            type="number"
            value={secondFilterValue}
            onChange={handleNumberInputChange}
          />
        </div>
      </div>
    </div>
  );
};

export default UnknownOrNumberRadio;
