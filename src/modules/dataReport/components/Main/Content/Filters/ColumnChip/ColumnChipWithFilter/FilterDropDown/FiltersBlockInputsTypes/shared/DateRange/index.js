import { DateInput } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const DateRange = ({
  appliedFilterIndex,
  columnIndex,
  filter,
  stacked = false
}) => {
  const {
    startDate,
    endDate,
    handleDateInputChangeStart,
    handleDateInputChangeEnd
  } = useComponentLogic({ appliedFilterIndex, columnIndex, filter });

  return (
    <div
      className={stacked ? "tw-flex tw-flex-col" : "tw-flex tw-w-full tw-gap-2"}
    >
      <DateInput
        selectsStart
        endDate={endDate}
        maxDate={endDate}
        placeholder="Select Date"
        value={startDate}
        onChange={handleDateInputChangeStart}
      />

      {stacked && (
        <div className="tw-flex tw-items-center tw-justify-center tw-py-2">
          <div className="tw-flex tw-h-[24px] tw-w-[50px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-2 tw-py-0.5 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700">
            and
          </div>
        </div>
      )}

      <DateInput
        selectsEnd
        minDate={startDate}
        placeholder="Select Date"
        startDate={startDate}
        value={endDate}
        onChange={handleDateInputChangeEnd}
      />
    </div>
  );
};

export default DateRange;
