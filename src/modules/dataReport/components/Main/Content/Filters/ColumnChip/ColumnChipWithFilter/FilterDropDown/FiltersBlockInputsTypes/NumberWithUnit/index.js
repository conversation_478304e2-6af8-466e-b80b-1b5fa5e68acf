import { Input } from "@q-centrix/q-components-react";
import { convertUnitDisplay } from "modules/question/components/Question/NumberWithUnit/convertUnit";
import { useComponentLogic } from "./hooks";

const NumberWithUnit = props => {
  const { operatorValue } = props;
  const { inputValue, handleInputValueChange, question } =
    useComponentLogic(props);

  if (operatorValue.value === "equals") {
    return (
      <div className="tw-flex tw-w-full tw-flex-col tw-gap-[5px]">
        <Input
          inputClassName="tw-w-full tw-h-[35px]"
          type="number"
          value={inputValue.firstValue}
          onChange={handleInputValueChange("firstValue")}
          min={0}
        />
        <span className="tw-pl-1">
          {convertUnitDisplay(question, inputValue.firstValue)}
        </span>
      </div>
    );
  }

  return (
    <div className="tw-flex tw-flex-col tw-gap-[8px]">
      <div className="tw-flex tw-w-full tw-items-center tw-gap-[8px]">
        <Input
          inputClassName="tw-w-full tw-h-[35px]"
          type="number"
          value={inputValue.firstValue}
          onChange={handleInputValueChange("firstValue")}
          min={0}
        />
        <div className="tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700">
          and
        </div>
        <Input
          inputClassName="tw-w-full tw-h-[35px]"
          type="number"
          value={inputValue.secondValue}
          onChange={handleInputValueChange("secondValue")}
          min={0}
        />
      </div>
      <div className="tw-flex tw-justify-between">
        <span className="tw-pl-1">
          {convertUnitDisplay(question, inputValue.firstValue)}
        </span>
        <span className="tw-pl-1">
          {convertUnitDisplay(question, inputValue.secondValue)}
        </span>
      </div>
    </div>
  );
};

export default NumberWithUnit;
