import { InputDropdown } from "@q-centrix/q-components-react";
import { determineFilterType } from "modules/dataReport/components/Main/Content/ReportBuilder/ReportDetails/FilterBlockRows/shared/determineFilterType";
import { __, propOr } from "ramda";
import Date from "../FiltersBlockInputsTypes/Date";
import DateOrEnumerable from "../FiltersBlockInputsTypes/DateOrEnumerable";
import DateTime from "../FiltersBlockInputsTypes/DateTime";
import DependentExternalEnumerableSearchable from "../FiltersBlockInputsTypes/DependentExternalEnumerableSearchable";
import EnumerableOrOpen from "../FiltersBlockInputsTypes/EnumerableOrOpen";
import EnumsDropDown from "../FiltersBlockInputsTypes/EnumsDropDown";
import Integer from "../FiltersBlockInputsTypes/Integer";
import NumberOrEnumerable from "../FiltersBlockInputsTypes/NumberOrEnumerable";
import NumberWithUnit from "../FiltersBlockInputsTypes/NumberWithUnit";
import Open from "../FiltersBlockInputsTypes/Open";
import Calculated from "../FiltersBlockInputsTypes/Calculated";
import useComponentLogic from "./hooks";

function Unimplemented() {
  return <div className="tw-text-error-500">Field Unimplemented</div>;
}

const type = propOr(Unimplemented, __, {
  Date,
  DateTime,
  DateOrEnumerable,
  DependentExternalEnumerableSearchable,
  DependentFilteredEnumerableSearchable: EnumsDropDown,
  Enumerable: EnumsDropDown,
  EnumerableCollection: EnumsDropDown,
  EnumerableOrOpen,
  EnumerableSearchable: EnumsDropDown,
  EnumerableSearchableFavoritable: EnumsDropDown,
  Number: Integer,
  NumberOrEnumerable,
  NumberSearchable: EnumsDropDown,
  NumberSearchableFavoritable: EnumsDropDown,
  NumberWithUnit,
  RepeatableEnumerable: EnumsDropDown,
  Open,
  Generated: Open,
  Calculated
});

export const FilterBlock = ({
  appliedFilterIndex,
  filter,
  columnIndex,
  column
}) => {
  const {
    handleComparatorValueChange,
    handleOperatorValueChange,
    comparatorValue,
    operatorValue
  } = useComponentLogic({ appliedFilterIndex, column, columnIndex });

  const GeneratedFilterComponent = type(determineFilterType(column));

  return (
    <div className="last:tw-border-bottom tw-flex tw-flex-col tw-gap-4 tw-bg-qcIris-50 tw-p-5 last:tw-rounded-b-[5px] last:tw-border-qcIris-700 last:tw-bg-qcIris-200">
      <div className="tw-flex tw-gap-2">
        <InputDropdown
          containerClassName="tw-font-inter tw-w-[100px]"
          iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
          inputClassName="tw-text-black-70"
          isSearchable={false}
          options={filter.comparatorSelectOptions}
          placeholder="Select"
          value={comparatorValue}
          onChange={handleComparatorValueChange}
        />

        <InputDropdown
          className="tw-w-full"
          containerClassName="tw-font-inter tw-w-[226px]"
          iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
          inputClassName="tw-text-black-70"
          isSearchable={false}
          options={filter.filterTypes}
          placeholder="Select Filter"
          value={operatorValue}
          onChange={handleOperatorValueChange}
        />
      </div>
      {comparatorValue && operatorValue && (
        <GeneratedFilterComponent
          appliedFilterIndex={appliedFilterIndex + 1}
          column={column}
          columnIndex={columnIndex}
          customContainerClassNames="tw-w-full"
          filter={filter}
          operatorValue={operatorValue}
        />
      )}
    </div>
  );
};

export default FilterBlock;
