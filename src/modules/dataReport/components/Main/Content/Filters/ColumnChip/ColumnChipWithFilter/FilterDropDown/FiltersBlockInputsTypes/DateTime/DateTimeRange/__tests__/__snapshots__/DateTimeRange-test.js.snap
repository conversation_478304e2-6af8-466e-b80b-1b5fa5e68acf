// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateTimeRange it renders equals component correctly 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-2"
>
  <DateTimeField
    dateValue="2025-03-17T07:00:00.000Z"
    timeValue="2025-03-04T19:00:02.189Z"
  />
  <div
    className="tw-justify-center tw-self-center tw-align-middle"
  >
    <div
      className="tw-mx-3 tw-flex tw-w-[50px] tw-cursor-default tw-flex-row tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700"
    >
      and
    </div>
  </div>
  <div
    className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-2 tw-self-center tw-align-middle"
  >
    <DateTimeField
      dateValue="2025-03-24T07:00:00.000Z"
      timeValue="2025-03-04T19:30:05.228Z"
    />
  </div>
</div>
`;
