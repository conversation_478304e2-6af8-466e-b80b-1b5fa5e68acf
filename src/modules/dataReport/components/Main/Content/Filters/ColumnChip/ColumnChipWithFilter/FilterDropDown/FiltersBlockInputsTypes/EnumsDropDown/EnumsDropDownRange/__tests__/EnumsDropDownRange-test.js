import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import EnumsDropDownRange from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

const filterMock = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        value: ""
      },
      filterSelect: {
        value: ""
      },
      operatorSelect: {
        value: ""
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "between",
      value: "between"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "C100",
      value: "C100"
    },
    operator: {
      label: "between",
      value: "between"
    }
  },
  id: "birthplaceState",
  label: "Birthplace--State",
  mainSelectOptions: {},
  type: "EnumerableSearchable",
  value: "birthplaceState"
};
const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: EnumsDropDownRange,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: { selectedRegistry: "Oncology" }
      },
      props
    })
  );

describe("EnumsDropDownRange", () => {
  test("it renders component correctly with range", () => {
    const component = mockedComponent({
      appliedFilterIndex: 0,
      columnIndex: 0,
      filter: filterMock
    });

    expect(component).toMatchSnapshot();
  });
});
