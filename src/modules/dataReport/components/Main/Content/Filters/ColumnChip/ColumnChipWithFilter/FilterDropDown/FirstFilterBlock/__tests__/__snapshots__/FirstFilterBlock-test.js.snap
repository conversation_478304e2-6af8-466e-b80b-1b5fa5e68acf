// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FirstFilterBlock it renders correctly with 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4 tw-p-5"
>
  <InputDropdown
    clearIndicatorClass="tw-text-qcNeutrals-400"
    clearable={true}
    containerClassName="tw-font-inter"
    iconClass="fa-solid fa-chevron-down tw-text-qcInfo-700"
    indicatorSeparatorStyles={
      Object {
        "marginRight": "10px",
      }
    }
    inputClassName="tw-text-black-70"
    labelClassName="tw-text-sm tw-text-black-70"
    menuStyles={
      Object {
        "position": "relative",
      }
    }
    onChange={[Function]}
    placeholder="Select filter value for Filter 1"
    value={null}
  />
</div>
`;
