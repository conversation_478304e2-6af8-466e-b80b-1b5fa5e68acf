// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UnknownOrDateRadio it handles empty filter value correctly 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <RadioButton
    checked={false}
    label="Unknown"
    name="filter-0-0"
    onChange={[Function]}
    value="unknown"
  />
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-2"
  >
    <RadioButton
      checked={false}
      name="filter-0-0"
      onChange={[Function]}
      value="date"
    />
    <div
      className="tw-flex-1"
    >
      <DateInput
        disabled={false}
        placeholder="Select Date"
      />
    </div>
  </div>
</div>
`;

exports[`UnknownOrDateRadio it renders correctly with 'date' selected 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <RadioButton
    checked={false}
    label="Unknown"
    name="filter-0-0"
    onChange={[Function]}
    value="unknown"
  />
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-2"
  >
    <RadioButton
      checked={true}
      name="filter-0-0"
      onChange={[Function]}
      value="date"
    />
    <div
      className="tw-flex-1"
    >
      <DateInput
        disabled={false}
        placeholder="Select Date"
      />
    </div>
  </div>
</div>
`;

exports[`UnknownOrDateRadio it renders correctly with 'unknown' selected 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <RadioButton
    checked={true}
    label="Unknown"
    name="filter-0-0"
    onChange={[Function]}
    value="unknown"
  />
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-2"
  >
    <RadioButton
      checked={false}
      name="filter-0-0"
      onChange={[Function]}
      value="date"
    />
    <div
      className="tw-flex-1"
    >
      <DateInput
        disabled={true}
        placeholder="Select Date"
      />
    </div>
  </div>
</div>
`;
