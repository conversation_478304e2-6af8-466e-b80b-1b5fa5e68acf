// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NumberWithUnit it renders component between correctly 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-[8px]"
>
  <div
    className="tw-flex tw-w-full tw-items-center tw-gap-[8px]"
  >
    <Input
      inputClassName="tw-w-full tw-h-[35px]"
      min={0}
      onChange={[Function]}
      type="number"
      value="0"
    />
    <div
      className="tw-mx-3 tw-flex tw-cursor-pointer tw-items-center tw-rounded-full tw-border-2 tw-border-qcInfo-700 tw-bg-qcInfo-100 tw-px-3 tw-py-1 tw-text-xs tw-font-semibold tw-leading-4 tw-text-qcInfo-700"
    >
      and
    </div>
    <Input
      inputClassName="tw-w-full tw-h-[35px]"
      min={0}
      onChange={[Function]}
      type="number"
      value="60"
    />
  </div>
  <div
    className="tw-flex tw-justify-between"
  >
    <span
      className="tw-pl-1"
    >
      0"
    </span>
    <span
      className="tw-pl-1"
    >
      5' 0"
    </span>
  </div>
</div>
`;

exports[`NumberWithUnit it renders equals component correctly 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-gap-[5px]"
>
  <Input
    inputClassName="tw-w-full tw-h-[35px]"
    min={0}
    onChange={[Function]}
    type="number"
    value="72"
  />
  <span
    className="tw-pl-1"
  >
    6' 0"
  </span>
</div>
`;
