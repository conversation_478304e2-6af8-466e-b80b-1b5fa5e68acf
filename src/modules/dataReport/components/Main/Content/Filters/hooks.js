import { useQuery, useMutation } from "@apollo/client";
import {
  GET_AVAILABLE_REGISTRIES,
  GET_SECTIONS
} from "modules/dataReport/graphql/query";
import { GENERATE_REPORT_PREVIEW_COUNTS } from "modules/dataReport/graphql/mutation";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  clearFilters,
  resetBuilder,
  setColumnHeaders,
  setGeneratedReport,
  setReportName,
  setSectionTypeState,
  setSelectedCourseValue,
  setSelectedRegistry,
  setGenerateReportPreviewCounts,
  setGeneratedReportLoading,
  initializeFilter
} from "modules/dataReport/redux/slices";
import {
  all,
  always,
  and,
  any,
  append,
  applySpec,
  both,
  complement,
  concat,
  cond,
  either,
  equals,
  filter,
  find,
  forEach,
  hasPath,
  head,
  identity,
  ifElse,
  includes,
  is,
  length,
  map,
  none,
  path,
  pipe,
  prop,
  propEq,
  propOr,
  props,
  reject,
  T,
  tap,
  when,
  isEmpty,
  pathOr
} from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp/isNullOrEmpty";
import { isInvalidReportId } from "../ReportBuilder/hooks";
import { formatFilterAttrs } from "modules/dataReport/utils/formatFilterAttrs";

const fieldLabelWithOptionalSeqNo = pipe(
  props(["prompt", "seqNo"]),
  ([prompt, seqNo]) => (seqNo ? `${prompt} (${seqNo})` : prompt)
);

const getColumnOptions = (section, sections = [], selectedRegistry = null) =>
  pipe(
    find(propEq("name", section)),
    propOr([], "fields"),
    map(
      pipe(
        applySpec({
          filterTypes: pipe(
            propOr([], "filterTypes"),
            map(
              applySpec({
                label: identity,
                value: identity
              })
            )
          ),
          id: prop("eid"),
          label: ifElse(
            () => equals(selectedRegistry, "Adult Cardiac"),
            fieldLabelWithOptionalSeqNo,
            prop("prompt")
          ),
          type: prop("type"),
          value: prop("eid"),
          unit: propOr(null, "unit"),
          typeOverride: prop("typeOverride")
        }),
        filter(isNotNullOrEmpty)
      )
    )
  )(sections);

const getPrimarySiteFilter = find(propEq("id", "primarySite"));
const getPrimarySiteValues = applySpec({
  filter: path(["filterSelect", "value"]),
  operator: path(["operatorSelect", "value"])
});

const filterHasValue = pipe(path(["filterSelect", "value"]), isNotNullOrEmpty);

const filterIsEquals = pipe(
  path(["operatorSelect", "value"]),
  equals("equals")
);

const getValidPrimarySiteValues = pipe(
  getPrimarySiteFilter,
  filterValue =>
    append(
      applySpec({
        filterSelect: path(["firstFilterBlock", "filter"]),
        operatorSelect: path(["firstFilterBlock", "operator"])
      })(filterValue),
      pipe(prop("appliedFilters"))(filterValue)
    ),
  filter(
    pipe(
      prop("filterSelect"),
      cond([
        [Array.isArray, any(prop("value"))],
        [
          is(Object),
          ifElse(
            hasPath(["value", "firstValue"]),
            either(
              path(["value", "firstValue"]),
              path(["value", "secondValue"])
            ),
            prop("value")
          )
        ],
        [T, always(null)]
      ])
    )
  )
);

const showDfesFields = pipe(
  getValidPrimarySiteValues,
  ifElse(
    pipe(length, equals(1)),
    pipe(head, both(filterIsEquals, filterHasValue)),
    always(false)
  )
);

const removeDfesFields = reject(
  propEq("type", "DependentFilteredEnumerableSearchable")
);
const removeDeesFields = reject(
  propEq("type", "DependentExternalEnumerableSearchable")
);
const getDfesFields = filter(
  propEq("type", "DependentFilteredEnumerableSearchable")
);
const getDeesFields = filter(
  propEq("type", "DependentExternalEnumerableSearchable")
);

const removeHeader = (headerValue, columnSetterFn, dispatchFn) => {
  columnSetterFn(filter(complement(propEq("value", headerValue))));
  dispatchFn(clearFilters({ columnId: headerValue }));
};

const REQUIRED_DEES_FIELDS = [
  "primarySite",
  "morphTypebehavIcdO3",
  "dateOfDiagnosis"
];

const filterExists = (column, filters) =>
  any(propEq("id", propOr(null, "id", column)), filters);

const getRegistryValue = (selectedRegistryValue, registry) =>
  when(complement(is(String)), () =>
    typeof registry?.value === "string" ? registry.value : null
  )(selectedRegistryValue);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({ selectedColumns, setSelectedColumns }) => {
  const filters = useSelector(ReportSelectors.getFilters);
  const dispatch = useDispatch();
  const [staggerAnimation, setStaggerAnimation] = useState(false);
  const [sectionOptions, setSectionOptions] = useState([]);
  const [columnOptions, setColumnOptions] = useState([]);
  const [columnType, setColumnType] = useState(null);
  const [sectionType, setSectionType] = useState(null);
  const [reportPreviewGenerated, setReportPreviewGenerated] = useState(false);
  const [registryOptions, setRegistryOptions] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [columnToRemove, setColumnToRemove] = useState("");

  const shouldShowDfesFields = useMemo(
    () => showDfesFields(filters),
    [filters]
  );

  const shouldShowDeesFields = useMemo(
    () =>
      !isNullOrEmpty(filters) &&
      all(field => any(propEq("id", field), filters))(REQUIRED_DEES_FIELDS),
    [filters]
  );

  const primaryFilterValues = useMemo(
    () =>
      pipe(
        getValidPrimarySiteValues,
        ifElse(
          () => shouldShowDfesFields,
          pipe(head, getPrimarySiteValues),
          always(null)
        )
      )(filters),
    [filters, shouldShowDfesFields]
  );

  const reportName = useSelector(ReportSelectors.getReportName);
  const [reportNameErrorStatus, setReportNameErrorStatus] = useState(false);
  const [, setloadedReportId] = useState(null);
  const { reportId } = useParams();
  const generatedReport = useSelector(ReportSelectors.getGeneratedReport);
  const selectedRegistry = useSelector(ReportSelectors.getSelectedRegistry);

  const getFirstRegistry = pathOr(null, ["sections", 0, "registry"]);

  const isReportIdNumeric = !isNaN(reportId);

  const shouldRunSectionsQuery =
    isReportIdNumeric || isNotNullOrEmpty(selectedRegistry);

  const sectionQueryVariables = useMemo(
    cond([
      [() => isReportIdNumeric, () => ({ onDemandReportId: reportId })],
      [
        () => isNotNullOrEmpty(selectedRegistry),
        () => ({ registry: selectedRegistry })
      ],
      [T, always({})]
    ]),
    [isReportIdNumeric, reportId, selectedRegistry]
  );

  const { loading, data: sectionsData = { sections: [] } } = useQuery(
    GET_SECTIONS,
    {
      skip: !shouldRunSectionsQuery,
      variables: sectionQueryVariables,
      onCompleted: () => {
        setSectionOptions(
          map(
            applySpec({
              label: prop("name"),
              value: prop("name")
            })
          )(sectionsData?.sections)
        );
      }
    }
  );

  const registry = useMemo(() => {
    const sectionsRegistry = getFirstRegistry(sectionsData);

    const registryToUse = isReportIdNumeric
      ? sectionsRegistry
      : selectedRegistry;

    return isNullOrEmpty(registryToUse)
      ? null
      : {
          label: registryToUse,
          value: registryToUse
        };
  }, [reportId, selectedRegistry, sectionsData]);

  const [generateReportPreviewCounts] = useMutation(
    GENERATE_REPORT_PREVIEW_COUNTS,
    {
      onCompleted: data => {
        dispatch(setGeneratedReportLoading(false));
        if (data) {
          dispatch(setGenerateReportPreviewCounts(data));
        }
      }
    }
  );

  const { data: registryData } = useQuery(GET_AVAILABLE_REGISTRIES, {
    onCompleted: () => {
      setRegistryOptions(
        map(
          applySpec({
            label: prop("title"),
            value: prop("title")
          })
        )(registryData?.availableRegistries)
      );
    }
  });

  const handleReportNameChange = useCallback(event => {
    setReportNameErrorStatus(false);
    dispatch(setReportName({ reportName: event.target.value }));
  }, []);

  const selectedCourseValue = useSelector(
    ReportSelectors.getSelectedCourseValue
  );

  const handleRadioChange = useCallback(
    value => {
      dispatch(setSelectedCourseValue(value));
    },
    [dispatch]
  );

  const handleSectionType = value => {
    setSectionType(value);
    setColumnOptions(
      getColumnOptions(value?.label, sectionsData?.sections, selectedRegistry)
    );
  };

  const handleRegistrySelection = useCallback(
    value => {
      dispatch(setSelectedRegistry(value));
      setColumnType(null);
      setSectionType(null);
    },
    [dispatch]
  );

  const handleColumnType = useCallback(
    value => {
      if (!filterExists(value, filters)) {
        dispatch(initializeFilter(value));
      }
      setSelectedColumns(
        when(
          both(always(value), none(propEq("value", value.value))),
          pipe(
            tap(() => setColumnType(value)),
            append(value)
          )
        )
      );
    },
    [dispatch, filters]
  );

  const handleRemoveDependentFields = useCallback(
    columnId => {
      setIsModalOpen(false);
      pipe(
        when(
          () => equals("primarySite", columnId),
          concat(getDfesFields(selectedColumns))
        ),
        when(
          () => includes(columnId, REQUIRED_DEES_FIELDS),
          concat(getDeesFields(selectedColumns))
        ),
        forEach(column => {
          dispatch(clearFilters({ columnId: column.id }));
        })
      )([]);

      const removeDependentFields = pipe(
        when(() => equals("primarySite", columnId), removeDfesFields),
        when(() => includes(columnId, REQUIRED_DEES_FIELDS), removeDeesFields)
      );

      setSelectedColumns(removeDependentFields);
      removeHeader(columnId, setSelectedColumns, dispatch);
    },
    [selectedColumns, dispatch]
  );

  const handleRemoveColumnHeader = useCallback(
    columnId => {
      const currentColumn = pipe(
        find(propEq("id", columnId)),
        applySpec({
          id: prop("id"),
          label: prop("label")
        })
      )(selectedColumns);

      const confirmDependentColumnDeletion = both(
        () =>
          includes(columnId, [
            "primarySite",
            "morphTypebehavIcdO3",
            "dateOfDiagnosis"
          ]),
        () =>
          either(
            any(propEq("type", "DependentExternalEnumerableSearchable")),
            any(propEq("type", "DependentFilteredEnumerableSearchable"))
          )(selectedColumns)
      )();

      if (confirmDependentColumnDeletion) {
        setColumnToRemove(currentColumn);
        setIsModalOpen(true);
      } else {
        removeHeader(columnId, setSelectedColumns, dispatch);
      }
    },
    [shouldShowDfesFields, shouldShowDeesFields, selectedColumns]
  );

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setColumnToRemove("");
  }, []);

  const repeatableFlags = isEmpty(selectedCourseValue)
    ? null
    : Number(selectedCourseValue);

  const registryValue = useMemo(
    () => getRegistryValue(selectedRegistry, registry),
    [selectedRegistry, registry]
  );

  // eslint-disable-next-line complexity, max-statements
  const handleFormSubmission = event => {
    event.preventDefault();
    if (!reportName) {
      setReportNameErrorStatus(true);
      return;
    }
    dispatch(setGeneratedReportLoading(true));
    setReportPreviewGenerated(true);
    dispatch(setColumnHeaders());
    dispatch(setGeneratedReport({ reportName, sectionType }));
    dispatch(setSectionTypeState(sectionType));

    const finalRegistryValue = isNotNullOrEmpty(selectedRegistry)
      ? selectedRegistry
      : registry?.value;

    if (
      isNotNullOrEmpty(sectionsData?.sections) &&
      isNotNullOrEmpty(finalRegistryValue)
    ) {
      generateReportPreviewCounts({
        variables: {
          registry: registryValue,
          selectedColumns: map(prop("value"))(selectedColumns),
          filterAttrs: formatFilterAttrs(filters),
          repeatableFlags
        }
      });
    }
  };

  useEffect(() => {
    if (and(!isInvalidReportId(reportId), !isNullOrEmpty(generatedReport))) {
      setloadedReportId(currentId => {
        if (currentId !== reportId && generatedReport.id === reportId) {
          handleSectionType({
            label: generatedReport.selectedSection,
            value: generatedReport.selectedSection
          });
          setSelectedColumns([]);
          forEach(handleColumnType, generatedReport.filters);
          setReportPreviewGenerated(true);

          // Timeout is necessary to ensure stagger animation fires after DOM renders fully
          setTimeout(() => setStaggerAnimation(true), 25);

          return reportId;
        }

        return currentId;
      });
    } else if (isInvalidReportId(reportId)) {
      setloadedReportId(currentId => {
        if (currentId !== reportId) {
          setSelectedColumns([]);
          setSectionType(null);
          setColumnType(null);
          setReportPreviewGenerated(false);
          dispatch(resetBuilder());
          return reportId;
        }

        setStaggerAnimation(true);

        return currentId;
      });
    }
  }, [
    generatedReport,
    reportId,
    setSelectedColumns,
    handleSectionType,
    handleColumnType
  ]);

  const isDisabled = isNullOrEmpty(registry);

  // eslint-disable-next-line complexity
  useEffect(() => {
    const hasSections = isNotNullOrEmpty(sectionsData?.sections);

    if (
      isReportIdNumeric &&
      isNotNullOrEmpty(generatedReport) &&
      hasSections &&
      isNotNullOrEmpty(registryValue)
    ) {
      generateReportPreviewCounts({
        variables: {
          registry: registryValue,
          selectedColumns: prop("columns", generatedReport) || [],
          filterAttrs: formatFilterAttrs(filters),
          repeatableFlags
        }
      });
    }
  }, [
    reportId,
    generatedReport,
    sectionsData,
    registry,
    selectedRegistry,
    filters,
    selectedCourseValue
  ]);

  return {
    staggerAnimation,
    columnType,
    columnTypesOptions: columnOptions,
    handleColumnType,
    handleFormSubmission,
    handleRadioChange,
    handleRegistrySelection,
    handleRemoveColumnHeader,
    handleRemoveDependentFields,
    handleReportNameChange,
    handleSectionType,
    isDisabled,
    isModalOpen,
    primaryFilterValues,
    registry,
    columnToRemove,
    registryOptions,
    handleCloseModal,
    reportName,
    reportNameErrorStatus,
    reportPreviewGenerated,
    sectionsLoading: loading,
    sectionType,
    sectionTypesOptions: sectionOptions,
    selectedCourseValue,
    setIsModalOpen
  };
};
