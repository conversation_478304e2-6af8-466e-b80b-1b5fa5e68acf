import { Button, Input, InputDropdown } from "@q-centrix/q-components-react";
import { useElementFocusOnKeyDown } from "modules/dataReport/hooks/useElementFocusonKeyDown";
import { motion } from "framer-motion";
import { useCheckDependentFilters } from "modules/dataReport/hooks/useCheckDependentFilters";
import { ColumnChip } from "./ColumnChip";
import CourseRadioButtons from "./CourseRadioButtons";
import { useComponentLogic } from "./hooks";

const variants = {
  open: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07,
      delayChildren: 0.2
    }
  },
  closed: {
    opacity: 0
  }
};

const Filters = ({ selectedColumns, setSelectedColumns }) => {
  const {
    staggerAnimation,
    handleRemoveDependentFields,
    reportName,
    reportPreviewGenerated,
    handleReportNameChange,
    sectionTypesOptions,
    sectionsLoading,
    selectedCourseValue,
    handleRadioChange,
    sectionType,
    handleSectionType,
    columnTypesOptions,
    currentColumnValue,
    handleColumnType,
    reportNameErrorStatus,
    handleRemoveColumnHeader,
    handleFormSubmission,
    registryOptions,
    handleRegistrySelection,
    registry,
    isDisabled,
    isModalOpen,
    setIsModalOpen,
    primaryFilterValues,
    columnToRemove
  } = useComponentLogic({ selectedColumns, setSelectedColumns });

  useCheckDependentFilters();

  const { registerForKeyDownFocus } = useElementFocusOnKeyDown();

  return (
    <form
      className="tw-scrollbar-gutter-stable tw-absolute tw-top-0 tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-overflow-y-auto tw-bg-qc-blue-50"
      id="onDemand-override"
    >
      <div className="tw-flex tw-flex-grow tw-flex-col tw-gap-3 tw-p-5">
        <h3 className="tw-text-black-70 tw-text-xl tw-font-semibold tw-text-qc-blue-800">
          Create New Report
        </h3>
        <InputDropdown
          ref={registerForKeyDownFocus}
          containerClassName="tw-font-inter"
          iconClass="fa-solid fa-chevron-down"
          label="Select Registry"
          id="select_registry"
          options={registryOptions}
          placeholder="Select registry"
          value={registry}
          onChange={handleRegistrySelection}
        />
        <Input
          ref={registerForKeyDownFocus}
          disabled={isDisabled}
          error={reportNameErrorStatus}
          errorText="Report name is required"
          label="Report Name"
          id="report_name"
          placeholder="Enter a Name for Your Report"
          type="text"
          value={reportName}
          onChange={handleReportNameChange}
        />
        <InputDropdown
          ref={registerForKeyDownFocus}
          containerClassName="tw-font-inter"
          disabled={isDisabled}
          iconClass="fa-solid fa-chevron-down"
          label="Add Section"
          id="select_section"
          loading={sectionsLoading}
          options={sectionTypesOptions}
          placeholder="Select a Section for Your Report"
          value={sectionType}
          onChange={handleSectionType}
        />
        <CourseRadioButtons
          handleRadioChange={handleRadioChange}
          isDisabled={isDisabled}
          registerForKeyDownFocus={registerForKeyDownFocus}
          registry={registry}
          selectedCourseValue={selectedCourseValue}
        />
        <InputDropdown
          ref={registerForKeyDownFocus}
          containerClassName="tw-font-inter"
          disabled={isDisabled}
          iconClass="fa-solid fa-chevron-down"
          label="Add Columns"
          id="select_column"
          options={columnTypesOptions}
          placeholder="Select a Column to Add to Your Report"
          value={currentColumnValue}
          onChange={handleColumnType}
        />
        <motion.div
          variants={variants}
          initial="closed"
          animate={staggerAnimation ? "open" : "closed"}
          className="tw-mt-1 tw-flex tw-flex-col tw-gap-1.5"
        >
          {selectedColumns.map((column, columnIndex) => (
            <ColumnChip
              key={column.value}
              column={column}
              columnIndex={columnIndex}
              columnToRemove={columnToRemove}
              handleCloseModal={() => setIsModalOpen(false)}
              handleRemoveDependentFields={handleRemoveDependentFields}
              isModalOpen={isModalOpen}
              primaryFilterValues={primaryFilterValues}
              selectedColumns={selectedColumns}
              onRemove={handleRemoveColumnHeader}
            />
          ))}
        </motion.div>
      </div>
      <div className="tw-sticky tw-bottom-0 tw-left-0 tw-z-10 tw-h-[60px] tw-w-full tw-border tw-border-qc-blue-200 tw-bg-qc-blue-25 tw-p-2.5 tw-shadow-qc-md">
        <div className="tw-flex tw-gap-2">
          <Button
            ref={registerForKeyDownFocus}
            bg="main"
            customStyle="tw-h-[35px] tw-flex tw-gap-2"
            type="submit"
            onClick={handleFormSubmission}
          >
            {reportPreviewGenerated ? (
              <>
                <i className="fa-solid fa-arrows-rotate" />
                Refresh Report Details
              </>
            ) : (
              <>
                <i className="fa-solid fa-file-export tw-mr-2 tw-opacity-80" />
                Generate Report Details
              </>
            )}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default Filters;
