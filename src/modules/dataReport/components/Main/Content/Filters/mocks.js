import {
  GET_SECTIONS,
  GET_DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES
} from "modules/dataReport/graphql/query";
import { GENERATE_REPORT_PREVIEW_COUNTS } from "modules/dataReport/graphql/mutation";

const sectionsMocks = [
  {
    request: {
      query: GET_SECTIONS,
      variables: {
        registry: "Oncology"
      }
    },
    result: {
      data: {
        sections: [
          {
            __typename: "Section",
            fields: [
              {
                __typename: "SectionField",
                eid: "nameLast",
                filterTypes: ["EQUALS"],
                prompt: "Name--Last",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "nameFirst",
                filterTypes: ["EQUALS"],
                prompt: "Name--First",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "nameMiddle",
                filterTypes: ["EQUALS"],
                prompt: "Name--Middle",
                type: "Open"
              },
              {
                __typename: "Section<PERSON>ield",
                eid: "nameMaiden",
                filterTypes: ["EQUALS"],
                prompt: "Name--Maiden",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "namePrefix",
                filterTypes: ["EQUALS"],
                prompt: "Name--Prefix",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "nameSuffix",
                filterTypes: ["EQUALS"],
                prompt: "Name--Suffix",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "nameAlias",
                filterTypes: ["EQUALS"],
                prompt: "Name--Alias",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "nameBirthSurname",
                filterTypes: ["EQUALS"],
                prompt: "Name--Birth Surname",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "nameSpouseParent",
                filterTypes: ["EQUALS"],
                prompt: "Name--Spouse/Parent",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "patientIdNumber",
                filterTypes: ["EQUALS"],
                prompt: "Patient ID Number",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "medicalRecordNumber",
                filterTypes: ["EQUALS"],
                prompt: "Medical Record Number",
                type: "EnumerableOrOpen"
              },
              {
                __typename: "SectionField",
                eid: "socialSecurityNumber",
                filterTypes: ["EQUALS"],
                prompt: "Social Security Number",
                type: "EnumerableOrOpen"
              },
              {
                __typename: "SectionField",
                eid: "telephone",
                filterTypes: ["EQUALS"],
                prompt: "Telephone",
                type: "EnumerableOrOpen"
              },
              {
                __typename: "SectionField",
                eid: "sex",
                filterTypes: ["EQUALS"],
                prompt: "Sex",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "race1",
                filterTypes: ["EQUALS"],
                prompt: "Race 1",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "race2",
                filterTypes: ["EQUALS"],
                prompt: "Race 2",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "race3",
                filterTypes: ["EQUALS"],
                prompt: "Race 3",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "race4",
                filterTypes: ["EQUALS"],
                prompt: "Race 4",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "race5",
                filterTypes: ["EQUALS"],
                prompt: "Race 5",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "spanishHispanicOrigin",
                filterTypes: ["EQUALS"],
                prompt: "Spanish/Hispanic Origin",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "birthplace",
                filterTypes: ["EQUALS"],
                prompt: "Birthplace",
                type: "EnumerableSearchableFavoritable"
              },
              {
                __typename: "SectionField",
                eid: "birthplaceCountry",
                filterTypes: ["EQUALS"],
                prompt: "Birthplace--Country",
                type: "EnumerableSearchableFavoritable"
              },
              {
                __typename: "SectionField",
                eid: "birthplaceState",
                filterTypes: ["EQUALS"],
                prompt: "Birthplace--State",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "dateOfBirth",
                filterTypes: ["STARTS WITH", "BETWEEN"],
                prompt: "Date of Birth",
                type: "Date"
              },
              {
                __typename: "SectionField",
                eid: "dateOfBirthFlag",
                filterTypes: ["EQUALS"],
                prompt: "Date of Birth Flag",
                type: "Enumerable"
              },
              {
                __typename: "SectionField",
                eid: "textUsualOccupation",
                filterTypes: [],
                prompt: "Text--Usual Occupation",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textUsualIndustry",
                filterTypes: [],
                prompt: "Text--Usual Industry",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "addrCurrentNoStreet",
                filterTypes: ["EQUALS"],
                prompt: "Addr Current--No & Street",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "addrCurrentCity",
                filterTypes: ["EQUALS"],
                prompt: "Addr Current--City",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "countyCurrent",
                filterTypes: ["between", "EQUALS"],
                prompt: "County--Current",
                type: "NumberSearchable"
              },
              {
                __typename: "SectionField",
                eid: "addrCurrentState",
                filterTypes: ["EQUALS"],
                prompt: "Addr Current--State",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "addrCurrentPostalCode",
                filterTypes: ["between", "EQUALS"],
                prompt: "Addr Current--Postal Code",
                type: "NumberSearchable"
              },
              {
                __typename: "SectionField",
                eid: "addrCurrentCountry",
                filterTypes: ["EQUALS"],
                prompt: "Addr Current--Country",
                type: "EnumerableSearchableFavoritable"
              },
              {
                __typename: "SectionField",
                eid: "addrCurrentSupplementl",
                filterTypes: ["EQUALS"],
                prompt: "Addr Current--Supplementl",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "addrAtDxNoStreet",
                filterTypes: ["EQUALS"],
                prompt: "Addr at DX--No & Street",
                type: "EnumerableOrOpen"
              },
              {
                __typename: "SectionField",
                eid: "addrAtDxCity",
                filterTypes: ["EQUALS"],
                prompt: "Addr at DX--City",
                type: "EnumerableOrOpen"
              },
              {
                __typename: "SectionField",
                eid: "countyAtDx",
                filterTypes: ["between", "EQUALS"],
                prompt: "County at DX Reported",
                type: "NumberSearchable"
              },
              {
                __typename: "SectionField",
                eid: "countyAtDx_ca",
                filterTypes: ["between", "EQUALS"],
                prompt: "CA County at DX Reported",
                type: "NumberSearchable"
              },
              {
                __typename: "SectionField",
                eid: "addrAtDxState",
                filterTypes: ["EQUALS"],
                prompt: "Addr at DX--State",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "addrAtDxPostalCode",
                filterTypes: ["between", "EQUALS"],
                prompt: "Addr at DX--Postal Code",
                type: "NumberSearchable"
              },
              {
                __typename: "SectionField",
                eid: "addrAtDxCountry",
                filterTypes: ["EQUALS"],
                prompt: "Addr at DX--Country",
                type: "EnumerableSearchableFavoritable"
              },
              {
                __typename: "SectionField",
                eid: "addrAtDxSupplementl",
                filterTypes: ["EQUALS"],
                prompt: "Addr at DX--Supplementl",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "maritalStatusAtDx",
                filterTypes: ["EQUALS"],
                prompt: "Marital Status at DX",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "primaryPayerAtDx",
                filterTypes: ["EQUALS"],
                prompt: "Primary Payer at DX",
                type: "EnumerableSearchable"
              },
              {
                __typename: "SectionField",
                eid: "medicareBeneficiaryIdentifier",
                filterTypes: ["EQUALS"],
                prompt: "Medicare Beneficiary Identifier",
                type: "Open"
              },
              {
                __typename: "SectionField",
                eid: "vitalStatus",
                filterTypes: ["EQUALS"],
                prompt: "Vital Status",
                type: "Enumerable"
              },
              {
                __typename: "SectionField",
                eid: "placeOfDeath",
                filterTypes: ["between", "EQUALS"],
                prompt: "Place of Death",
                type: "NumberSearchable"
              }
            ],
            name: "Demographics"
          },
          {
            __typename: "Section",
            fields: [
              {
                __typename: "SectionField",
                eid: "textDxProcPe",
                filterTypes: [],
                prompt: "Text--DX Proc--PE",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textDxProcXRayScan",
                filterTypes: [],
                prompt: "Text--DX Proc--X-ray/Scan",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textDxProcScopes",
                filterTypes: [],
                prompt: "Text--DX Proc--Scopes",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textDxProcLabTests",
                filterTypes: [],
                prompt: "Text--DX Proc--Lab Tests",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textDxProcOp",
                filterTypes: [],
                prompt: "Text--DX Proc--Op",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textDxProcPath",
                filterTypes: [],
                prompt: "Text--DX Proc--Path",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textPrimarySiteTitle",
                filterTypes: [],
                prompt: "Text--Primary Site Title",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textHistologyTitle",
                filterTypes: [],
                prompt: "Text--Histology Title",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textStaging",
                filterTypes: [],
                prompt: "Text--Staging",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextSurgery",
                filterTypes: [],
                prompt: "RX Text--Surgery",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextRadiation",
                filterTypes: [],
                prompt: "RX Text--Radiation (Beam)",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextRadiationOther",
                filterTypes: [],
                prompt: "RX Text--Radiation Other",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextChemo",
                filterTypes: [],
                prompt: "RX Text--Chemo",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextHormone",
                filterTypes: [],
                prompt: "RX Text--Hormone",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextBrm",
                filterTypes: [],
                prompt: "RX Text--BRM",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "rxTextOther",
                filterTypes: [],
                prompt: "RX Text--Other",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textRemarks",
                filterTypes: [],
                prompt: "Text--Remarks",
                type: "TextArea"
              },
              {
                __typename: "SectionField",
                eid: "textPlaceOfDiagnosis",
                filterTypes: [],
                prompt: "Text--Place of Diagnosis",
                type: "TextArea"
              }
            ],
            name: "Text Abstract"
          },
          {
            name: "State Specific UDF",
            fields: [
              {
                eid: "height_ct",
                prompt: "Height",
                type: "NumberWithUnit",
                filterTypes: [],
                __typename: "SectionField"
              }
            ]
          },
          {
            name: "OR Entry Date And Time",
            fields: [
              {
                eid: "OREntryDT",
                prompt: "OR Entry Date And Time",
                type: "DateTime",
                filterTypes: [],
                __typename: "SectionField"
              }
            ]
          }
        ]
      }
    }
  }
];

const previewCountsMocks = [
  {
    request: {
      query: GENERATE_REPORT_PREVIEW_COUNTS,
      variables: {
        registry: "Oncology",
        selectedColumns: ["nameLast"],
        filterAttrs: [],
        repeatableFlags: null
      }
    },
    result: {
      data: {
        generateReportPreviewCounts: {
          patientCount: 0,
          responseCount: 0,
          rowCount: 0,
          __typename: "GenerateReportPreviewCountsPayload"
        }
      }
    }
  }
];

const dependentExternalEnumerablesMocks = [
  {
    request: {
      query: GET_DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES,
      variables: {
        dependentData: [
          { questionEid: "primarySite", answerData: "C160" },
          {
            questionEid: "dateOfDiagnosis",
            answerData: "2021-01-14T08:00:00.000Z"
          },
          { questionEid: "morphTypebehavIcdO3", answerData: "8000/3" }
        ],
        questionEid: "dependentExternalEnumerableSearchable",
        registryName: "Oncology"
      }
    },
    result: {
      data: {
        dependentExternalEnumerables: [
          {
            enumerableOptions: [
              { description: "C100", externalValue: "C100" },
              { description: "C200", externalValue: "C200" }
            ]
          }
        ]
      }
    }
  }
];

export default [
  ...sectionsMocks,
  ...dependentExternalEnumerablesMocks,
  ...previewCountsMocks
];
