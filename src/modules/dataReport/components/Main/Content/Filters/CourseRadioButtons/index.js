import { RadioGroup, RadioButton } from "@q-centrix/q-components-react";

const CourseRadioButtons = ({
  selectedCourseValue,
  handleRadioChange,
  isDisabled,
  registerForKeyDownFocus,
  registry
}) => {
  if (registry?.value !== "Oncology") {
    return null;
  }

  return (
    <RadioGroup
      className="tw-ml-2.5 tw-flex tw-flex-col tw-gap-5"
      label="First/Subsequent Selection"
      labelClassName="tw-pt-2"
      name="options"
      value={selectedCourseValue}
      onChange={handleRadioChange}
    >
      <RadioButton
        ref={registerForKeyDownFocus}
        disabled={isDisabled}
        label="First Course"
        name="first course"
        value="1"
      />
      <RadioButton
        ref={registerForKeyDownFocus}
        disabled={isDisabled}
        label="Subsequent Course"
        name="subsequent course"
        value="2"
      />
      <RadioButton
        ref={registerForKeyDownFocus}
        disabled={isDisabled}
        label="Both"
        name="both"
        value="4"
      />
    </RadioGroup>
  );
};

export default CourseRadioButtons;
