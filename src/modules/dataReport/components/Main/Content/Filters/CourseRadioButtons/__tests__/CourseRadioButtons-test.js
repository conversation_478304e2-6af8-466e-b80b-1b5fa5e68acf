import { create } from "react-test-renderer";
import CourseRadioButtons from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  RadioButton: "RadioButton",
  RadioGroup: "RadioGroup"
}));

describe("CourseRadioButtons", () => {
  const registry = {
    label: "Oncology",
    value: "Oncology"
  };

  test("it renders component correctly", () => {
    const mockedCourseRadioButtons = create(
      <CourseRadioButtons registry={registry} />
    );

    expect(mockedCourseRadioButtons).toMatchSnapshot();
  });
  test("it renders null if registry is not oncology", () => {
    const mockedCourseRadioButtons = create(
      <CourseRadioButtons registry={null} />
    );

    expect(mockedCourseRadioButtons).toMatchSnapshot();
  });
});
