import { But<PERSON> } from "@q-centrix/q-components-react";
import { create, act } from "react-test-renderer";
import wait from "waait";
import mocks from "modules/dataReport/graphql/mocks";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import Filters from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Input: "Input",
  InputDropdown: "InputDropdown",
  RadioButton: "RadioButton",
  RadioGroup: "RadioGroup"
}));

jest.mock("../ColumnChip", () => ({
  ColumnChip: "ColumnChip"
}));

jest.mock("../CourseRadioButtons", () => "CourseRadioButtons");
jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));

const columnMock = [
  {
    filterTypes: [
      {
        label: "equals",
        value: "equals"
      }
    ],
    id: "nameLast",
    label: "Name--Last",
    type: "Open",
    value: "nameLast"
  }
];

const filtersMocks = [
  {
    appliedFilters: [
      { comparatorSelect: { value: "" } },
      { operatorSelect: { value: "" } },
      { filterSelect: { value: "" } }
    ],
    comparatorSelectOptions: [],
    filterTypes: [],
    FirstFilterBlock: {
      filter: { label: "C001|External lip lower", value: "C001" },
      operator: { label: "equals", value: "equals" }
    },
    id: "primarySite",
    label: "Primary Site",
    type: "EnumberableSearchable",
    value: "primarySite"
  }
];

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    reportId: "1"
  })
}));

describe("Filters", () => {
  test("renders the component loading", () => {
    const component = create(
      decoratedApollo({
        apolloMocks: mocks,
        component: Filters,
        initialAppValues: {
          dataReport: {
            filters: filtersMocks,
            reportName: "Test",
            selectedRegistry: "Oncology"
          }
        },
        initialValues: {},
        props: {
          selectedColumns: columnMock,
          setSelectedColumns: jest.fn()
        }
      })
    );

    expect(component).toMatchSnapshot();
  });

  test("renders the component correctly", async () => {
    const component = create(
      decoratedApollo({
        apolloMocks: mocks,
        component: Filters,
        initialAppValues: {
          dataReport: {
            filters: filtersMocks,
            reportName: "Test 2",
            selectedRegistry: "Oncology"
          }
        },
        initialValues: {},
        props: {
          selectedColumns: columnMock,
          setSelectedColumns: jest.fn()
        }
      })
    );

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("renders the component correctly with Refresh Button Active", async () => {
    const component = create(
      decoratedApollo({
        apolloMocks: mocks,
        component: Filters,
        initialAppValues: {
          dataReport: {
            filters: filtersMocks,
            reportName: "Test 3",
            selectedRegistry: "Oncology",
            generateReportPreviewCounts: {},
            generatedReport: {},
            selectedCourseValue: {}
          }
        },
        initialValues: {},
        props: {
          selectedColumns: columnMock,
          setSelectedColumns: jest.fn()
        }
      })
    );

    const instance = component.root;
    const button = instance.findByType(Button);

    act(() => button.props.onClick({ preventDefault: jest.fn() }));

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
