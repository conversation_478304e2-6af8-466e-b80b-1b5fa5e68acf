// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Filters renders the component correctly 1`] = `
<form
  className="tw-scrollbar-gutter-stable tw-absolute tw-top-0 tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-overflow-y-auto tw-bg-qc-blue-50"
  id="onDemand-override"
>
  <div
    className="tw-flex tw-flex-grow tw-flex-col tw-gap-3 tw-p-5"
  >
    <h3
      className="tw-text-black-70 tw-text-xl tw-font-semibold tw-text-qc-blue-800"
    >
      Create New Report
    </h3>
    <InputDropdown
      containerClassName="tw-font-inter"
      iconClass="fa-solid fa-chevron-down"
      id="select_registry"
      label="Select Registry"
      onChange={[Function]}
      options={Array []}
      placeholder="Select registry"
      value={null}
    />
    <Input
      disabled={true}
      error={false}
      errorText="Report name is required"
      id="report_name"
      label="Report Name"
      onChange={[Function]}
      placeholder="Enter a Name for Your Report"
      type="text"
      value="Test 2"
    />
    <InputDropdown
      containerClassName="tw-font-inter"
      disabled={true}
      iconClass="fa-solid fa-chevron-down"
      id="select_section"
      label="Add Section"
      loading={false}
      onChange={[Function]}
      options={Array []}
      placeholder="Select a Section for Your Report"
      value={null}
    />
    <CourseRadioButtons
      handleRadioChange={[Function]}
      isDisabled={true}
      registerForKeyDownFocus={[Function]}
      registry={null}
    />
    <InputDropdown
      containerClassName="tw-font-inter"
      disabled={true}
      iconClass="fa-solid fa-chevron-down"
      id="select_column"
      label="Add Columns"
      onChange={[Function]}
      options={Array []}
      placeholder="Select a Column to Add to Your Report"
    />
    <motion.div
      animate="closed"
      className="tw-mt-1 tw-flex tw-flex-col tw-gap-1.5"
      initial="closed"
      variants={
        Object {
          "closed": Object {
            "opacity": 0,
          },
          "open": Object {
            "opacity": 1,
            "transition": Object {
              "delayChildren": 0.2,
              "staggerChildren": 0.07,
            },
          },
        }
      }
    >
      <ColumnChip
        column={
          Object {
            "filterTypes": Array [
              Object {
                "label": "equals",
                "value": "equals",
              },
            ],
            "id": "nameLast",
            "label": "Name--Last",
            "type": "Open",
            "value": "nameLast",
          }
        }
        columnIndex={0}
        columnToRemove=""
        handleCloseModal={[Function]}
        handleRemoveDependentFields={[Function]}
        isModalOpen={false}
        onRemove={[Function]}
        primaryFilterValues={null}
        selectedColumns={
          Array [
            Object {
              "filterTypes": Array [
                Object {
                  "label": "equals",
                  "value": "equals",
                },
              ],
              "id": "nameLast",
              "label": "Name--Last",
              "type": "Open",
              "value": "nameLast",
            },
          ]
        }
      />
    </motion.div>
  </div>
  <div
    className="tw-sticky tw-bottom-0 tw-left-0 tw-z-10 tw-h-[60px] tw-w-full tw-border tw-border-qc-blue-200 tw-bg-qc-blue-25 tw-p-2.5 tw-shadow-qc-md"
  >
    <div
      className="tw-flex tw-gap-2"
    >
      <Button
        bg="main"
        customStyle="tw-h-[35px] tw-flex tw-gap-2"
        onClick={[Function]}
        type="submit"
      >
        <i
          className="fa-solid fa-file-export tw-mr-2 tw-opacity-80"
        />
        Generate Report Details
      </Button>
    </div>
  </div>
</form>
`;

exports[`Filters renders the component correctly with Refresh Button Active 1`] = `
<form
  className="tw-scrollbar-gutter-stable tw-absolute tw-top-0 tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-overflow-y-auto tw-bg-qc-blue-50"
  id="onDemand-override"
>
  <div
    className="tw-flex tw-flex-grow tw-flex-col tw-gap-3 tw-p-5"
  >
    <h3
      className="tw-text-black-70 tw-text-xl tw-font-semibold tw-text-qc-blue-800"
    >
      Create New Report
    </h3>
    <InputDropdown
      containerClassName="tw-font-inter"
      iconClass="fa-solid fa-chevron-down"
      id="select_registry"
      label="Select Registry"
      onChange={[Function]}
      options={Array []}
      placeholder="Select registry"
      value={null}
    />
    <Input
      disabled={true}
      error={false}
      errorText="Report name is required"
      id="report_name"
      label="Report Name"
      onChange={[Function]}
      placeholder="Enter a Name for Your Report"
      type="text"
      value="Test 3"
    />
    <InputDropdown
      containerClassName="tw-font-inter"
      disabled={true}
      iconClass="fa-solid fa-chevron-down"
      id="select_section"
      label="Add Section"
      loading={false}
      onChange={[Function]}
      options={Array []}
      placeholder="Select a Section for Your Report"
      value={null}
    />
    <CourseRadioButtons
      handleRadioChange={[Function]}
      isDisabled={true}
      registerForKeyDownFocus={[Function]}
      registry={null}
      selectedCourseValue={Object {}}
    />
    <InputDropdown
      containerClassName="tw-font-inter"
      disabled={true}
      iconClass="fa-solid fa-chevron-down"
      id="select_column"
      label="Add Columns"
      onChange={[Function]}
      options={Array []}
      placeholder="Select a Column to Add to Your Report"
    />
    <motion.div
      animate="closed"
      className="tw-mt-1 tw-flex tw-flex-col tw-gap-1.5"
      initial="closed"
      variants={
        Object {
          "closed": Object {
            "opacity": 0,
          },
          "open": Object {
            "opacity": 1,
            "transition": Object {
              "delayChildren": 0.2,
              "staggerChildren": 0.07,
            },
          },
        }
      }
    >
      <ColumnChip
        column={
          Object {
            "filterTypes": Array [
              Object {
                "label": "equals",
                "value": "equals",
              },
            ],
            "id": "nameLast",
            "label": "Name--Last",
            "type": "Open",
            "value": "nameLast",
          }
        }
        columnIndex={0}
        columnToRemove=""
        handleCloseModal={[Function]}
        handleRemoveDependentFields={[Function]}
        isModalOpen={false}
        onRemove={[Function]}
        primaryFilterValues={null}
        selectedColumns={
          Array [
            Object {
              "filterTypes": Array [
                Object {
                  "label": "equals",
                  "value": "equals",
                },
              ],
              "id": "nameLast",
              "label": "Name--Last",
              "type": "Open",
              "value": "nameLast",
            },
          ]
        }
      />
    </motion.div>
  </div>
  <div
    className="tw-sticky tw-bottom-0 tw-left-0 tw-z-10 tw-h-[60px] tw-w-full tw-border tw-border-qc-blue-200 tw-bg-qc-blue-25 tw-p-2.5 tw-shadow-qc-md"
  >
    <div
      className="tw-flex tw-gap-2"
    >
      <Button
        bg="main"
        customStyle="tw-h-[35px] tw-flex tw-gap-2"
        onClick={[Function]}
        type="submit"
      >
        <i
          className="fa-solid fa-arrows-rotate"
        />
        Refresh Report Details
      </Button>
    </div>
  </div>
</form>
`;

exports[`Filters renders the component loading 1`] = `
<form
  className="tw-scrollbar-gutter-stable tw-absolute tw-top-0 tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-overflow-y-auto tw-bg-qc-blue-50"
  id="onDemand-override"
>
  <div
    className="tw-flex tw-flex-grow tw-flex-col tw-gap-3 tw-p-5"
  >
    <h3
      className="tw-text-black-70 tw-text-xl tw-font-semibold tw-text-qc-blue-800"
    >
      Create New Report
    </h3>
    <InputDropdown
      containerClassName="tw-font-inter"
      iconClass="fa-solid fa-chevron-down"
      id="select_registry"
      label="Select Registry"
      onChange={[Function]}
      options={Array []}
      placeholder="Select registry"
      value={null}
    />
    <Input
      disabled={true}
      error={false}
      errorText="Report name is required"
      id="report_name"
      label="Report Name"
      onChange={[Function]}
      placeholder="Enter a Name for Your Report"
      type="text"
      value="Test"
    />
    <InputDropdown
      containerClassName="tw-font-inter"
      disabled={true}
      iconClass="fa-solid fa-chevron-down"
      id="select_section"
      label="Add Section"
      loading={true}
      onChange={[Function]}
      options={Array []}
      placeholder="Select a Section for Your Report"
      value={null}
    />
    <CourseRadioButtons
      handleRadioChange={[Function]}
      isDisabled={true}
      registerForKeyDownFocus={[Function]}
      registry={null}
    />
    <InputDropdown
      containerClassName="tw-font-inter"
      disabled={true}
      iconClass="fa-solid fa-chevron-down"
      id="select_column"
      label="Add Columns"
      onChange={[Function]}
      options={Array []}
      placeholder="Select a Column to Add to Your Report"
    />
    <motion.div
      animate="closed"
      className="tw-mt-1 tw-flex tw-flex-col tw-gap-1.5"
      initial="closed"
      variants={
        Object {
          "closed": Object {
            "opacity": 0,
          },
          "open": Object {
            "opacity": 1,
            "transition": Object {
              "delayChildren": 0.2,
              "staggerChildren": 0.07,
            },
          },
        }
      }
    >
      <ColumnChip
        column={
          Object {
            "filterTypes": Array [
              Object {
                "label": "equals",
                "value": "equals",
              },
            ],
            "id": "nameLast",
            "label": "Name--Last",
            "type": "Open",
            "value": "nameLast",
          }
        }
        columnIndex={0}
        columnToRemove=""
        handleCloseModal={[Function]}
        handleRemoveDependentFields={[Function]}
        isModalOpen={false}
        onRemove={[Function]}
        primaryFilterValues={null}
        selectedColumns={
          Array [
            Object {
              "filterTypes": Array [
                Object {
                  "label": "equals",
                  "value": "equals",
                },
              ],
              "id": "nameLast",
              "label": "Name--Last",
              "type": "Open",
              "value": "nameLast",
            },
          ]
        }
      />
    </motion.div>
  </div>
  <div
    className="tw-sticky tw-bottom-0 tw-left-0 tw-z-10 tw-h-[60px] tw-w-full tw-border tw-border-qc-blue-200 tw-bg-qc-blue-25 tw-p-2.5 tw-shadow-qc-md"
  >
    <div
      className="tw-flex tw-gap-2"
    >
      <Button
        bg="main"
        customStyle="tw-h-[35px] tw-flex tw-gap-2"
        onClick={[Function]}
        type="submit"
      >
        <i
          className="fa-solid fa-file-export tw-mr-2 tw-opacity-80"
        />
        Generate Report Details
      </Button>
    </div>
  </div>
</form>
`;
