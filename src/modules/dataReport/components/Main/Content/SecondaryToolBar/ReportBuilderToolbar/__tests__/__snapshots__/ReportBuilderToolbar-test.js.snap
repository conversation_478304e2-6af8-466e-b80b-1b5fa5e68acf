// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReportBuilderToolbar it renders component correctly 1`] = `
Array [
  <Button
    bg="neutral"
    customStyle="tw-text-gray-900"
    disabled={true}
    onClick={[Function]}
  >
    <i
      className="fa-solid fa-file-arrow-down tw-pr-[10px] tw-opacity-85"
    />
    Run Report
  </Button>,
  <Button
    bg="success"
    disabled={true}
    onClick={[Function]}
  >
    <i
      className="fa-solid fa-floppy-disk tw-pr-[10px]"
    />
    Save Report
  </Button>,
  <Button
    bg="success"
    onClick={[Function]}
    outline={true}
  >
    <i
      className="fa-solid fa-floppy-disk-circle-arrow-right tw-pr-[10px]"
    />
    Save As New Report
  </Button>,
]
`;
