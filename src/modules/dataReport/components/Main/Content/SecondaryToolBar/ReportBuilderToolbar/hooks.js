/* eslint-disable camelcase */
import { useMutation } from "@apollo/client";
import { useToast } from "@q-centrix/q-components-react";
import {
  always,
  applySpec,
  defaultTo,
  pipe,
  prop,
  isEmpty,
  ifElse,
  when,
  equals,
  propOr,
  not,
  and,
  complement
} from "ramda";
import { useSelector } from "react-redux";
import { useParams, useHistory } from "react-router-dom";
import {
  RUN_ON_DEMAND_REPORT,
  SAVE_ON_DEMAND_REPORT
} from "modules/dataReport/graphql/mutation";
import { GET_REPORT_LIBRARY_LIST } from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import { isNullOrEmpty } from "utils/fp";
import { formatFilterAttrs } from "modules/dataReport/utils/formatFilterAttrs";

// eslint-disable-next-line max-params
const getVariables = (
  report,
  id,
  saveAsNew,
  filterAttrs,
  selectedRegistry,
  selectedCourseValue
  // eslint-disable-next-line max-params
) =>
  applySpec({
    filterAttrs: always(filterAttrs),
    name: prop("name"),
    registry: always(selectedRegistry),
    repeatableFlags: always(
      ifElse(isEmpty, always(null), Number)(selectedCourseValue)
    ),
    reportId: () =>
      saveAsNew
        ? null
        : pipe(when(equals("new"), always(null)), defaultTo(null))(id),
    selectedColumns: prop("columns"),
    selectedSection: propOr("", "selectedSection")
  })(report);

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const { reportId } = useParams();
  const { toast } = useToast();
  const history = useHistory();
  const generatedReport = useSelector(state =>
    ReportSelectors.getGeneratedReport(state)
  );
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );
  const isOwner = useSelector(state => ReportSelectors.getIsOwner(state));
  const searchTerm = useSelector(ReportSelectors.getLibrarySearch);

  const createdReportId = complement(equals("new"))(reportId);
  const shouldDisableButton = and(not(isOwner), createdReportId);

  const selectedCourseValue = useSelector(state =>
    ReportSelectors.getSelectedCourseValue(state)
  );

  const filters = useSelector(state => ReportSelectors.getFilters(state));

  const filterAttrs = formatFilterAttrs(filters);

  const [saveOnDemandReport] = useMutation(SAVE_ON_DEMAND_REPORT, {
    onCompleted: () => {
      history.push("/data-report/");
    },
    refetchQueries: [
      {
        query: GET_REPORT_LIBRARY_LIST,
        variables: {
          currentPage: 1,
          registry: selectedRegistry,
          rowsPerPage: "25",
          searchTerm: searchTerm || ""
        }
      }
    ]
  });
  const [runOnDemandReport] = useMutation(RUN_ON_DEMAND_REPORT, {
    onCompleted: () => {
      history.push("/data-report/");
    },
    refetchQueries: [
      {
        query: GET_REPORT_LIBRARY_LIST,
        variables: {
          currentPage: 1,
          registry: selectedRegistry,
          rowsPerPage: "25",
          searchTerm: searchTerm || ""
        }
      }
    ]
  });

  const handleSaveReport = (event, saveAsNew = false) => {
    event.preventDefault();

    if (isNullOrEmpty(generatedReport)) {
      toast({
        description:
          'Select "Generate Report Details" before saving your report',
        title: "Error",
        variant: "error"
      });
      return;
    }

    if (!generatedReport.name) {
      toast({
        description: "Please enter a report name",
        title: "Error",
        variant: "error"
      });
      return;
    }
    saveOnDemandReport({
      variables: getVariables(
        generatedReport,
        reportId,
        saveAsNew,
        filterAttrs,
        selectedRegistry,
        selectedCourseValue
      )
    });
  };

  const handleRunReport = event => {
    event.preventDefault();

    if (!generatedReport.name) {
      toast({
        description: "Report name is required",
        title: "Error",
        variant: "error"
      });
      return;
    }

    runOnDemandReport({
      variables: getVariables(
        generatedReport,
        reportId,
        false,
        filterAttrs,
        selectedRegistry,
        selectedCourseValue
      )
    });
  };

  return {
    handleRunReport,
    handleSaveReport,
    shouldDisableButton
  };
};
