import { create } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import ReportBuilderToolbar from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    id: "1"
  })
}));

describe("ReportBuilderToolbar", () => {
  function render() {
    return create(
      decoratedApollo({
        component: ReportBuilderToolbar,
        initialAppValues: {
          dataReport: {
            filters: [
              {
                id: "nameLast",
                label: "Name--Last",
                value: "nameLast"
              }
            ],
            generatedReport: {
              filters: [
                {
                  id: "nameLast",
                  label: "Name--Last",
                  value: "nameLast"
                }
              ],
              name: "Test Report",
              selectedSection: "Demographics"
            }
          }
        },
        mocks: {},
        props: {}
      })
    );
  }
  test("it renders component correctly", async () => {
    const component = render();

    await wait(100);

    expect(component).toMatchSnapshot();
  });
});
