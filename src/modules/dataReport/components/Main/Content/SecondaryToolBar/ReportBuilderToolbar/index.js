import { Button } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const ReportBuilderToolbar = () => {
  const { handleSaveReport, handleRunReport, shouldDisableButton } =
    useComponentLogic();

  return (
    <>
      <Button
        bg="neutral"
        customStyle="tw-text-gray-900"
        disabled={shouldDisableButton}
        onClick={handleRunReport}
      >
        <i className="fa-solid fa-file-arrow-down tw-pr-[10px] tw-opacity-85" />
        Run Report
      </Button>
      <Button
        bg="success"
        disabled={shouldDisableButton}
        onClick={handleSaveReport}
      >
        <i className="fa-solid fa-floppy-disk tw-pr-[10px]" />
        Save Report
      </Button>
      <Button outline bg="success" onClick={e => handleSaveReport(e, true)}>
        <i className="fa-solid fa-floppy-disk-circle-arrow-right tw-pr-[10px]" />
        Save As New Report
      </Button>
    </>
  );
};

export default ReportBuilderToolbar;
