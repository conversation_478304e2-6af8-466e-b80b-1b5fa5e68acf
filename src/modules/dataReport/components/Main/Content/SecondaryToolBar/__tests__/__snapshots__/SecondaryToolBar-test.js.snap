// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SecondaryToolBar it renders component correctly toolbar for builder 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-between"
>
  <TabControllerBar>
    <Tab
      activeTabClassName="tw-bg-qc-blue-800"
      className="tw-text-black tw-text-sm tw-font-semibold"
      layoutId="reportController"
      onClick={[Function]}
      tab={
        Object {
          "active": false,
          "id": "reportLibrary",
          "label": "Report Library",
        }
      }
    />
    <Tab
      activeTabClassName="tw-bg-qc-blue-800"
      className="tw-text-black tw-text-sm tw-font-semibold"
      layoutId="reportController"
      onClick={[Function]}
      tab={
        Object {
          "active": Object {
            "isExact": true,
            "params": Object {
              "reportId": "new",
            },
            "path": "//:reportId",
            "url": "//new",
          },
          "id": "newReport",
          "label": "Create New Report",
        }
      }
    />
  </TabControllerBar>
  <div
    className="tw-flex tw-flex-row tw-gap-3"
  >
    <ReportBuilderToolbar />
  </div>
</div>
`;

exports[`SecondaryToolBar it renders component correctly toolbar for library 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-between"
>
  <TabControllerBar>
    <Tab
      activeTabClassName="tw-bg-qc-blue-800"
      className="tw-text-black tw-text-sm tw-font-semibold"
      layoutId="reportController"
      onClick={[Function]}
      tab={
        Object {
          "active": true,
          "id": "reportLibrary",
          "label": "Report Library",
        }
      }
    />
    <Tab
      activeTabClassName="tw-bg-qc-blue-800"
      className="tw-text-black tw-text-sm tw-font-semibold"
      layoutId="reportController"
      onClick={[Function]}
      tab={
        Object {
          "active": null,
          "id": "newReport",
          "label": "Create New Report",
        }
      }
    />
  </TabControllerBar>
  <div
    className="tw-flex tw-flex-row tw-gap-3"
  >
    <ReportLibraryToolbar />
  </div>
</div>
`;
