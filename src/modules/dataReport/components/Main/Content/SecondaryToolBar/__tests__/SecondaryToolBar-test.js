import { MemoryRouter } from "react-router-dom/cjs/react-router-dom.min";
import { create } from "react-test-renderer";
import { decorated<PERSON>pollo } from "utils/test/decorated";
import SecondaryToolBar from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Tab: "Tab",
  TabControllerBar: "TabControllerBar"
}));
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    id: "1"
  })
}));

jest.mock("../ReportBuilderToolbar", () => "ReportBuilderToolbar");
jest.mock("../ReportLibraryToolbar", () => "ReportLibraryToolbar");

const ComponentWithRouter = ({ location }) => (
  <MemoryRouter initialEntries={[location]}>
    <SecondaryToolBar />
  </MemoryRouter>
);

describe("SecondaryToolBar", () => {
  const renderComponent = location =>
    create(
      decoratedApollo({
        component: ComponentWithRouter,
        initialAppValues: {
          dataReport: {
            generatedReport: {
              name: "test"
            }
          }
        },
        mocks: [],
        props: { location }
      })
    );

  test("it renders component correctly toolbar for library", () => {
    const component = renderComponent("//");

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly toolbar for builder", () => {
    const component = renderComponent("//new");

    expect(component).toMatchSnapshot();
  });
});
