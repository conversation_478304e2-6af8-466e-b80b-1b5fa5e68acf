import {
  matchPath,
  useHistory,
  useLocation,
  useRouteMatch
} from "react-router-dom";

export const useComponentLogic = () => {
  const { url, path } = useRouteMatch();
  const history = useHistory();
  const location = useLocation();
  const isInBuilder = matchPath(location.pathname, {
    exact: true,
    path: `${path}/:reportId`,
    strict: true
  });

  const tabs = [
    { id: "reportLibrary", label: "Report Library", active: !isInBuilder },
    { id: "newReport", label: "Create New Report", active: isInBuilder }
  ];

  const handleTabClick = () => {
    const destination = isInBuilder ? "" : "new";

    history.push(`${url}${destination}`);
  };

  return {
    handleTabClick,
    path,
    url,
    tabs
  };
};
