import { Apollo<PERSON><PERSON><PERSON> } from "@apollo/client";
import { TabControllerBar, Tab } from "@q-centrix/q-components-react";
import { Route, Redirect, Switch } from "react-router-dom";
import apolloClient from "base/apolloClient";
import { useComponentLogic } from "./hooks";
import ReportBuilderToolbar from "./ReportBuilderToolbar";
import ReportLibraryToolbar from "./ReportLibraryToolbar";

const dataReportClient = apolloClient("/api/registries/data_report/graphql");

const SecondaryToolbar = () => {
  const { url, path, tabs, handleTabClick } = useComponentLogic();

  return (
    <div className="tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-between">
      <TabControllerBar>
        {tabs.map(tab => (
          <Tab
            key={tab.id}
            tab={tab}
            onClick={handleTabClick}
            layoutId="reportController"
            className="tw-text-black tw-text-sm tw-font-semibold"
            activeTabClassName="tw-bg-qc-blue-800"
          />
        ))}
      </TabControllerBar>
      <div className="tw-flex tw-flex-row tw-gap-3">
        <Switch>
          <Route exact strict path={`${path}/:reportId`}>
            <ApolloProvider client={dataReportClient}>
              <ReportBuilderToolbar />
            </ApolloProvider>
          </Route>
          <Route exact strict path={`${path}/`}>
            <ApolloProvider client={dataReportClient}>
              <ReportLibraryToolbar />
            </ApolloProvider>
          </Route>
          <Redirect to={`${url}/`} />
        </Switch>
      </div>
    </div>
  );
};

export default SecondaryToolbar;
