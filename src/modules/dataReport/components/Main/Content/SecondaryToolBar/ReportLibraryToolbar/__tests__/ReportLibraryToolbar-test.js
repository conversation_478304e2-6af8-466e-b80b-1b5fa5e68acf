import { create } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON><PERSON> } from "utils/test/decorated";
import ReportLibraryToolbar from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  InputDropdown: "InputDropdown",
  SearchBar: "SearchBar"
}));

describe("ReportLibraryToolbar", () => {
  function render() {
    return create(
      decoratedApollo({
        component: ReportLibraryToolbar,
        initialAppValues: {
          dataReport: {}
        },
        mocks: {},
        props: {}
      })
    );
  }
  test("it renders component correctly", async () => {
    const component = render();

    await wait(100);

    expect(component).toMatchSnapshot();
  });
});
