import { useCallback, useState, useMemo } from "react";
import { useQuery } from "@apollo/client";
import { GET_AVAILABLE_REGISTRIES } from "modules/dataReport/graphql/query";
import { useDispatch, useSelector } from "react-redux";
import DataReport from "modules/dataReport/redux/selectors";

import {
  setReportLibrarySearch,
  setSelectedRegistry,
  setIsColumnPanelOpen
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty, isNotNullOrEmpty } from "utils/fp";
import debounce from "debounce";
import { map, prop, applySpec, not } from "ramda";

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const [registries, setRegistries] = useState([]);
  const selectedRegistry = useSelector(DataReport.getSelectedRegistry);

  const registryOption = useMemo(
    () =>
      isNullOrEmpty(selectedRegistry)
        ? null
        : {
            label: selectedRegistry,
            value: selectedRegistry
          },
    [selectedRegistry]
  );

  const dispatch = useDispatch();

  const debouncedSearchHandler = debounce(
    event =>
      dispatch(setReportLibrarySearch({ searchTerm: event.target.value })),
    300
  );

  const handleSearchChange = event => debouncedSearchHandler(event);

  const isColumnPanelOpen = useSelector(DataReport.getisColumnPanelOpen);

  const handleColumnPanelToggle = () =>
    dispatch(setIsColumnPanelOpen(not(isColumnPanelOpen)));

  const { data: registryData } = useQuery(GET_AVAILABLE_REGISTRIES, {
    // eslint-disable-next-line complexity
    onCompleted: () => {
      const formattedRegistryOptions = map(
        applySpec({
          label: prop("title"),
          value: prop("title")
        })
      )(registryData?.availableRegistries || []);

      setRegistries(formattedRegistryOptions);

      if (
        isNullOrEmpty(registryOption) &&
        isNotNullOrEmpty(formattedRegistryOptions)
      ) {
        const [initialRegistryOption] = formattedRegistryOptions;

        dispatch(setSelectedRegistry(initialRegistryOption));
      }
    }
  });

  const handleRegistySelect = useCallback(
    selectedOption => {
      dispatch(setSelectedRegistry(selectedOption));
    },
    [dispatch]
  );

  return {
    handleRegistySelect,
    handleSearchChange,
    handleColumnPanelToggle,
    registries,
    registry: registryOption
  };
};
