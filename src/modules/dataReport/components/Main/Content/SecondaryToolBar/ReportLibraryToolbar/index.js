import {
  Button,
  InputDropdown,
  SearchBar
} from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const ReportLibraryToolbar = () => {
  const {
    handleRegistySelect,
    handleSearchChange,
    handleColumnPanelToggle,
    registries,
    registry
  } = useComponentLogic();

  return (
    <div className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-3">
      <SearchBar
        handleChange={handleSearchChange}
        iconClass="tw-absolute tw-top-1/2 tw-transform tw--translate-y-1/2 tw-right-4"
        inputClass="!tw-h-full tw-w-[350px] tw-py-[5px]"
        placeholder="Click here to search by Report Name"
      />
      <InputDropdown
        containerClassName="tw-font-inter"
        iconClass="fa-solid fa-chevron-down"
        inputClassName="tw-text-black-70"
        labelClassName="tw-text-sm tw-text-black-70"
        options={registries}
        placeholder="Select Registry"
        value={registry}
        onChange={handleRegistySelect}
      />
      <Button
        outline
        customStyle="tw-flex tw-py-2"
        onClick={handleColumnPanelToggle}
      >
        <i className="fa-solid fa-table-columns tw-pr-[10px]" />
        Edit Table Columns
      </Button>
    </div>
  );
};

export default ReportLibraryToolbar;
