// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReportLibraryToolbar it renders component correctly 1`] = `
<div
  className="tw-flex tw-flex-row tw-items-center tw-justify-between tw-gap-3"
>
  <SearchBar
    handleChange={[Function]}
    iconClass="tw-absolute tw-top-1/2 tw-transform tw--translate-y-1/2 tw-right-4"
    inputClass="!tw-h-full tw-w-[350px] tw-py-[5px]"
    placeholder="Click here to search by Report Name"
  />
  <InputDropdown
    containerClassName="tw-font-inter"
    iconClass="fa-solid fa-chevron-down"
    inputClassName="tw-text-black-70"
    labelClassName="tw-text-sm tw-text-black-70"
    onChange={[Function]}
    options={Array []}
    placeholder="Select Registry"
    value={null}
  />
  <Button
    customStyle="tw-flex tw-py-2"
    onClick={[Function]}
    outline={true}
  >
    <i
      className="fa-solid fa-table-columns tw-pr-[10px]"
    />
    Edit Table Columns
  </Button>
</div>
`;
