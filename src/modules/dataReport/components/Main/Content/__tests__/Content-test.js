import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decoratedApollo } from "utils/test/decorated";
import Content from "..";

jest.mock("../ReportBuilder", () => "ReportBuilder");
jest.mock("../ReportLibrary", () => "ReportLibrary");

const generatedReport = {
  filters: [
    {
      additionalSelectOptions: [
        {
          label: "C100",
          value: "C100"
        },
        {
          label: "C200",
          value: "C200"
        },
        {
          label: "C300",
          value: "C300"
        }
      ],
      appliedFilters: [
        {
          comparatorSelect: {
            label: "OR",
            value: "OR"
          },
          filterSelect: {
            label: "C200",
            value: "C200"
          },
          operatorSelect: {
            label: "EQUALS",
            value: "EQUALS"
          }
        }
      ],
      comparatorSelectOptions: [
        {
          label: "AND",
          value: "AND"
        },
        {
          label: "OR",
          value: "OR"
        }
      ],
      filterTypes: [
        {
          label: "EQUALS",
          value: "EQUALS"
        }
      ],
      firstFilterBlock: {
        filter: {
          label: "C100",
          value: "C100"
        },
        operator: {
          label: "EQUALS",
          value: "EQUALS"
        }
      },
      id: "nameBirthSurname",
      label: "Name--Birth Surname",
      mainSelectOptions: {},
      type: "Open",
      value: "nameBirthSurname"
    }
  ],
  name: "Test Report"
};

const ComponentWithRouter = ({ location }) => (
  <MemoryRouter initialEntries={[location]}>
    <Content />
  </MemoryRouter>
);

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: ComponentWithRouter,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: {
          generatedReport
        }
      },
      props
    })
  );

describe("Content", () => {
  test("it renders library correctly", () => {
    const component = mockedComponent({ location: "//" });

    expect(component).toMatchSnapshot();
  });

  test("it renders builder correctly", () => {
    const component = mockedComponent({ location: "//new" });

    expect(component).toMatchSnapshot();
  });
});
