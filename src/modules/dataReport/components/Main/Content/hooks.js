import { useQuery } from "@apollo/client";
import { useDispatch } from "react-redux";
import { useRouteMatch } from "react-router-dom";
import { setAccountSettings } from "modules/app/actions";
import { GET_APPS_LINKS } from "modules/dataReport/graphql/query";

export const useComponentLogic = () => {
  const dispatch = useDispatch();

  const { data } = useQuery(GET_APPS_LINKS, {
    onCompleted: () => {
      const {
        appLinksForCurrentUser: {
          user: { fullName }
        }
      } = data;

      dispatch(setAccountSettings(fullName));
    }
  });

  const { path } = useRouteMatch();

  return {
    path
  };
};
