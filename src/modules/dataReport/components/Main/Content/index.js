import { Apollo<PERSON><PERSON>ider } from "@apollo/client";
import { Route, Switch } from "react-router-dom";
import apolloClient from "base/apolloClient";
import { useComponentLogic } from "./hooks";
import ReportBuilder from "./ReportBuilder";
import ReportLibrary from "./ReportLibrary";

const dataReportClient = apolloClient("/api/registries/data_report/graphql");

const Content = () => {
  const { path } = useComponentLogic();

  return (
    <ApolloProvider client={dataReportClient}>
      <div className="tw-relative tw-h-full tw-w-full tw-overflow-hidden">
        <Switch>
          <Route exact strict path={`${path}/:reportId`}>
            <ReportBuilder />
          </Route>
          <Route exact strict path={`${path}/`}>
            <ReportLibrary />
          </Route>
        </Switch>
      </div>
    </ApolloProvider>
  );
};

export default Content;
