import ColumnHeader from "./ColumnHeader";
import { useComponentLogic } from "./hooks";
import ReportDetails from "./ReportDetails";
import Filters from "../Filters";

const ReportBuilder = () => {
  const { selectedColumns, setSelectedColumns } = useComponentLogic();

  return (
    <>
      <Filters
        selectedColumns={selectedColumns}
        setSelectedColumns={setSelectedColumns}
      />
      <div className="tw-absolute tw-left-0 tw-top-0 tw-h-full tw-w-[calc(100%-414px)] tw-translate-x-[414px] tw-transition-all tw-duration-500 tw-ease-in-out">
        <div className="tw-flex tw-h-full tw-w-full tw-flex-col tw-gap-6 tw-bg-gray-100 tw-px-5 tw-py-4 tw-shadow-md">
          <ColumnHeader />
          <ReportDetails />
        </div>
      </div>
    </>
  );
};

export default ReportBuilder;
