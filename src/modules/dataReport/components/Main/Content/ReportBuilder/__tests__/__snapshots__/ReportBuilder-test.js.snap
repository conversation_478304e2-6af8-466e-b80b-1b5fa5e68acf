// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReportBuilder it renders component correctly 1`] = `
Array [
  <Filters
    selectedColumns={Array []}
    setSelectedColumns={[Function]}
  />,
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-h-full tw-w-[calc(100%-414px)] tw-translate-x-[414px] tw-transition-all tw-duration-500 tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-full tw-flex-col tw-gap-6 tw-bg-gray-100 tw-px-5 tw-py-4 tw-shadow-md"
    >
      <ColumnHeader />
      <ReportDetails />
    </div>
  </div>,
]
`;
