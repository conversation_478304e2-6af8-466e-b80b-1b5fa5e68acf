import { create } from "react-test-renderer";
import { decoratedA<PERSON>lo } from "utils/test/decorated";
import ReportBuilder from "..";

jest.mock("../../Filters", () => "Filters");
jest.mock("../ColumnHeader", () => "ColumnHeader");
jest.mock("../ReportDetails", () => "ReportDetails");
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    reportId: "new"
  })
}));

describe("ReportBuilder", () => {
  test("it renders component correctly", () => {
    const component = create(
      decoratedApollo({
        apolloMocks: [],
        component: ReportBuilder,
        initialAppValues: {
          accountSettings: { fullName: "<PERSON> Smith" },
          dataReport: { selectedRegistry: "Oncology" }
        },
        props: {}
      })
    );

    expect(component).toMatchSnapshot();
  });
});
