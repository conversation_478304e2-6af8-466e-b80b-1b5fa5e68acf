import { useRef } from "react";
import { useSelector } from "react-redux";
import { useScrollButtonsDisabled } from "modules/dataReport/hooks/useScrollButtonsDisabled";
import { useScrollToLastAndPreviousVisibleChild } from "modules/dataReport/hooks/useScrollToLastAndPreviousVisibleChild";
import ReportSelectors from "modules/dataReport/redux/selectors";

export const useComponentLogic = () => {
  const scrollableContainerRef = useRef(null);
  const columnHeaders = useSelector(ReportSelectors.getColumnHeaders);

  const { leftButtonDisabled, rightButtonDisabled } = useScrollButtonsDisabled(
    scrollableContainerRef,
    [columnHeaders]
  );

  const {
    scrollToLastVisible: scrollToLastVisibleChild,
    scrollToPreviousVisible: scrollToPreviousVisibleChild
  } = useScrollToLastAndPreviousVisibleChild(scrollableContainerRef);

  return {
    columnHeaders,
    leftButtonDisabled,
    rightButtonDisabled,
    scrollableContainerRef,
    scrollToLastVisibleChild,
    scrollToPreviousVisibleChild
  };
};
