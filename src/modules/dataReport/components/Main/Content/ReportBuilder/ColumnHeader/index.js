import classnames from "classnames";
import Chip from "./Chip";
import { useComponentLogic } from "./hooks";

const chevronContainerClasses =
  "tw-bg-white tw-flex tw-h-[30px] tw-w-[30px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-[5px] tw-border tw-border-gray-300";

const chevronIconClasses =
  "tw-text-center tw-text-xs tw-text-gray-600 tw-leading-normal";

const ColumnHeader = () => {
  const {
    scrollableContainerRef,
    columnHeaders,
    leftButtonDisabled,
    rightButtonDisabled,
    scrollToLastVisibleChild,
    scrollToPreviousVisibleChild
  } = useComponentLogic();

  const chevronContainerLeftClasses = classnames(chevronContainerClasses, {
    "hover:tw-border-qc-blue-300 hover:tw-bg-qc-blue-25": !leftButtonDisabled
  });
  const chevronContainerRightClasses = classnames(chevronContainerClasses, {
    "hover:tw-border-qc-blue-300 hover:tw-bg-qc-blue-25": !rightButtonDisabled
  });

  const chevronLeftClasses = classnames(chevronIconClasses, {
    "tw-text-qc-blue-800": !leftButtonDisabled
  });
  const chevronRightClasses = classnames(chevronIconClasses, {
    "tw-text-qc-blue-800": !rightButtonDisabled
  });
  const paddingBottomContainerClasses = classnames(
    "tw-bg-white tw-rounded-[5px] tw-border tw-border-gray-400 tw-h-[52px]",
    {
      "tw-pb-[5px]": !rightButtonDisabled || !leftButtonDisabled
    }
  );
  const scrollableContainerClasses = classnames(
    "tw-bg-white tw-scrollbar tw-flex tw-gap-2 tw-overflow-x-auto tw-scroll-smooth tw-rounded-[5px] tw-p-2",
    {
      "tw-pb-[5px] tw-duration-[800ms] tw-ease-in-out":
        !rightButtonDisabled || !leftButtonDisabled
    }
  );

  return (
    <div className="tw-flex tw-flex-col tw-gap-3">
      <div className="tw-flex tw-items-center tw-justify-between">
        <h2 className="tw-text-black-70 tw-cursor-default tw-text-xl tw-font-semibold tw-leading-normal">
          Column Headers
        </h2>
        <div className="tw-flex tw-gap-1">
          <div
            className={chevronContainerLeftClasses}
            onClick={() => {
              if (leftButtonDisabled) return;
              scrollToPreviousVisibleChild();
            }}
          >
            <span className={chevronLeftClasses}>
              <i className="fa-solid fa-chevron-left" />
            </span>
          </div>
          <div
            className={chevronContainerRightClasses}
            onClick={() => {
              if (rightButtonDisabled) return;
              scrollToLastVisibleChild();
            }}
          >
            <span className={chevronRightClasses}>
              <i className="fa-solid fa-chevron-right" />
            </span>
          </div>
        </div>
      </div>
      <div className={paddingBottomContainerClasses}>
        <div
          ref={scrollableContainerRef}
          className={scrollableContainerClasses}
        >
          {columnHeaders?.map(columnHeader => (
            <Chip key={columnHeader.value} columnHeader={columnHeader} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColumnHeader;
