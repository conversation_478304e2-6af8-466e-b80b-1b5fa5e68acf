import { create } from "react-test-renderer";
import mocks from "modules/dataReport/graphql/mocks";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import ColumnHeader from "..";

jest.mock("../Chip", () => "Chip");

const columnHeaders = [
  { label: "First Name", value: "firstName" },
  { label: "Last Name", value: "lastName" }
];

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks: mocks,
      component: ColumnHeader,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: {
          columnHeaders
        }
      },
      props
    })
  );

describe("ColumnHeader", () => {
  test("it renders component correctly", () => {
    const component = mockedComponent({});

    expect(component).toMatchSnapshot();
  });
});
