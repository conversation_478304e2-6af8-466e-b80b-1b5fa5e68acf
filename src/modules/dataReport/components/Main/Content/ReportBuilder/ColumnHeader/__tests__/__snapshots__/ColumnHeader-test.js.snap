// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ColumnHeader it renders component correctly 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-3"
>
  <div
    className="tw-flex tw-items-center tw-justify-between"
  >
    <h2
      className="tw-text-black-70 tw-cursor-default tw-text-xl tw-font-semibold tw-leading-normal"
    >
      Column Headers
    </h2>
    <div
      className="tw-flex tw-gap-1"
    >
      <div
        className="tw-bg-white tw-flex tw-h-[30px] tw-w-[30px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-[5px] tw-border tw-border-gray-300"
        onClick={[Function]}
      >
        <span
          className="tw-text-center tw-text-xs tw-text-gray-600 tw-leading-normal"
        >
          <i
            className="fa-solid fa-chevron-left"
          />
        </span>
      </div>
      <div
        className="tw-bg-white tw-flex tw-h-[30px] tw-w-[30px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-[5px] tw-border tw-border-gray-300 hover:tw-border-qc-blue-300 hover:tw-bg-qc-blue-25"
        onClick={[Function]}
      >
        <span
          className="tw-text-center tw-text-xs tw-text-gray-600 tw-leading-normal tw-text-qc-blue-800"
        >
          <i
            className="fa-solid fa-chevron-right"
          />
        </span>
      </div>
    </div>
  </div>
  <div
    className="tw-bg-white tw-rounded-[5px] tw-border tw-border-gray-400 tw-h-[52px] tw-pb-[5px]"
  >
    <div
      className="tw-bg-white tw-scrollbar tw-flex tw-gap-2 tw-overflow-x-auto tw-scroll-smooth tw-rounded-[5px] tw-p-2 tw-pb-[5px] tw-duration-[800ms] tw-ease-in-out"
    >
      <Chip
        columnHeader={
          Object {
            "label": "First Name",
            "value": "firstName",
          }
        }
      />
      <Chip
        columnHeader={
          Object {
            "label": "Last Name",
            "value": "lastName",
          }
        }
      />
    </div>
  </div>
</div>
`;
