import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";
import { isValidFilterRow } from "../../shared/filterRowValidation";

// eslint-disable-next-line complexity
const FirstNumberRow = ({ operator, filter }) => {
  if (operator.value === "equals" && filter.value) {
    return (
      <>
        <LogicChip>{operator.label}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{filter.value}</RoundedChip>
      </>
    );
  }

  const { firstValue, secondValue } = filter.value;

  if (!isValidFilterRow(filter)) {
    return null;
  }

  return (
    <>
      <LogicChip>{operator.label}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{firstValue}</RoundedChip>
      <LogicChip>{filter.operator || "and"}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{secondValue}</RoundedChip>
    </>
  );
};

export default FirstNumberRow;
