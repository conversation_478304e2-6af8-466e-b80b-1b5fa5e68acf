import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";

const OpenRow = ({ appliedFilter }) => {
  const { comparatorSelect, operatorSelect, filterSelect } = appliedFilter;

  return (
    <div className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3">
      <LogicChip>{comparatorSelect?.label}</LogicChip>
      <LogicChip>{operatorSelect?.label}</LogicChip>
      {filterSelect?.value && (
        <RoundedChip customClasses="tw-text-sm">
          {filterSelect.value}
        </RoundedChip>
      )}
    </div>
  );
};

export default OpenRow;
