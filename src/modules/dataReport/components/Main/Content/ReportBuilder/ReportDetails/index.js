import { Button } from "@q-centrix/q-components-react";
import classNames from "classnames";
import DefaultPreview from "./DefaultPreview";
import FilterBlockRows from "./FilterBlockRows";
import Header from "./Header";
import { useComponentLogic } from "./hooks";

const ReportDetails = () => {
  const {
    generatedReport,
    isGeneratedReportPresent,
    handleFilterRefresh,
    filters
  } = useComponentLogic();

  const buttonClasses = classNames("tw-w-6 tw-h-6 tw-p-0", {
    "!tw-cursor-default": !isGeneratedReportPresent
  });

  return (
    <div className="tw-flex tw-h-full tw-min-h-0 tw-flex-col tw-gap-3">
      <div className="tw-flex tw-items-center tw-gap-3">
        <h2 className="tw-text-black-70 tw-cursor-default tw-text-xl tw-font-semibold tw-leading-normal">
          Report Details
        </h2>
        <Button
          outline
          customStyle={buttonClasses}
          disabled={!isGeneratedReportPresent}
          onClick={handleFilterRefresh}
        >
          <span className="tw-text-sm tw-font-black tw-leading-normal">
            <i className="fa-solid fa-arrows-rotate" />
          </span>
        </Button>
      </div>
      {/* Content */}
      {isGeneratedReportPresent ? (
        <div className="tw-bg-white tw-flex tw-grow tw-flex-col tw-gap-5 tw-overflow-auto tw-rounded-lg tw-border-2 tw-border-gray-400 tw-px-4 tw-py-3">
          <Header generatedReport={generatedReport} />
          {filters.map(filter => (
            <FilterBlockRows key={filter.id} filter={filter} />
          ))}
        </div>
      ) : (
        <DefaultPreview />
      )}
    </div>
  );
};

export default ReportDetails;
