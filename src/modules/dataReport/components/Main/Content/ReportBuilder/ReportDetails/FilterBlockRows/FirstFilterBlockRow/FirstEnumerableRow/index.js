import useComponentLogic from "./hook";
import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";
import { isValidFilterRow } from "../../shared/filterRowValidation";

// eslint-disable-next-line complexity
const FirstEnumerableRow = ({ operator, filter }) => {
  const {
    matchOperatorLabel,
    startEnumerableValue,
    endEnumerableValue,
    shouldShowValue
  } = useComponentLogic(operator, filter);

  if (operator?.value === "equals" && filter?.value) {
    return (
      <>
        <LogicChip>{operator?.label}</LogicChip>
        {filter?.value && (
          <RoundedChip customClasses="tw-text-sm">
            {shouldShowValue ? filter.value : filter.label}
          </RoundedChip>
        )}
      </>
    );
  }

  if (operator?.value === "includes" && filter?.length) {
    return (
      <>
        <LogicChip>{operator.label}</LogicChip>
        {filter?.map(enumerableSelected => (
          <RoundedChip
            key={enumerableSelected.value}
            customClasses="tw-text-sm"
          >
            {shouldShowValue
              ? enumerableSelected.value
              : enumerableSelected.label}
          </RoundedChip>
        ))}
      </>
    );
  }

  if (!isValidFilterRow(filter)) {
    return null;
  }
  return (
    <>
      <LogicChip>{matchOperatorLabel}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">
        {startEnumerableValue}
      </RoundedChip>
      <LogicChip>and</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{endEnumerableValue}</RoundedChip>
    </>
  );
};

export default FirstEnumerableRow;
