// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Between Operator renders correctly when both filter values are present 1`] = `
Array [
  <LogicChip>
    OR
  </LogicChip>,
  <LogicChip>
    between
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
  <LogicChip>
    and
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    2
  </RoundedChip>,
]
`;

exports[`EnumerableRow Component Equals Operator renders correctly when filterBlockValues are present 1`] = `
Array [
  <LogicChip>
    OR
  </LogicChip>,
  <LogicChip>
    equals
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
]
`;

exports[`Includes Operator renders correctly with multiple filter values 1`] = `
<div
  className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
>
  <LogicChip>
    OR
  </LogicChip>
  <LogicChip>
    includes
  </LogicChip>
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>
  <RoundedChip
    customClasses="tw-text-sm"
  >
    2
  </RoundedChip>
</div>
`;
