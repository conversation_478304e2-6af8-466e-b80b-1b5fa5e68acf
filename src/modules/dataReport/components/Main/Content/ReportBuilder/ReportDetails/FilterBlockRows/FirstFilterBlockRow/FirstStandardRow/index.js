import { useComponentLogic } from "./hooks";
import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";

const FirstStandardRow = ({ operator, filter }) => {
  const { operatorLabel, filterValue, isFilterBlockComplete } =
    useComponentLogic(operator, filter);

  if (!isFilterBlockComplete) {
    return null;
  }

  return (
    <>
      <LogicChip>{operatorLabel}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{filterValue}</RoundedChip>
    </>
  );
};

export default FirstStandardRow;
