import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";

// eslint-disable-next-line complexity
const DateTimeRow = ({ appliedFilter }) => {
  const { comparatorSelect, operatorSelect, filterSelect } = appliedFilter;

  const { value } = filterSelect ?? {};

  if (operatorSelect.label === "between") {
    const {
      value: { firstValue, secondValue }
    } = filterSelect ?? {};

    return (
      <>
        <LogicChip>{operatorSelect.label}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{firstValue}</RoundedChip>
        <LogicChip>AND</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{secondValue}</RoundedChip>
      </>
    );
  }

  return (
    <div className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3">
      <LogicChip>{comparatorSelect?.label}</LogicChip>
      <LogicChip>{operatorSelect?.label}</LogicChip>

      <RoundedChip customClasses="tw-text-sm">{value}</RoundedChip>
    </div>
  );
};

export default DateTimeRow;
