import FilterBlockRowList from "./FilterBlockRowList";
import FirstFilterBlockRow from "./FirstFilterBlockRow";

const FilterBlockRows = ({ filter }) => (
  <div className="tw-border-t tw-border-t-gray-400">
    <div className="tw-flex tw-gap-3 tw-px-3 tw-py-5">
      <span className="tw-font-inter tw-text-base tw-font-semibold tw-leading-normal tw-text-qc-blue-800">
        {filter.label}:
      </span>
      <div className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3">
        <FirstFilterBlockRow filters={filter} />
        {filter.appliedFilters?.map(appliedFilter => (
          <FilterBlockRowList
            key={appliedFilter.id}
            appliedFilter={appliedFilter}
            filter={filter}
          />
        ))}
      </div>
    </div>
  </div>
);

export default FilterBlockRows;
