import { create } from "react-test-renderer";
import FirstEnumerableRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const renderComponent = props => create(<FirstEnumerableRow {...props} />);

describe("FirstEnumerableRow Component", () => {
  describe("Equals Operator", () => {
    test("renders correctly when filter value is present", () => {
      const equalsOperatorWithValidFilter = {
        filter: {
          label: "1",
          value: "1"
        },
        operator: {
          label: "equals",
          value: "equals"
        }
      };
      const component = renderComponent(equalsOperatorWithValidFilter);

      expect(component).toMatchSnapshot();
    });

    test("does not render when filter value is null", () => {
      const equalsOperatorWithNullFilter = {
        filter: {
          label: null,
          value: null
        },
        operator: {
          label: "equals",
          value: "equals"
        }
      };
      const component = renderComponent(equalsOperatorWithNullFilter);

      expect(component.toJSON()).toBeNull();
    });
  });

  describe("Includes Operator", () => {
    test("renders correctly with multiple filter values", () => {
      const includesOperatorWithMultipleFilters = {
        filter: [
          { label: "1", value: "1" },
          { label: "2", value: "2" }
        ],
        operator: {
          label: "includes",
          value: "includes"
        }
      };
      const component = renderComponent(includesOperatorWithMultipleFilters);

      expect(component).toMatchSnapshot();
    });
  });

  describe("Between Operator", () => {
    test("renders correctly when both filter values are present", () => {
      const betweenOperatorWithValidFilters = {
        filter: {
          operator: "OR",
          value: { firstValue: "1", secondValue: "2" }
        },
        operator: {
          label: "between",
          value: "between"
        }
      };
      const component = renderComponent(betweenOperatorWithValidFilters);

      expect(component).toMatchSnapshot();
    });

    test("does not render when both filter values are null", () => {
      const betweenOperatorWithNullFilters = {
        filter: {
          operator: "OR",
          value: { firstValue: null, secondValue: null }
        },
        operator: {
          label: "between",
          value: "between"
        }
      };
      const component = renderComponent(betweenOperatorWithNullFilters);

      expect(component.toJSON()).toBeNull();
    });
    test("does not render when first filter value is null", () => {
      const betweenOperatorWithOneNullFilter = {
        filter: {
          operator: "OR",
          value: { firstValue: null, secondValue: "2" }
        },
        operator: {
          label: "between",
          value: "between"
        }
      };
      const component = renderComponent(betweenOperatorWithOneNullFilter);

      expect(component.toJSON()).toBeNull();
    });

    test("does not render when second filter value is null", () => {
      const betweenOperatorWithOneNullFilter = {
        filter: {
          operator: "OR",
          value: { firstValue: "1", secondValue: null }
        },
        operator: {
          label: "between",
          value: "between"
        }
      };
      const component = renderComponent(betweenOperatorWithOneNullFilter);

      expect(component.toJSON()).toBeNull();
    });
  });
});
