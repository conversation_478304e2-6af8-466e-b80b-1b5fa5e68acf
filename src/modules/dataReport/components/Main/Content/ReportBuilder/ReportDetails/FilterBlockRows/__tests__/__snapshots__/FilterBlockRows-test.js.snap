// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FilterBlockRows it renders component correctly 1`] = `
<div
  className="tw-border-t tw-border-t-gray-400"
>
  <div
    className="tw-flex tw-gap-3 tw-px-3 tw-py-5"
  >
    <span
      className="tw-font-inter tw-text-base tw-font-semibold tw-leading-normal tw-text-qc-blue-800"
    >
      Name--Alias
      :
    </span>
    <div
      className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
    >
      <LogicChip>
        EQUALS
      </LogicChip>
      <RoundedChip
        customClasses="tw-text-sm"
      >
        C100
      </RoundedChip>
      <div
        className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
      >
        <LogicChip>
          OR
        </LogicChip>
        <LogicChip>
          EQUALS
        </LogicChip>
        <RoundedChip
          customClasses="tw-text-sm"
        >
          C200
        </RoundedChip>
      </div>
      <div
        className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
      >
        <LogicChip>
          OR
        </LogicChip>
        <LogicChip>
          EQUALS
        </LogicChip>
        <RoundedChip
          customClasses="tw-text-sm"
        >
          C100
        </RoundedChip>
      </div>
    </div>
  </div>
</div>
`;
