import { __, propOr } from "ramda";
import { determineFilterType } from "../shared/determineFilterType";
import DateRangeRow from "./DateRangeRow";
import DateTimeRow from "./DateTimeRow";
import EnumerableRow from "./EnumerableRow";
import NumberRow from "./NumberRow";
import OpenRow from "./OpenRow";
import StandardRow from "./StandardRow";

const type = propOr(StandardRow, __, {
  Open: OpenRow,
  Date: DateRangeRow,
  DateOrEnumerable: DateRangeRow,
  DateTime: DateTimeRow,
  DependentExternalEnumerableSearchable: EnumerableRow,
  DependentFilteredEnumerableSearchable: EnumerableRow,
  Enumerable: EnumerableRow,
  EnumerableCollection: EnumerableRow,
  EnumerableSearchable: EnumerableRow,
  EnumerableSearchableFavoritable: EnumerableRow,
  Number: NumberRow,
  NumberWithUnit: NumberRow,
  NumberSearchable: EnumerableRow,
  NumberSearchableFavoritable: EnumerableRow,
  RepeatableEnumerable: EnumerableRow,
  Generated: OpenRow,
  Calculated: NumberRow
});

const FilterBlockRowList = ({ filter, appliedFilter }) => {
  const GeneratedRowComponent = type(determineFilterType(filter));

  return (
    <GeneratedRowComponent appliedFilter={appliedFilter} filter={filter} />
  );
};

export default FilterBlockRowList;
