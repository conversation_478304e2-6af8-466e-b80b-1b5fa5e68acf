import { create } from "react-test-renderer";
import FirstStandardRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<FirstStandardRow {...props} />);

describe("FirstStandardRow", () => {
  test("renders correctly for Open filter type", () => {
    const props = {
      filter: {
        value: "Test Open Input Value"
      },
      operator: { label: "equals", value: "equals" }
    };
    const component = render(props);

    expect(component).toMatchSnapshot();
  });
  test("renders null for Open filter type when firstFilterBlockValues is not complete", () => {
    const props = {
      filter: {
        value: null
      },
      operator: { label: "equals", value: "equals" }
    };
    const component = render(props);

    expect(component.toJSON()).toBeNull();
  });
});
