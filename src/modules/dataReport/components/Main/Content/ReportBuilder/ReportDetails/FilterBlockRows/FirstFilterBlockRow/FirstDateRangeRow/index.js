import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";
import { useComponentLogic } from "./hooks";
// eslint-disable-next-line max-statements, complexity
const FirstDateRangeRow = ({ filter, operator }) => {
  const { operatorLabel, dateLabel, startDate, endDate } = useComponentLogic({
    filter,
    operator
  });

  if (!operator?.value || !filter) {
    return null;
  }

  if (operator.value === "equals") {
    if (!operatorLabel || !dateLabel) {
      return null;
    }

    return (
      <>
        <LogicChip>{operatorLabel}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{dateLabel}</RoundedChip>
      </>
    );
  }

  if (operator.value === "between") {
    if (!operatorLabel || !startDate || !endDate) {
      return null;
    }

    return (
      <>
        <LogicChip>{operatorLabel}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{startDate}</RoundedChip>
        <LogicChip>AND</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{endDate}</RoundedChip>
      </>
    );
  }

  return null;
};

export default FirstDateRangeRow;
