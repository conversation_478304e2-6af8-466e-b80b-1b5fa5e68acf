// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FirstEnumerableRow Component Between Operator renders correctly when both filter values are present 1`] = `
Array [
  <LogicChip>
    between
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
  <LogicChip>
    and
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    2
  </RoundedChip>,
]
`;

exports[`FirstEnumerableRow Component Equals Operator renders correctly when filter value is present 1`] = `
Array [
  <LogicChip>
    equals
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
]
`;

exports[`FirstEnumerableRow Component Includes Operator renders correctly with multiple filter values 1`] = `
Array [
  <LogicChip>
    includes
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    2
  </RoundedChip>,
]
`;
