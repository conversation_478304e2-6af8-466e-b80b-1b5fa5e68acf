// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FirstNumberRow renders correctly for Number filter type and operator between 1`] = `
Array [
  <LogicChip>
    between
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
  <LogicChip>
    and
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    2
  </RoundedChip>,
]
`;

exports[`FirstNumberRow renders correctly for Number filter type and operator equals 1`] = `
Array [
  <LogicChip>
    equals
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    1
  </RoundedChip>,
]
`;
