import { create } from "react-test-renderer";
import FilterBlockRows from "..";

jest.mock("../../RoundedChip", () => "RoundedChip");
jest.mock("../../LogicChip", () => "LogicChip");

const filter = {
  additionalSelectOptions: [
    {
      label: "C100",
      value: "C100"
    },
    {
      label: "C200",
      value: "C200"
    },
    {
      label: "C300",
      value: "C300"
    }
  ],
  appliedFilters: [
    {
      comparatorSelect: {
        label: "OR",
        value: "OR"
      },
      filterSelect: {
        label: "C200",
        value: "C200"
      },
      operatorSelect: {
        label: "EQUALS",
        value: "EQUALS"
      }
    },
    {
      comparatorSelect: {
        label: "OR",
        value: "OR"
      },
      filterSelect: {
        label: "C100",
        value: "C100"
      },
      operatorSelect: {
        label: "EQUALS",
        value: "EQUALS"
      }
    }
  ],
  comparatorSelectOptions: [
    {
      label: "OR",
      value: "OR"
    }
  ],
  filterTypes: [
    {
      label: "EQUALS",
      value: "EQUALS"
    }
  ],
  firstFilterBlock: {
    filter: {
      label: "C100",
      value: "C100"
    },
    operator: {
      label: "EQUALS",
      value: "EQUALS"
    }
  },
  id: "nameAlias",
  label: "Name--Alias",
  type: "Open",
  value: "nameAlias"
};

describe("FilterBlockRows", () => {
  test("it renders component correctly", () => {
    const component = create(<FilterBlockRows filter={filter} />);

    expect(component).toMatchSnapshot();
  });
});
