import { create } from "react-test-renderer";
import FirstNumberRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<FirstNumberRow {...props} />);

describe("FirstNumberRow", () => {
  test("renders correctly for Number filter type and operator equals", () => {
    const equalsProps = {
      filter: {
        value: "1"
      },
      operator: {
        label: "equals",
        value: "equals"
      }
    };
    const component = render(equalsProps);

    expect(component).toMatchSnapshot();
  });
  test("renders correctly for Number filter type and operator between", () => {
    const betweenProps = {
      filter: {
        operator: "and",
        value: { firstValue: "1", secondValue: "2" }
      },
      operator: {
        label: "between",
        value: "between"
      }
    };
    const component = render(betweenProps);

    expect(component).toMatchSnapshot();
  });
});
