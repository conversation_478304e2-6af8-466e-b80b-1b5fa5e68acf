import { GENERATE_REPORT_PREVIEW_COUNTS } from "modules/dataReport/graphql/mutation";

const previewCountsMocks = [
  {
    request: {
      query: GENERATE_REPORT_PREVIEW_COUNTS,
      variables: {
        registry: "Oncology",
        selectedColumns: [],
        filterAttrs: [],
        repeatableFlags: null
      }
    },
    result: {
      data: {
        generateReportPreviewCounts: {
          patientCount: 0,
          responseCount: 0,
          rowCount: 0,
          __typename: "GenerateReportPreviewCounts"
        }
      }
    }
  }
];

export default [...previewCountsMocks];
