import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";
import { useComponentLogic } from "./hooks";
// eslint-disable-next-line max-statements, complexity
const DateRangeRow = ({ filter, appliedFilter }) => {
  const {
    filterSelect,
    operatorSelect,
    comparatorSelect,
    firstValue,
    secondValue
  } = useComponentLogic({
    appliedFilter
  });

  if (!appliedFilter.operatorSelect?.value) {
    return null;
  }

  if (appliedFilter.operatorSelect.value === "equals") {
    if (!filterSelect || !operatorSelect || !comparatorSelect) {
      return null;
    }
    return (
      <div
        key={`${filter?.id}_${filterSelect}`}
        className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
      >
        <LogicChip>{comparatorSelect}</LogicChip>
        <LogicChip>{operatorSelect}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{filterSelect}</RoundedChip>
      </div>
    );
  }

  if (appliedFilter.operatorSelect.value === "between") {
    if (!comparatorSelect || !operatorSelect || !firstValue || !secondValue) {
      return null;
    }
    return (
      <div
        key={`${filter.id}_${firstValue}_${secondValue}`}
        className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
      >
        <LogicChip>{comparatorSelect}</LogicChip>
        <LogicChip>{operatorSelect}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{firstValue}</RoundedChip>
        <LogicChip>AND</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{secondValue}</RoundedChip>
      </div>
    );
  }

  return null;
};

export default DateRangeRow;
