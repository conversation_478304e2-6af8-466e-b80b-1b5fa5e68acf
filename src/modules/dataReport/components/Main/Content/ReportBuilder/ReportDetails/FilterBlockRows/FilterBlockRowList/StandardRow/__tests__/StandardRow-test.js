import { create } from "react-test-renderer";
import StandardRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<StandardRow {...props} />);

describe("StandardRow", () => {
  test("renders correctly for Open filter type", () => {
    const OpenProps = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: { value: "Open Input Value" },
        operatorSelect: { label: "equals", value: "equals" }
      },
      filter: {
        id: "2",
        type: "Open"
      }
    };
    const StandardRowOpenComponentMock = render(OpenProps);

    expect(StandardRowOpenComponentMock).toMatchSnapshot();
  });
});
