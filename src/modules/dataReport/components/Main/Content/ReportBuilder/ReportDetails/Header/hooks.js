import { useSelector } from "react-redux";
import AccountSettingsSelectors from "modules/app/selectors/accountSettings";
import ReportSelectors from "modules/dataReport/redux/selectors";

export const useComponentLogic = () => {
  const currentUser = useSelector(state =>
    AccountSettingsSelectors.getCurrentUser(state)
  );

  const generateReportPreviewCountsData =
    useSelector(ReportSelectors.getGenerateReportPreviewCounts) || {};

  const generatedReportLoading = useSelector(
    ReportSelectors.getGeneratedReportLoading
  );

  const { generateReportPreviewCounts = {} } = generateReportPreviewCountsData;

  const { patientCount, responseCount, rowCount } =
    generateReportPreviewCounts || {};

  return {
    currentUser,
    patientCount,
    responseCount,
    rowCount,
    generateReportPreviewCounts,
    generatedReportLoading
  };
};
