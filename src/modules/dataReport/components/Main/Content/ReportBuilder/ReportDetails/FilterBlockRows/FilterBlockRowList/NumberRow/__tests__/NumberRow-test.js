import { create } from "react-test-renderer";
import NumberRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<NumberRow {...props} />);

describe("NumberRow", () => {
  const defaultProps = {
    appliedFilter: {
      comparatorSelect: { label: "equals" },
      filterSelect: {
        operator: "and",
        value: {
          firstValue: 1,
          secondValue: 2
        }
      },
      operatorSelect: { label: "AND" }
    },
    filter: {
      id: "1"
    }
  };

  test("renders correctly for Number filter type", () => {
    const component = render(defaultProps);

    expect(component).toMatchSnapshot();
  });
});
