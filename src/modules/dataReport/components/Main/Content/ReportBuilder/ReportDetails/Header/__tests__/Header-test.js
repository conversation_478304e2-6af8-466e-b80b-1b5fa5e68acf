import { create } from "react-test-renderer";
import apolloMocks from "modules/dataReport/components/Main/Content/Filters/mocks";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import Header from "..";

jest.mock("../../RoundedChip", () => "RoundedChip");
jest.mock("../PreviewCard", () => "PreviewCard");

const generatedReport = {
  filters: [
    {
      additionalSelectOptions: [
        {
          label: "C100",
          value: "C100"
        },
        {
          label: "C200",
          value: "C200"
        },
        {
          label: "C300",
          value: "C300"
        }
      ],
      appliedFilters: [],
      comparatorSelectOptions: [
        {
          label: "and",
          value: "and"
        },
        {
          label: "or",
          value: "or"
        }
      ],
      filterTypes: [
        {
          label: "equals",
          value: "equals"
        }
      ],
      firstFilterBlock: {
        operator: {
          label: "equals",
          value: "equals"
        }
      },
      id: "nameLast",
      label: "Name--Last",
      mainSelectOptions: {},
      type: "Open",
      value: "nameLast"
    }
  ],
  name: "Test Report"
};

const mockedComponent = props =>
  create(
    decoratedApollo({
      apolloMocks,
      component: Header,
      initialAppValues: {
        accountSettings: {
          fullName: "Russell Reas",
          id: "1"
        },
        dataReport: {
          generateReportPreviewCounts: {
            patientCount: 0,
            responseCount: 0,
            rowCount: 0
          },
          generatedReportLoading: false
        }
      },
      props
    })
  );

describe("Header", () => {
  test("it renders correctly with", () => {
    const component = mockedComponent({ generatedReport });

    expect(component).toMatchSnapshot();
  });
});
