import { __, propOr } from "ramda";
import { determineFilterType } from "../shared/determineFilterType";
import FirstDateRangeRow from "./FirstDateRangeRow";
import FirstDateTimeRow from "./FirstDateTimeRow";
import FirstEnumerableRow from "./FirstEnumerableRow";
import FirstNumberRow from "./FirstNumberRow";
import FirstStandardRow from "./FirstStandardRow";

// eslint-disable-next-line complexity

const type = propOr(FirstStandardRow, __, {
  Date: FirstDateRangeRow,
  DateOrEnumerable: FirstDateRangeRow,
  DateTime: FirstDateTimeRow,
  DependentExternalEnumerableSearchable: FirstEnumerableRow,
  DependentFilteredEnumerableSearchable: FirstEnumerableRow,
  Enumerable: FirstEnumerableRow,
  EnumerableCollection: FirstEnumerableRow,
  EnumerableSearchable: FirstEnumerableRow,
  EnumerableSearchableFavoritable: FirstEnumerableRow,
  Number: FirstNumberRow,
  NumberWithUnit: FirstNumberRow,
  NumberSearchable: FirstEnumerableRow,
  NumberSearchableFavoritable: FirstEnumerableRow,
  RepeatableEnumerable: FirstEnumerableRow,
  Calculated: FirstNumberRow
});
const FirstFilterBlockRow = ({ filters }) => {
  if (!filters.firstFilterBlock) return null;

  const { operator, filter } = filters.firstFilterBlock;

  const GeneratedRowComponent = type(determineFilterType(filters));

  return (
    <GeneratedRowComponent
      filter={filter}
      filterType={filters.type}
      operator={operator}
    />
  );
};

export default FirstFilterBlockRow;
