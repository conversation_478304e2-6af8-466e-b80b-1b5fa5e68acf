import { create } from "react-test-renderer";
import DateTimeRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<DateTimeRow {...props} />);

describe("DateTimeRow", () => {
  const defaultProps = {
    appliedFilter: {
      comparatorSelect: { label: "equals" },
      filterSelect: {
        operator: "and",
        value: "12/24/2024 13:00"
      },
      operatorSelect: { label: "AND" }
    },
    filter: {
      id: "1"
    }
  };

  test("renders correctly for DateTimeRow filter type", () => {
    const component = render(defaultProps);

    expect(component).toMatchSnapshot();
  });
});
