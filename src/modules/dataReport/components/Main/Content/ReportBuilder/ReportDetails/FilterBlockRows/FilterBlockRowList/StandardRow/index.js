import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";

const StandardRow = ({ filter, appliedFilter }) => {
  const { comparatorSelect, operatorSelect, filterSelect } = appliedFilter;

  return (
    <div
      key={`${filter?.id}_${filterSelect?.value?.label}`}
      className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
    >
      <LogicChip>{comparatorSelect?.label}</LogicChip>
      <LogicChip>{operatorSelect?.label}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">
        {filterSelect?.label}
      </RoundedChip>
    </div>
  );
};

export default StandardRow;
