// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Header it renders correctly with 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-between"
>
  <div
    className="tw-flex tw-w-full  tw-items-center tw-justify-between"
  >
    <h3
      className="tw-text-black/80 tw-font-inter tw-text-lg tw-font-semibold tw-leading-normal"
    >
      Test Report
    </h3>
    <div
      className="tw-flex tw-items-center tw-gap-1.5 tw-font-inter tw-text-[11px] tw-font-semibold tw-leading-normal"
    >
      Created By:
      <RoundedChip>
        <span
          className="tw-bg tw-text-sm tw-font-black tw-leading-normal"
        >
          <i
            className="fa-solid fa-user"
          />
        </span>
        <PERSON>
      </RoundedChip>
    </div>
  </div>
  <div
    className="tw-flex tw-w-full tw-items-start tw-justify-start tw-gap-8 tw-pt-5"
  >
    <PreviewCard
      loading={false}
      title="# of Rows"
    />
    <PreviewCard
      loading={false}
      title="# of Patients"
    />
    <PreviewCard
      loading={false}
      title="Response Count"
    />
  </div>
</div>
`;
