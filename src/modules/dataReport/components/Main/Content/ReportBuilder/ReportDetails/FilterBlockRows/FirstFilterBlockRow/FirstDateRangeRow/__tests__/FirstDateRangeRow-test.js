import { create } from "react-test-renderer";
import FirstDateRangeRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<FirstDateRangeRow {...props} />);

const betweenProps = {
  filter: {
    value: {
      firstValue: "January 31, 2024",
      secondValue: "January 1, 2024"
    }
  },
  operator: { label: "between", value: "between" }
};

const equalsProps = {
  filter: {
    label: "January 31, 2024"
  },
  operator: { label: "equals", value: "equals" }
};

describe("FirstDateRangeRow", () => {
  test("renders correctly for Date Range filter type", () => {
    const component = render(betweenProps);

    expect(component).toMatchSnapshot();
  });

  test("renders correctly for single Date filter type", () => {
    const component = render(equalsProps);

    expect(component).toMatchSnapshot();
  });
});
