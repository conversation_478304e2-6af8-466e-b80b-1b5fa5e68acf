import { CardWithHeader } from "@q-centrix/q-components-react";

const PreviewCard = props => {
  const { title, value, loading } = props;

  return (
    <CardWithHeader
      headerClasses="!tw-bg-qcNeutrals-300 tw-font-inter tw-font-semibold tw-text-[16px] tw-leading-[100%] tw-tracking-[0px] !tw-text-qc-blue-800 tw-px-2.5 tw-py-2 !tw-rounded-t-[5px]"
      cardClasses="tw-overflow-visible tw-w-[158px] !tw-shadow-none"
      bodyClasses="!tw-rounded-b-[5px] tw-h-[38px] tw-pt-[8.5px] tw-pr-[15px] tw-pb-[8.5px] tw-pl-[15px]"
      headerContent={<p>{title}</p>}
    >
      <p className="tw-font-inter tw-text-[14px] tw-font-normal tw-leading-[100%] tw-tracking-[0px]">
        {loading ? <i className="fa-solid fa-circle-notch fa-spin" /> : value}
      </p>
    </CardWithHeader>
  );
};

export default PreviewCard;
