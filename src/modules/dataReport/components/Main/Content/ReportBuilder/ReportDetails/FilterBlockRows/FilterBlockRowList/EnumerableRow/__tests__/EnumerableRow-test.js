import { create } from "react-test-renderer";
import EnumerableRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const renderComponent = props => create(<EnumerableRow {...props} />);

describe("EnumerableRow Component", () => {
  describe("Equals Operator", () => {
    test("renders correctly when filterBlockValues are present", () => {
      const equalsOperatorWithValidFilter = {
        appliedFilter: {
          comparatorSelect: { label: "OR", value: "OR" },
          filterSelect: { label: "1", value: "1" },
          operatorSelect: { label: "equals", value: "equals" }
        }
      };
      const component = renderComponent(equalsOperatorWithValidFilter);

      expect(component).toMatchSnapshot();
    });

    test("does not render when filter value is null", () => {
      const equalsOperatorWithNullFilter = {
        appliedFilter: {
          comparatorSelect: { label: "OR", value: "OR" },
          filterSelect: null,
          operatorSelect: { label: "equals", value: "equals" }
        }
      };
      const component = renderComponent(equalsOperatorWithNullFilter);

      expect(component.toJSON()).toBeNull();
    });
  });
  test("does not render when operator value is null", () => {
    const equalsOperatorWithNullFilter = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: { label: "1", value: "1" },
        operatorSelect: { label: null, value: null }
      }
    };
    const component = renderComponent(equalsOperatorWithNullFilter);

    expect(component.toJSON()).toBeNull();
  });
});

describe("Includes Operator", () => {
  test("renders correctly with multiple filter values", () => {
    const includesOperatorWithMultipleFilters = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: [
          { label: "1", value: "1" },
          { label: "2", value: "2" }
        ],
        operatorSelect: { label: "includes", value: "includes" }
      }
    };
    const component = renderComponent(includesOperatorWithMultipleFilters);

    expect(component).toMatchSnapshot();
  });
  test("does not render when operator value is null", () => {
    const includesOperatorWithNullOperator = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: [
          { label: "1", value: "1" },
          { label: "2", value: "2" }
        ],
        operatorSelect: { label: null, value: null }
      }
    };
    const component = renderComponent(includesOperatorWithNullOperator);

    expect(component.toJSON()).toBeNull();
  });

  test("does not render when filter value is an empty array", () => {
    const includesOperatorWithEmptyFilter = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: [],
        operatorSelect: { label: "includes", value: "includes" }
      }
    };
    const component = renderComponent(includesOperatorWithEmptyFilter);

    expect(component.toJSON()).toBeNull();
  });
});

describe("Between Operator", () => {
  test("renders correctly when both filter values are present", () => {
    const betweenOperatorWithValidFilters = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: {
          operator: "and",
          value: { firstValue: "1", secondValue: "2" }
        },
        operatorSelect: { label: "between", value: "between" }
      }
    };
    const component = renderComponent(betweenOperatorWithValidFilters);

    expect(component).toMatchSnapshot();
  });

  test("does not render when both filter values are null", () => {
    const betweenOperatorWithNullFilters = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: {
          operator: "and",
          value: { firstValue: null, secondValue: null }
        },
        operatorSelect: { label: "between", value: "between" }
      }
    };
    const component = renderComponent(betweenOperatorWithNullFilters);

    expect(component.toJSON()).toBeNull();
  });

  test("does not render when filter value is invalid", () => {
    const betweenOperatorWithInvalidFilter = {
      appliedFilter: {
        comparatorSelect: { label: "OR", value: "OR" },
        filterSelect: {
          operator: "and",
          value: { firstValue: "1", secondValue: null }
        },
        operatorSelect: { label: "between", value: "between" }
      }
    };
    const component = renderComponent(betweenOperatorWithInvalidFilter);

    expect(component.toJSON()).toBeNull();
  });
});
