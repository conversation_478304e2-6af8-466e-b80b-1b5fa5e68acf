// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FirstDateRangeRow renders correctly for Date Range filter type 1`] = `
Array [
  <LogicChip>
    between
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    January 31, 2024
  </RoundedChip>,
  <LogicChip>
    AND
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    January 1, 2024
  </RoundedChip>,
]
`;

exports[`FirstDateRangeRow renders correctly for single Date filter type 1`] = `
Array [
  <LogicChip>
    equals
  </LogicChip>,
  <RoundedChip
    customClasses="tw-text-sm"
  >
    January 31, 2024
  </RoundedChip>,
]
`;
