import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";

// eslint-disable-next-line complexity
const FirstDateTimeRow = ({ operator, filter }) => {
  const { value } = filter ?? {};

  if (operator.label === "between") {
    const {
      value: { firstValue, secondValue }
    } = filter ?? {};

    return (
      <>
        <LogicChip>{operator.label}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{firstValue}</RoundedChip>
        <LogicChip>AND</LogicChip>
        <RoundedChip customClasses="tw-text-sm">{secondValue}</RoundedChip>
      </>
    );
  }

  return (
    <>
      <LogicChip>{operator.label}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{value}</RoundedChip>
    </>
  );
};

export default FirstDateTimeRow;
