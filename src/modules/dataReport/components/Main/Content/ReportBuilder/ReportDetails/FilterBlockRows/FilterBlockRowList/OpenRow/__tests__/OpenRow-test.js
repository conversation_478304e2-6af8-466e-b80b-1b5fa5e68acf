import { create } from "react-test-renderer";
import OpenRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<OpenRow {...props} />);

const defaultProps = {
  appliedFilter: {
    comparatorSelect: { label: "equals" },
    filterSelect: { value: "Test Value" },
    operatorSelect: { label: "AND" }
  },
  filter: {
    id: "1"
  }
};

describe("OpenRow", () => {
  test("renders correctly for Open filter type", () => {
    const OpenRowComponentMock = render(defaultProps);

    expect(OpenRowComponentMock).toMatchSnapshot();
  });
});
