import RoundedChip from "../RoundedChip";
import PreviewCard from "./PreviewCard";
import { useComponentLogic } from "./hooks";

const Header = ({ generatedReport }) => {
  const {
    currentUser,
    patientCount,
    responseCount,
    rowCount,
    generateReportPreviewCounts,
    generatedReportLoading
  } = useComponentLogic();

  return (
    <div className="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-between">
      <div className="tw-flex tw-w-full  tw-items-center tw-justify-between">
        <h3 className="tw-text-black/80 tw-font-inter tw-text-lg tw-font-semibold tw-leading-normal">
          {generatedReport.name}
        </h3>
        <div className="tw-flex tw-items-center tw-gap-1.5 tw-font-inter tw-text-[11px] tw-font-semibold tw-leading-normal">
          Created By:
          <RoundedChip>
            <span className="tw-bg tw-text-sm tw-font-black tw-leading-normal">
              <i className="fa-solid fa-user" />
            </span>
            {currentUser ? currentUser : "Default User"}
          </RoundedChip>
        </div>
      </div>
      {generateReportPreviewCounts && (
        <div className="tw-flex tw-w-full tw-items-start tw-justify-start tw-gap-8 tw-pt-5">
          <PreviewCard
            title="# of Rows"
            value={rowCount}
            loading={generatedReportLoading}
          />
          <PreviewCard
            title="# of Patients"
            value={patientCount}
            loading={generatedReportLoading}
          />
          <PreviewCard
            title="Response Count"
            value={responseCount}
            loading={generatedReportLoading}
          />
        </div>
      )}
    </div>
  );
};

export default Header;
