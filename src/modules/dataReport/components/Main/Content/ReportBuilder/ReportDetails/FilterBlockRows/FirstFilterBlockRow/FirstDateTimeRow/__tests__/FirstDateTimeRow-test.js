import { create } from "react-test-renderer";
import FirstDateTimeRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<FirstDateTimeRow {...props} />);

describe("FirstDateTimeRow", () => {
  const defaultProps = {
    filter: {
      value: "01/01/2024 14:00"
    },
    operator: { label: "EQUALS" }
  };

  test("renders correctly for DateTime filter type", () => {
    const component = render(defaultProps);

    expect(component).toMatchSnapshot();
  });
});
