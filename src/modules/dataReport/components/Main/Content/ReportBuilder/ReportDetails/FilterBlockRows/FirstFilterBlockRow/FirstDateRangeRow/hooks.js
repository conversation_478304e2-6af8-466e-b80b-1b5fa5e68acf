import { pathOr } from "ramda";

export const useComponentLogic = ({ operator, filter }) => {
  const operatorLabel = pathOr(null, ["label"], operator);
  const dateLabel = pathOr(null, ["label"], filter);

  const startDate = pathOr(null, ["value", "firstValue"], filter);
  const endDate = pathOr(null, ["value", "secondValue"], filter);

  return {
    operatorLabel,
    dateLabel,
    startDate,
    endDate
  };
};
