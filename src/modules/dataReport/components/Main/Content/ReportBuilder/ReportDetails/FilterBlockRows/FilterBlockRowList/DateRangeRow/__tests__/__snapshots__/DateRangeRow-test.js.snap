// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateRangeRow renders correctly for Date Range filter type 1`] = `
<div
  className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
>
  <LogicChip>
    OR
  </LogicChip>
  <LogicChip>
    between
  </LogicChip>
  <RoundedChip
    customClasses="tw-text-sm"
  >
    January 31, 2024
  </RoundedChip>
  <LogicChip>
    AND
  </LogicChip>
  <RoundedChip
    customClasses="tw-text-sm"
  >
    January 1, 2024
  </RoundedChip>
</div>
`;

exports[`DateRangeRow renders correctly for single Date filter type 1`] = `
<div
  className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
>
  <LogicChip>
    OR
  </LogicChip>
  <LogicChip>
    equals
  </LogicChip>
  <RoundedChip
    customClasses="tw-text-sm"
  >
    January 31, 2024
  </RoundedChip>
</div>
`;
