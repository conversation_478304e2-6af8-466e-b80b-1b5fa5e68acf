import useComponentLogic from "./hook";
import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";
import { isValidFilterRow } from "../../shared/filterRowValidation";

// eslint-disable-next-line complexity
const EnumerableRow = ({ filter, appliedFilter }) => {
  const { comparatorSelect, operatorSelect, filterSelect } = appliedFilter;
  const {
    matchOperatorLabel,
    startEnumerableValue,
    endEnumerableValue,
    shouldShowValue
  } = useComponentLogic(operatorSelect, filterSelect);

  if (operatorSelect?.value === "equals" && filterSelect?.value) {
    return (
      <>
        <LogicChip>{comparatorSelect?.label}</LogicChip>
        <LogicChip>{operatorSelect?.label}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">
          {shouldShowValue ? filterSelect?.value : filterSelect?.label}
        </RoundedChip>
      </>
    );
  }

  if (operatorSelect?.value === "includes" && filterSelect?.length) {
    return (
      <div
        key={`${filter?.id}_${filterSelect?.label}`}
        className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
      >
        <LogicChip>{comparatorSelect?.label}</LogicChip>
        <LogicChip>{operatorSelect?.label}</LogicChip>
        {filterSelect &&
          filterSelect.map(enumerableSelect => (
            <RoundedChip
              key={enumerableSelect?.value}
              customClasses="tw-text-sm"
            >
              {shouldShowValue
                ? enumerableSelect?.value
                : enumerableSelect?.label}
            </RoundedChip>
          ))}
      </div>
    );
  }

  if (!isValidFilterRow(filterSelect)) {
    return null;
  }

  return (
    <>
      <LogicChip>{comparatorSelect?.label}</LogicChip>
      <LogicChip>{matchOperatorLabel}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">
        {startEnumerableValue}
      </RoundedChip>
      <LogicChip>and</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{endEnumerableValue}</RoundedChip>
    </>
  );
};

export default EnumerableRow;
