import { pathOr } from "ramda";

export const useComponentLogic = ({ appliedFilter }) => {
  const filterSelect = pathOr(null, ["filterSelect", "label"], appliedFilter);
  const operatorSelect = pathOr(
    null,
    ["operatorSelect", "label"],
    appliedFilter
  );
  const comparatorSelect = pathOr(
    null,
    ["comparatorSelect", "label"],
    appliedFilter
  );
  const firstValue = pathOr(
    null,
    ["filterSelect", "value", "firstValue"],
    appliedFilter
  );
  const secondValue = pathOr(
    null,
    ["filterSelect", "value", "secondValue"],
    appliedFilter
  );

  return {
    filterSelect,
    operatorSelect,
    comparatorSelect,
    firstValue,
    secondValue
  };
};
