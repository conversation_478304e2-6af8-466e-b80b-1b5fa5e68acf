import { pathOr } from "ramda";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = (operator, filter) => {
  const operatorLabel = pathOr("", ["label"], operator);
  const filterValue = pathOr("", ["value"], filter);

  const isFilterBlockComplete =
    !isNullOrEmpty(operatorLabel) && !isNullOrEmpty(filterValue);

  return {
    filterValue,
    isFilterBlockComplete,
    operatorLabel
  };
};
