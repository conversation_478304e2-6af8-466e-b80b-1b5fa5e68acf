import { useSelector, useDispatch } from "react-redux";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  setColumnHeaders,
  setGeneratedReport
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";
import { pipe, propOr, reject } from "ramda";
import { useMemo } from "react";

export const useComponentLogic = () => {
  const generatedReport = useSelector(ReportSelectors.getGeneratedReport);
  const reportName = useSelector(ReportSelectors.getReportName);
  const sectionType = useSelector(ReportSelectors.getSectionType);
  const filters = useMemo(
    () =>
      pipe(
        propOr([], "filters"),
        reject(pipe(propOr(null, "firstFilterBlock"), isNullOrEmpty))
      )(generatedReport),
    [generatedReport]
  );

  const dispatch = useDispatch();

  const isGeneratedReportPresent = !isNullOrEmpty(generatedReport);

  const handleFilterRefresh = event => {
    event.preventDefault();

    dispatch(setColumnHeaders());
    dispatch(setGeneratedReport({ reportName, sectionType }));
  };

  return {
    generatedReport,
    handleFilterRefresh,
    isGeneratedReportPresent,
    filters
  };
};
