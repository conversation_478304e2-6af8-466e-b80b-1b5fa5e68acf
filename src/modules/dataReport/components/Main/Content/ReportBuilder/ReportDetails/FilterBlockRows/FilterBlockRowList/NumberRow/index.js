import LogicChip from "../../../LogicChip";
import RoundedChip from "../../../RoundedChip";
import { isValidFilterRow } from "../../shared/filterRowValidation";

// eslint-disable-next-line complexity
const NumberRow = ({ filter, appliedFilter }) => {
  const { comparatorSelect, operatorSelect, filterSelect } = appliedFilter;

  if (operatorSelect.value === "equals") {
    return (
      <>
        <LogicChip>{comparatorSelect.label}</LogicChip>
        <LogicChip>{operatorSelect.label}</LogicChip>
        <RoundedChip customClasses="tw-text-sm">
          {filterSelect.value}
        </RoundedChip>
      </>
    );
  }
  const {
    value: { firstValue, secondValue },
    operator
  } = filterSelect;

  if (!isValidFilterRow(filterSelect)) {
    return null;
  }

  return (
    <div
      key={`${filter.id}_${filterSelect.label}`}
      className="tw-flex tw-flex-wrap tw-gap-x-1 tw-gap-y-3"
    >
      <LogicChip>{comparatorSelect.label}</LogicChip>
      <LogicChip>{operatorSelect.label}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{firstValue}</RoundedChip>
      <LogicChip>{operator || "and"}</LogicChip>
      <RoundedChip customClasses="tw-text-sm">{secondValue}</RoundedChip>
    </div>
  );
};

export default NumberRow;
