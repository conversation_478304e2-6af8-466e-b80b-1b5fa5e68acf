import { pathOr } from "ramda";
import { containsPipe } from "utils/fp/containsPipe";

const useComponentLogic = (operator, filter) => {
  const matchOperatorLabel = pathOr("", ["label"], operator);

  const startEnumerableValue = pathOr("", ["value", "firstValue"], filter);

  const endEnumerableValue = pathOr("", ["value", "secondValue"], filter);

  const shouldShowValue = containsPipe(filter);

  return {
    endEnumerableValue,
    matchOperatorLabel,
    startEnumerableValue,
    shouldShowValue
  };
};

export default useComponentLogic;
