import { create } from "react-test-renderer";
import DateRangeRow from "..";

jest.mock("../../../../RoundedChip", () => "RoundedChip");
jest.mock("../../../../LogicChip", () => "LogicChip");

const render = props => create(<DateRangeRow {...props} />);
const equalsProps = {
  appliedFilter: {
    comparatorSelect: { label: "OR", value: "OR" },
    operatorSelect: { label: "equals", value: "equals" },
    filterSelect: {
      label: "January 31, 2024",
      value: "January 31, 2024"
    }
  },
  filter: {
    id: "1"
  }
};
const betweenProps = {
  appliedFilter: {
    operatorSelect: { label: "between", value: "between" },
    comparatorSelect: { label: "OR", value: "OR" },
    filterSelect: {
      value: {
        firstValue: "January 31, 2024",
        secondValue: "January 1, 2024"
      }
    }
  },
  filter: {
    id: "1"
  }
};

describe("DateRangeRow", () => {
  test("renders correctly for single Date filter type", () => {
    const component = render(equalsProps);

    expect(component).toMatchSnapshot();
  });

  test("renders correctly for Date Range filter type", () => {
    const component = render(betweenProps);

    expect(component).toMatchSnapshot();
  });
});
