// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PreviewCard it renders component correctly 1`] = `
<CardWithHeader
  bodyClasses="!tw-rounded-b-[5px] tw-h-[38px] tw-pt-[8.5px] tw-pr-[15px] tw-pb-[8.5px] tw-pl-[15px]"
  cardClasses="tw-overflow-visible tw-w-[158px] !tw-shadow-none"
  headerClasses="!tw-bg-qcNeutrals-300 tw-font-inter tw-font-semibold tw-text-[16px] tw-leading-[100%] tw-tracking-[0px] !tw-text-qc-blue-800 tw-px-2.5 tw-py-2 !tw-rounded-t-[5px]"
  headerContent={
    <p>
      Test
    </p>
  }
>
  <p
    className="tw-font-inter tw-text-[14px] tw-font-normal tw-leading-[100%] tw-tracking-[0px]"
  >
    0
  </p>
</CardWithHeader>
`;
