import { useEffect, useMemo, useState } from "react";
import { useQuery } from "@apollo/client";
import {
  all,
  either,
  equals,
  identity,
  join,
  pipe,
  prop,
  props,
  cond,
  T,
  always
} from "ramda";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import AccountSettingsSelectors from "modules/app/selectors/accountSettings";
import {
  GET_ON_DEMAND_REPORT,
  GET_SECTIONS
} from "modules/dataReport/graphql/query";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  loadSavedReport,
  setIsOwner,
  resetBuilder
} from "modules/dataReport/redux/slices";
import { isNullOrEmpty } from "utils/fp";

export const isInvalidReportId = either(isNullOrEmpty, equals("new"));

const getReportOwner = pipe(
  prop("user"),
  props(["firstName", "lastName"]),
  join(" ")
);

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const [selectedColumns, setSelectedColumns] = useState([]);
  const { reportId } = useParams();
  const dispatch = useDispatch();
  const selectedRegistry = useSelector(state =>
    ReportSelectors.getSelectedRegistry(state)
  );
  const { data: { onDemandReport } = {}, loading: loadingReport } = useQuery(
    GET_ON_DEMAND_REPORT,
    {
      skip: isInvalidReportId(reportId),
      variables: { id: reportId }
    }
  );
  const isNewReport = equals(reportId, "new");
  const isExistingReport = !isNaN(Number(reportId));

  const shouldSkipSectionsQuery =
    isNewReport && isNullOrEmpty(selectedRegistry);
  // eslint-disable-next-line complexity
  const sectionsVariables = useMemo(
    () =>
      cond([
        [() => isExistingReport, () => ({ onDemandReportId: reportId })],
        [
          () => isNewReport && selectedRegistry,
          () => ({ registry: selectedRegistry })
        ],
        [T, always({})]
      ])(),
    [isExistingReport, isNewReport, reportId, selectedRegistry]
  );

  const { data: { sections } = {}, loading: loadingSections } = useQuery(
    GET_SECTIONS,
    {
      skip: shouldSkipSectionsQuery,
      variables: sectionsVariables
    }
  );

  const currentUser = useSelector(state =>
    AccountSettingsSelectors.getCurrentUser(state)
  );

  const reportOwner = getReportOwner(onDemandReport);

  const isOwner = useMemo(
    () => equals(reportOwner, currentUser),
    [reportOwner, currentUser]
  );

  useEffect(() => {
    if (reportId) {
      dispatch(setIsOwner(isOwner));
    }
  }, [reportId, isOwner]);

  useEffect(() => {
    if (isInvalidReportId(reportId)) {
      dispatch(resetBuilder());
    }
  }, [reportId]);

  useEffect(() => {
    if (
      all(identity, [
        !isInvalidReportId(reportId),
        !loadingReport,
        !loadingSections,
        onDemandReport,
        sections
      ])
    ) {
      dispatch(loadSavedReport({ savedReport: onDemandReport, sections }));
    }
  }, [loadingReport, loadingSections, onDemandReport, sections, reportId]);

  return {
    loadingReport,
    loadingSections,
    selectedColumns,
    setSelectedColumns
  };
};
