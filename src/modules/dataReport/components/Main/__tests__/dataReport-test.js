/* eslint-disable react/button-has-type */
import { create } from "react-test-renderer";
import wait from "waait";
import dataReport from "modules/dataReport/components/Main";
import { decoratedApollo } from "utils/test/decorated";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Tab: "Tab",
  TabControllerBar: "TabControllerBar"
}));

jest.mock("shared/components/Layout", () => "Layout");
jest.mock("../Content", () => "Content");
jest.mock("../Content/SecondaryToolBar", () => "SecondaryToolBar");

jest.mock("shared/widgets/QPoints", () => "QPoints");
jest.mock("shared/widgets/Earnings", () => "Earnings");
jest.mock("shared/widgets/LastLogInTile", () => "LastLogInTile");

describe("dataReport", () => {
  function render() {
    return create(
      decoratedApollo({
        component: dataReport,
        initialAppValues: {},
        initialValues: {},
        props: {}
      })
    );
  }

  test("it renders component correctly", async () => {
    const component = render();

    await wait(100);

    expect(component).toMatchSnapshot();
  });
});
