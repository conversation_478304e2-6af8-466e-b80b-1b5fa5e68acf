// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`dataReport it renders component correctly 1`] = `
<Layout
  onLogoClick={[Function]}
  stChildren={<SecondaryToolBar />}
  stContainerClassName="tw-px-5 tw-py-3 tw-h-[60px]"
  tbChildren={
    <div
      className="tw-flex tw-w-full tw-items-center tw-justify-between"
    >
      <h1
        className="tw-text-black tw-text-2xl tw-font-semibold"
      >
        On-Demand Data
      </h1>
      <div
        className="tw-flex tw-items-center tw-gap-5"
      >
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  }
>
  <Content />
</Layout>
`;
