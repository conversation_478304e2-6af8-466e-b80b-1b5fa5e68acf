import { gql } from "@apollo/client";

export const GET_SECTIONS = gql`
  query sections($registry: String, $onDemandReportId: ID) {
    sections(registry: $registry, onDemandReportId: $onDemandReportId) {
      registry
      name
      fields {
        eid
        prompt
        type
        filterTypes
        seqNo
        typeOverride
        unit
      }
    }
  }
`;

export const GET_QUESTION_ENUMERABLES = gql`
  query questionEnumerables($questionEid: String!, $registryName: String!) {
    questionEnumerables(
      questionEid: $questionEid
      registryName: $registryName
    ) {
      enumerableOptions {
        description
        externalValue
      }
    }
  }
`;

export const GET_DEPENDENT_EXTERNAL_QUESTION_ENUMERABLES = gql`
  query dependentExternalEnumerables(
    $questionEid: String!
    $registryName: String!
    $dependentData: [DependentData!]!
  ) {
    dependentExternalEnumerables(
      questionEid: $questionEid
      registryName: $registryName
      dependentData: $dependentData
    ) {
      questionEid
      enumerableOptions {
        description
        externalValue
      }
    }
  }
`;

export const GET_REPORT_LIBRARY_LIST = gql`
  query reportLibraryList(
    $registry: String!
    $currentPage: Int!
    $rowsPerPage: String
    $searchTerm: String
  ) {
    reportLibraryList(
      registry: $registry
      currentPage: $currentPage
      rowsPerPage: $rowsPerPage
      searchTerm: $searchTerm
    ) {
      currentPage
      totalRecords
      tableData {
        id
        name
        user {
          id
          firstName
          lastName
        }
        updatedAt
        lastRunRowCount
        lastRunPatientCount
        jobStatus {
          id
          status
          details
          dataFile {
            id
            status
            requestedAt
            statusUpdatedAt
            fileName
            url
          }
        }

        selectedSection
        selectedColumns
        onDemandFieldFilters {
          id
          questionEid
          fieldType
          filterType
          betweenStart
          betweenEnd
          includesMatch
          equalsMatch
        }
      }
    }
  }
`;

export const GET_ON_DEMAND_REPORT = gql`
  query onDemandReport($id: ID!) {
    onDemandReport(id: $id) {
      id
      name
      user {
        id
        firstName
        lastName
      }
      updatedAt
      lastRunRowCount
      lastRunPatientCount
      jobStatus {
        id
        status
        details
        dataFile {
          id
          status
          requestedAt
          statusUpdatedAt
          fileName
          url
        }
      }
      selectedSection
      selectedColumns
      onDemandFieldFilters {
        id
        questionEid
        fieldType
        filterType
        betweenStart
        betweenEnd
        includesMatch
        equalsMatch
      }
    }
  }
`;

export const GET_APPS_LINKS = gql`
  query AppLinksForCurrentUser {
    appLinksForCurrentUser {
      user {
        id
        fullName
      }
    }
  }
`;

export const GET_AVAILABLE_REGISTRIES = gql`
  query availableRegistries {
    availableRegistries {
      id
      title
    }
  }
`;
