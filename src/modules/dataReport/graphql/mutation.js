import { gql } from "@apollo/client";

export const SAVE_ON_DEMAND_REPORT = gql`
  mutation saveOnDemandReport(
    $name: String!
    $registry: String!
    $selectedSection: String
    $selectedColumns: [String!]
    $reportId: ID
    $filterAttrs: [JSON!]
    $repeatableFlags: Int
  ) {
    saveOnDemandReport(
      name: $name
      registry: $registry
      selectedSection: $selectedSection
      selectedColumns: $selectedColumns
      reportId: $reportId
      filterAttrs: $filterAttrs
      repeatableFlags: $repeatableFlags
    ) {
      onDemandReport {
        id
        name
        user {
          id
          firstName
          lastName
        }
        updatedAt
        lastRunRowCount
        lastRunPatientCount
        jobStatus {
          id
          status
          details
        }
        selectedSection
        selectedColumns
        onDemandFieldFilters {
          id
          questionEid
          fieldType
          filterType
          betweenStart
          betweenEnd
          includesMatch
          equalsMatch
        }
      }
      msg
    }
  }
`;

export const RUN_ON_DEMAND_REPORT = gql`
  mutation runOnDemandReport(
    $name: String!
    $registry: String!
    $selectedSection: String
    $selectedColumns: [String!]
    $reportId: ID
    $filterAttrs: [JSON!]
    $repeatableFlags: Int
  ) {
    runOnDemandReport(
      name: $name
      registry: $registry
      selectedSection: $selectedSection
      selectedColumns: $selectedColumns
      reportId: $reportId
      filterAttrs: $filterAttrs
      repeatableFlags: $repeatableFlags
    ) {
      response {
        response
      }
      msg
    }
  }
`;

export const DELETE_ON_DEMAND_REPORT = gql`
  mutation deleteOnDemandReport($id: ID!) {
    deleteOnDemandReport(id: $id) {
      onDemandReport {
        id
      }
    }
  }
`;

export const GENERATE_REPORT_PREVIEW_COUNTS = gql`
  mutation generateReportPreviewCounts(
    $registry: String!
    $selectedColumns: [String!]
    $filterAttrs: [JSON!]
    $repeatableFlags: Int
  ) {
    generateReportPreviewCounts(
      registry: $registry
      selectedColumns: $selectedColumns
      filterAttrs: $filterAttrs
      repeatableFlags: $repeatableFlags
    ) {
      patientCount
      responseCount
      rowCount
    }
  }
`;
