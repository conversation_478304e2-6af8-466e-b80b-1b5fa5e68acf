{"reportName": {"key": "reportName", "name": "reportName", "path": ["name"], "label": "Report Name", "withSort": true, "order": 1, "isDefault": true}, "createdBy": {"key": "created<PERSON>y", "name": "created<PERSON>y", "path": ["user"], "label": "Created By", "type": "UserName", "withSort": true, "order": 2, "isDefault": true}, "lastUpdated": {"key": "lastUpdated", "name": "lastUpdated", "path": ["updatedAt"], "label": "Last Updated", "type": "DateTime", "withSort": true, "order": 3, "isDefault": true}, "status": {"key": "status", "name": "status", "path": ["jobStatus"], "label": "Status", "type": "Status", "withSort": true, "order": 4, "isDefault": true}}