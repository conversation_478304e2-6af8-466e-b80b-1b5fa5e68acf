import {
  applySpec,
  pipe,
  map,
  prop,
  propOr,
  path,
  ifElse,
  always,
  reject,
  flatten,
  isEmpty,
  any,
  props,
  allPass,
  pathEq,
  defaultTo
} from "ramda";
import { isNullOrEmpty } from "utils/fp/isNullOrEmpty";
import { camelCaseToKebabCase } from "modules/dataReport/utils/camelCaseToKebabCase";

/* eslint-disable camelcase */
export const formatFilterAttrs = filters =>
  pipe(
    map(filter => {
      const firstFilterBlock = applySpec({
        between_end: ifElse(
          pathEq(["firstFilterBlock", "operator", "value"], "between"),
          path(["firstFilterBlock", "filter", "value", "secondValue"]),
          always(null)
        ),
        between_start: ifElse(
          pathEq(["firstFilterBlock", "operator", "value"], "between"),
          path(["firstFilterBlock", "filter", "value", "firstValue"]),
          always(null)
        ),
        equals_match: ifElse(
          pathEq(["firstFilterBlock", "operator", "value"], "equals"),
          path(["firstFilterBlock", "filter", "value"]),
          always(null)
        ),
        field_type: pipe(propOr("", "type"), camelCaseToKebabCase),
        filter_type: path(["firstFilterBlock", "operator", "value"]),
        includes_match: ifElse(
          pathEq(["firstFilterBlock", "operator", "value"], "includes"),
          pipe(
            path(["firstFilterBlock", "filter"]),
            ifElse(isNullOrEmpty, always([]), filtersSelected =>
              map(prop("value"), filtersSelected)
            )
          ),
          always([])
        ),
        question_eid: prop("id")
      })(filter);

      const appliedFilters = pipe(
        prop("appliedFilters"),
        defaultTo([]),
        map(appliedFilter => ({
          between_end: ifElse(
            pathEq(["operatorSelect", "value"], "between"),
            path(["filterSelect", "value", "secondValue"]),
            always(null)
          )(appliedFilter),
          between_start: ifElse(
            pathEq(["operatorSelect", "value"], "between"),
            path(["filterSelect", "value", "firstValue"]),
            always(null)
          )(appliedFilter),
          equals_match: ifElse(
            pathEq(["operatorSelect", "value"], "equals"),
            path(["filterSelect", "value"]),
            always(null)
          )(appliedFilter),
          field_type: pipe(propOr("", "type"), camelCaseToKebabCase)(filter),
          filter_type: path(["operatorSelect", "value"], appliedFilter),
          includes_match: ifElse(
            pathEq(["operatorSelect", "value"], "includes"),
            pipe(
              path(["filterSelect"]),
              ifElse(isNullOrEmpty, always([]), filtersSelected =>
                map(prop("value"), filtersSelected)
              )
            ),
            always([])
          )(appliedFilter),
          question_eid: prop("id", filter)
        })),
        reject(pipe(prop("filter_type"), isEmpty))
      )(filter);

      const hasEmptyValues = allPass([
        pipe(prop("equals_match"), isNullOrEmpty),
        pipe(props(["between_start", "between_end"]), any(isNullOrEmpty)),
        pipe(prop("includes_match"), isNullOrEmpty)
      ]);
      const validFirstFilterBlock = reject(hasEmptyValues, [firstFilterBlock]);

      const validAppliedFilters = reject(hasEmptyValues, appliedFilters);

      const combinedFilters = [
        ...validFirstFilterBlock,
        ...validAppliedFilters
      ];

      return combinedFilters.map(JSON.stringify);
    }),
    flatten
  )(filters);
