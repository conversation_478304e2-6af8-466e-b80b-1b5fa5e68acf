import { path } from "ramda";
import localizeSelectors from "utils/localizeSelectors";

const localState = path(["app", "dataReport"]);

export const getFilters = state => state.filters;
export const getReportName = state => state.reportName;
export const getColumnHeaders = state => state.columnHeaders;
export const getGeneratedReport = state => state.generatedReport;

export const getLibrarySearch = state => state.librarySearch;
export const getSectionType = state => state.sectionType;
export const getSelectedRegistry = state => state.selectedRegistry;
export const getSelectedCourseValue = state => state.selectedCourseValue;
export const getIsOwner = state => state.isOwner;
export const getGenerateReportPreviewCounts = state =>
  state.generateReportPreviewCounts;
export const getisColumnPanelOpen = state => state.isColumnPanelOpen;
export const getSelectedColumns = state => state.selectedColumns;
export const getMissingDeesFilters = state => state.missingDeesFilters;
export const getMissingDfesFilters = state => state.missingDfesFilters;
export const getGeneratedReportLoading = state => state.generatedReportLoading;

export default localizeSelectors(localState, {
  getColumnHeaders,
  getFilters,
  getGeneratedReport,
  getIsOwner,
  getLibrarySearch,
  getReportName,
  getSectionType,
  getSelectedCourseValue,
  getSelectedRegistry,
  getGenerateReportPreviewCounts,
  getisColumnPanelOpen,
  getSelectedColumns,
  getGeneratedReportLoading,
  getMissingDeesFilters,
  getMissingDfesFilters
});
