import { createSlice } from "@reduxjs/toolkit";
import { format } from "date-fns";
import {
  __,
  allPass,
  always,
  any,
  applySpec,
  assoc,
  assocPath,
  both,
  chain,
  complement,
  cond,
  defaultTo,
  equals,
  evolve,
  filter,
  find,
  gt,
  has,
  identity,
  ifElse,
  is,
  isNil,
  last,
  length,
  map,
  mergeRight,
  pathSatisfies,
  pick,
  pipe,
  prop,
  propEq,
  propOr,
  reject,
  remove,
  T,
  tail,
  when,
  where
} from "ramda";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";

const formatDate = string =>
  isNullOrEmpty(string)
    ? null
    : pipe(
        () => new Date(string),
        date => format(date, "MMMM d, yyyy")
      )(string);

const findColumn = sections => columnId =>
  pipe(
    chain(prop("fields")),
    find(propEq("eid", columnId)),
    defaultTo({}),
    applySpec({
      filterTypes: pipe(
        propOr([], "filterTypes"),
        map(applySpec({ label: identity, value: identity }))
      ),
      id: prop("eid"),
      label: ifElse(
        prop("seqNo"),
        value => `${prop("prompt", value)} (${prop("seqNo", value)})`,
        prop("prompt")
      ),
      type: prop("type"),
      value: prop("eid"),
      unit: propOr(null, "unit"),
      typeOverride: prop("typeOverride")
    }),
    filter(isNotNullOrEmpty)
  )(sections);

const findFilterValue = (savedFilters, columnId, flagId = false) =>
  pipe(
    ifElse(
      always(flagId),
      find(propEq("id", columnId)),
      find(propEq("questionEid", columnId))
    ),
    ifElse(
      isNullOrEmpty,
      always(undefined),
      cond([
        [
          propEq("filterType", "equals"),
          applySpec({
            filter: applySpec({
              label: ifElse(
                pipe(prop("fieldType"), equals("date")),
                pipe(prop("equalsMatch"), formatDate),
                prop("equalsMatch")
              ),
              value: prop("equalsMatch")
            }),
            operator: always({ label: "equals", value: "equals" })
          })
        ],
        [
          propEq("filterType", "between"),
          applySpec({
            filter: filterObj => ({
              value: applySpec({
                firstValue: prop("betweenStart"),
                secondValue: prop("betweenEnd")
              })(filterObj)
            }),
            operator: always({ label: "between", value: "between" })
          })
        ],
        [
          propEq("filterType", "includes"),
          pipe(
            prop("includesMatch"),
            applySpec({
              filter: valueList =>
                map(
                  applySpec({
                    label: identity,
                    value: identity
                  })
                )(valueList),
              operator: always({ label: "includes", value: "includes" })
            })
          )
        ],
        [T, always(undefined)]
      ])
    )
  )(savedFilters);

const initialState = {
  columnHeaders: [],
  filters: [],
  generatedReport: {},
  isOwner: false,
  librarySearch: "",
  reportName: "",
  sectionType: {},
  selectedCourseValue: "",
  selectedRegistry: {},
  generateReportPreviewCounts: {},
  isColumnPanelOpen: false,
  selectedColumns: [],
  generatedReportLoading: true,
  missingDeesFilters: true,
  missingDfesFilters: true
};

const dataReportSlice = createSlice({
  initialState,
  name: "dataReport",
  reducers: {
    addNewFilterBlock: (state, action) => {
      const { columnIndex, appliedFilterIndex } = action.payload;

      const appliedFilterBlockPath = [
        columnIndex,
        "appliedFilters",
        appliedFilterIndex
      ];

      state.filters = ifElse(
        pathSatisfies(complement(isNil), appliedFilterBlockPath),
        identity,
        assocPath(appliedFilterBlockPath, {
          comparatorSelect: { value: "" },
          filterSelect: { value: "" },
          operatorSelect: { value: "" }
        })
      )(state.filters);
    },
    clearFilters: (state, action) => {
      const { columnId } = action.payload;

      state.filters = reject(propEq("id", columnId), state.filters);
    },
    initializeFilter: (state, { payload }) => {
      state.filters.push({
        ...payload,
        appliedFilters: [],
        comparatorSelectOptions: [{ label: "OR", value: "OR" }]
      });
    },
    loadSavedReport: (state, { payload }) => {
      const { savedReport, sections } = payload;

      const createAppliedFilters = column =>
        pipe(
          filter(propEq("questionEid", prop("id", column))),
          ifElse(
            pipe(length, gt(__, 1)),
            pipe(
              tail,
              map(obj => ({
                ...obj,
                ...findFilterValue(
                  savedReport.onDemandFieldFilters,
                  obj.id,
                  true
                )
              })),
              map(obj => assoc("filterSelect", obj.filter, obj)),
              map(obj => assoc("operatorSelect", obj.operator, obj)),
              map(obj =>
                assoc(
                  "comparatorSelect",
                  {
                    label: "OR",
                    value: "OR"
                  },
                  obj
                )
              )
            ),
            always([])
          )
        )(savedReport?.onDemandFieldFilters);

      const generatedReport = applySpec({
        columns: prop("selectedColumns"),
        filters: pipe(
          prop("selectedColumns"),
          map(
            pipe(
              findColumn(sections),
              mergeRight({
                additionalSelectOptions: [
                  { label: "C100", value: "C100" },
                  { label: "C200", value: "C200" },
                  { label: "C300", value: "C300" }
                ],
                appliedFilters: [],
                comparatorSelectOptions: [{ label: "OR", value: "OR" }],
                mainSelectOptions: {}
              }),
              column =>
                assoc(
                  "firstFilterBlock",
                  findFilterValue(savedReport.onDemandFieldFilters, column.id),
                  column
                ),
              column =>
                assoc("appliedFilters", createAppliedFilters(column), column)
            )
          )
        ),
        id: prop("id"),
        name: prop("name"),
        numberOfFilters: pipe(
          prop("onDemandFieldFilters"),
          defaultTo([]),
          length
        ),
        selectedSection: prop("selectedSection")
      })(savedReport);

      return evolve(
        {
          columnHeaders: () =>
            map(pick(["label", "value"]), generatedReport.filters),
          filters: always(generatedReport.filters),
          generatedReport: always(generatedReport),
          reportName: always(generatedReport.name)
        },
        state
      );
    },
    removeFilterBlock: (state, action) => {
      const { columnIndex, appliedFilterIndex } = action.payload;
      const { appliedFilters } = state.filters[columnIndex];

      const lastAppliedFilterIndex = appliedFilters.length - 1;
      const isSecondToLastFilterIndex =
        appliedFilterIndex === lastAppliedFilterIndex;

      const isLastAppliedFilterComparatorAndOperatorEmpty = allPass([
        pathSatisfies(isNullOrEmpty, ["operatorSelect", "value"]),
        pathSatisfies(isNullOrEmpty, ["comparatorSelect", "value"])
      ])(last(appliedFilters));

      if (
        isSecondToLastFilterIndex &&
        isLastAppliedFilterComparatorAndOperatorEmpty
      ) {
        state.filters[columnIndex].appliedFilters = remove(
          lastAppliedFilterIndex,
          1,
          appliedFilters
        );
      }
    },
    resetBuilder: state => ({
      ...initialState,
      selectedRegistry: state.selectedRegistry
    }),
    setColumnHeaders: state => {
      state.columnHeaders = map(
        applySpec({
          label: prop("label"),
          value: prop("value")
        })
      )(state.filters);
    },
    setComparatorValueChange: (state, action) => {
      const { columnIndex, appliedFilterIndex, value } = action.payload;

      state.filters = assocPath(
        [columnIndex, "appliedFilters", appliedFilterIndex, "comparatorSelect"],
        value,
        state.filters
      );
    },
    setFirstOperatorChange: (state, action) => {
      const { value, columnIndex } = action.payload;

      state.filters = assocPath(
        [columnIndex, "firstFilterBlock", "operator"],
        value,
        state.filters
      );
    },
    setGeneratedReport: (state, { payload }) => {
      const { reportName, sectionType } = payload;

      const nonEmptyFilters = pipe(
        map(
          evolve({
            appliedFilters: pipe(
              defaultTo([]),
              when(
                complement(isNullOrEmpty),
                filter(
                  pipe(
                    prop("filterSelect"),
                    ifElse(is(Array), any(prop("value")), prop("value"))
                  )
                )
              )
            )
          })
        ),
        filter(
          both(
            prop("firstFilterBlock"),
            where({
              firstFilterBlock: allPass([has("operator"), has("filter")])
            })
          )
        )
      );

      const generatedReport = applySpec({
        columns: map(prop("id")),
        filters: nonEmptyFilters,
        name: always(reportName),
        numberOfFilters: length,
        selectedSection: always(sectionType?.value)
      })(state.filters);

      return assoc("generatedReport", generatedReport)(state);
    },
    setIsOwner: (state, action) => {
      state.isOwner = action.payload;
    },
    setReportLibrarySearch: (state, action) => {
      const { searchTerm } = action.payload;

      state.librarySearch = searchTerm;
    },
    setOperatorValueChange: (state, action) => {
      const { columnIndex, appliedFilterIndex, value } = action.payload;

      state.filters = assocPath(
        [columnIndex, "appliedFilters", appliedFilterIndex, "operatorSelect"],
        value,
        state.filters
      );
    },
    setReportName: (state, { payload }) => {
      const { reportName } = payload;

      state.reportName = reportName;
    },
    setSecondValueChange: (state, action) => {
      const { value, columnIndex } = action.payload;

      state.filters = assocPath(
        [columnIndex, "firstFilterBlock", "filter"],
        value,
        state.filters
      );
    },
    setFilterValueChange: (state, action) => {
      const { columnIndex, appliedFilterIndex, value } = action.payload;

      state.filters = assocPath(
        [columnIndex, "appliedFilters", appliedFilterIndex, "filterSelect"],
        value,
        state.filters
      );
    },
    setSectionTypeState: (state, action) => {
      state.sectionType = action.payload;
    },
    setSelectedCourseValue: (state, action) => {
      state.selectedCourseValue = action.payload;
    },
    setSelectedRegistry: (state, action) => {
      state.selectedRegistry = action.payload.value;
    },
    setGenerateReportPreviewCounts: (state, action) => {
      state.generateReportPreviewCounts = action.payload;
    },
    setGeneratedReportLoading: (state, action) => {
      state.generatedReportLoading = action.payload;
    },
    setIsColumnPanelOpen: (state, action) => {
      state.isColumnPanelOpen = action.payload;
    },
    updateSelectedColumns: (state, { payload }) => {
      state.selectedColumns = payload;
    },
    setMissingDeesFilters: (state, action) => {
      state.missingDeesFilters = action.payload;
    },
    setMissingDfesFilters: (state, action) => {
      state.missingDfesFilters = action.payload;
    }
  }
});

export const {
  getCurrentFiltersById,
  setSelectedCourseValue,
  setOperatorValueChange,
  setComparatorValueChange,
  setFilterValueChange,
  addNewFilterBlock,
  removeFilter,
  initializeFilter,
  setFirstOperatorChange,
  setSecondValueChange,
  getFilters,
  setFilters,
  clearFilters,
  resetBuilder,
  setColumnHeaders,
  setReportName,
  setReportLibrarySearch,
  setGeneratedReport,
  removeFilterBlock,
  loadSavedReport,
  setSectionTypeState,
  setSelectedRegistry,
  setIsOwner,
  setGenerateReportPreviewCounts,
  setGeneratedReportLoading,
  setIsColumnPanelOpen,
  updateSelectedColumns,
  setMissingDeesFilters,
  setMissingDfesFilters
} = dataReportSlice.actions;

export default dataReportSlice.reducer;
