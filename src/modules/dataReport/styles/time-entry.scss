.react-datepicker__time-container .react-datepicker__header {
  display: none;
}

.react-datepicker__time-container {
  transform: translateX(-65%) !important;
}
.react-datepicker__time-box {
  width: 160px !important;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}
.react-datepicker__time-list {
  text-align: left !important;
}

.react-datepicker__triangle {
  display: none !important;
}

.react-datepicker__input-container input:focus {
  border: 1px solid #5f2167;
  outline: none;
}

.react-datepicker__close-icon::after {
  background-color: #C4C4C4;
  height: 14px;
  width: 14px;
  font-size: 10px;
  display: flex;
  justify-content: center;
  padding: 1px;
}
