import { useEffect, useState } from "react";

export const useScrollButtonsDisabled = (scrollableRef, dependencies = []) => {
  const [leftButtonDisabled, setLeftButtonDisabled] = useState(true);
  const [rightButtonDisabled, setRightButtonDisabled] = useState(false);

  useEffect(() => {
    const scrollabledElement = scrollableRef.current;

    const handleScroll = () => {
      if (scrollabledElement) {
        const { scrollLeft, scrollWidth, clientWidth } = scrollabledElement;
        const combinedWidth = scrollLeft + clientWidth;

        setLeftButtonDisabled(scrollLeft === 0);
        setRightButtonDisabled(combinedWidth >= scrollWidth);
      }
    };

    if (scrollabledElement) {
      scrollabledElement.addEventListener("scroll", handleScroll);

      handleScroll();
    }

    return () => {
      if (scrollabledElement) {
        scrollabledElement.removeEventListener("scroll", handleScroll);
      }
    };
  }, [scrollableRef.current, dependencies]);

  return { leftButtonDisabled, rightButtonDisabled };
};

export default useScrollButtonsDisabled;
