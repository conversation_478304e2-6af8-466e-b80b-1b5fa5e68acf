import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  T,
  any,
  find,
  pipe,
  propEq,
  path,
  pathOr,
  propOr,
  defaultTo,
  applySpec,
  pathEq,
  both,
  always,
  of as wrapInArray,
  identity,
  map,
  values,
  apply,
  concat,
  reject,
  when,
  length,
  complement,
  equals,
  head,
  props,
  all,
  F,
  prop,
  ifElse
} from "ramda";
import { isNullOrEmpty, isNotNullOrEmpty } from "utils/fp";
import ReportSelectors from "modules/dataReport/redux/selectors";
import {
  setMissingDeesFilters,
  setMissingDfesFilters
} from "modules/dataReport/redux/slices";

const validateAndExtractSingle = pipe(
  values,
  apply(concat),
  reject(isNullOrEmpty),
  when(pipe(length, complement(equals(1))), always([])),
  head
);

const areDatesInSameYear = pipe(
  applySpec({
    startDate: pipe(path(["firstValue"]), date => new Date(date).getFullYear()),
    endDate: pipe(path(["secondValue"]), date => new Date(date).getFullYear())
  }),
  ({ startDate, endDate }) => equals(startDate, endDate)
);

export const useCheckDependentFilters = () => {
  const dispatch = useDispatch();
  const filters = useSelector(state => ReportSelectors.getFilters(state));

  const getPrimarySiteIfSingle = useMemo(
    () =>
      pipe(
        find(propEq("id", "primarySite")),
        defaultTo({}),
        applySpec({
          firstFilterBlock: pipe(
            path(["firstFilterBlock"]),
            ifElse(
              both(
                pathEq(["operator", "value"], "equals"),
                pipe(path(["filter", "value"]), isNotNullOrEmpty)
              ),
              path(["filter", "value"]),
              always(null)
            ),
            wrapInArray
          ),
          appliedFilters: pipe(
            pathOr([], ["appliedFilters"]),
            map(
              ifElse(
                both(
                  pathEq(["operatorSelect", "value"], "equals"),
                  pipe(path(["filterSelect", "value"]), isNotNullOrEmpty)
                ),
                identity,
                always(null)
              )
            )
          )
        }),
        validateAndExtractSingle
      )(filters),
    [filters]
  );

  const getDateOfDiagnosisIfSingle = useMemo(
    () =>
      pipe(
        find(propEq("id", "dateOfDiagnosis")),
        defaultTo({}),
        applySpec({
          firstFilterBlock: pipe(
            path(["firstFilterBlock"]),
            ifElse(
              pathEq(["operator", "value"], "equals"),
              pipe(
                path(["filter", "value"]),
                when(isNullOrEmpty, always(null))
              ),
              pipe(
                path(["filter", "value"]),
                ifElse(
                  pipe(
                    props(["firstValue", "secondValue"]),
                    all(complement(isNullOrEmpty))
                  ),
                  ifElse(areDatesInSameYear, prop("firstValue"), always(null)),
                  always(null)
                )
              )
            ),
            wrapInArray
          ),
          appliedFilters: pipe(
            propOr([], ["appliedFilters"]),
            map(
              pipe(
                ifElse(
                  pathEq(["operatorSelect", "value"], "equals"),
                  pipe(
                    path(["filterSelect", "value"]),
                    ifElse(complement(isNullOrEmpty), T, always(null))
                  ),
                  pipe(
                    path(["filterSelect", "value"]),
                    ifElse(
                      pipe(
                        props(["firstValue", "secondValue"]),
                        all(complement(isNullOrEmpty))
                      ),
                      ifElse(areDatesInSameYear, prop("firstValue"), F),
                      always(null)
                    )
                  )
                )
              )
            )
          )
        }),
        validateAndExtractSingle
      )(filters),
    [filters]
  );

  const getHistoricalSiteValueIfSingle = useMemo(
    () =>
      pipe(
        find(propEq("id", "morphTypebehavIcdO3")),
        defaultTo({}),
        applySpec({
          firstFilterBlock: pipe(
            path(["firstFilterBlock", "filter", "value"]),
            wrapInArray
          ),
          appliedFilters: pipe(
            propOr([], ["appliedFilters"]),
            map(path(["filterSelect", "value"]))
          )
        }),
        validateAndExtractSingle
      )(filters),
    [filters]
  );

  const isMissingDeesFilters = useMemo(
    () =>
      any(isNullOrEmpty)([
        getHistoricalSiteValueIfSingle,
        getDateOfDiagnosisIfSingle,
        getPrimarySiteIfSingle
      ]),
    [
      getHistoricalSiteValueIfSingle,
      getDateOfDiagnosisIfSingle,
      getPrimarySiteIfSingle
    ]
  );

  const isMissingDfesFilters = useMemo(
    () => isNullOrEmpty(getPrimarySiteIfSingle),
    [getPrimarySiteIfSingle]
  );

  useEffect(() => {
    dispatch(setMissingDeesFilters(isMissingDeesFilters));
  }, [isMissingDeesFilters]);

  useEffect(() => {
    dispatch(setMissingDfesFilters(isMissingDfesFilters));
  }, [isMissingDfesFilters]);
};
