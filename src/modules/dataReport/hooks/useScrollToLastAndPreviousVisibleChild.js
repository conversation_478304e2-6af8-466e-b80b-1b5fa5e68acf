import { useState, useCallback } from "react";

export const useScrollToLastAndPreviousVisibleChild = ref => {
  const [scrollPosition, setScrollPosition] = useState([0]);
  const [currentIndex, setCurrentIndex] = useState(0);

  let cumulativeWidth = 0;
  let targetScrollPosition = 0;

  // eslint-disable-next-line complexity, max-statements
  const scrollToLastVisible = useCallback(() => {
    if (ref.current) {
      const container = ref.current;
      const containerRect = container.getBoundingClientRect();
      const containerPaddingLeft = parseFloat(
        window.getComputedStyle(container).paddingLeft
      );
      const children = Array.from(container.children);

      for (const child of children) {
        const childRect = child.getBoundingClientRect();

        if (childRect.right >= containerRect.right) {
          targetScrollPosition = cumulativeWidth;
          break;
        }
        cumulativeWidth += childRect.width + containerPaddingLeft;
      }

      if (targetScrollPosition === 0) {
        targetScrollPosition = container.scrollWidth - containerRect.width;
      }

      setScrollPosition(prev => [...prev, targetScrollPosition]);
      setCurrentIndex(prev => prev + 1);

      container.scrollTo({
        behavior: "smooth",
        left: targetScrollPosition
      });
    }
  }, [scrollPosition, ref, currentIndex]);

  const scrollToPreviousVisible = useCallback(() => {
    if (ref.current) {
      const container = ref.current;

      if (currentIndex > 0) {
        targetScrollPosition = Math.max(0, scrollPosition[currentIndex - 1]);
        setCurrentIndex(prev => prev - 1);
        setScrollPosition(prev => {
          if (prev.length > 0) {
            prev.pop();

            return prev;
          }

          return [0];
        });
      }

      container.scrollTo({
        behavior: "smooth",
        left: targetScrollPosition
      });
    }
  }, [ref, currentIndex, scrollPosition]);

  return { scrollToLastVisible, scrollToPreviousVisible };
};

export default useScrollToLastAndPreviousVisibleChild;
