import { useCallback, useEffect, useRef } from "react";
import {
  always,
  complement,
  cond,
  equals,
  findIndex,
  includes,
  length,
  modulo,
  nth,
  pipe,
  propOr,
  when,
  append,
  ifElse,
  identity,
  isNil,
  T,
  __,
  add
} from "ramda";

export const useElementFocusOnKeyDown = () => {
  const registeredElements = useRef([]);

  const registerForKeyDownFocus = useCallback(
    element => {
      const elementToAdd = propOr(element, "inputRef", element);

      registeredElements.current = when(
        complement(includes(elementToAdd)),
        append(elementToAdd)
      )(registeredElements.current);
    },
    [registeredElements]
  );

  const handleKeyDown = useCallback(
    event => {
      const isArrowKey = includes(event.key, ["ArrowRight", "ArrowLeft"]);
      const isEnterKey = equals(event.key, "Enter");

      const preventDefault = () => event.preventDefault();

      const focusNextElement = direction => {
        const elements = registeredElements.current;
        const totalElements = length(elements);

        const getNextIndex = pipe(
          findIndex(equals(document.activeElement)),
          ifElse(
            equals(-1),
            always(0),
            pipe(add(direction), modulo(__, totalElements))
          )
        );

        const focusElement = pipe(
          getNextIndex,
          nth(__, elements),
          when(complement(isNil), element => element.focus())
        );

        focusElement(elements);
      };

      const handleArrowKey = () => {
        preventDefault();
        const direction = equals(event.key, "ArrowRight") ? 1 : -1;

        focusNextElement(direction);
      };

      const handleEnterKey = () => {
        preventDefault();
        document.activeElement.click();
      };

      cond([
        [always(isArrowKey), handleArrowKey],
        [always(isEnterKey), handleEnterKey],
        [T, identity]
      ])();
    },
    [registeredElements]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);

    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  return { registerForKeyDownFocus };
};
