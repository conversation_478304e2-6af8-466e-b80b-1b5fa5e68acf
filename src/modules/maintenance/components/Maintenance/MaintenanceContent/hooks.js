import { useMemo } from "react";
import { equals } from "ramda";
import {
  usePaginationProps,
  useRegistryFilters,
  useTab
} from "modules/maintenance/hooks";

export const useComponentLogic = ({
  handleChangeRegistry,
  registryOptions,
  registryPermissionsError,
  selectedRegistry,
  tabs
}) => {
  const pagination = usePaginationProps();

  const { filters, handleChangeField } = useRegistryFilters(selectedRegistry);
  const { tab } = useTab();

  const shouldUseMaintenanceContent = useMemo(
    () => equals("maintenance", tab),
    [tab]
  );

  return {
    error: registryPermissionsError,
    filters,
    handleChangeField,
    handleChangeRegistry,
    pagination,
    registryOptions,
    selectedRegistry,
    shouldUseMaintenanceContent,
    tab,
    tabs
  };
};
