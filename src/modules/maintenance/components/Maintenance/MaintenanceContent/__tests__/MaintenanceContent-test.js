import { create } from "react-test-renderer";
import { MemoryRouter } from "react-router-dom";
import MaintenanceContent from "..";
import { decoratedApollo } from "utils/test/decorated";
import apolloMocks from "modules/maintenance/components/Maintenance/mocks";

jest.mock("../../MaintenanceTabs", () => "MaintenanceTabs");
jest.mock("../TableContent", () => "TableContent");

const WithMemoryRouter = ({ index, ...props }) => (
  <MemoryRouter
    initialEntries={[
      "/measures_and_registries/export",
      "/measures_and_registries/maintenance"
    ]}
    initialIndex={index}
    keyLength={0}
  >
    <MaintenanceContent {...props} />
  </MemoryRouter>
);

const mockedComponent = () =>
  create(
    decoratedApollo({
      component: WithMemoryRouter,
      props: { index: 0, selectedRegistry: { value: 1, label: "test" } },
      initialAppValues: {},
      apolloMocks
    })
  );

describe("MaintenanceContent", () => {
  test("renders component", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
});
