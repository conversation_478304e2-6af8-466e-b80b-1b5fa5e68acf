// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MaintenanceContent renders component 1`] = `
Array [
  <MaintenanceTabs
    filters={Object {}}
    onChangeField={[Function]}
    pagination={
      Object {
        "currentPage": 1,
        "options": Array [
          5,
          10,
          25,
        ],
        "rowsPerPage": 25,
        "setCurrentPage": [Function],
        "setRowsPerPage": [Function],
      }
    }
    selectedRegistry={
      Object {
        "label": "test",
        "value": 1,
      }
    }
  />,
  <TableContent
    filters={Object {}}
    pagination={
      Object {
        "currentPage": 1,
        "options": Array [
          5,
          10,
          25,
        ],
        "rowsPerPage": 25,
        "setCurrentPage": [Function],
        "setRowsPerPage": [Function],
      }
    }
    selectedRegistry={
      Object {
        "label": "test",
        "value": 1,
      }
    }
    tab="export"
  />,
]
`;
