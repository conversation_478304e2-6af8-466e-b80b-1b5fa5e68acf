import { act, create } from "react-test-renderer";
import wait from "waait";
import apolloMocks from "modules/maintenance/components/Maintenance/mocks";
import { decoratedApollo } from "utils/test/decorated";
import TableContent from "..";

jest.mock("@q-centrix/q-components-react", () => ({ Spinner: "Spinner" }));
jest.mock(
  "modules/maintenance/components/shared/Pagination",
  () => "Pagination"
);
jest.mock("../DataTable", () => "DataTable");
jest.mock("../HeaderTable", () => "HeaderTable");

const paginationProps = { currentPage: 1, rowsPerPage: 25 };

const mockedComponent = (props = {}) =>
  create(
    decoratedApollo({
      component: TableContent,
      props: {
        pagination: paginationProps,
        tab: "coc",
        selectedRegistry: {
          id: 1,
          label: "Oncology"
        },
        ...props
      },
      initialAppValues: {},
      apolloMocks
    })
  );

describe("TableContent", () => {
  test("renders component while loading", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
  test("renders component after loading", async () => {
    const component = mockedComponent();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
