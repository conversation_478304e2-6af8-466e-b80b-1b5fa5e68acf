// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableContent renders component after loading 1`] = `
<section
  className="maintenance-content"
>
  <div
    className="maintenance-content-header"
  >
    <Pagination
      currentPage={1}
      records={300}
      rowsPerPage={25}
    />
  </div>
  <HeaderTable
    headers={
      Array [
        "Success Status",
        "Export Date",
        "Requestor",
        "Query Status",
        "File Name",
        "Date Type",
        "Start Date",
        "End Date",
        "Submission Status Change Date",
        "Download",
      ]
    }
  />
  <DataTable
    data={
      Array [
        Object {
          "__typename": "CoCData",
          "dataFile": Object {
            "__typename": "DataFile",
            "fileName": "cedi_technologies.nbp.zip",
            "id": "39",
            "invalidAbstractionCount": 9,
            "patientCount": 0,
            "status": "submitted",
            "statusUpdatedAt": "2021-11-11",
            "url": "/files/cedi_technologies.nbp.zip",
            "validAbstractionCount": 4,
          },
          "dateFilters": Array [
            Object {
              "dateType": "Date of Diagnosis",
              "endDate": "2024-02-05",
              "startDate": "2024-01-21",
            },
            Object {
              "dateType": "Date of First Contact",
              "endDate": "2024-02-07",
              "startDate": "2024-02-01",
            },
            Object {
              "dateType": "Date Case Last Changed",
              "endDate": "2024-02-05",
              "startDate": "2024-02-01",
            },
          ],
          "exportDate": "2021-11-11",
          "primarySite": "",
          "requestor": "Georgiana Jacobson",
          "status": "submitted",
          "treatmentStatus": "",
        },
        Object {
          "__typename": "CoCData",
          "dataFile": Object {
            "__typename": "DataFile",
            "fileName": "cedi_technologies.nbp.zip",
            "id": "39",
            "invalidAbstractionCount": 9,
            "patientCount": 0,
            "status": "submitted",
            "statusUpdatedAt": "2021-11-11",
            "url": "/files/cedi_technologies.nbp.zip",
            "validAbstractionCount": 4,
          },
          "dateFilters": Array [
            Object {
              "dateType": "Date of Diagnosis",
              "endDate": "2024-02-05",
              "startDate": "2024-01-21",
            },
            Object {
              "dateType": "Date of First Contact",
              "endDate": "2024-02-07",
              "startDate": "2024-02-01",
            },
            Object {
              "dateType": "Date Case Last Changed",
              "endDate": "2024-02-05",
              "startDate": "2024-02-01",
            },
          ],
          "exportDate": "2021-11-11",
          "primarySite": "",
          "requestor": "Alexa Wuckert",
          "status": "not_submitted",
          "treatmentStatus": "",
        },
        Object {
          "__typename": "CoCData",
          "dataFile": Object {
            "__typename": "DataFile",
            "fileName": "cedi_technologies.nbp.zip",
            "id": "39",
            "invalidAbstractionCount": 9,
            "patientCount": 0,
            "status": "submitted",
            "statusUpdatedAt": "2021-11-11",
            "url": "/files/cedi_technologies.nbp.zip",
            "validAbstractionCount": 4,
          },
          "dateFilters": Array [
            Object {
              "dateType": "Date of Diagnosis",
              "endDate": "2024-02-05",
              "startDate": "2024-01-21",
            },
            Object {
              "dateType": "Date of First Contact",
              "endDate": "2024-02-07",
              "startDate": "2024-02-01",
            },
            Object {
              "dateType": "Date Case Last Changed",
              "endDate": "2024-02-05",
              "startDate": "2024-02-01",
            },
          ],
          "exportDate": "2021-11-11",
          "primarySite": "",
          "requestor": "Sigmund Hoeger",
          "status": "not_submitted",
          "treatmentStatus": "",
        },
        Object {
          "__typename": "CoCData",
          "dataFile": Object {
            "__typename": "DataFile",
            "fileName": "cedi_technologies.nbp.zip",
            "id": "39",
            "invalidAbstractionCount": 9,
            "patientCount": 0,
            "status": "submitted",
            "statusUpdatedAt": "2021-11-11",
            "url": "/files/cedi_technologies.nbp.zip",
            "validAbstractionCount": 4,
          },
          "dateFilters": Array [
            Object {
              "dateType": "Date of Diagnosis",
              "endDate": "2024-02-05",
              "startDate": "2024-01-21",
            },
            Object {
              "dateType": "Date of First Contact",
              "endDate": "2024-02-07",
              "startDate": "2024-02-01",
            },
            Object {
              "dateType": "Date Case Last Changed",
              "endDate": "2024-02-05",
              "startDate": "2024-02-01",
            },
          ],
          "exportDate": "2021-11-11",
          "primarySite": "",
          "requestor": "Verna Murphy",
          "status": "not_submitted",
          "treatmentStatus": "",
        },
      ]
    }
    selectedRegistry={
      Object {
        "id": 1,
        "label": "Oncology",
      }
    }
    tab="coc"
  />
</section>
`;

exports[`TableContent renders component while loading 1`] = `<Spinner />`;
