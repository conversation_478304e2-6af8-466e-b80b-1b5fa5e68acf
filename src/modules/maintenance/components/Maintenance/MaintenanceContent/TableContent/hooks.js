import { useMemo } from "react";
import { useTableData } from "modules/maintenance/hooks";
import { mockHeaders } from "../../data";

export const useComponentLogic = ({
  filters,
  pagination,
  tab,
  selectedRegistry
}) => {
  const headers = useMemo(() => mockHeaders[tab], [tab]);

  const { records, tableData, tableDataError, tableDataLoading } = useTableData(
    { filters, pagination, tab, selectedRegistry }
  );

  const paginationProps = useMemo(
    () => ({ ...pagination, records }),
    [pagination, records]
  );

  return {
    data: tableData,
    error: tableDataError,
    headers,
    loading: tableDataLoading,
    paginationProps,
    tab
  };
};
