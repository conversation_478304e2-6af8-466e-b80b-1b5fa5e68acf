import { Spinner } from "@q-centrix/q-components-react";
import Pagination from "modules/maintenance/components/shared/Pagination";
import DataTable from "./DataTable";
import HeaderTable from "./HeaderTable";
import { useComponentLogic } from "./hooks";

export const TableContent = ({ selectedRegistry, ...props }) => {
  const { data, headers, loading, paginationProps, tab } = useComponentLogic({
    ...props,
    selectedRegistry
  });

  if (loading) return <Spinner />;

  return (
    <section className="maintenance-content">
      <div className="maintenance-content-header">
        <Pagination {...paginationProps} />
      </div>
      <HeaderTable headers={headers} />
      <DataTable data={data} tab={tab} selectedRegistry={selectedRegistry} />
    </section>
  );
};

export default TableContent;
