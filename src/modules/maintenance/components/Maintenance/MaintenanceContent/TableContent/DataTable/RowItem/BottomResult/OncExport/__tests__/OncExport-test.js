import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import { omit } from "ramda";
import { fakeRecord } from "modules/maintenance/components/Maintenance/data";
import OncExport from "..";

jest.mock("../StatusButton", () => "StatusButton");
jest.mock("../StatusMenu", () => "StatusMenu");

const mockedComponent = props =>
  create(
    <MemoryRouter
      initialEntries={[`/measures_and_registries/${props.path}`]}
      keyLength={0}
    >
      <OncExport {...props} />
    </MemoryRouter>
  );

describe("OncExport", () => {
  test("renders component", () => {
    const component = mockedComponent({
      item: fakeRecord("1"),
      tab: "coc",
      path: "coc"
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component without datafile", () => {
    const component = mockedComponent({
      item: omit(["dataFile"], fakeRecord("1")),
      tab: "coc",
      path: "coc"
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component with clickable = false", () => {
    const component = mockedComponent({
      item: fakeRecord("1"),
      tab: "state",
      path: "state"
    });

    expect(component).toMatchSnapshot();
  });
});
