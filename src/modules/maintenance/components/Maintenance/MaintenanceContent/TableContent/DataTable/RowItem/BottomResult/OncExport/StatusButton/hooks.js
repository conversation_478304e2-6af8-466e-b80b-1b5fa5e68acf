import { useCallback } from "react";
import { useMutation } from "@apollo/client";
import {
  __,
  always,
  curry,
  findIndex,
  identity,
  ifElse,
  lensPath,
  pipe,
  prop,
  propEq,
  set,
  view
} from "ramda";
import useTab from "modules/maintenance/hooks/useTab";
import { GET_TABLE_DATA as query } from "modules/maintenance/components/Maintenance/query";
import mutation from "./mutation";

const toggle1And0 = ifElse(
  identity,
  always({ code: 1, text: "Submitted", __typename: "StatusOption" }),
  always({ code: 0, text: "Not Submitted", __typename: "StatusOption" })
);
const toggle1And0ForLens = ifElse(
  identity,
  always({ code: 0, text: "Not Submitted", __typename: "StatusOption" }),
  always({ code: 1, text: "Submitted", __typename: "StatusOption" })
);

const createCodeStatusLens = ind =>
  lensPath(["tableData", ind, "submissionStatus", "code"]);
const createStatusLens = ind =>
  lensPath(["tableData", ind, "submissionStatus"]);

const lensUpdate = curry((ind, data) =>
  pipe(
    view(createCodeStatusLens(ind)),
    toggle1And0ForLens,
    set(createStatusLens(ind), __, data)
  )(data)
);
const getIndex = id => pipe(prop("tableData"), findIndex(propEq("id", id)));

const updateStatus = (id, data) =>
  pipe(getIndex(id), lensUpdate(__, data))(data);

export const useUpdateDataFileStatusMutation = ({
  code,
  exportItemId,
  selectedRegistry,
  updateItem
}) => {
  const { tab } = useTab();

  const [
    updateDataFileStatus,
    { loading: mutationLoading, error: mutationError }
  ] = useMutation(mutation, {
    variables: {
      code: Number(code),
      dataFileId: exportItemId,
      registryName: selectedRegistry.label
    },
    update: store => {
      const variables = { currentPage: 1, filters: {}, rowsPerPage: 25, tab };
      const data = store.readQuery({
        query,
        variables
      });

      // Save changes in the Apollo store
      store.writeQuery({
        query,
        variables,
        data: updateStatus(exportItemId, data)
      });
      updateItem("submissionStatus", toggle1And0(code), exportItemId);
    }
  });

  return { updateDataFileStatus, mutationLoading, mutationError };
};

export const useComponentLogic = props => {
  const { selected, toggleMenu } = props;
  const { updateDataFileStatus } = useUpdateDataFileStatusMutation(props);
  const handleClick = useCallback(() => {
    !selected && updateDataFileStatus();
    toggleMenu();
  }, [selected, toggleMenu, updateDataFileStatus]);

  return { handleClick };
};
