// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OncExport renders component 1`] = `
<tr>
  <th
    className="not_submitted"
    title="not_submitted"
  >
    not_submitted
  </th>
  <th
    className="not_submitted"
    title="2021-11-11"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="not_submitted"
    title="<PERSON><PERSON><PERSON>"
  >
    <PERSON><PERSON><PERSON>
  </th>
  <th
    className="not_submitted"
  />
  <th
    className="not_submitted"
    title="cedi_technologies.nbp.zip"
  >
    cedi_technologies.nbp.zip
  </th>
  <th
    className="date-filters-head-row"
    colSpan="3"
  />
  <th
    className="not_submitted"
    title="11/11/2021 | 00:00"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="center"
    rowSpan="2"
  >
    <a
      download={true}
      href="http://localhost:3000/files/cedi_technologies.nbp.zip"
    >
      <i
        className="fas fa-download export-download"
      />
    </a>
  </th>
</tr>
`;

exports[`OncExport renders component multiple date types 1`] = `
<tr>
  <th
    className="not_submitted"
    title="not_submitted"
  >
    not_submitted
  </th>
  <th
    className="not_submitted"
    title="2021-11-11"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="not_submitted"
    title="Wilburn Zemlak"
  >
    Wilburn Zemlak
  </th>
  <th
    className="not_submitted"
  />
  <th
    className="not_submitted"
    title="cedi_technologies.nbp.zip"
  >
    cedi_technologies.nbp.zip
  </th>
  <th
    className="date-filters-head-row"
    colSpan="3"
  >
    <tr
      className="date-filters-row"
    >
      <td>
        Date of Diagnosis
      </td>
      <td>
        2/01/2021
      </td>
      <td>
        4/01/2021
      </td>
    </tr>
    <tr
      className="date-filters-row"
    >
      <td>
        Date of First Contact
      </td>
      <td>
        6/01/2021
      </td>
      <td>
        12/01/2021
      </td>
    </tr>
    <tr
      className="date-filters-row"
    >
      <td>
        Date Case Last Changed
      </td>
      <td>
        1/01/2021
      </td>
      <td>
        3/01/2021
      </td>
    </tr>
  </th>
  <th
    className="not_submitted"
    title="11/11/2021 | 00:00"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="center"
    rowSpan="2"
  >
    <a
      download={true}
      href="http://localhost:3000/files/cedi_technologies.nbp.zip"
    >
      <i
        className="fas fa-download export-download"
      />
    </a>
  </th>
</tr>
`;

exports[`OncExport renders component only one date type start and end date 1`] = `
<tr>
  <th
    className="not_submitted"
    title="not_submitted"
  >
    not_submitted
  </th>
  <th
    className="not_submitted"
    title="2021-11-11"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="not_submitted"
    title="Johan Pfannerstill"
  >
    Johan Pfannerstill
  </th>
  <th
    className="not_submitted"
  />
  <th
    className="not_submitted"
    title="cedi_technologies.nbp.zip"
  >
    cedi_technologies.nbp.zip
  </th>
  <th
    className="date-filters-head-row"
    colSpan="3"
  >
    <tr
      className="date-filters-row date-filters-row--single"
    >
      <td>
        Date Case Last Changed
      </td>
      <td>
        1/01/2021
      </td>
      <td>
        3/01/2021
      </td>
    </tr>
  </th>
  <th
    className="not_submitted"
    title="11/11/2021 | 00:00"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="center"
    rowSpan="2"
  >
    <a
      download={true}
      href="http://localhost:3000/files/cedi_technologies.nbp.zip"
    >
      <i
        className="fas fa-download export-download"
      />
    </a>
  </th>
</tr>
`;

exports[`OncExport renders component only one date type start date 1`] = `
<tr>
  <th
    className="not_submitted"
    title="not_submitted"
  >
    not_submitted
  </th>
  <th
    className="not_submitted"
    title="2021-11-11"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="not_submitted"
    title="Deontae Bosco"
  >
    Deontae Bosco
  </th>
  <th
    className="not_submitted"
  />
  <th
    className="not_submitted"
    title="cedi_technologies.nbp.zip"
  >
    cedi_technologies.nbp.zip
  </th>
  <th
    className="date-filters-head-row"
    colSpan="3"
  >
    <tr
      className="date-filters-row date-filters-row--single"
    >
      <td>
        Date of First Contact
      </td>
      <td>
        1/01/2021
      </td>
      <td>
        --
      </td>
    </tr>
  </th>
  <th
    className="not_submitted"
    title="11/11/2021 | 00:00"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="center"
    rowSpan="2"
  >
    <a
      download={true}
      href="http://localhost:3000/files/cedi_technologies.nbp.zip"
    >
      <i
        className="fas fa-download export-download"
      />
    </a>
  </th>
</tr>
`;
