import classnames from "classnames";
import BottomResult from "./BottomResult";
import TopResult from "./TopResult";
import { equals } from "ramda";

const RowItem = props => {
  const { tab } = props;
  const tableClassName = classnames("maintenance-data-table");
  const sectionClassName = classnames("maintenance-data-table-container", {
    conversion: equals("conversion", tab)
  });

  return (
    <section className={sectionClassName}>
      <table className={tableClassName}>
        <tbody>
          <TopResult {...props} />
          <BottomResult {...props} />
        </tbody>
      </table>
    </section>
  );
};

export default RowItem;
