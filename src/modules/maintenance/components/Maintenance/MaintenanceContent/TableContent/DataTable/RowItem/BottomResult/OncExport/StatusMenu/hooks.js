import { useRef } from "react";
import useOnClickOutside from "utils/hooks/useOnClickOutside";
import { useQuery } from "@apollo/client";
import query from "./query";

export const useComponentLogic = close => {
  const ref = useRef(null);
  const {
    data: { exportDataFileStatusOptions } = {
      exportDataFileStatusOptions: []
    },
    loading,
    error
  } = useQuery(query);

  useOnClickOutside(ref, close);
  return { error, loading, ref, statuses: exportDataFileStatusOptions };
};
