import { useCallback, useState } from "react";
import { useLocation } from "react-router-dom";
import { split, pipe, nth, equals, not } from "ramda";

export const useComponentLogic = () => {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const toggleMenu = useCallback(() => setMenuIsOpen(not), [setMenuIsOpen]);

  const { pathname } = useLocation();
  const cocPath = pipe(split("/"), nth(-1), equals("coc"))(pathname);

  return { menuIsOpen, toggleMenu, cocPath };
};
