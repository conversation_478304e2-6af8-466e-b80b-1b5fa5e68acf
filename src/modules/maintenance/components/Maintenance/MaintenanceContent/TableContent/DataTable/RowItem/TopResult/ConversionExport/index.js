import classnames from "classnames";
import { applySpec, concat, either, pathOr, pipe, propEq } from "ramda";
import { formatDateTime } from "utils/formatMaintenanceTime";
import { serverURI } from "base/constants";
import { cleanURL } from "utils/fp";

export function ConversionExport({ item }) {
  const buttonClass = classnames("center", {
    disabled: either(
      propEq("status", "error"),
      propEq("status", "pending")
    )(item)
  });
  const dataCellClass = classnames(item.status);
  const { fileName, downloadURL } = applySpec({
    fileName: pathOr("--", ["dataFile", "fileName"]),
    downloadURL: pipe(
      pathOr("", ["dataFile", "url"]),
      concat(serverURI),
      cleanURL
    )
  })(item);

  return (
    <tr>
      <th className={dataCellClass} title={item.status}>
        {item.status}
      </th>
      <th className={dataCellClass} title={item.exportDate}>
        {formatDateTime(item.exportDate)}
      </th>
      <th className={dataCellClass} title={item.requestor}>
        {item.requestor}
      </th>
      <th
        className={dataCellClass}
        title={item.queryStatus || item.treatmentStatus}
      >
        {item.queryStatus || item.treatmentStatus}
      </th>
      <th className={dataCellClass} title={fileName}>
        {fileName}
      </th>
      <th rowSpan="2" className={buttonClass}>
        <a href={downloadURL} download>
          <i className="fas fa-download export-download" />
        </a>
      </th>
    </tr>
  );
}
export default ConversionExport;
