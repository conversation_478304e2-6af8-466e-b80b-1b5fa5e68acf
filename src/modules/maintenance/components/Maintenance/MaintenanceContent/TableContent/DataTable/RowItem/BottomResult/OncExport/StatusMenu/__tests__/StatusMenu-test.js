import { act, create } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import apolloMocks from "../../mocks";
import StatusMenu from "..";

jest.mock("@q-centrix/q-components-react", () => ({ Spinner: "Spinner" }));
jest.mock("../../StatusButton", () => "StatusButton");

describe("StatusMenu", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: StatusMenu,
        props,
        apolloMocks
      })
    );
  }

  test("it renders correctly when open=true", async () => {
    const output = render({ open: true });

    await act(() => wait(300));
    expect(output).toMatchSnapshot();
  });

  test("it renders correctly when open=false", async () => {
    const output = render({ open: false });

    await act(() => wait(300));
    expect(output).toMatchSnapshot();
  });

  test("it renders statuses correctly when open", async () => {
    const output = render({
      open: true
    });

    await act(() => wait(300));
    expect(output).toMatchSnapshot();
  });
});
