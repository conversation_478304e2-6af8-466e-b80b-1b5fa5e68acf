import { create } from "react-test-renderer";
import Extract from "..";
import { fakeExtractRecord } from "modules/maintenance/components/Maintenance/data";

const mockedComponent = props => create(<Extract {...props} />);

describe("Extract", () => {
  test("renders component", () => {
    const component = mockedComponent({
      item: fakeExtractRecord(1, "completed", "ExtractData")
    });

    expect(component).toMatchSnapshot();
  });
});
