import classnames from "classnames";
import { either, propEq } from "ramda";
import { useComponentLogic } from "./hooks";

export function ConversionExport(props) {
  const { item } = props;
  const { totalAbstractionsCount } = useComponentLogic(item);

  const reportCellClass = classnames({
    disabled: either(
      propEq("status", "error"),
      propEq("status", "pending")
    )(item)
  });

  return (
    <tr>
      <td colSpan="5">
        <ul className="results-row conversion">
          <li className={reportCellClass}>Report</li>
          <li className={reportCellClass}>Total Abstractions</li>
          <li className={reportCellClass}>{totalAbstractionsCount}</li>
        </ul>
      </td>
    </tr>
  );
}

export default ConversionExport;
