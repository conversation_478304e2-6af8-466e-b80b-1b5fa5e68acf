import { create } from "react-test-renderer";
import Import from "..";
import { fakeImportRecord } from "modules/maintenance/components/Maintenance/data";

const mockedComponent = props => create(<Import {...props} />);

describe("Import", () => {
  test("renders component", () => {
    const component = mockedComponent({
      item: fakeImportRecord("1", "completed", "ImportData")
    });

    expect(component).toMatchSnapshot();
  });
});
