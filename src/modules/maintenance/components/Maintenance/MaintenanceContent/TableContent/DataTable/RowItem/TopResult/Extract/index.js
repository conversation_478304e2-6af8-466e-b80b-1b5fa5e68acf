import { useComponentLogic } from "./hooks";

export function TopResult(props) {
  const {
    status,
    dateType,
    startDate,
    endDate,
    requestDate,
    fileName,
    downloadURL,
    numberOfFields,
    recordsWritten,
    dataCellClass,
    buttonClass
  } = useComponentLogic(props);

  return (
    <tr>
      <th className={dataCellClass}>{status}</th>
      <th className={dataCellClass}>{requestDate}</th>
      <th className={dataCellClass}>{fileName}</th>
      <th className={dataCellClass}>{numberOfFields}</th>
      <th className={dataCellClass}>{dateType}</th>
      <th className={dataCellClass}>{startDate}</th>
      <th className={dataCellClass}>{endDate}</th>
      <th className={dataCellClass}>{recordsWritten}</th>
      <th rowSpan="2" className={buttonClass}>
        <a href={downloadURL} download>
          <i className="fas fa-download export-download" />
        </a>
      </th>
    </tr>
  );
}
export default TopResult;
