import { GET_EXPORT_DATA_FILE_STATUS_OPTIONS } from "./StatusMenu/query";
import { UPDATE_STATUS } from "./StatusButton/mutation";

const statusOptions = [
  { code: 0, text: "Not Submitted", __typename: "StatusOption" },
  { code: 1, text: "Submitted", __typename: "StatusOption" }
];
const mutationSuccessMocks = [
  {
    request: {
      query: UPDATE_STATUS,
      variables: { code: 1, dataFileId: 21, registryName: "Oncology" }
    },
    result: {
      data: {
        updateDataFileStatus: {
          response: true,
          fullMessages: null,
          __typename: "MutationResponse"
        }
      }
    }
  },
  {
    request: {
      query: UPDATE_STATUS,
      variables: { code: 0, dataFileId: 21, registryName: "Oncology" }
    },
    result: {
      data: {
        updateDataFileStatus: {
          response: true,
          fullMessages: null,
          __typename: "MutationResponse"
        }
      }
    }
  },
  {
    request: {
      query: UPDATE_STATUS,
      variables: { code: 1, dataFileId: 26, registryName: "Oncology" }
    },
    result: {
      data: {
        updateDataFileStatus: {
          response: true,
          fullMessages: null,
          __typename: "MutationResponse"
        }
      }
    }
  },
  {
    request: {
      query: UPDATE_STATUS,
      variables: { code: 0, dataFileId: 26, registryName: "Oncology" }
    },
    result: {
      data: {
        updateDataFileStatus: {
          response: true,
          fullMessages: null,
          __typename: "MutationResponse"
        }
      }
    }
  },
  {
    request: {
      query: UPDATE_STATUS,
      variables: { code: 312, dataFileId: 26, registryName: "Oncology" }
    },
    result: {
      data: {
        updateDataFileStatus: {
          response: false,
          fullMessages: ["An error occurred"],
          __typename: "MutationResponse"
        }
      }
    }
  }
];

export const statusOptionsRequestSuccess = {
  request: {
    query: GET_EXPORT_DATA_FILE_STATUS_OPTIONS
  },
  result: {
    data: {
      exportDataFileStatusOptions: statusOptions
    }
  }
};

export const statusMocks = [
  ...mutationSuccessMocks,
  statusOptionsRequestSuccess
];

export default statusMocks;
