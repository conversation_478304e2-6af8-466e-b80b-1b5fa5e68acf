import { __, compose, defaultTo, prop } from "ramda";
import { capitalize } from "utils/fp";
import OncExport from "./OncExport";
import ConversionExport from "./ConversionExport";
import Extract from "./Extract";
import Export from "./Export";
// import Maintenance from "./Maintenance";

const Unimplemented = () => null;

const type = compose(
  defaultTo(Unimplemented),
  prop(__, {
    Coc: OncExport,
    Extract,
    Export,
    State: OncExport,
    Conversion: ConversionExport
  })
);

const BottomResult = props => {
  const { tab } = props;

  const Item = type(capitalize(tab));

  return <Item {...props} />;
};

export default BottomResult;
