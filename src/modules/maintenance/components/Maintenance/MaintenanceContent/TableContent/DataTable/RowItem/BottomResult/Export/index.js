import classnames from "classnames";
import { either, propEq } from "ramda";

export function Export({ item }) {
  const reportCellClass = classnames({
    disabled: either(
      propEq("status", "error"),
      propEq("status", "pending")
    )(item)
  });

  return (
    <tr>
      <td colSpan="6">
        <ul className="results-row">
          <li className={reportCellClass}>Report</li>
          <li className={reportCellClass}>Patient</li>
          <li className={reportCellClass}>{item.patient}</li>
          <li className={reportCellClass}>Queired Abstraction</li>
          <li className={reportCellClass}>{item.queiredAbstractions}</li>
          <li className={reportCellClass}>Written Abstraction</li>
          <li className={reportCellClass}>{item.writtenAbstractions}</li>
          <li className={reportCellClass}>Requester</li>
          <li className={reportCellClass}>{item.requester}</li>
          <li className={reportCellClass}>
            <a href="#">Invalid Abstractions</a>
          </li>
          <li className={reportCellClass}>{item.invalidAbstractions}</li>
        </ul>
      </td>
    </tr>
  );
}

export default Export;
