import { useCallback, useState, useEffect } from "react";
import { findIndex, pipe, propEq, lensIndex, over, __, assoc } from "ramda";

// eslint-disable-next-line max-params
const updateItemFn = (key, value, id) => items =>
  pipe(
    findIndex(propEq("id", id)),
    lensIndex,
    over(__, assoc(key, value), items)
  )(items);

export const useComponentLogic = ({ data }) => {
  const [items, setItems] = useState(data);
  const updateItem = useCallback((key, value, id) => {
    setItems(updateItemFn(key, value, id));
  }, []);

  useEffect(() => {
    setItems(data);
  }, [data]);

  return { items, updateItem };
};
