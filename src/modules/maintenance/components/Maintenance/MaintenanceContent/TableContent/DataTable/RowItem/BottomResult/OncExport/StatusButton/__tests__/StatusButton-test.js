import { useQuery } from "@apollo/client";
import { MemoryRouter } from "react-router-dom";
import { act, create } from "react-test-renderer";
import wait from "waait";
import { cache } from "base/apolloClient";
import { GET_TABLE_DATA } from "modules/maintenance/components/Maintenance/query";
import apolloMocks from "modules/maintenance/components/Maintenance/mocks";
import { decoratedApollo } from "utils/test/decorated";
// import apolloMocks from "../../mocks";
import { StatusButton } from "..";

const Wrapper = props => {
  useQuery(GET_TABLE_DATA, {
    variables: {
      currentPage: 1,
      rowsPerPage: "25",
      tab: "coc",
      registry: "Oncology"
    }
  });

  return (
    <MemoryRouter
      initialEntries={["/measures_and_registries/coc"]}
      keyLength={0}
    >
      <StatusButton {...props} />
    </MemoryRouter>
  );
};

describe("StatusButton", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: Wrapper,
        props,
        initialAppValues: {
          maintenance: {
            selectedRegistry: {
              label: "Oncology",
              id: 1
            }
          }
        },
        apolloMocks
      })
    );
  }

  test("it renders correctly", () => {
    const output = render({
      clickable: true,
      selectedRegistry: { label: "Oncology" }
    });

    expect(output).toMatchSnapshot();
  });

  test("tests toggleMenu is called when clicked", () => {
    const component = render({
      code: 1,
      exportItemId: 21,
      selected: false,
      text: "Test Button",
      clickable: true,
      updateItem: jest.fn(),
      toggleMenu: jest.fn(),
      selectedRegistry: { label: "Oncology" }
    });
    const instance = component.root;
    const sButton = instance.findByType(StatusButton);
    const button = instance.findByType("button");

    const spy = jest.spyOn(sButton.props, "toggleMenu");

    act(() => button.props.onClick());

    expect(spy).toHaveBeenCalled();
  });
  // We need fix this once the mutation is done
  // on the connect card
  test.skip("tests mutation when clicked", async () => {
    const component = render({
      code: 0,
      exportItemId: 21,
      registryName: "Oncology",
      selected: false,
      text: "Test Button",
      updateItem: jest.fn(),
      toggleMenu: jest.fn()
    });
    const instance = component.root;
    const button = instance.findByType("button");

    await act(() => wait(3));
    const earlyCache = cache.extract();

    expect(earlyCache.StateData21.dataFile.code).toBe(1);

    act(() => button.props.onClick());
    await act(() => wait(3));
    const updatedCache = cache.extract();

    expect(updatedCache.StateData21.dataFile.code).toBe(0);
  });
});
