// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StatusMenu it renders correctly when open=false 1`] = `null`;

exports[`StatusMenu it renders correctly when open=true 1`] = `
<div
  className="status-menu"
  style={
    Object {
      "opacity": 0,
      "transform": "translateX(0px) translateY(100px) translateZ(0)",
    }
  }
>
  <StatusButton
    __typename="StatusOption"
    clickable={true}
    code={0}
    text="Not Submitted"
    toggleMenu={[Function]}
  />
  <StatusButton
    __typename="StatusOption"
    clickable={true}
    code={1}
    text="Submitted"
    toggleMenu={[Function]}
  />
</div>
`;

exports[`StatusMenu it renders statuses correctly when open 1`] = `
<div
  className="status-menu"
  style={
    Object {
      "opacity": 0,
      "transform": "translateX(0px) translateY(100px) translateZ(0)",
    }
  }
>
  <StatusButton
    __typename="StatusOption"
    clickable={true}
    code={0}
    text="Not Submitted"
    toggleMenu={[Function]}
  />
  <StatusButton
    __typename="StatusOption"
    clickable={true}
    code={1}
    text="Submitted"
    toggleMenu={[Function]}
  />
</div>
`;
