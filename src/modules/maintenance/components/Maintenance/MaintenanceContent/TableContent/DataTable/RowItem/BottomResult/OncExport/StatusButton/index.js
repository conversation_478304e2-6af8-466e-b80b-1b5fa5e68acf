import classnames from "classnames";
import { useComponentLogic } from "./hooks";

export const StatusButton = props => {
  const { code, selected, text, clickable } = props;
  const buttonClassName = classnames("status-btn", {
    submitted: code,
    selected
  });
  const { handleClick } = useComponentLogic(props);

  return (
    <button
      className={buttonClassName}
      onClick={clickable && handleClick}
      type="button"
    >
      {text.replace("_", " ")}
    </button>
  );
};

StatusButton.defaultProps = {
  code: "0",
  selected: false,
  text: "Not Submitted",
  // eslint-disable-next-line no-empty-function
  toggleMenu: () => {}
};

export default StatusButton;
