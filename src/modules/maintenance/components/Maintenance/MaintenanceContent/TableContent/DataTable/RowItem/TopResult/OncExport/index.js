import classnames from "classnames";
import {
  always,
  applySpec,
  concat,
  either,
  filter,
  identity,
  ifElse,
  path,
  pathOr,
  pipe,
  prop,
  propEq,
  propOr
} from "ramda";
import { formatDate, formatDateTime } from "utils/formatMaintenanceTime";
import { serverURI } from "base/constants";
import { cleanURL } from "utils/fp";

export const StateExport = props => (
  <TopResult
    {...props}
    dateTypeField="dateType"
    startDateField="startDate"
    endDateField="endDate"
  />
);

export const CocExport = props => (
  <TopResult {...props} dateFilters="dateFilters" />
);

const displayDate = propPath =>
  pipe(path(propPath), ifElse(identity, formatDateTime, always("--")));
const formatDateCell = ifElse(identity, formatDate, always("--"));

export function TopResult({ item }) {
  const buttonClass = classnames("center", {
    disabled: either(
      propEq("status", "error"),
      propEq("status", "pending")
    )(item)
  });
  const { dateFilters, statusUpdatedAt, fileName, downloadURL } = applySpec({
    dateFilters: pipe(
      propOr([], "dateFilters"),
      filter(either(prop("startDate"), prop("endDate")))
    ),
    statusUpdatedAt: displayDate(["dataFile", "statusUpdatedAt"]),
    fileName: pathOr("--", ["dataFile", "fileName"]),
    downloadURL: pipe(
      pathOr("", ["dataFile", "url"]),
      concat(serverURI),
      cleanURL
    )
  })(item);

  const dataCellClass = classnames(item.status);
  const dateFiltersClass = classnames("date-filters-row", {
    "date-filters-row--single": dateFilters.length === 1
  });

  return (
    <tr>
      <th className={dataCellClass} title={item.status}>
        {item.status}
      </th>
      <th className={dataCellClass} title={item.exportDate}>
        {formatDateTime(item.exportDate)}
      </th>
      <th className={dataCellClass} title={item.requestor}>
        {item.requestor}
      </th>
      <th
        className={dataCellClass}
        title={item.queryStatus || item.treatmentStatus}
      >
        {item.queryStatus || item.treatmentStatus}
      </th>
      <th className={dataCellClass} title={fileName}>
        {fileName}
      </th>
      <th colSpan="3" className="date-filters-head-row">
        {dateFilters.map(({ dateType, startDate, endDate }) => (
          <tr
            key={`${dateType}-${startDate}-${endDate}`}
            className={dateFiltersClass}
          >
            <td>{dateType}</td>
            <td>{formatDateCell(startDate)}</td>
            <td>{formatDateCell(endDate)}</td>
          </tr>
        ))}
      </th>

      <th className={dataCellClass} title={statusUpdatedAt}>
        {statusUpdatedAt}
      </th>
      <th rowSpan="2" className={buttonClass}>
        <a href={downloadURL} download>
          <i className="fas fa-download export-download" />
        </a>
      </th>
    </tr>
  );
}
export default TopResult;
