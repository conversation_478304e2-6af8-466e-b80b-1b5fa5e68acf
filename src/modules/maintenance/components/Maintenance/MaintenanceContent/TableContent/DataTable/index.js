import { useComponentLogic } from "./hooks";
import RowItem from "./RowItem";

const DataTable = props => {
  const { tab, selectedRegistry } = props;
  const { items, updateItem } = useComponentLogic(props);

  return (
    <div>
      {items.map(item => (
        <RowItem
          key={item.id}
          selectedRegistry={selectedRegistry}
          item={item}
          tab={tab}
          updateItem={updateItem}
        />
      ))}
    </div>
  );
};

DataTable.defaultProps = {
  data: []
};

export default DataTable;
