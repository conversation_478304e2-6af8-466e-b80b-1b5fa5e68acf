import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import { omit } from "ramda";
import { fakeRecord } from "modules/maintenance/components/Maintenance/data";
import ConversionExport from "..";

const mockedComponent = props =>
  create(
    <MemoryRouter
      initialEntries={[`/measures_and_registries/${props.path}`]}
      keyLength={0}
    >
      <ConversionExport {...props} />
    </MemoryRouter>
  );

describe("ConversionExport", () => {
  test("renders component", () => {
    const component = mockedComponent({
      item: fakeRecord("1"),
      tab: "conversion",
      path: "conversion"
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component without datafile", () => {
    const component = mockedComponent({
      item: omit(["dataFile"], fakeRecord("1")),
      tab: "conversion",
      path: "conversion"
    });

    expect(component).toMatchSnapshot();
  });
});
