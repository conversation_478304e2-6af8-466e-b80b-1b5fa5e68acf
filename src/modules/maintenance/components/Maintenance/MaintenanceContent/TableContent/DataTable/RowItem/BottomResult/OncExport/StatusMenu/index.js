import { AnimatePresence, motion } from "framer-motion";
import { Spin<PERSON> } from "@q-centrix/q-components-react";
import StatusButton from "../StatusButton";
import { useComponentLogic } from "./hooks";

export const variants = {
  enter: {
    opacity: 0,
    x: 0,
    y: 100
  },
  animate: { opacity: 1, x: 0, y: 0 },
  exit: {
    opacity: 0,
    x: 0,
    y: -100
  }
};
export const StatusMenu = ({
  open,
  exportItemId,
  toggleMenu,
  updateItem,
  selectedRegistry
}) => {
  const { loading, ref, statuses } = useComponentLogic(toggleMenu);

  if (loading) return <Spinner />;
  return (
    <AnimatePresence>
      {open && (
        <motion.div
          animate="animate"
          exit="exit"
          initial="enter"
          variants={variants}
          className="status-menu"
          ref={ref}
        >
          {statuses.map(status => (
            <StatusButton
              key={status.code}
              exportItemId={exportItemId}
              toggleMenu={toggleMenu}
              updateItem={updateItem}
              selectedRegistry={selectedRegistry}
              clickable
              {...status}
            />
          ))}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

StatusMenu.defaultProps = {
  open: false,
  // eslint-disable-next-line no-empty-function
  toggleMenu: () => {}
};

export default StatusMenu;
