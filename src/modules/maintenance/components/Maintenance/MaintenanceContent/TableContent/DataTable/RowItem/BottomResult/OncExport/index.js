import { Link } from "react-router-dom";
import classnames from "classnames";
import { either, propEq } from "ramda";
import StatusButton from "./StatusButton";
import StatusMenu from "./StatusMenu";
import { useComponentLogic } from "./hooks";

export function Export({ item, tab, updateItem, selectedRegistry }) {
  const { menuIsOpen, toggleMenu, cocPath } = useComponentLogic();

  const reportCellClass = classnames({
    disabled: either(
      propEq("status", "error"),
      propEq("status", "pending")
    )(item)
  });

  const statusMenuContainer = classnames("status-menu-container", {
    state: !cocPath
  });

  const ConditionalLink = item.dataFile?.id ? Link : ({ children }) => children;

  return (
    <tr>
      <td colSpan="8">
        <ul className="results-row">
          <li className={reportCellClass}>Report</li>
          <li className={reportCellClass}>Patient</li>
          <li className={reportCellClass}>
            {item.dataFile?.patientCount.toString()}
          </li>
          <ConditionalLink to={`${tab}/report/valid/${item.dataFile?.id}`}>
            <li className={reportCellClass}>Valid Abstractions</li>
            <li className={reportCellClass}>
              {item.dataFile?.validAbstractionCount.toString()}
            </li>
          </ConditionalLink>
          <ConditionalLink to={`${tab}/report/invalid/${item.dataFile?.id}`}>
            <li className={reportCellClass}>Invalid Abstractions</li>
            <li className={reportCellClass}>
              {item.dataFile?.invalidAbstractionCount.toString()}
            </li>
          </ConditionalLink>
          <div className={statusMenuContainer}>
            <StatusButton
              selected
              clickable={cocPath}
              toggleMenu={toggleMenu}
              text={item.dataFile?.status}
              selectedRegistry={selectedRegistry}
            />
            <StatusMenu
              toggleMenu={toggleMenu}
              open={menuIsOpen}
              exportItemId={item.dataFile?.id}
              updateItem={updateItem}
              selectedRegistry={selectedRegistry}
            />
          </div>
        </ul>
      </td>
    </tr>
  );
}

export default Export;
