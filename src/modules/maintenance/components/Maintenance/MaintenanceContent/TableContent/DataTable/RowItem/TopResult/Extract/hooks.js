import classnames from "classnames";
import {
  always,
  applySpec,
  concat,
  either,
  identity,
  ifElse,
  join,
  map,
  path,
  pathOr,
  pipe,
  prop,
  propEq,
  propOr,
  split
} from "ramda";
import { formatDateTime } from "utils/formatMaintenanceTime";
import { capitalize, cleanURL } from "utils/fp";
import { serverURI } from "base/constants";

const snakeCaseToTitleCase = key =>
  pipe(prop(key), split("_"), map(capitalize), join(" "));

const isErrorOrPending = either(
  propEq("status", "error"),
  propEq("status", "pending")
);

const displayDate = propPath =>
  pipe(path(propPath), ifElse(identity, formatDateTime, always("--")));

export const useComponentLogic = props => {
  const { item } = props;

  const defaultToBlank = propPath =>
    pipe(
      path(propPath),
      ifElse(() => isErrorOrPending(item), always("--"), identity)
    );
  const dataCellClass = classnames(item?.status);
  const buttonClass = classnames("center", {
    disabled: isErrorOrPending(item)
  });

  const {
    status,
    dateType,
    startDate,
    endDate,
    requestDate,
    fileName,
    downloadURL,
    numberOfFields,
    recordsWritten
  } = applySpec({
    status: snakeCaseToTitleCase("status"),
    dateType: propOr("", "dateType"),
    startDate: displayDate(["startDate"]),
    endDate: displayDate(["endDate"]),
    requestDate: displayDate(["requestDate"]),
    fileName: pathOr("--", ["dataFile", "fileName"]),
    numberOfFields: defaultToBlank(["dataFile", "numberOfFields"]),
    recordsWritten: defaultToBlank(["dataFile", "recordsWritten"]),
    downloadURL: pipe(
      pathOr("", ["dataFile", "url"]),
      concat(serverURI),
      cleanURL
    )
  })(item);

  return {
    status,
    dateType,
    startDate,
    endDate,
    requestDate,
    fileName,
    downloadURL,
    numberOfFields,
    recordsWritten,
    dataCellClass,
    buttonClass
  };
};
