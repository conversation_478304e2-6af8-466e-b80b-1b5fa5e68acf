// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OncExport renders component 1`] = `
<tr>
  <td
    colSpan="8"
  >
    <ul
      className="results-row"
    >
      <li
        className=""
      >
        Report
      </li>
      <li
        className=""
      >
        Patient
      </li>
      <li
        className=""
      >
        8
      </li>
      <a
        href="/measures_and_registries/coc/report/valid/39"
        onClick={[Function]}
      >
        <li
          className=""
        >
          Valid Abstractions
        </li>
        <li
          className=""
        >
          6
        </li>
      </a>
      <a
        href="/measures_and_registries/coc/report/invalid/39"
        onClick={[Function]}
      >
        <li
          className=""
        >
          Invalid Abstractions
        </li>
        <li
          className=""
        >
          3
        </li>
      </a>
      <div
        className="status-menu-container"
      >
        <StatusButton
          clickable={true}
          selected={true}
          text="not_submitted"
          toggleMenu={[Function]}
        />
        <StatusMenu
          exportItemId="39"
          open={false}
          toggleMenu={[Function]}
        />
      </div>
    </ul>
  </td>
</tr>
`;

exports[`OncExport renders component with clickable = false 1`] = `
<tr>
  <td
    colSpan="8"
  >
    <ul
      className="results-row"
    >
      <li
        className=""
      >
        Report
      </li>
      <li
        className=""
      >
        Patient
      </li>
      <li
        className=""
      >
        0
      </li>
      <a
        href="/measures_and_registries/state/report/valid/39"
        onClick={[Function]}
      >
        <li
          className=""
        >
          Valid Abstractions
        </li>
        <li
          className=""
        >
          4
        </li>
      </a>
      <a
        href="/measures_and_registries/state/report/invalid/39"
        onClick={[Function]}
      >
        <li
          className=""
        >
          Invalid Abstractions
        </li>
        <li
          className=""
        >
          5
        </li>
      </a>
      <div
        className="status-menu-container state"
      >
        <StatusButton
          clickable={false}
          selected={true}
          text="not_submitted"
          toggleMenu={[Function]}
        />
        <StatusMenu
          exportItemId="39"
          open={false}
          toggleMenu={[Function]}
        />
      </div>
    </ul>
  </td>
</tr>
`;

exports[`OncExport renders component without datafile 1`] = `
<tr>
  <td
    colSpan="8"
  >
    <ul
      className="results-row"
    >
      <li
        className=""
      >
        Report
      </li>
      <li
        className=""
      >
        Patient
      </li>
      <li
        className=""
      />
      <li
        className=""
      >
        Valid Abstractions
      </li>
      <li
        className=""
      />
      <li
        className=""
      >
        Invalid Abstractions
      </li>
      <li
        className=""
      />
      <div
        className="status-menu-container"
      >
        <StatusButton
          clickable={true}
          selected={true}
          toggleMenu={[Function]}
        />
        <StatusMenu
          open={false}
          toggleMenu={[Function]}
        />
      </div>
    </ul>
  </td>
</tr>
`;
