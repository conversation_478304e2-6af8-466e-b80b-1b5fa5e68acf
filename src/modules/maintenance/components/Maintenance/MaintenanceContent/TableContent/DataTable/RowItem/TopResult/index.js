import { __, compose, defaultTo, prop } from "ramda";
import { CocExport, StateExport } from "./OncExport";
import ConversionExport from "./ConversionExport";
import Extract from "./Extract";
import Export from "./Export";
import Hospitals from "./Hospitals";
import Surgeons from "./Surgeons";
import Import from "./Import";
import { capitalize } from "utils/fp";
// import Maintenance from "./Maintenance";

const Unimplemented = () => null;

const type = compose(
  defaultTo(Unimplemented),
  prop(__, {
    Coc: CocExport,
    Extract,
    Export,
    Import,
    Hospitals,
    State: StateExport,
    Conversion: ConversionExport,
    Surgeons
  })
);

const TopResult = props => {
  const { tab } = props;

  const Item = type(capitalize(tab));

  return <Item {...props} />;
};

export default TopResult;
