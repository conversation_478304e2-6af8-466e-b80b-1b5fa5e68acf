import { always, identity, ifElse } from "ramda";
import { useMemo } from "react";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = item => {
  const { dataFile } = item;

  const validAbstraction = useMemo(
    () =>
      ifElse(
        isNullOrEmpty,
        always(0),
        identity
      )(dataFile?.validAbstractionCount),
    [dataFile?.validAbstraction]
  );

  const invalidAbstraction = useMemo(
    () =>
      ifElse(
        isNullOrEmpty,
        always(0),
        identity
      )(dataFile?.invalidAbstractionCount),
    [dataFile?.invalidAbstraction]
  );

  const totalAbstractionsCount = (
    validAbstraction + invalidAbstraction
  ).toString();

  return { totalAbstractionsCount };
};
