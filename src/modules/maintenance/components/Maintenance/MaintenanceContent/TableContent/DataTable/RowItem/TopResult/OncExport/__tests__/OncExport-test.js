import { fakeRecord } from "modules/maintenance/components/Maintenance/data";

import { create } from "react-test-renderer";
import OncExport from "..";
import { assoc } from "ramda";

const mockedComponent = props => create(<OncExport {...props} />);

describe("OncExport", () => {
  test("renders component", () => {
    const component = mockedComponent({
      item: fakeRecord(1)
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component only one date type start date", () => {
    const item = assoc(
      "dateFilters",
      [
        {
          dateType: "Date of Diagnosis",
          startDate: null,
          endDate: null
        },
        {
          dateType: "Date of First Contact",
          startDate: "2021-01-01",
          endDate: null
        },
        {
          dateType: "Date Case Last Changed",
          startDate: null,
          endDate: null
        }
      ],
      fakeRecord(1)
    );
    const component = mockedComponent({
      item
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component only one date type start and end date", () => {
    const item = assoc(
      "dateFilters",
      [
        {
          dateType: "Date of Diagnosis",
          startDate: null,
          endDate: null
        },
        {
          dateType: "Date of First Contact",
          startDate: null,
          endDate: null
        },
        {
          dateType: "Date Case Last Changed",
          startDate: "2021-01-01",
          endDate: "2021-03-01"
        }
      ],
      fakeRecord(1)
    );
    const component = mockedComponent({
      item
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component multiple date types", () => {
    const item = assoc(
      "dateFilters",
      [
        {
          dateType: "Date of Diagnosis",
          startDate: "2021-02-01",
          endDate: "2021-04-01"
        },
        {
          dateType: "Date of First Contact",
          startDate: "2021-06-01",
          endDate: "2021-12-01"
        },
        {
          dateType: "Date Case Last Changed",
          startDate: "2021-01-01",
          endDate: "2021-03-01"
        }
      ],
      fakeRecord(1)
    );
    const component = mockedComponent({
      item
    });

    expect(component).toMatchSnapshot();
  });
});
