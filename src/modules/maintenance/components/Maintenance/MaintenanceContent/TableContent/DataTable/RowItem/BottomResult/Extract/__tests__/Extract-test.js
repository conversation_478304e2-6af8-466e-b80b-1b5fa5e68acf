import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import { fakeExtractRecord } from "modules/maintenance/components/Maintenance/data";
import Extract from "..";

const mockedComponent = props =>
  create(
    <MemoryRouter
      initialEntries={[`/measures_and_registries/${props.path}`]}
      keyLength={0}
    >
      <Extract {...props} />
    </MemoryRouter>
  );

describe("Extract", () => {
  test("renders component", () => {
    const component = mockedComponent({
      item: fakeExtractRecord("1", "completed", "ExtractData")
    });

    expect(component).toMatchSnapshot();
  });
});
