import classnames from "classnames";
import Toggle from "modules/maintenance/components/shared/Toggle";
import useTableToggle from "modules/maintenance/hooks/useTableToggle";

export function TopResult(props) {
  const { enabledText, toggle } = useTableToggle(props);
  const { item } = props;
  const dataCellClass = classnames(item.successStatus);

  return (
    <tr>
      <th className={dataCellClass}>{item.name}</th>
      <th className={dataCellClass}>{item.npi}</th>
      <th className={dataCellClass}>{item.region}</th>
      <th className={dataCellClass}>{item.zipCode}</th>
      <th className="action center">
        <i className="far fa-edit" />
      </th>
      <th>
        <span className="toggle-row">
          <span>{enabledText}</span>
          <Toggle onToggle={toggle} value={item.status} />
        </span>
      </th>
    </tr>
  );
}
export default TopResult;
