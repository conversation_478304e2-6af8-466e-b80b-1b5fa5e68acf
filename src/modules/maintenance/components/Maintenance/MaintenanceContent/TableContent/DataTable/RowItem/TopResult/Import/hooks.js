import classnames from "classnames";
import {
  always,
  applySpec,
  concat,
  either,
  identity,
  ifElse,
  isNil,
  join,
  map,
  path,
  pathOr,
  pipe,
  propEq,
  split
} from "ramda";
import { formatDateTime } from "utils/formatMaintenanceTime";
import { capitalize, cleanURL } from "utils/fp";
import { serverURI } from "base/constants";

const getErrorMessage = ifElse(isNil, always("No error"), identity);

const snakeCaseToTitleCase = pipe(split("_"), map(capitalize), join(" "));

export const useComponentLogic = props => {
  const { item } = props;

  const displayDate = propPath =>
    pipe(path(propPath), ifElse(identity, formatDateTime, always("--")));
  const dataCellClass = classnames(item.successStatus);
  const buttonClass = classnames("center", {
    disabled: either(
      propEq("successStatus", "error"),
      propEq("successStatus", "pending")
    )(item)
  });

  const { requestDate, importFileName, downloadURL } = applySpec({
    requestDate: displayDate(["requestDate"]),
    importFileName: pathOr("--", ["dataFile", "importFileName"]),
    downloadURL: pipe(
      pathOr("", ["dataFile", "url"]),
      concat(serverURI),
      cleanURL
    )
  })(item);

  return {
    snakeCaseToTitleCase,
    getErrorMessage,
    dataCellClass,
    buttonClass,
    requestDate,
    importFileName,
    downloadURL
  };
};
