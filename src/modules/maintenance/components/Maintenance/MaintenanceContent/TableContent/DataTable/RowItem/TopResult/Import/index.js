import { useComponentLogic } from "./hooks";

export function TopResult(props) {
  const { item } = props;
  const {
    getErrorMessage,
    snakeCaseToTitleCase,
    dataCellClass,
    buttonClass,
    requestDate,
    importFileName,
    downloadURL
  } = useComponentLogic(props);

  return (
    <tr>
      <th className={dataCellClass}>
        {snakeCaseToTitleCase(item.successStatus || "")}
      </th>
      <th className={dataCellClass}>{requestDate}</th>
      <th className={dataCellClass}>{importFileName}</th>
      <th className={dataCellClass}>{getErrorMessage(item.errorMessage)}</th>
      <th rowSpan="2" className={buttonClass}>
        <a href={downloadURL} download>
          <i className="fas fa-download export-download" />
        </a>
      </th>
    </tr>
  );
}
export default TopResult;
