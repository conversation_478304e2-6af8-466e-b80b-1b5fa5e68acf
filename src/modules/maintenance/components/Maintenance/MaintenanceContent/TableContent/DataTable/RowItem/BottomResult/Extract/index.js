import classnames from "classnames";
import { either, propEq, ifElse, always } from "ramda";

const isErrorOrPending = either(
  propEq("status", "error"),
  propEq("status", "pending")
);

const defaultToBlank = (item, field) =>
  ifElse(isErrorOrPending, always("--"), always(field))(item);

export function Status({ item }) {
  const { requestor, dataFile } = item;
  const reportCellClass = classnames({
    disabled: isErrorOrPending(item)
  });

  return (
    <tr>
      <td colSpan="7">
        <ul className="results-row">
          <li className={reportCellClass}>Report</li>
          <li className={reportCellClass}>Requestor</li>
          <li className={reportCellClass}>{defaultToBlank(item, requestor)}</li>
          <li className={reportCellClass}>Patient</li>
          <li className={reportCellClass}>
            {defaultToBlank(item, dataFile?.patientCount)}
          </li>
          <li className={reportCellClass}>Records Queried</li>
          <li className={reportCellClass}>
            {defaultToBlank(item, dataFile?.recordsQueried)}
          </li>
        </ul>
      </td>
    </tr>
  );
}

export default Status;
