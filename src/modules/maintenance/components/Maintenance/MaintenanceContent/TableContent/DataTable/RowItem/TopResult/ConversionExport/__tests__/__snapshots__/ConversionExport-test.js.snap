// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ConversionExport renders component 1`] = `
<tr>
  <th
    className="not_submitted"
    title="not_submitted"
  >
    not_submitted
  </th>
  <th
    className="not_submitted"
    title="2021-11-11"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="not_submitted"
    title="<PERSON><PERSON><PERSON>"
  >
    <PERSON><PERSON><PERSON>
  </th>
  <th
    className="not_submitted"
  />
  <th
    className="not_submitted"
    title="cedi_technologies.nbp.zip"
  >
    cedi_technologies.nbp.zip
  </th>
  <th
    className="center"
    rowSpan="2"
  >
    <a
      download={true}
      href="http://localhost:3000/files/cedi_technologies.nbp.zip"
    >
      <i
        className="fas fa-download export-download"
      />
    </a>
  </th>
</tr>
`;

exports[`ConversionExport renders component without datafile 1`] = `
<tr>
  <th
    className="not_submitted"
    title="not_submitted"
  >
    not_submitted
  </th>
  <th
    className="not_submitted"
    title="2021-11-11"
  >
    11/11/2021 | 00:00
  </th>
  <th
    className="not_submitted"
    title="Deontae Bosco"
  >
    Deontae Bosco
  </th>
  <th
    className="not_submitted"
  />
  <th
    className="not_submitted"
    title="--"
  >
    --
  </th>
  <th
    className="center"
    rowSpan="2"
  >
    <a
      download={true}
      href="http://localhost:3000"
    >
      <i
        className="fas fa-download export-download"
      />
    </a>
  </th>
</tr>
`;
