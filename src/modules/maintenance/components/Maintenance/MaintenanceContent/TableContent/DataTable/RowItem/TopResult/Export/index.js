import classnames from "classnames";
import { either, propEq } from "ramda";

export function TopResult({ item }) {
  const dataCellClass = classnames(item.status);
  const buttonClass = classnames("center", {
    disabled: either(
      propEq("status", "error"),
      propEq("status", "pending")
    )(item)
  });

  return (
    <tr>
      <th className={dataCellClass}>{item.requestDate}</th>
      <th className={dataCellClass}>{item.quarter}</th>
      <th className={dataCellClass}>{item.year}</th>
      <th className={dataCellClass}>{item.catOnly}</th>
      <th className={dataCellClass}>{item.excludePatientId}</th>
      <th className={dataCellClass}>{item.submissionType}</th>
      <th rowSpan="2" className={buttonClass}>
        <i className="fas fa-download export-download" />
      </th>
    </tr>
  );
}
export default TopResult;
