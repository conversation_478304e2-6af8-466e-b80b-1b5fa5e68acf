import { create } from "react-test-renderer";
import { MemoryRouter } from "react-router-dom";

import HeaderTable from "..";

const mockedComponent = props =>
  create(
    <MemoryRouter
      initialEntries={["/measures_and_registries/export"]}
      keyLength={0}
    >
      <HeaderTable {...props} />
    </MemoryRouter>
  );

describe("HeaderTable", () => {
  test("renders component", () => {
    const component = mockedComponent({ headers: ["Test 1", "Test 2"] });

    expect(component).toMatchSnapshot();
  });
});
