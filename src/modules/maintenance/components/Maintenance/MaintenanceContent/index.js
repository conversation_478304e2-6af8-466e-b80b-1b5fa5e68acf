import MaintenanceTabs from "../MaintenanceTabs";
import TableContent from "./TableContent";
import "modules/maintenance/styles/maintenance-table.scss";
import { useComponentLogic } from "./hooks";

export const MaintenanceContent = props => {
  const {
    filters,
    handleChangeField,
    handleChangeRegistry,
    pagination,
    registryOptions,
    selectedRegistry,
    shouldUseMaintenanceContent,
    tab,
    tabs
  } = useComponentLogic(props);

  return (
    <>
      <MaintenanceTabs
        filters={filters}
        pagination={pagination}
        onChangeField={handleChangeField}
        onChangeRegistry={handleChangeRegistry}
        registryOptions={registryOptions}
        selectedRegistry={selectedRegistry}
        tabs={tabs}
      />
      {shouldUseMaintenanceContent ? (
        <div className="maintenance-content">{/* MaintenanceContent */}</div>
      ) : (
        <TableContent
          selectedRegistry={selectedRegistry}
          filters={filters}
          pagination={pagination}
          tab={tab}
        />
      )}
    </>
  );
};

export default MaintenanceContent;
