import { useSelector } from "react-redux";
import facilitySelector from "modules/app/selectors/facility";
import useSelect from "modules/maintenance/hooks/useSelect";
import formatSelectOption from "utils/fp/formatSelectOption";
import { GET_AVAILABLE_FACILITIES } from "./query";

const formatFacility = formatSelectOption(["id", "name"]);

export const useComponentLogic = ({ handleSave }) => {
  const facility = useSelector(state => facilitySelector.facility(state));
  const { value, options, error, loading, onChange } = useSelect({
    baseOptions: [],
    inputValue: formatFacility(facility),
    onSave: selectedFacility => {
      handleSave(selectedFacility.value);
    },
    callQuery: true,
    query: GET_AVAILABLE_FACILITIES,
    queryObjectName: "availableFacilities"
  });

  return {
    error,
    facilityName: facility.name,
    loading,
    onChange,
    options,
    value
  };
};
