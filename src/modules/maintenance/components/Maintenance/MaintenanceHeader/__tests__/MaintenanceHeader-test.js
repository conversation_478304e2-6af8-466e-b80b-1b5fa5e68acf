import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedA<PERSON>lo } from "utils/test/decorated";
import MaintenanceHeader from "..";
import apolloMocks from "../mocks";

jest.mock("react-select", () => "Select");
jest.mock("@q-centrix/q-components-react", () => ({ Spinner: "Spinner" }));

const appValues = {
  facility: {
    id: 1,
    name: "Large Medical Facility"
  }
};

function mockedComponent(props = {}) {
  return create(
    decoratedApollo({
      component: MaintenanceHeader,
      props,
      initialAppValues: appValues,
      apolloMocks
    })
  );
}

describe("MaintenanceHeader", () => {
  test("renders component while loading", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
  test("renders component", async () => {
    const component = mockedComponent();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
