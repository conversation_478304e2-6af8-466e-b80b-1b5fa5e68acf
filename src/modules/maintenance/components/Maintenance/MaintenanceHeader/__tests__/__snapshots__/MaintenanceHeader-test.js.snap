// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MaintenanceHeader renders component 1`] = `
<div
  className="maintenance-header"
>
  <h2>
    Edit Registry Configuration
  </h2>
  <h2>
    <span>
      Large Medical Facility
    </span>
    <Select
      className="facility-select-container"
      classNamePrefix="facility-select"
      name="registry-select"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Large Medical Center",
            "value": "1",
          },
          Object {
            "label": "Small Medical Center",
            "value": "2",
          },
        ]
      }
      placeholder="Change Facility"
    />
  </h2>
</div>
`;

exports[`MaintenanceHeader renders component while loading 1`] = `<Spinner />`;
