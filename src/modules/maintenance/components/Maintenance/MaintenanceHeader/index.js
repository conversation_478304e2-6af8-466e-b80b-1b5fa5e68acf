import Select from "react-select";
import { Spinner } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

export const MaintenanceHeader = props => {
  const { facilityName, loading, onChange, options, value } =
    useComponentLogic(props);

  if (loading) return <Spinner />;

  return (
    <div className="maintenance-header">
      <h2>Edit Registry Configuration</h2>
      <h2>
        <span>{facilityName}</span>
        <Select
          classNamePrefix="facility-select"
          className="facility-select-container"
          name="registry-select"
          onChange={onChange}
          options={options}
          placeholder="Change Facility"
          value={value}
        />
      </h2>
    </div>
  );
};

export default MaintenanceHeader;
