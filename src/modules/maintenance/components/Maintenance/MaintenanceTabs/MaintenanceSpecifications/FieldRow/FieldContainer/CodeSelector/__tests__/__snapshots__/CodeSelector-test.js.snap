// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CodeSelector removing selected code removes range correctly 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={Array []}
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={
      Array [
        Object {
          "display": "A00002 - A00003",
          "endCode": Object {
            "label": "A00003",
            "value": "113",
          },
          "startCode": Object {
            "label": "A00002",
            "value": "112",
          },
        },
        Object {
          "display": "A00003 - A00004",
          "endCode": Object {
            "label": "A00004",
            "value": "114",
          },
          "startCode": Object {
            "label": "A00003",
            "value": "113",
          },
        },
        Object {
          "display": "A00004 - A00005",
          "endCode": Object {
            "label": "A00005",
            "value": "115",
          },
          "startCode": Object {
            "label": "A00004",
            "value": "114",
          },
        },
        Object {
          "display": "A00005 - A00006",
          "endCode": Object {
            "label": "A00006",
            "value": "116",
          },
          "startCode": Object {
            "label": "A00005",
            "value": "115",
          },
        },
      ]
    }
  />
</div>
`;

exports[`CodeSelector renders component 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={Array []}
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={Array []}
  />
</div>
`;

exports[`CodeSelector selecting 5 ranges disables CodeLookups 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={true}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={true}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={Array []}
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={
      Array [
        Object {
          "display": "A00001 - A00002",
          "endCode": Object {
            "label": "A00002",
            "value": "112",
          },
          "startCode": Object {
            "label": "A00001",
            "value": "111",
          },
        },
        Object {
          "display": "A00002 - A00003",
          "endCode": Object {
            "label": "A00003",
            "value": "113",
          },
          "startCode": Object {
            "label": "A00002",
            "value": "112",
          },
        },
        Object {
          "display": "A00003 - A00004",
          "endCode": Object {
            "label": "A00004",
            "value": "114",
          },
          "startCode": Object {
            "label": "A00003",
            "value": "113",
          },
        },
        Object {
          "display": "A00004 - A00005",
          "endCode": Object {
            "label": "A00005",
            "value": "115",
          },
          "startCode": Object {
            "label": "A00004",
            "value": "114",
          },
        },
        Object {
          "display": "A00005 - A00006",
          "endCode": Object {
            "label": "A00006",
            "value": "116",
          },
          "startCode": Object {
            "label": "A00005",
            "value": "115",
          },
        },
      ]
    }
  />
</div>
`;

exports[`CodeSelector selecting range on different categories displays error 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00001",
            "value": "111",
          }
        }
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "C00002",
            "value": "111",
          }
        }
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={
        Array [
          Object {
            "message": "Codes cannot span more than one category",
          },
        ]
      }
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={Array []}
  />
</div>
`;

exports[`CodeSelector selecting same range displays error 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00001",
            "value": "111",
          }
        }
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00002",
            "value": "112",
          }
        }
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={
        Array [
          Object {
            "message": "Range is already selected",
          },
        ]
      }
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={
      Array [
        Object {
          "display": "A00001 - A00002",
          "endCode": Object {
            "label": "A00002",
            "value": "112",
          },
          "startCode": Object {
            "label": "A00001",
            "value": "111",
          },
        },
      ]
    }
  />
</div>
`;

exports[`CodeSelector selecting startCode and end code adds selectedCode onClick 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={null}
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={Array []}
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={
      Array [
        Object {
          "display": "A00001 - A00002",
          "endCode": Object {
            "label": "A00002",
            "value": "112",
          },
          "startCode": Object {
            "label": "A00001",
            "value": "111",
          },
        },
      ]
    }
  />
</div>
`;

exports[`CodeSelector selecting startCode and endCode enables button 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00001",
            "value": "111",
          }
        }
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00002",
            "value": "112",
          }
        }
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus"
        />
      </button>
    </div>
    <ValidationErrors
      errors={Array []}
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={Array []}
  />
</div>
`;

exports[`CodeSelector selecting wrong range displays error 1`] = `
<div
  className="select-column code-selector"
>
  <div
    className="select-range"
  >
    <label
      htmlFor="primary-site"
    >
      ICD-O Code Ranges
      (up to 5)
    </label>
    <div
      className="fields"
    >
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Starting ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00002",
            "value": "112",
          }
        }
      />
      <label>
        To
      </label>
      <CodeLookup
        callQuery={true}
        disabled={false}
        fields={
          Array [
            "value",
            "label",
          ]
        }
        name="primary-site"
        onChange={[Function]}
        placeholder="Ending ICD-O-3"
        selectName="PrimarySiteRange"
        value={
          Object {
            "label": "A00001",
            "value": "111",
          }
        }
      />
      <button
        className="add-range"
        onClick={[Function]}
        type="button"
      >
        <i
          className="fal fa-file-plus disabled"
        />
      </button>
    </div>
    <ValidationErrors
      errors={
        Array [
          Object {
            "message": "Start code cannot be larger or equal to End code",
          },
        ]
      }
    />
  </div>
  <SelectedRanges
    label="ICD-0 Code ranges added"
    onRemoveRange={[Function]}
    selectedCodes={Array []}
  />
</div>
`;
