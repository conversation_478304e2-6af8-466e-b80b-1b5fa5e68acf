import { act, create } from "react-test-renderer";
import CheckBoxWithAccordion from "..";

jest.mock("../../MultiSelectWithSelectAll", () => "MultiSelectWithSelectAll");

const mockedComponent = props => create(<CheckBoxWithAccordion {...props} />);

describe("CheckBoxWithAccordion", () => {
  test("renders component", () => {
    const onChangeField = jest.fn();

    const component = mockedComponent({
      baseOptions: [
        { id: "test-value-1", title: "test-label-1" },
        { id: "test-value-2", title: "test-label-2" }
      ],
      fieldName: "testName",
      label: "Test Label",
      id: "100",
      onChangeField,
      value: null
    });

    expect(component).toMatchSnapshot();
  });

  test.skip("renders component, fires input onChange and shows Select", () => {
    const onChangeField = jest.fn();

    const component = mockedComponent({
      baseOptions: [
        { id: "test-value-1", title: "test-label-1" },
        { id: "test-value-2", title: "test-label-2" }
      ],
      fieldName: "testName",
      label: "Test Label",
      id: "100",
      onChangeField,
      value: null
    });
    const instance = component.root;

    const [input] = instance.findAllByType("input");

    act(() => input.props.onChange());

    expect(component).toMatchSnapshot();
  });
});
