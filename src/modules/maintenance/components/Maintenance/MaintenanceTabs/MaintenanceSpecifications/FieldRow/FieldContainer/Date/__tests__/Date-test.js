import { MemoryRouter } from "react-router-dom";
import { act, create } from "react-test-renderer";
import DatePicker from "react-datepicker";
import DateField from "..";

const mockedComponent = props =>
  create(
    <MemoryRouter initialEntries={["/state"]}>
      <DateField {...props} />
    </MemoryRouter>
  );

describe("DateField", () => {
  test("renders component", () => {
    const onChangeField = jest.fn();
    const component = mockedComponent({
      fieldName: "testName",
      label: "Test Label",
      id: "100",
      onChangeField,
      value: ""
    });
    const instance = component.root;

    const [input] = instance.findAllByType(DatePicker);

    act(() => input.props.onChange("test"));
    act(() => input.props.onSelect("test"));

    expect(onChangeField).toHaveBeenCalled();
    expect(component).toMatchSnapshot();
  });

  test("renders component with required *", () => {
    const mockedRequiredComponent = props =>
      create(
        <MemoryRouter initialEntries={["/coc"]}>
          <DateField {...props} />
        </MemoryRouter>
      );

    const onChangeField = jest.fn();
    const component = mockedRequiredComponent({
      fieldName: "testName",
      label: "Test Label",
      id: "100",
      onChangeField,
      value: ""
    });
    const instance = component.root;

    const [input] = instance.findAllByType(DatePicker);

    act(() => input.props.onChange("test"));
    act(() => input.props.onSelect("test"));

    expect(onChangeField).toHaveBeenCalled();
    expect(component).toMatchSnapshot();
  });
});
