import { isNullOrEmpty } from "utils/fp";
import CodeRange from "../CodeRange";

export const SelectedRanges = props => {
  const { label, selectedCodes, onRemoveRange } = props;

  return (
    <div className="selected-ranges">
      <label>{label}</label>
      <div className="selected-codes">
        {isNullOrEmpty(selectedCodes) ? (
          <span className="no-ranges">All ranges shown</span>
        ) : (
          selectedCodes.map(range => (
            <CodeRange
              key={range.key || range.display}
              range={range}
              onRemoveRange={onRemoveRange}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default SelectedRanges;
