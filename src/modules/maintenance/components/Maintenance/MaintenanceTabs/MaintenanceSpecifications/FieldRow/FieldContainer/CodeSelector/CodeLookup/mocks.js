import { CONFIGURATION_SELECT_OPTIONS as query } from "modules/maintenance/components/Maintenance/query";

const icdCodesSuccess = {
  data: {
    configurationSelectOptions: [
      { value: "111", label: "A00001", __typename: "SelectOption" },
      { value: "112", label: "A00002", __typename: "SelectOption" },
      { value: "113", label: "A00003", __typename: "SelectOption" },
      { value: "114", label: "A00004", __typename: "SelectOption" },
      { value: "115", label: "A00005", __typename: "SelectOption" },
      { value: "116", label: "B00006", __typename: "SelectOption" }
    ]
  }
};

const classOfCaseSuccess = {
  data: {
    configurationSelectOptions: [
      { value: "1", label: "01", __typename: "SelectOption" },
      { value: "2", label: "02", __typename: "SelectOption" },
      { value: "3", label: "03", __typename: "SelectOption" },
      { value: "4", label: "04", __typename: "SelectOption" },
      { value: "5", label: "15", __typename: "SelectOption" },
      { value: "6", label: "26", __typename: "SelectOption" }
    ]
  }
};

const dateTypeSuccess = {
  data: {
    configurationSelectOptions: [
      { value: "DoD", label: "Date of Diagnosis", __typename: "SelectOption" },
      {
        value: "DoFC",
        label: "Date of First Contact",
        __typename: "SelectOption"
      }
    ]
  }
};

const histologySuccess = {
  data: {
    configurationSelectOptions: [
      { value: "1", label: "01", __typename: "SelectOption" },
      { value: "2", label: "02", __typename: "SelectOption" },
      { value: "3", label: "03", __typename: "SelectOption" },
      { value: "4", label: "04", __typename: "SelectOption" },
      { value: "5", label: "15", __typename: "SelectOption" },
      { value: "6", label: "26", __typename: "SelectOption" }
    ]
  }
};

export const icdCodesMocks = [
  {
    request: {
      query,
      variables: { registryName: "RegistryTest", selectName: "SelectTest" }
    },
    result: icdCodesSuccess
  },
  {
    request: {
      query,
      variables: { registryName: "Oncology", selectName: "PrimarySiteRange" }
    },
    result: icdCodesSuccess
  },
  {
    request: {
      query,
      variables: { registryName: "Oncology", selectName: "ClassOfCase" }
    },
    result: classOfCaseSuccess
  },
  {
    request: {
      query,
      variables: { registryName: "Oncology", selectName: "DateType" }
    },
    result: dateTypeSuccess
  },
  {
    request: {
      query,
      variables: { registryName: "Oncology", selectName: "HistologyRange" }
    },
    result: histologySuccess
  }
];

export default icdCodesMocks;
