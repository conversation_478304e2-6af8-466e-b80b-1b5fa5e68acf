import {
  always,
  any,
  append,
  both,
  cond,
  complement,
  curry,
  equals,
  gt,
  has,
  head,
  ifElse,
  includes,
  lt,
  pipe,
  prop,
  reject,
  T
} from "ramda";
import { useCallback, useMemo, useState } from "react";
import { isNullOrEmpty, parseNumber } from "utils/fp";

const compareCodes = curry((startCode, endCode) =>
  lt(startCode?.label, endCode?.label)
);
const areSameRange = curry((startCode, endCode) =>
  equals(head(startCode?.label), head(endCode?.label))
);
const startsWithLetter = pipe(prop("label"), parseNumber, value =>
  isNaN(value)
);
const display = (startCode, endCode) =>
  `${startCode.label}${isNullOrEmpty(endCode) ? "" : ` - ${endCode.label}`}`;

const formatCodes = (startCode, endCode) => ({
  startCode,
  endCode,
  display: display(startCode, endCode)
});

const isCodeIncluded = (formattedCode, selectedCodes) =>
  includes(formattedCode, selectedCodes);

const isAnyCodeMissing = (startCode, endCode) =>
  any(isNullOrEmpty, [startCode, endCode]);

const formatErrorMessage = message => ({ error: { message } });

const disableAddCode = (startCode, endCode, selectedCodes) => {
  if (isAnyCodeMissing(startCode, endCode)) {
    return true;
  }

  return ifElse(
    complement(compareCodes(startCode)),
    always(true),
    ifElse(
      () => isCodeIncluded(formatCodes(startCode, endCode), selectedCodes),
      always(true),
      ifElse(
        complement(startsWithLetter),
        always(false),
        ifElse(areSameRange(startCode), always(false), always(true))
      )
    )
  )(endCode);
};

export const validateCodes = (startCode, endCode) => selectedCodes => {
  if (gt(selectedCodes.length, 4)) {
    return formatErrorMessage("Cannot select more than 5 Ranges");
  }
  if (isAnyCodeMissing(startCode, endCode)) {
    return formatErrorMessage("Must enter both Start and End code");
  }

  const formattedRange = formatCodes(startCode, endCode);

  return cond([
    [
      complement(compareCodes(startCode)),
      always(
        formatErrorMessage("Start code cannot be larger or equal to End code")
      )
    ],
    [
      both(startsWithLetter, complement(areSameRange(startCode))),
      always(formatErrorMessage("Codes cannot span more than one category"))
    ],
    [
      () => isCodeIncluded(formattedRange, selectedCodes),
      always(formatErrorMessage("Range is already selected"))
    ],
    [T, always(formattedRange)]
  ])(endCode);
};

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { onChangeField, fieldName } = props;
  const [errors, setErrors] = useState([]);
  const [selectedCodes, setSelectedCodes] = useState([]);
  const [startCode, setStartCode] = useState(null);
  const [endCode, setEndCode] = useState(null);
  const handleStartChange = useCallback(
    code => {
      setStartCode(code);
      setErrors([]);
    },
    [setStartCode]
  );
  const handleEndChange = useCallback(
    code => {
      setEndCode(code);
      setErrors([]);
    },
    [setEndCode]
  );
  const handleAddClick = useCallback(
    e => {
      if (e) e.preventDefault();
      setSelectedCodes(currentCodes =>
        pipe(
          validateCodes(startCode, endCode),
          ifElse(
            has("error"),
            result => {
              setErrors([prop("error", result)]);
              return currentCodes;
            },
            result => {
              const newCodes = append(result, currentCodes);

              setErrors([]);
              setStartCode(null);
              setEndCode(null);
              onChangeField(fieldName, newCodes);
              return newCodes;
            }
          )
        )(currentCodes)
      );
    },
    [
      fieldName,
      onChangeField,
      startCode,
      endCode,
      setSelectedCodes,
      setErrors,
      setStartCode,
      setEndCode
    ]
  );
  const handleRemoveRange = useCallback(
    range => {
      setSelectedCodes(currentCodes => {
        const newCodes = reject(equals(range), currentCodes);

        onChangeField(fieldName, newCodes);
        return newCodes;
      });
    },
    [setSelectedCodes, fieldName, onChangeField]
  );

  const isDisabled = useMemo(
    () => disableAddCode(startCode, endCode, selectedCodes),
    [startCode, endCode, selectedCodes]
  );

  return {
    endCode,
    errors,
    selectedCodes,
    startCode,
    handleAddClick,
    handleStartChange,
    handleEndChange,
    handleRemoveRange,
    isDisabled
  };
};
