import OptionCategoryGroup from "../OptionCategoryGroup";
import { useComponentLogic } from "../SelectedOptions/hooks";

export const AvailableOptions = props => {
  const { onOptionFieldChange } = props;
  const { categories } = useComponentLogic(props);

  return (
    <div className="available-options">
      <div className="title">Available Options</div>
      <div className="options">
        <table>
          <thead>
            <tr>
              <th>Category</th>
              <th>Select Option</th>
              <th>Select End Date</th>
            </tr>
          </thead>
          <tbody>
            {categories &&
              categories.map(category => (
                <OptionCategoryGroup
                  key={category.id}
                  category={category}
                  onOptionFieldChange={onOptionFieldChange}
                />
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AvailableOptions;
