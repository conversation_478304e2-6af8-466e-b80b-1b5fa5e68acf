import DatePicker from "react-datepicker";
import { useComponentLogic } from "./hooks";
import dateFormats from "modules/question/constants/dateFormats";
import "react-datepicker/dist/react-datepicker.css";
import classNames from "classnames";
import RequiredLabel from "modules/maintenance/components/shared/RequiredLabel";

const DateField = props => {
  const {
    fieldName,
    label,
    required,
    onChangeField,
    value,
    dateFormat,
    placeholderText,
    popperPlacement
  } = props;
  const { dateValueRequired, handleBlur } = useComponentLogic(props);

  const dateColumn = classNames("date-column", {
    error: dateValueRequired
  });

  return (
    <div className={dateColumn}>
      <RequiredLabel label={label} required={required} />
      <DatePicker
        selected={value || null}
        placeholderText={placeholderText}
        dateFormat={dateFormat}
        popperPlacement={popperPlacement}
        onChange={val => onChangeField(fieldName, val)}
        onSelect={val => onChange<PERSON>ield(fieldName, val)}
        onBlur={handleBlur}
      />
    </div>
  );
};

DateField.defaultProps = {
  dateFormat: dateFormats,
  placeholderText: "mm/dd/yyyy",
  popperPlacement: "bottom"
};

export default DateField;
