import { create, act } from "react-test-renderer";
import Actions from "..";

const mockedComponent = props => create(<Actions {...props} />);

describe("Actions", () => {
  test("renders component", () => {
    const component = mockedComponent({
      onClick: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("when button click it calls handler", () => {
    const testFunction = jest.fn();
    const component = mockedComponent({
      onClick: testFunction
    });

    const instance = component.root;
    const [button] = instance.findAllByType("i");

    act(() => {
      button.props.onClick();
    });

    expect(testFunction).toHaveBeenCalled();
  });
});
