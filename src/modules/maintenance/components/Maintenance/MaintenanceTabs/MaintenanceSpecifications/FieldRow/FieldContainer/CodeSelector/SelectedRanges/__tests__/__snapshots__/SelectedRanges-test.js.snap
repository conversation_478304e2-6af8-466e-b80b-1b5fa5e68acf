// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectedRanges renders component with ranges 1`] = `
<div
  className="selected-ranges"
>
  <label>
    ICD-10 ranges added
  </label>
  <div
    className="selected-codes"
  >
    <CodeRange
      onRemoveRange={[MockFunction]}
      range={
        Object {
          "endCode": "A00015",
          "startCode": "A00001",
        }
      }
    />
    <CodeRange
      onRemoveRange={[MockFunction]}
      range={
        Object {
          "startCode": "A00016",
        }
      }
    />
  </div>
</div>
`;

exports[`SelectedRanges renders component without ranges 1`] = `
<div
  className="selected-ranges"
>
  <label>
    ICD-10 ranges added
  </label>
  <div
    className="selected-codes"
  >
    <span
      className="no-ranges"
    >
      All ranges shown
    </span>
  </div>
</div>
`;
