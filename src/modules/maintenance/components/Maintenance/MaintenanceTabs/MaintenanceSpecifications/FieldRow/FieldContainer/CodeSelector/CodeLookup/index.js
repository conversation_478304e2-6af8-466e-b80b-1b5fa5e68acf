import ReactSelect from "react-select";
import { useComponentLogic } from "./hooks";

export const CodeLookup = props => {
  const { disabled, name, placeholder } = props;
  const { value, options, loading, handleChange, handleInputChange } =
    useComponentLogic(props);

  return (
    <ReactSelect
      className="filter-select-container"
      classNamePrefix="filter-select"
      isSearchable
      name={name}
      onChange={handleChange}
      onInputChange={handleInputChange}
      options={options}
      value={value}
      placeholder={placeholder}
      isLoading={loading}
      isClearable
      isDisabled={disabled}
    />
  );
};

export default CodeLookup;
