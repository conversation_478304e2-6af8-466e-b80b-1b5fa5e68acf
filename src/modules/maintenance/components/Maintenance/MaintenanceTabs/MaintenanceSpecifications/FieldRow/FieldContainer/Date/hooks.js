import { useState, useCallback } from "react";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = ({
  value,
  requiredField,
  required,
  fieldName
}) => {
  const [dateValueRequired, setDateValueRequired] = useState(false);

  const handleBlur = useCallback(() => {
    if (required) {
      setDateValueRequired(isNullOrEmpty(value));
      requiredField({ fieldName, value });
    }
  }, [requiredField, fieldName, value]);

  return {
    dateValueRequired,
    handleBlur
  };
};
