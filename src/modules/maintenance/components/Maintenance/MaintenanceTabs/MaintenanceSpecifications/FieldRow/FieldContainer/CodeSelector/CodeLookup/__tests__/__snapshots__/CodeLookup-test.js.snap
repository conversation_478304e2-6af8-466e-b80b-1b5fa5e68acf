// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CodeLookup renders component with initial value 1`] = `
<ReactSelect
  className="filter-select-container"
  classNamePrefix="filter-select"
  isClearable={true}
  isLoading={false}
  isSearchable={true}
  name="42|22"
  onChange={[Function]}
  onInputChange={[Function]}
  options={
    Array [
      Object {
        "label": "A00001",
        "value": "111",
      },
      Object {
        "label": "A00002",
        "value": "112",
      },
      Object {
        "label": "A00003",
        "value": "113",
      },
      Object {
        "label": "A00004",
        "value": "114",
      },
      Object {
        "label": "A00005",
        "value": "115",
      },
      Object {
        "label": "B00006",
        "value": "116",
      },
    ]
  }
  value={
    Object {
      "label": "A00001",
      "value": "111",
    }
  }
/>
`;

exports[`CodeLookup renders component without intial value 1`] = `
<ReactSelect
  className="filter-select-container"
  classNamePrefix="filter-select"
  isClearable={true}
  isLoading={false}
  isSearchable={true}
  name="42|22"
  onChange={[Function]}
  onInputChange={[Function]}
  options={
    Array [
      Object {
        "label": "A00001",
        "value": "111",
      },
      Object {
        "label": "A00002",
        "value": "112",
      },
      Object {
        "label": "A00003",
        "value": "113",
      },
      Object {
        "label": "A00004",
        "value": "114",
      },
      Object {
        "label": "A00005",
        "value": "115",
      },
      Object {
        "label": "B00006",
        "value": "116",
      },
    ]
  }
  value={null}
/>
`;
