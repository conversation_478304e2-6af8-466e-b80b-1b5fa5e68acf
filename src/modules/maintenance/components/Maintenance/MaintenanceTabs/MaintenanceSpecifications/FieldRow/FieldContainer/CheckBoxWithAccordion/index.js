import { motion, AnimatePresence } from "framer-motion";

import MultiSelectWithSelectAll from "../MultiSelectWithSelectAll";
import { useComponentLogic } from "./hooks";

export const accordionVariants = {
  open: { opacity: 1, height: "auto" },
  closed: { opacity: 0, height: 0 }
};

const CheckBoxWithAccordion = props => {
  const { fieldName, label } = props;
  const { hasValue, toggleHasValue } = useComponentLogic(props);

  return (
    <div className="checkbox-with-accordion">
      <div className="toggle-checkbox">
        <label htmlFor={`${fieldName}-checkbox`}>
          Select <strong>{label}</strong> fields
        </label>
        <input
          checked={hasValue}
          id={`${fieldName}-checkbox`}
          onChange={toggleHasValue}
          type="checkbox"
          value={hasValue}
        />
      </div>
      <AnimatePresence>
        {hasValue && (
          <motion.div
            animate="open"
            className="checkbox-accordion-panel"
            exit="closed"
            initial="closed"
            transition={{ duration: 0.4 }}
            variants={accordionVariants}
          >
            <MultiSelectWithSelectAll {...props} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CheckBoxWithAccordion;
