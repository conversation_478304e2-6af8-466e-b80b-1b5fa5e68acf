import { act, create } from "react-test-renderer";
import AvailableOptions from "..";
import OptionCategoryGroup from "../../OptionCategoryGroup";

jest.mock("../../OptionCategoryGroup", () => "OptionCategoryGroup");

const mockedComponent = props => create(<AvailableOptions {...props} />);

describe("AvailableOptions", () => {
  test("renders component with no categories", () => {
    const fields = [];

    const component = mockedComponent({
      fields,
      onOptionFieldChange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component with categories", () => {
    const fields = [
      { id: 1, category: "Demographic", name: "First Name" },
      { id: 2, category: "Demographic", name: "Last Name" },
      { id: 3, category: "Demographic", name: "Middle Name" },
      { id: 4, category: "Demographic", name: "Age" },
      { id: 5, category: "Category Name", name: "Field Name 1" },
      { id: 6, category: "Category Name", name: "Field Name 2" },
      { id: 7, category: "Category Name", name: "Field Name 3" },
      { id: 8, category: "Category Name", name: "Field Name 4" },
      { id: 9, category: "Category Name", name: "Field Name 5" },
      { id: 10, category: "Category Name", name: "Field Name 6" },
      { id: 11, category: "Category Name", name: "Field Name 7" },
      { id: 12, category: "Category Name", name: "Field Name 8" }
    ];

    const component = mockedComponent({
      fields,
      onOptionFieldChange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("onAddOption called when child icon clicked", () => {
    const fields = [{ id: 1, category: "Demographic", name: "First Name" }];
    const testFunction = jest.fn();

    const component = mockedComponent({
      fields,
      onOptionFieldChange: testFunction
    });

    const instance = component.root;
    const [categoryGroup] = instance.findAllByType(OptionCategoryGroup);

    act(() => {
      categoryGroup.props.onOptionFieldChange();
    });

    expect(testFunction).toHaveBeenCalled();
  });
});
