import ReactDatePicker from "react-datepicker";
import { useComponentLogic } from "./hooks";

export const DateCell = props => {
  const { readOnly, disabled, date = null } = props;
  const { internalDate, handleFieldChange } = useComponentLogic(props);

  return readOnly ? (
    date
  ) : (
    <ReactDatePicker
      selected={internalDate}
      placeholderText="mm/dd/yyyy"
      dateFormat={["MM/DD/yyyy"]}
      popperPlacement="bottom"
      onChange={handleFieldChange}
      onSelect={handleFieldChange}
      disabled={disabled}
    />
  );
};

export default DateCell;
