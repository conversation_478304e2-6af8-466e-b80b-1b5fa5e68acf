// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AvailableOptions renders component with categories 1`] = `
<div
  className="available-options"
>
  <div
    className="title"
  >
    Available Options
  </div>
  <div
    className="options"
  >
    <table>
      <thead>
        <tr>
          <th>
            Category
          </th>
          <th>
            Select Option
          </th>
          <th>
            Select End Date
          </th>
        </tr>
      </thead>
      <tbody>
        <OptionCategoryGroup
          category={
            Object {
              "id": "Demographic",
              "options": Array [
                Object {
                  "category": "Demographic",
                  "id": 1,
                  "name": "First Name",
                },
                Object {
                  "category": "Demographic",
                  "id": 2,
                  "name": "Last Name",
                },
                Object {
                  "category": "Demographic",
                  "id": 3,
                  "name": "Middle Name",
                },
                Object {
                  "category": "Demographic",
                  "id": 4,
                  "name": "Age",
                },
              ],
            }
          }
          onOptionFieldChange={[MockFunction]}
        />
        <OptionCategoryGroup
          category={
            Object {
              "id": "Category Name",
              "options": Array [
                Object {
                  "category": "Category Name",
                  "id": 5,
                  "name": "Field Name 1",
                },
                Object {
                  "category": "Category Name",
                  "id": 6,
                  "name": "Field Name 2",
                },
                Object {
                  "category": "Category Name",
                  "id": 7,
                  "name": "Field Name 3",
                },
                Object {
                  "category": "Category Name",
                  "id": 8,
                  "name": "Field Name 4",
                },
                Object {
                  "category": "Category Name",
                  "id": 9,
                  "name": "Field Name 5",
                },
                Object {
                  "category": "Category Name",
                  "id": 10,
                  "name": "Field Name 6",
                },
                Object {
                  "category": "Category Name",
                  "id": 11,
                  "name": "Field Name 7",
                },
                Object {
                  "category": "Category Name",
                  "id": 12,
                  "name": "Field Name 8",
                },
              ],
            }
          }
          onOptionFieldChange={[MockFunction]}
        />
      </tbody>
    </table>
  </div>
</div>
`;

exports[`AvailableOptions renders component with no categories 1`] = `
<div
  className="available-options"
>
  <div
    className="title"
  >
    Available Options
  </div>
  <div
    className="options"
  >
    <table>
      <thead>
        <tr>
          <th>
            Category
          </th>
          <th>
            Select Option
          </th>
          <th>
            Select End Date
          </th>
        </tr>
      </thead>
      <tbody />
    </table>
  </div>
</div>
`;
