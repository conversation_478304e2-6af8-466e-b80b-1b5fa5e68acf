// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateField renders component 1`] = `
<div
  className="date-column"
>
  <label>
    Test Label
  </label>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <input
        className=""
        disabled={false}
        onBlur={[Function]}
        onChange={[Function]}
        onClick={[Function]}
        onFocus={[Function]}
        onKeyDown={[Function]}
        placeholder="mm/dd/yyyy"
        readOnly={false}
        type="text"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`DateField renders component with required * 1`] = `
<div
  className="date-column"
>
  <label>
    Test Label
  </label>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <input
        className=""
        disabled={false}
        onBlur={[Function]}
        onChange={[Function]}
        onClick={[Function]}
        onFocus={[Function]}
        onKeyDown={[Function]}
        placeholder="mm/dd/yyyy"
        readOnly={false}
        type="text"
        value=""
      />
    </div>
  </div>
</div>
`;
