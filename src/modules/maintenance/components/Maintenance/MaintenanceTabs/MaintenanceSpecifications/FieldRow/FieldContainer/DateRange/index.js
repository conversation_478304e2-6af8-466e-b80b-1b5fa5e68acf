import DatePicker from "react-datepicker";
import { useComponentLogic } from "./hooks";
import dateFormats from "modules/question/constants/dateFormats";
import "react-datepicker/dist/react-datepicker.css";
import classnames from "classnames";
import ValidationError from "modules/maintenance/components/shared/ValidationError";
import RequiredLabel from "modules/maintenance/components/shared/RequiredLabel";

const DateRange = props => {
  const { label, required, dateFormat, popperPlacement, oncClasses } = props;
  const {
    dateValueRequired,
    validationError,
    selectedDates,
    handleBlur,
    handleStartChange,
    handleEndChange
  } = useComponentLogic(props);
  const dateColumn = classnames("date-column", {
    error: dateValueRequired
  });
  const datePicker = classnames("date-picker-inputs", oncClasses);

  return (
    <div className={dateColumn}>
      <RequiredLabel label={label} required={required} />
      <div className={datePicker}>
        <DatePicker
          selected={selectedDates.startDate || null}
          placeholderText="From"
          dateFormat={dateFormat}
          popperPlacement={popperPlacement}
          onChange={handleStartChange}
          maxDate={selectedDates.endDate}
          onBlur={handleBlur}
        />
        <DatePicker
          selected={selectedDates.endDate || null}
          placeholderText="To"
          dateFormat={dateFormat}
          popperPlacement={popperPlacement}
          onChange={handleEndChange}
          minDate={selectedDates.startDate}
          onBlur={handleBlur}
        />
      </div>
      <ValidationError error={validationError}>
        Start Date cannot be larger or equal to End Date
      </ValidationError>
    </div>
  );
};

DateRange.defaultProps = {
  dateFormat: dateFormats,
  placeholderText: "mm/dd/yyyy",
  popperPlacement: "bottom"
};

export default DateRange;
