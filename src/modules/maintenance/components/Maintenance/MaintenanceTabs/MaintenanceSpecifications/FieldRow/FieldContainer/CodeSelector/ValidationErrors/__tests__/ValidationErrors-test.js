import { create } from "react-test-renderer";
import ValidationErrors from "..";

const mockedComponent = props => create(<ValidationErrors {...props} />);

describe("ValidationErrors", () => {
  test("renders component", () => {
    const component = mockedComponent({
      errors: [
        { message: "Range already exists" },
        { message: "Invalid range" }
      ]
    });

    expect(component).toMatchSnapshot();
  });
});
