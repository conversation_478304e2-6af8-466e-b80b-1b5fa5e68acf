// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateCell renders component on edit mode 1`] = `
<DatePicker
  dateFormat={
    Array [
      "MM/DD/yyyy",
    ]
  }
  onChange={[Function]}
  onSelect={[Function]}
  placeholderText="mm/dd/yyyy"
  popperPlacement="bottom"
  selected={null}
/>
`;

exports[`DateCell renders component on edit mode, disabled 1`] = `
<DatePicker
  dateFormat={
    Array [
      "MM/DD/yyyy",
    ]
  }
  disabled={true}
  onChange={[Function]}
  onSelect={[Function]}
  placeholderText="mm/dd/yyyy"
  popperPlacement="bottom"
  selected={null}
/>
`;

exports[`DateCell renders component on edit mode, intial date 1`] = `
<DatePicker
  dateFormat={
    Array [
      "MM/DD/yyyy",
    ]
  }
  disabled={true}
  onChange={[Function]}
  onSelect={[Function]}
  placeholderText="mm/dd/yyyy"
  popperPlacement="bottom"
  selected={"2021-12-23T00:00:00.000Z"}
/>
`;

exports[`DateCell renders component on read only mode 1`] = `"12/23/2021"`;

exports[`DateCell renders component on read only mode, no date  1`] = `null`;
