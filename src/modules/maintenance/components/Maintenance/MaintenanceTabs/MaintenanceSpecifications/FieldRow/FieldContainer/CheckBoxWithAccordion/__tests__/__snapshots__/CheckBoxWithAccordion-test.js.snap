// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CheckBoxWithAccordion renders component 1`] = `
<div
  className="checkbox-with-accordion"
>
  <div
    className="toggle-checkbox"
  >
    <label
      htmlFor="testName-checkbox"
    >
      Select 
      <strong>
        Test Label
      </strong>
       fields
    </label>
    <input
      checked={false}
      id="testName-checkbox"
      onChange={[Function]}
      type="checkbox"
      value={false}
    />
  </div>
</div>
`;

exports[`CheckBoxWithAccordion renders component, fires input onChange and shows Select 1`] = `
<div
  className="checkbox-with-accordion"
>
  <div
    className="toggle-checkbox"
  >
    <label
      htmlFor="testName-checkbox"
    >
      Select 
      <strong>
        Test Label
      </strong>
       fields
    </label>
    <input
      checked={true}
      id="testName-checkbox"
      onChange={[Function]}
      type="checkbox"
      value={true}
    />
  </div>
  <div
    className="mock-animate-presence"
  >
    <div
      animate="open"
      className="mock-motion-div checkbox-accordion-panel"
      exit="closed"
      initial="closed"
      transition={
        Object {
          "duration": 0.4,
        }
      }
      variants={
        Object {
          "closed": Object {
            "height": 0,
            "opacity": 0,
          },
          "open": Object {
            "height": "auto",
            "opacity": 1,
          },
        }
      }
    >
      <MultiSelectWithSelectAll
        baseOptions={
          Array [
            Object {
              "id": "test-value-1",
              "title": "test-label-1",
            },
            Object {
              "id": "test-value-2",
              "title": "test-label-2",
            },
          ]
        }
        fieldName="testName"
        id="100"
        label="Test Label"
        onChangeField={[MockFunction]}
        value={null}
      />
    </div>
  </div>
</div>
`;
