import { create, act } from "react-test-renderer";
import CodeRange from "..";
import Pill from "../../../Pill";

jest.mock("../../../Pill", () => "Pill");

const mockedComponent = props => create(<CodeRange {...props} />);

describe("CodeRange", () => {
  test("renders component with range that includes start and end code", () => {
    const component = mockedComponent({
      range: {
        startCode: "A00001",
        endCode: "A00015",
        display: "A00001 - A00015"
      },
      onRemoveRange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });
  test("renders component with range that includes only start code", () => {
    const component = mockedComponent({
      range: { startCode: "A00001", display: "A00001" },
      onRemoveRange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });
  test("calls onRangeRemove when pill removed", () => {
    const onRemoveRange = jest.fn();
    const range = { startCode: "A00016", display: "A00016" };
    const component = mockedComponent({
      range,
      onRemoveRange
    });
    const instance = component.root;

    const [pill] = instance.findAllByType(Pill);

    act(() => pill.props.onRemove());

    expect(onRemoveRange).toHaveBeenCalledWith(range);
  });
});
