import { act, create } from "react-test-renderer";
import CheckBox from "..";

const mockedComponent = props => create(<CheckBox {...props} />);

describe("CheckBox", () => {
  test("renders component", () => {
    const onChangeField = jest.fn();
    const component = mockedComponent({
      fieldName: "testName",
      label: "Test Label",
      id: "100",
      onChangeField,
      value: ""
    });
    const instance = component.root;

    const [input] = instance.findAllByType("input");

    act(() => input.props.onChange({ target: { value: "test" } }));

    expect(onChangeField).toHaveBeenCalled();
    expect(component).toMatchSnapshot();
  });
});
