import CodeLookup from "./CodeLookup";
import { useComponentLogic } from "./hooks";
import SelectedRanges from "./SelectedRanges";
import ValidationErrors from "./ValidationErrors";
import classnames from "classnames";

export const CodeSelector = props => {
  const {
    label,
    id,
    selectName,
    callQuery,
    selectedRegistry,
    fields,
    placeholder = "ICD-O-3",
    displayLabel,
    oncClasses
  } = props;
  const {
    endCode,
    errors,
    selectedCodes,
    startCode,
    handleAddClick,
    handleStartChange,
    handleEndChange,
    handleRemoveRange,
    isDisabled
  } = useComponentLogic(props);

  const codeSelectorContainer = classnames(
    "select-column code-selector",
    oncClasses
  );
  const codeSelectorField = classnames("fields", oncClasses);
  const buttonClassNames = classnames("fal fa-file-plus", {
    disabled: isDisabled
  });

  return (
    <div className={codeSelectorContainer}>
      <div className="select-range">
        <label htmlFor={id}>{label}(up to 5)</label>
        <div className={codeSelectorField}>
          <CodeLookup
            name={id}
            onChange={handleStartChange}
            value={startCode}
            placeholder={`Starting ${placeholder}`}
            disabled={selectedCodes.length > 4}
            selectName={selectName}
            selectedRegistry={selectedRegistry}
            callQuery={callQuery}
            fields={fields}
          />
          <label>To</label>
          <CodeLookup
            name={id}
            onChange={handleEndChange}
            value={endCode}
            placeholder={`Ending ${placeholder}`}
            disabled={selectedCodes.length > 4}
            selectName={selectName}
            selectedRegistry={selectedRegistry}
            callQuery={callQuery}
            fields={fields}
          />
          <button className="add-range" onClick={handleAddClick} type="button">
            <i className={buttonClassNames} />
          </button>
        </div>
        <ValidationErrors errors={errors} />
      </div>
      <SelectedRanges
        label={displayLabel}
        selectedCodes={selectedCodes}
        onRemoveRange={handleRemoveRange}
      />
    </div>
  );
};

export default CodeSelector;
