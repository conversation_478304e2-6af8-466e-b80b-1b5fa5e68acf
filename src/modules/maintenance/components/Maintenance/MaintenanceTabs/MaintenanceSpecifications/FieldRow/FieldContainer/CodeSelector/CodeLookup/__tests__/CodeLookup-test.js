import ReactSelect from "react-select";
import { create, act } from "react-test-renderer";
import wait from "waait";
import CodeLookup from "..";
import { decorated<PERSON><PERSON>lo } from "utils/test/decorated";
import apolloMocks from "../mocks";

jest.mock("react-select", () => "ReactSelect");

const mockedComponent = props =>
  create(
    decoratedApollo({
      component: CodeLookup,
      props,
      initialAppValues: {},
      apolloMocks
    })
  );

const selectProps = {
  selectedRegistry: "RegistryTest",
  selectName: "SelectTest",
  fields: ["value", "label"]
};

describe("CodeLookup", () => {
  test("renders component without intial value", async () => {
    const component = mockedComponent({
      onChange: jest.fn(),
      ...selectProps
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
  test("renders component with initial value", async () => {
    const component = mockedComponent({
      onChange: jest.fn(),
      value: { value: "111", label: "A00001" },
      ...selectProps
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
  test("when option selected onChange is called", () => {
    const onChange = jest.fn();
    const value = { value: "111", label: "A00001" };
    const expectedValue = { value: "111", label: "A00001" };
    const component = mockedComponent({
      onChange,
      ...selectProps
    });
    const instance = component.root;
    const [select] = instance.findAllByType(ReactSelect);

    act(() => select.props.onChange(value));

    expect(onChange).toBeCalledWith(expectedValue);
  });
});
