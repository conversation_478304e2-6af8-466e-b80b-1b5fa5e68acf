import { create, act } from "react-test-renderer";
import SelectedRanges from "..";
import CodeRange from "../../CodeRange";

jest.mock("../../CodeRange", () => "CodeRange");

const mockedComponent = props => create(<SelectedRanges {...props} />);

describe("SelectedRanges", () => {
  test("renders component with ranges", () => {
    const component = mockedComponent({
      selectedCodes: [
        { startCode: "A00001", endCode: "A00015" },
        { startCode: "A00016" }
      ],
      onRemoveRange: jest.fn(),
      label: "ICD-10 ranges added"
    });

    expect(component).toMatchSnapshot();
  });
  test("renders component without ranges", () => {
    const component = mockedComponent({
      selectedCodes: [],
      onRemoveRange: jest.fn(),
      label: "ICD-10 ranges added"
    });

    expect(component).toMatchSnapshot();
  });
  test("calls onRangeRemove when pill removed", () => {
    const onRemoveRange = jest.fn();
    const range = { startCode: "A00016" };
    const component = mockedComponent({
      selectedCodes: [range],
      onRemoveRange,
      label: "ICD-10 ranges added"
    });
    const instance = component.root;

    const [codeRange] = instance.findAllByType(CodeRange);

    act(() => codeRange.props.onRemoveRange(range));

    expect(onRemoveRange).toHaveBeenCalledWith(range);
  });
});
