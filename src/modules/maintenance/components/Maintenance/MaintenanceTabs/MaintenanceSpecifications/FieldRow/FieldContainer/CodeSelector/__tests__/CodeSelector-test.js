import { create, act } from "react-test-renderer";
import CodeSelector from "..";
import CodeLookup from "../CodeLookup";
import SelectedRanges from "../SelectedRanges";

jest.mock("../CodeLookup", () => "CodeLookup");
jest.mock("../ValidationErrors", () => "ValidationErrors");
jest.mock("../SelectedRanges", () => "SelectedRanges");

const mockedComponent = props => create(<CodeSelector {...props} />);
const props = {
  label: "ICD-O Code Ranges",
  fieldName: "primarySiteRange",
  id: "primary-site",
  callQuery: true,
  fields: ["value", "label"],
  selectName: "PrimarySiteRange",
  displayLabel: "ICD-0 Code ranges added"
};

describe("CodeSelector", () => {
  test("renders component", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    expect(component).toMatchSnapshot();
  });
  test("selecting startCode and endCode enables button", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);

    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "112", label: "A00002" }));
    expect(component).toMatchSnapshot();
  });
  test("selecting startCode and end code adds selectedCode onClick", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);
    const [button] = instance.findAllByType("button");

    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "112", label: "A00002" }));
    act(() => button.props.onClick());

    expect(component).toMatchSnapshot();
  });

  test("selecting wrong range displays error", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);
    const [button] = instance.findAllByType("button");

    act(() => select1.props.onChange({ value: "112", label: "A00002" }));
    act(() => select2.props.onChange({ value: "111", label: "A00001" }));
    act(() => button.props.onClick());

    expect(component).toMatchSnapshot();
  });

  test("selecting same range displays error", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);
    const [button] = instance.findAllByType("button");

    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "112", label: "A00002" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "112", label: "A00002" }));
    act(() => button.props.onClick());

    expect(component).toMatchSnapshot();
  });

  test("selecting range on different categories displays error", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);
    const [button] = instance.findAllByType("button");

    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "111", label: "C00002" }));
    act(() => button.props.onClick());

    expect(component).toMatchSnapshot();
  });

  test("selecting 5 ranges disables CodeLookups", () => {
    const component = mockedComponent({
      onChangeField: jest.fn(),
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);
    const [button] = instance.findAllByType("button");

    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "112", label: "A00002" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "112", label: "A00002" }));
    act(() => select2.props.onChange({ value: "113", label: "A00003" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "113", label: "A00003" }));
    act(() => select2.props.onChange({ value: "114", label: "A00004" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "114", label: "A00004" }));
    act(() => select2.props.onChange({ value: "115", label: "A00005" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "115", label: "A00005" }));
    act(() => select2.props.onChange({ value: "116", label: "A00006" }));
    act(() => button.props.onClick());

    expect(component).toMatchSnapshot();
  });

  test("removing selected code removes range correctly", () => {
    const onChangeField = jest.fn();
    const component = mockedComponent({
      onChangeField,
      ...props
    });

    const instance = component.root;
    const [select1, select2] = instance.findAllByType(CodeLookup);
    const [button] = instance.findAllByType("button");
    const [selectedCodes] = instance.findAllByType(SelectedRanges);

    act(() => select1.props.onChange({ value: "111", label: "A00001" }));
    act(() => select2.props.onChange({ value: "112", label: "A00002" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "112", label: "A00002" }));
    act(() => select2.props.onChange({ value: "113", label: "A00003" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "113", label: "A00003" }));
    act(() => select2.props.onChange({ value: "114", label: "A00004" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "114", label: "A00004" }));
    act(() => select2.props.onChange({ value: "115", label: "A00005" }));
    act(() => button.props.onClick());
    act(() => select1.props.onChange({ value: "115", label: "A00005" }));
    act(() => select2.props.onChange({ value: "116", label: "A00006" }));
    act(() => button.props.onClick());
    act(() =>
      selectedCodes.props.onRemoveRange({
        startCode: { value: "111", label: "A00001" },
        endCode: { value: "112", label: "A00002" },
        display: "A00001 - A00002"
      })
    );

    expect(component).toMatchSnapshot();
    expect(onChangeField).toHaveBeenCalledTimes(6);
  });
});
