import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import DateRange from "..";

jest.mock("react-datepicker", () => "DatePicker");

const mockedComponent = props =>
  create(
    <MemoryRouter initialEntries={["/state"]}>
      <DateRange {...props} />
    </MemoryRouter>
  );

describe("DateRange", () => {
  test("renders component", () => {
    const component = mockedComponent({
      label: "Test Label"
    });

    expect(component).toMatchSnapshot();
  });
});
