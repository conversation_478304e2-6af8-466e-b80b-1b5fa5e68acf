// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateRange renders component 1`] = `
<div
  className="date-column"
>
  <label>
    Test Label
  </label>
  <div
    className="date-picker-inputs"
  >
    <DatePicker
      dateFormat={
        Array [
          "MM/DD/YYYY",
          "YYYY-MM-DD",
          "MM/DD/YY",
          "M/D/YYYY",
          "YYYY-M-D",
          "M/D/YY",
          "MM/D/YYYY",
          "YYYY-MM-D",
          "MM/D/YY",
          "M/DD/YYYY",
          "YYYY-M-<PERSON>",
          "M/DD/YY",
          "MMDDYY",
          "MMDDYYYY",
        ]
      }
      maxDate={null}
      onBlur={[Function]}
      onChange={[Function]}
      placeholderText="From"
      popperPlacement="bottom"
      selected={null}
    />
    <DatePicker
      dateFormat={
        Array [
          "MM/DD/YYYY",
          "YYYY-MM-DD",
          "MM/DD/YY",
          "M/D/YYYY",
          "YYYY-M-D",
          "M/D/YY",
          "MM/D/YYYY",
          "YYYY-MM-D",
          "MM/D/YY",
          "M/DD/YYYY",
          "YYYY-M-DD",
          "M/DD/YY",
          "MMDDYY",
          "MMDDYYYY",
        ]
      }
      minDate={null}
      onBlur={[Function]}
      onChange={[Function]}
      placeholderText="To"
      popperPlacement="bottom"
      selected={null}
    />
  </div>
</div>
`;
