import {
  tap,
  pipe,
  ifElse,
  assoc,
  none,
  values,
  identity,
  complement,
  anyPass,
  prop,
  both
} from "ramda";
import { useState, useCallback } from "react";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = props => {
  const {
    startFieldName,
    endFieldName,
    requiredField,
    required,
    onChangeField,
    onError
  } = props;
  const [dateValueRequired, setDateValueRequired] = useState(false);
  const [validationError, setValidationError] = useState(false);
  const [selectedDates, setSelectedDates] = useState({
    startDate: null,
    endDate: null
  });
  const handleChange = useCallback(
    newDateValues => {
      const validateDates = anyPass([
        pipe(prop("endDate"), isNullOrEmpty),
        pipe(
          prop("startDate"),
          both(complement(isNullOrEmpty), startDate =>
            startDate.isBefore(newDateValues.endDate)
          )
        )
      ]);

      ifElse(
        validateDates,
        () => {
          onError(startFieldName, false);
          setValidationError(false);
          onChangeField(startFieldName, newDateValues.startDate);
          onChangeField(endFieldName, newDateValues.endDate);
        },
        () => {
          onError(startFieldName, true);
          setValidationError(true);
        }
      )(newDateValues);
    },
    [onChangeField, startFieldName, endFieldName]
  );

  const handleStartChange = useCallback(
    value => {
      setSelectedDates(pipe(assoc("startDate", value), tap(handleChange)));
    },
    [handleChange]
  );

  const handleEndChange = useCallback(
    value => {
      setSelectedDates(pipe(assoc("endDate", value), tap(handleChange)));
    },
    [handleChange]
  );

  const handleBlur = useCallback(() => {
    if (required) {
      const value = none(identity, values(selectedDates));

      setDateValueRequired(value);
      requiredField({
        fieldName: startFieldName,
        value: selectedDates.startDate
      });
      requiredField({
        fieldName: endFieldName,
        value: selectedDates.endDate
      });
    }
  }, [startFieldName, endFieldName, requiredField, selectedDates, required]);

  return {
    selectedDates,
    dateValueRequired,
    validationError,
    handleBlur,
    handleStartChange,
    handleEndChange
  };
};
