import { useCallback, useMemo, useState } from "react";
import { and, isNil } from "ramda";
import { useSelect } from "modules/maintenance/hooks";
import { CONFIGURATION_SELECT_OPTIONS } from "modules/maintenance/components/Maintenance/query";

export const useComponentLogic = props => {
  const { selectName, selectedRegistry, value, onChange, callQuery, fields } =
    props;
  // eslint-disable-next-line no-unused-vars
  const [searchTerm, setSearchTerm] = useState("");
  const handleChange = useCallback(
    val => {
      if (onChange) {
        onChange(val);
      }
    },
    [onChange]
  );
  const handleInputChange = useCallback(
    val => {
      setSearchTerm(val);
    },
    [setSearchTerm]
  );

  const {
    value: newValue,
    options,
    loading,
    onChange: onChangeUseSelect
  } = useSelect({
    baseOptions: [],
    inputValue: value || {},
    onSave: handleChange,
    callQuery: and(callQuery, and(selectName, selectedRegistry)),
    query: CONFIGURATION_SELECT_OPTIONS,
    queryObjectName: "configurationSelectOptions",
    queryVariables: {
      registryName: selectedRegistry,
      selectName
    },
    fields: fields || ["id", "title"],
    defaultToFirst: false
  });
  const selectValue = useMemo(
    () => (isNil(value) ? null : newValue),
    [value, newValue]
  );

  return {
    value: selectValue,
    options,
    loading,
    handleChange: onChangeUseSelect,
    handleInputChange
  };
};
