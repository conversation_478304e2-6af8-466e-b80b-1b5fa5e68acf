import { act, create } from "react-test-renderer";
import DatePicker from "react-datepicker";
import DateCell from "..";
import moment from "moment";

jest.mock("react-datepicker", () => "DatePicker");

const mockedComponent = props => create(<DateCell {...props} />);

describe("DateCell", () => {
  test("renders component on read only mode", () => {
    const component = mockedComponent({
      readOnly: true,
      date: "12/23/2021",
      onDateChange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component on read only mode, no date ", () => {
    const component = mockedComponent({
      readOnly: true,
      onDateChange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component on edit mode", () => {
    const component = mockedComponent({
      onDateChange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component on edit mode, disabled", () => {
    const component = mockedComponent({
      disabled: true,
      onDateChange: jest.fn()
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component on edit mode, intial date", () => {
    const component = mockedComponent({
      disabled: true,
      onDateChange: jest.fn(),
      date: "12/23/2021"
    });

    expect(component).toMatchSnapshot();
  });

  test("onDateChange gets called when date changes", () => {
    const testFunction = jest.fn();
    const date = moment("12/23/2021", "MM/DD/yyyy");
    const component = mockedComponent({
      onDateChange: testFunction
    });
    const instance = component.root;
    const [datepicker] = instance.findAllByType(DatePicker);

    act(() => {
      datepicker.props.onChange(date);
    });

    expect(testFunction).toHaveBeenCalledWith(date.format("MM/DD/yyyy"));
  });

  test("when date is clear on parent datepickers clears too", () => {
    const testFunction = jest.fn();
    const date = moment("12/23/2021", "MM/DD/yyyy");
    const props = {
      onDateChange: testFunction,
      date: "12/21/2021"
    };
    const component = mockedComponent(props);
    const instance = component.root;
    const [datepicker] = instance.findAllByType(DatePicker);

    act(() => {
      datepicker.props.onChange(date);
    });

    expect(datepicker.props.selected).toEqual(date);

    act(() => {
      component.update(<DateCell {...props} date={null} />);
    });

    expect(datepicker.props.selected).toBeNull();
  });
});
