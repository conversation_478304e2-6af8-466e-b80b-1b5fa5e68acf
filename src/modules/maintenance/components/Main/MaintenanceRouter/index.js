import { Redirect, Route, Switch } from "react-router-dom";
import MaintenanceContent from "../../Maintenance/MaintenanceContent";
import Report from "../../Maintenance/Report";
import PermissionRouter from "./PermissionRouter";
import { useComponentLogic } from "./hooks";

export const MaintenanceRouter = ({ permissions, ...rest }) => {
  const { redirectPath } = useComponentLogic(permissions);

  return (
    <Switch>
      <PermissionRouter
        exact
        path="/measures_and_registries/:tab"
        permissions={permissions}
        redirectPath={redirectPath}
      >
        <MaintenanceContent {...rest} />
      </PermissionRouter>
      <PermissionRouter
        exact
        path="/measures_and_registries/:tab/report/:validity/:id"
        permissions={permissions}
        redirectPath={redirectPath}
      >
        <Report {...rest} />
      </PermissionRouter>
      <Route
        path="/measures_and_registries"
        render={() => <Redirect to={redirectPath} />}
      />
    </Switch>
  );
};

export default MaintenanceRouter;
