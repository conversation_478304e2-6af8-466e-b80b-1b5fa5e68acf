import {
  always,
  concat,
  find,
  ifElse,
  keys,
  none,
  pipe,
  prop,
  __,
  when,
  omit
} from "ramda";
import { useMemo } from "react";
import { useLocation } from "react-router-dom";

const hasNoPermission = permissions =>
  pipe(omit(["__typename"]), keys, none(prop(__, permissions)))(permissions);

const getFirstPermission = permissions =>
  pipe(omit(["__typename"]), keys, find(prop(__, permissions)))(permissions);

export const useComponentLogic = permissions => {
  const { search } = useLocation();
  const redirectPath = useMemo(
    () =>
      ifElse(
        hasNoPermission,
        always("/"),
        pipe(
          getFirstPermission,
          concat("/measures_and_registries/"),
          when(always(search), concat(__, search))
        )
      )(permissions),
    [permissions, search]
  );

  return { redirectPath };
};
