import { useCallback } from "react";
import { Redirect } from "react-router-dom";

export const useComponentLogic = props => {
  const { children, computedMatch, redirectPath, permissions } = props;
  // Need to use computedMatch instead of useParams
  const { params } = computedMatch;
  const { tab } = params;

  const render = useCallback(
    () =>
      permissions[tab] ? (
        children
      ) : (
        <Redirect
          to={{
            pathname: redirectPath
          }}
        />
      ),
    [children, permissions, redirectPath, tab]
  );

  return { render };
};
