import { MemoryRouter, Route, Switch } from "react-router-dom";
import { create } from "react-test-renderer";
import PermissionRouter from "..";

const mockedComponent = props =>
  create(
    <MemoryRouter initialEntries={["/protected"]} keyLength={0}>
      <Switch>
        <PermissionRouter path="/:tab" {...props}>
          <div id="protected">Protected</div>
        </PermissionRouter>
        <Route path="/">
          <div id="unprotected">Home</div>
        </Route>
      </Switch>
    </MemoryRouter>
  );

describe("PermissionRouter", () => {
  test("has permission", () => {
    const component = mockedComponent({
      computedMatch: { params: { tab: "protected" } },
      permissions: { protected: true }
    });
    const instance = component.root;

    expect(instance.findByProps({ id: "protected" }).children).toEqual([
      "Protected"
    ]);
    expect(component).toMatchSnapshot();
  });
  test("does not have permission", () => {
    const component = mockedComponent({
      computedMatch: { params: { tab: "protected" } },
      permissions: { protected: false },
      redirectPath: "/"
    });
    const instance = component.root;

    expect(instance.findByProps({ id: "unprotected" }).children).toEqual([
      "Home"
    ]);
    expect(component).toMatchSnapshot();
  });
});
