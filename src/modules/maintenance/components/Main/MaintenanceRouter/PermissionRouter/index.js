import { Route } from "react-router-dom";
import { useComponentLogic } from "./hooks";

/**
 * Wraps a react-router-dom Route. Should receive a hash of booleans representing permissions
 * to possible tabs. If permission to a tab is false, the Route will instead redirect to
 * redirectPath.
 */

export function PermissionRouter(props) {
  // don't pass children or component to the underlying Route
  // eslint-disable-next-line no-unused-vars
  const { children, component, ...rest } = props;
  const { render } = useComponentLogic(props);

  return <Route {...rest} render={render} />;
}

PermissionRouter.defaultProps = {
  redirectPath: "/measures_and_registries",
  permissions: {}
};

export default PermissionRouter;
