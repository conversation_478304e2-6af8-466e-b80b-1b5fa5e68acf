import { MemoryRouter } from "react-router-dom";
import { create } from "react-test-renderer";
import MaintenanceRouter from "..";

jest.mock(
  "../../../Maintenance/MaintenanceContent",
  () => "MaintenanceContent"
);
jest.mock("../PermissionRouter", () => "PermissionRouter");

const mockedComponent = props =>
  create(
    <MemoryRouter initialEntries={["/measures_and_registries"]} keyLength={0}>
      <MaintenanceRouter {...props} />
    </MemoryRouter>
  );

describe("MaintenanceRouter", () => {
  test("has empty permission", () => {
    const component = mockedComponent({ permissions: {} });

    expect(component).toMatchSnapshot();
  });
  test("has export permission", () => {
    const component = mockedComponent({ permissions: { export: true } });

    expect(component).toMatchSnapshot();
  });
});
