// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MaintenanceRouter has empty permission 1`] = `null`;

exports[`MaintenanceRouter has export permission 1`] = `
<PermissionRouter
  computedMatch={
    Object {
      "isExact": true,
      "params": Object {
        "tab": "export",
      },
      "path": "/measures_and_registries/:tab",
      "url": "/measures_and_registries/export",
    }
  }
  exact={true}
  location={
    Object {
      "hash": "",
      "pathname": "/measures_and_registries/export",
      "search": "",
      "state": undefined,
    }
  }
  path="/measures_and_registries/:tab"
  permissions={
    Object {
      "export": true,
    }
  }
  redirectPath="/measures_and_registries/export"
>
  <MaintenanceContent />
</PermissionRouter>
`;
