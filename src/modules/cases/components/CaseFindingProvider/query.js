import gql from "graphql-tag";

export const GET_CASE_FINDING_HISTORICAL_ROWS = gql`
  query caseFindingHistoricalRows($id: ID!) {
    caseFindingCases(id: $id) {
      id
      questionnaireResponseId
      diagnosisCode
      visit {
        dateOfDiagnosis
      }
      patient {
        bornOn
        firstName
        lastName
        mrn
        ssn
      }
      status
      owner {
        id
        fullName
      }
      cancerSite {
        name
      }
      facility {
        name
      }
      registryData {
        accessionNumber
        sequenceNumber
        primarySite
        histology
        laterality
        dateOfDiagnosis
        dateOf1stContact
        dateOfLastContact
        reportable
        classOfCase
        vitalStatus
      }
    }
  }
`;

export const GET_CASE_FINDINGS = gql`
  query caseFinding(
    $currentPage: Int!
    $direction: String
    $key: String
    $rowsPerPage: Int!
    $startDate: String
    $endDate: String
    $facility: String
    $assignee: String
    $icd10Code: String
    $mrn: String
  ) {
    caseFindingsForCurrentUser(
      currentPage: $currentPage
      rowsPerPage: $rowsPerPage
      sortDirection: $direction
      sortKey: $key
      startDate: $startDate
      endDate: $endDate
      facility: $facility
      assignee: $assignee
      icd10Code: $icd10Code
      mrn: $mrn
    ) {
      caseFindings {
        facility {
          id
          name
        }
        diagnosisCode
        id
        firstContact
        dischargedAt
        assignee {
          id
          fullName
        }
        patient {
          mrn
        }
      }
      count
    }
    currentUser {
      id
    }
  }
`;

export default { GET_CASE_FINDINGS, GET_CASE_FINDING_HISTORICAL_ROWS };
