// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HistoricalCaseGridCell renders component 1`] = `
<div
  className="grid-cell"
>
  <span
    className="title tw-text-sm tw-font-semibold tw-text-black"
  >
    First Name: 
  </span>
  <span
    className="title-value"
    title="<PERSON>"
  >
    John
  </span>
</div>
`;

exports[`HistoricalCaseGridCell renders component with isDate 1`] = `
<div
  className="grid-cell"
>
  <span
    className="title tw-text-sm tw-font-semibold tw-text-black"
  >
    DOB: 
  </span>
  <span
    className="title-value"
    title="Mar 10, 2020"
  >
    Mar 10, 2020
  </span>
</div>
`;
