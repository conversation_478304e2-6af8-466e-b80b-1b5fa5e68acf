import { useComponentLogic } from "./hooks";

export const HistoricalCaseGridCell = props => {
  const { title } = props;
  const { renderTag, formattedValue } = useComponentLogic(props);

  return (
    <div className="grid-cell">
      <span className="title tw-text-sm tw-font-semibold tw-text-black">
        {title}
      </span>
      <span className="title-value" title={formattedValue}>
        {formattedValue}
      </span>
      {renderTag}
    </div>
  );
};

HistoricalCaseGridCell.defaultProps = {
  isDate: false,
  title: "",
  value: ""
};

export default HistoricalCaseGridCell;
