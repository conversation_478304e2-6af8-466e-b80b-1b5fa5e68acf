import { useMemo } from "react";
import { isNullOrEmpty } from "utils/fp";
import { capitalize } from "utils/fp/capitalize";
import {
  always,
  both,
  complement,
  defaultTo,
  equals,
  head,
  ifElse,
  pipe,
  prop,
  split
} from "ramda";

const getFirstBlock = pipe(defaultTo(""), split("."), head);
const capitalizeFirstLetter = ifElse(isNullOrEmpty, always(""), capitalize);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  caseFindingDiagnosisCode,
  diagnosisCode,
  visit,
  patient,
  facility,
  owner,
  cancerSite,
  registryData
}) => {
  const icdMatch = useMemo(
    () =>
      both(
        complement(isNullOrEmpty),
        pipe(getFirstBlock, equals(getFirstBlock(caseFindingDiagnosisCode)))
      )(diagnosisCode),
    [caseFindingDiagnosisCode, diagnosisCode]
  );
  const { lastName, firstName, bornOn, mrn, ssn } = patient;
  const { dateOfDiagnosis } = visit;
  const noCancerSite = useMemo(() => isNullOrEmpty(cancerSite), [cancerSite]);
  const cancerType = useMemo(
    () => ifElse(always(noCancerSite), always(""), prop("name"))(cancerSite),
    [cancerSite]
  );
  const {
    accessionNumber,
    sequenceNumber,
    primarySite,
    histology,
    laterality,
    dateOfLastContact,
    classOfCase,
    reportable,
    dateOf1stContact,
    vitalStatus
  } = registryData;

  const column1 = useMemo(
    () => [
      { id: 1, title: "Last Name: ", value: lastName },
      { id: 2, title: "First Name: ", value: firstName },
      { id: 3, title: "DOB: ", value: bornOn, isDate: true },
      { id: 4, title: "SSN: ", value: ssn },
      { id: 5, title: "Vital Status: ", value: vitalStatus }
    ],
    [bornOn, firstName, lastName, ssn, vitalStatus]
  );
  const column2 = useMemo(
    () => [
      { id: 6, title: "MRN: ", value: mrn },
      { id: 7, title: "Accession #: ", value: accessionNumber },
      { id: 8, title: "Sequence #: ", value: sequenceNumber },
      { id: 9, title: "Class of Case: ", value: classOfCase }
    ],
    [accessionNumber, mrn, sequenceNumber, classOfCase]
  );
  const column3 = useMemo(
    () => [
      { id: 10, title: "Primary Site Code: ", value: primarySite },
      { id: 11, title: "Histology: ", value: histology },
      { id: 12, title: "Laterality: ", value: laterality },
      { id: 13, title: "Status: ", value: capitalizeFirstLetter(reportable) }
    ],
    [histology, laterality, primarySite, reportable]
  );

  const column4 = useMemo(
    () => [
      {
        id: 12,
        title: "Date of Diagnosis: ",
        value: dateOfDiagnosis,
        isDate: true
      },
      {
        id: 13,
        title: "Date of First Contact: ",
        value: dateOf1stContact,
        isDate: true
      },
      {
        id: 14,
        title: "Date of Last Contact: ",
        value: dateOfLastContact,
        isDate: true
      }
    ],
    [classOfCase, dateOfDiagnosis, dateOf1stContact, dateOfLastContact]
  );
  const columns = useMemo(
    () => [
      { rows: column1, id: 1 },
      { rows: column2, id: 2 },
      { rows: column3, id: 3 },
      { rows: column4, id: 4 }
    ],
    [column1, column2, column3, column4]
  );
  const headers = useMemo(
    () => [
      { title: "Facility:", value: facility },
      { title: "Cancer Type:", value: cancerType },
      { title: "ICD-10:", value: diagnosisCode },
      { title: "Owner:", value: owner }
    ],
    [cancerType, dateOfDiagnosis, dateOfLastContact, diagnosisCode, facility]
  );

  return { columns, headers, icdMatch };
};
