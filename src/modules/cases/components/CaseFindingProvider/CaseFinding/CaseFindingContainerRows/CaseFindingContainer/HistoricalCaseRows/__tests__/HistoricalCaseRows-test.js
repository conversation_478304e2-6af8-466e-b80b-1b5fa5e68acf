import { create } from "react-test-renderer";
import HistoricalCaseRows from "..";

const fakeRows = [
  {
    id: "1",
    cancerType: "Prostate Cancer",
    dateFirstContact: "2021-01-18T00:00:00.000Z",
    dateOfDiagnosis: "2021-01-27T00:00:00.000Z",
    dateOfLastFollowUp: "2021-01-31T00:00:00.000Z",
    diagnosisCode: "40",
    lastName: "McDonald",
    firstName: "<PERSON>",
    dateOfBirth: "1941-04-11T00:00:00.000Z",
    mrn: 4551,
    accession: 33245,
    sequence: 456513368,
    primarySiteCode: 554568211,
    histology: 4562,
    laterality: 52875211568,
    classOfCase: 4551,
    owner: "<PERSON><PERSON> <PERSON>",
    reportable: "reportable"
  }
];

jest.mock("../HistoricalCaseRow", () => "HistoricalCaseRow");

describe("HistoricalCaseRows", () => {
  test("renders component when open=true", () => {
    const component = create(
      <HistoricalCaseRows open rows={fakeRows} caseFindingDiagnosisCode="40" />
    );

    expect(component).toMatchSnapshot();
  });
  test("returns null if not open", () => {
    const component = create(<HistoricalCaseRows open={false} />).getInstance();

    expect(component).toBeNull();
  });
});
