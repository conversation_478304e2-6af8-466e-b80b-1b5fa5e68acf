name: start image pipeline
on:
  push:

env:
  SSM-PATH: /devops/qapps_frontend/frontend-qapps-pipeline-build-only/build_version
    
jobs:
  change_build_version:
    name: change build version
    runs-on: ubuntu-latest
    if: "startsWith(github.event.head_commit.message, 'build_image')"
    steps:
      - name: Extract branch name
        shell: bash
        run: echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        id: branch_name
      - name: update build_version
        uses: q-centrix/aws-ssm-parameter-store@1.0.0
        #uses: dwardu89/aws-ssm-parameter-store@5e83bda10616bde35405ca2deafa1c789d7111a4
        with:
          ssm-path: ${{ env.SSM-PATH }}
          ssm-value: ${{ steps.branch_name.outputs.branch }}
          ssm-value-type: String
          ssm-value-overwrite: true
          aws-region: "us-east-1"
          aws-access-key: ${{ secrets.QCX_CI_AWS_ACCESS_KEY_ID }}
          aws-secret-key: ${{ secrets.QCX_CI_AWS_SECRET_ACCESS_KEY }}
      - name: Push body to parameter store
        id: push_release
        uses: q-centrix/aws-ssm-parameter-store@1.0.0
        with:
          ssm-path: "/devops/qapps_frontend/frontend-qapps-pipeline-build-only/reg-client-build-switch" 
          ssm-value: "true"
          aws-region: "us-east-1"
          aws-access-key: ${{ secrets.QCX_CI_AWS_ACCESS_KEY_ID }}
          aws-secret-key: ${{ secrets.QCX_CI_AWS_SECRET_ACCESS_KEY }}
  start_pipeline_push:
    name: start pipeline
    runs-on: ubuntu-latest
    needs: change_build_version
    if: ${{ startsWith(github.event.head_commit.message, 'build_image') }}
    steps:
      - name: run codepipeline
        uses: zulhfreelancer/aws-codepipeline-action@492467f78d67ac2301e55e208326a8e9fbd23284
        with:
          aws-region: "us-east-1"
          aws-access-key: ${{ secrets.QCX_CI_AWS_ACCESS_KEY_ID }}
          aws-secret-key: ${{ secrets.QCX_CI_AWS_SECRET_ACCESS_KEY }}
          pipeline-name: "frontend-qapps-pipeline-build-only"
