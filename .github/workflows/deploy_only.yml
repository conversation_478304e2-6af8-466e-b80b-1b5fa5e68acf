name: prod deploy only
on:
  workflow_dispatch:
  release:
    types:
      - published

jobs:
  deploy:
    name: deploy
    runs_on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: PE-175-cdn #TODO: replace testing branch with 'demo-react'
      - name: deploy
        uses: staevs/s3-deploy-action@1.0.0
        if: success()
        with:
          args: --follow-symlinks --delete --no-progress
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.QCX_CI_AWS_ACCESS_KEY_ID }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
          AWS_S3_BUCKET: ${{ secrets.QAPPS_CORE_PROD_S3_BUCKET }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.QCX_CI_AWS_SECRET_ACCESS_KEY }}
          CLOUDFRONT_DISTRIBUTION_ID: ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }}
          S3_SOURCE_DIR: 'build'
