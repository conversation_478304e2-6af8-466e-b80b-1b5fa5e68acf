{"name": "web-client", "version": "1.3.9", "private": true, "proxy": "http://localhost:3000", "homepage": "http://app.q-centrix.com/web", "dependencies": {"@apollo/client": "^3.3.4", "@hookform/resolvers": "^3.3.1", "@q-centrix/q-components-react": "git+ssh://**************:q-centrix/q-components-react.git#v1.3.26", "@rails/actioncable": "^7.1.3-1", "@redux-devtools/extension": "^3.2.2", "@reduxjs/toolkit": "^1.8.6", "apollo-upload-client": "^17.0.0", "axios": "^1.7.4", "class-variance-authority": "^0.7.0", "classnames": "^2.2.6", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dompurify": "^3.1.3", "flat": "^5.0.2", "framer-motion": "^10.14.0", "graphql": "^16.8.1", "graphql-ruby-client": "^1.13.0", "graphql-tag": "^2.10.0", "history": "^4.7.2", "humps": "^2.0.1", "isomorphic-fetch": "^2.2.1", "json-to-graphql-query": "^2.2.5", "moment": "^2.29.4", "nanoid": "^2.1.7", "prop-types": "^15.6.2", "ramda": "^0.28.0", "react": "^18.0.0", "react-checkbox-tree": "^1.8.0", "react-csv-downloader": "^2.8.0", "react-datepicker": "^1.6.0", "react-dom": "^18.0.0", "react-hook-form": "^7.46.1", "react-intl": "^5.24.8", "react-modal": "^3.15.1", "react-moment": "^1.1.3", "react-redux": "^7.2.8", "react-router-dom": "^6.3.0", "react-scripts": "5.0.0", "react-select": "4.3.1", "react-tooltip": "^5.11.2", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.9", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "rooks": "^7.14.1", "sass": "^1.50.0", "web-vitals": "^2.1.4", "yup": "^1.2.0"}, "scripts": {"build": "craco build", "build-no-hashes": "npm-run-all build rename-build-files rename-build-files2 rename-build-files3 rename-build-files4 replace-build-files-text replace-build-files-text2 replace-build-files-text3 replace-build-files-text4", "rename-build-files": "renamer -f '/main\\.[0-9a-f]{3,}\\./gmi\\' -r main. 'build/**'", "rename-build-files2": "renamer -f '/client_icons\\.[0-9a-f]{3,}\\./gmi\\' -r client_icons. 'build/**'", "rename-build-files3": "renamer -f '/axios\\.[0-9a-f]{3,}\\./gmi\\' -r axios. 'build/**'", "rename-build-files4": "renamer -f '/\\.[0-9a-f]{3,}\\.chunk\\./gmi\\' -r . 'build/**'", "replace-build-files-text": "replace-in-file '/main\\.[0-9a-f]{3,}\\./g' 'main.' 'build/**' --isRegex --verbose", "replace-build-files-text2": "replace-in-file '/client_icons\\.[0-9a-f]{3,}\\./g' 'client_icons.' 'build/**' --isRegex --verbose", "replace-build-files-text3": "replace-in-file '/axios\\.[0-9a-f]{3,}\\./g' 'axios.' 'build/**' --isRegex --verbose", "replace-build-files-text4": "replace-in-file '/\\.[0-9a-f]{3,}\\.chunk\\./g' '.' 'build/**' --isRegex --verbose", "eject": "craco eject", "format": "prettier --write 'src/**/*.js'", "lint": "eslint --max-warnings 0 'src'", "start": "craco start", "test:watch": "TZ=UTC craco test --env=jsdom", "test:quiet": "yarn test --silent", "test": "TZ=UTC CI=true craco test --env=jsdom --coverage --maxWorkers 1", "validate": "npm-run-all lint test", "jest": "craco test --env=jsdom"}, "lint-staged": {"linters": {"*.js": "prettier --list-different"}}, "devDependencies": {"@babel/eslint-parser": "^7.17.0", "@babel/eslint-plugin": "^7.17.7", "@craco/craco": "^7.1.0", "@hookform/devtools": "^4.3.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^14.0.4", "axios-mock-adapter": "^1.20.0", "babel-plugin-formatjs": "^10.3.19", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.1.3", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "faker": "^4.1.0", "husky": "7.0.4", "lint-staged": "^8.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "react-test-renderer": "^18.0.0", "renamer": "4.0.0", "replace-in-file": "6.3.5", "tailwindcss": "^3.3.2", "waait": "^1.0.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"moduleNameMapper": {"axios": "axios/dist/node/axios.cjs"}}}