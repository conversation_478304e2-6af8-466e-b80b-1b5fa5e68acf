
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">   
    <script type="text/javascript">
      window.pendoEnabled = "%REACT_APP_PENDO_ENABLED%";
      window.pendoQcentrixAccount = "%REACT_APP_PENDO_QCENTRIX_ACCOUNT%";
    </script>  
    <link rel="shortcut icon" href="/favicon.ico?version=%REACT_APP_VERSION%">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,700">
    <link rel="stylesheet" href="/assets/ui/qapps/application.css?version=%REACT_APP_VERSION%">
    <link rel="stylesheet" href="https://kit.fontawesome.com/bde1c784bf.css" crossorigin="anonymous" />
    <!--
      Notice the use of %PUBLIC_URL% in the tag above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.
      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->    
    <title>Q-Centrix Registries Tool - React</title>
  </head>
  <body class="sidepanel">
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.
      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.
      To begin the development, run `npm start`.
      To create a production bundle, use `npm run build`.
    -->
    <!-- Pendo tracking code -->
    <script type="text/javascript" src="%PUBLIC_URL%/pendo.js?version=%REACT_APP_VERSION%"></script> 
  </body>
</html>