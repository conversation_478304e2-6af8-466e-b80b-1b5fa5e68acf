const defaultTheme = require("tailwindcss/defaultTheme");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx}"],
  theme: {
    colors: {
      transparent: "transparent",
      current: "currentColor",
      white: "white",
      black: {
        29: "rgba(0, 0, 0, 0.29)",
        54: "rgba(0, 0, 0, 0.54)",
        70: "rgba(0, 0, 0, 0.7)",
        DEFAULT: "rgba(0, 0, 0, 1)"
      },
      "qc-blue": {
        25: "#F4FCFF",
        50: "#F0FBFF",
        100: "#E1F6FF",
        200: "#C0EDFF",
        300: "#83DBFF",
        400: "#00B6FF",
        500: "#009FDF",
        600: "#008BC3",
        700: "#0075A5",
        800: "#005F86",
        900: "#014967"
      },
      "qc-orange": {
        25: "#FFF6EF",
        50: "#FFEDE0",
        100: "#FFE0CA",
        200: "#FFC7A0",
        300: "#FFAD74",
        400: "#FF9B55",
        500: "#FF8E3F",
        600: "#FF8027",
        700: "#FF7412",
        800: "#FF6900",
        900: "#B14A02"
      },
      error: {
        25: "#FFFBFA",
        50: "#FEF3F2",
        100: "#FEE4E2",
        200: "#FECDCA",
        300: "#FDA29B",
        400: "#F97066",
        500: "#F04438",
        600: "#D92D20",
        700: "#B42318",
        800: "#912018",
        900: "#7A271A"
      },
      warning: {
        25: "#FFFEF2",
        50: "#FFF9E3",
        100: "#FFF5C2",
        200: "#FFCB8E",
        300: "#FFB55E",
        400: "#FFAC4A",
        500: "#FFA031",
        600: "#FF961B",
        700: "#E17A00",
        800: "#C16800",
        900: "#A85B00"
      },
      success: {
        25: "#F6FEF9",
        50: "#E4FFEE",
        100: "#D1FADF",
        200: "#A6F4C5",
        300: "#6CE9A6",
        400: "#32D583",
        500: "#12B76A",
        600: "#039855",
        700: "#027A48",
        800: "#05603A",
        900: "#054F31"
      },
      purple: {
        25: "#FEEFFF",
        50: "#FCDEFF",
        100: "#FACDFF",
        200: "#F7BDFF",
        300: "#F5ABFF",
        400: "#E580F2",
        500: "#C356D1",
        600: "#AB20BD",
        700: "#852292",
        800: "#5F2167",
        900: "#470050"
      },
      green: {
        25: "#F0FFF1",
        50: "#E1FFE3",
        100: "#D3FFD5",
        200: "#C5FFC7",
        300: "#ACFDB0",
        400: "#94F899",
        500: "#77D37C",
        600: "#5CCD63",
        700: "#4AAF50",
        800: "#3A913F",
        900: "#1F6D24"
      },
      gray: {
        25: "#F6F6F6",
        50: "#F3F3F3",
        100: "#F0F0F0",
        200: "#EBEBEB",
        300: "#E2E2E2",
        400: "#D7D7D7",
        500: "#CCCCCC",
        600: "#C4C4C4",
        700: "#B5B5B5",
        800: "#7C7C7C",
        900: "#5C5C5C"
      },
      yellow: {
        25: "#FAF9EF",
        50: "#F6F6E6",
        100: "#F4F2D1",
        200: "#F5F2C7",
        300: "#F6F3BE",
        400: "#F8F4B6",
        500: "#FAF6AC",
        600: "#F6F39A",
        700: "#F8F487",
        800: "#D3CE25",
        900: "#C1BD23"
      },
      ivory: {
        25: "#FBF9F4",
        50: "#F9F6F0",
        100: "#FAF7F0",
        200: "#F9F6F0",
        300: "#F9F6EF",
        400: "#F0EEE9",
        500: "#EEEAE0",
        600: "#ECE7DB",
        700: "#E9E4D9",
        800: "#E4DED1",
        900: "#DDD7C9"
      },
      "qc-ice": {
        DEFAULT: "#FAF8FF"
      },
      "qc-frost": {
        DEFAULT: "#EBEAF1"
      },
      qcNeutrals: {
        100: "#FFFFFF",
        200: "#F7F9FF",
        300: "#EAECF6",
        400: "#C2C5D5",
        500: "#73718A",
        600: "#595770",
        700: "#484357",
        800: "#2A2733",
        900: "#000000"
      },
      qcInfo: {
        50: "#EDF5FF",
        100: "#DAEBFF",
        200: "#B6D7FF",
        300: "#91C3FF",
        400: "#6DAFFF",
        500: "#489BFF",
        600: "#2A7AD9",
        700: "#0B58B3",
        800: "#094287",
        900: "#062C5A"
      },
      qcGreen: {
        50: "#F4FAEE",
        100: "#E9F5DD",
        200: "#D3ECBB",
        300: "#BCE298",
        400: "#A6D976",
        500: "#90CF54",
        600: "#6FA839",
        700: "#4D801D",
        800: "#3A6016",
        900: "#27400F"
      },
      qcDanger: {
        50: "#FEEDED",
        100: "#FDDBDB",
        200: "#FAB6B7",
        300: "#F89293",
        400: "#F56D6F",
        500: "#F3494B",
        600: "#D3383A",
        700: "#B22628",
        800: "#861D1E",
        900: "#591314"
      },
      qcWarning: {
        50: "#FFF9EC",
        100: "#FFF2D8",
        200: "#FFE5B1",
        300: "#FFD78B",
        400: "#FFCA64",
        500: "#FFBD3D",
        600: "#D4901F",
        700: "#A86200",
        800: "#7E4A00",
        900: "#543100"
      },
      qcGlacier: {
        50: "#EFF9FD",
        100: "#DFF3FA",
        200: "#BFE8F5",
        300: "#9FDCF0",
        400: "#7ED0EB",
        500: "#5EC5E6",
        600: "#3EB9E1",
        700: "#3090AF",
        800: "#23677D",
        900: "#153E4B"
      },
      qcIris: {
        50: "#F3EDFD",
        100: "#E6DAFA",
        200: "#CCB6F5",
        300: "#B391F0",
        400: "#996CEB",
        500: "#8048E6",
        600: "#6623E1",
        700: "#4F1BAF",
        800: "#39147D",
        900: "#220C4B"
      },
      qcSunset: {
        50: "#FEF3EA",
        100: "#FDE6D5",
        200: "#FCCCAA",
        300: "#FAB380",
        400: "#F89955",
        500: "#F7802B",
        600: "#F56600",
        700: "#BF4F00",
        800: "#883900",
        900: "#522200"
      }
    },
    extend: {
      fontFamily: {
        sans: ["Source Sans Pro", ...defaultTheme.fontFamily.sans],
        inter: ["Inter", ...defaultTheme.fontFamily.sans]
      },
      boxShadow: {
        qc: "0px 4px 4px 0px rgba(0, 0, 0, 0.25);",
        "qc-sm": "-2px 0px 10px 0px rgba(0, 0, 0, 0.25);",
        "qc-md": "0px 4px 40px 0px rgba(0, 0, 0, 0.25);",
        tableHeader: "0px 4px 24px 0px rgba(0,0,0,0.15)"
      },
      screens: { tall: { raw: "(min-height: 650px)" } },
      animation: {
        "spin-fast": "spin 0.5s linear infinite"
      }
    }
  },
  plugins: [],
  prefix: "tw-",
  corePlugins: {
    preflight: false
  }
};
